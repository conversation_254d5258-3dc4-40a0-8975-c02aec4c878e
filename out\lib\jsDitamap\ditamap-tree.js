import { DitamapNode } from "./ditamap-node";
export class DitamapTree {
    constructor(rootMapName, files) {
        /**
         * Underlying tree data structure
         */
        this.root = null;
        /**
         * Name of the root map
         */
        this.rootMapName = null;
        this.workspace = files ?? new Map();
        this.root = this.buildTree(rootMapName);
        this.rootMapName = rootMapName;
    }
    buildTree(mapName, parent) {
        if (this.workspace.has(mapName)) {
            return this._treeBfs(this.root);
        }
        return null;
    }
    _fetchMap(mapName) { }
    _attachNodeToParent(node, parent) { }
    /**
     * Builds json tree from xml
     * @param rootHtml - parsed xml element
     */
    _treeBfs(rootHtml) {
        let tree = new DitamapNode();
        tree.domElement = rootHtml;
        let Q = [[rootHtml, tree]];
        while (Array.isArray(Q) && Q.length > 0) {
            let [element, parent, mapName] = Q.shift();
            for (let child of element.children) {
                let newNode = new DitamapNode();
                if (DitamapTree.isMap(child)) {
                    this._decorateMapNode(child, newNode);
                    let newMapName = DitamapTree.getMapName(child, this.rootMapName);
                    let mapNameToUse = newMapName ?? mapName;
                    if (!newMapName) {
                        console.warn("No map name found for", child);
                    }
                    Q.push([child, newNode, mapNameToUse]);
                    parent.children.push(newNode);
                }
                if (DitamapTree.isContent(child)) {
                    Q.push([child, newNode, mapName]);
                    parent.children.push(newNode);
                }
            }
        }
        return tree;
    }
    //---- Helpers ----
    /**
     * Checks if an html element is a ditamap
     * @param element
     */
    static isMap(element) {
        const isBookmap = element.tagName === "bookmap";
        const isDitamap = element.tagName === "map";
        const hasDitamapFormat = element.getAttribute("format") == "ditamap";
        const hasHref = element.getAttribute("href");
        return isDitamap || isBookmap || Boolean(hasDitamapFormat && hasHref);
    }
    /**
     * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)
     * @param element
     */
    static isContent(element) {
        return element.tagName === "topicref" || element.tagName === "chapter";
    }
    /**
     * Decorates a tree map node with information from the xml
     * @param element - xml element
     * @param parent - parent node in the json tree
     */
    _decorateMapNode(element, parent, mapName) {
        let node = new DitamapNode();
        node.href = element.getAttribute("href");
        node.containingMap = mapName;
        node.title = this._removeXmlNoise(DitamapTree.getMapTitle(element));
        node.domElement = element;
        node.embeddedElement = element;
        return node;
    }
    static getMapName(element, rootMapName) {
        if (element.getAttribute("href")) {
            return element.getAttribute("href");
        }
        if (rootMapName &&
            (element.tagName.toLowerCase() === "bookmap" ||
                element.tagName.toLowerCase() === "map")) {
            return rootMapName;
        }
        return undefined;
    }
    static getMapTitle(element) {
        let mainbooktitleEl = element.querySelector("mainbooktitle");
        if (mainbooktitleEl)
            return mainbooktitleEl.innerHTML;
        let titleEl = element.querySelector("title");
        if (titleEl)
            return titleEl.innerText || titleEl.innerHTML;
        let titleAtt = element.getAttribute("title");
        if (titleAtt)
            return titleAtt;
        return element.tagName;
    }
    _removeXmlNoise(str) {
        if (str)
            return str.replace("<?xm-replace_text Main Book Title ?>", "");
        return str;
    }
}
//# sourceMappingURL=ditamap-tree.js.map