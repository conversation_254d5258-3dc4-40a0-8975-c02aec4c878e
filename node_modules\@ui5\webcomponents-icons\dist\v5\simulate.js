import { registerIcon } from "@ui5/webcomponents-base/dist/asset-registries/Icons.js";

const name = "simulate";
const pathData = "M390 32q24 0 41 17t17 41v332q0 24-17 41t-41 17H122q-24 0-41-17t-17-41V90q0-24 17-41t41-17h268zm7 58q0-7-7-7H122q-7 0-7 7v332q0 7 7 7h268q7 0 7-7V90zm-71 38q11 0 18.5 7.5T352 154v12q0 11-7.5 18.5T326 192H186q-11 0-18.5-7.5T160 166v-12q0-11 7.5-18.5T186 128h140zm-150 96q16 0 16 16t-16 16-16-16 16-16zm64 0q16 0 16 16t-16 16-16-16 16-16zm80 16q0 16-16 16t-16-16 16-16 16 16zm-144 48q16 0 16 16t-16 16-16-16 16-16zm64 0q16 0 16 16t-16 16-16-16 16-16zm74 0q11 0 18 7.5t7 18.5v44q0 11-7 18.5t-18 7.5-18.5-7.5T288 358v-44q0-11 7.5-18.5T314 288zm-138 64q16 0 16 16t-16 16-16-16 16-16zm64 0q16 0 16 16t-16 16-16-16 16-16z";
const ltr = false;
const accData = null;
const collection = "SAP-icons-v5";
const packageName = "@ui5/webcomponents-icons";

registerIcon(name, { pathData, ltr, collection, packageName });

export default "SAP-icons-v5/simulate";
export { pathData, ltr, accData };