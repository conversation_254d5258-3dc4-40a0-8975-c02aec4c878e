import { provide, createContext } from "@lit/context";
import { html, LitElement } from "lit";
import { customElement } from "lit/decorators";

// Only the key "store" matters here as that is what is used in the context consumer
const mockStore = createContext<any>(Symbol("store"));

@customElement("store-wrapper")
class StoreWrapper extends LitElement {
  store: any = {};

  @provide({ context: mockStore })
  globalStore = this.store;

  render() {
    return html`<div><slot></slot></div>`;
  }
}
