import { LitElement, PropertyValues } from "lit-element";
import "@ui5/webcomponents/dist/MultiComboBox.js";
import "@ui5/webcomponents/dist/MultiComboBoxItem.js";
import "@ui5/webcomponents/dist/MultiComboBoxItemGroup.js";
/**
 * MultiComboBox component using UI5 Web Components.
 *
 * comboBoxGroup is either the group target, or a single MCB
 *
 */
interface McbData {
    [mcbTargetGroup: string]: McbGroupData;
}
interface McbGroupData {
    placeholder: string;
    mcbItems: string[];
}
export declare class WexMultiComboBox extends LitElement {
    mcbData: McbData | null;
    mcbItems: string[];
    initialSelectedItems: McbGroupData | null;
    dataGroup: string | null;
    placeholder: string;
    private _selectedMcbItems;
    static styles: import("lit-element").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    updated(changedProperties: PropertyValues): void;
    _init(): void;
    _handleSelectionChange(e: CustomEvent): void;
    _renderItems(): import("lit-element").TemplateResult[];
    _renderGroupedItems(): import("lit-element").TemplateResult[] | null;
    render(): import("lit-element").TemplateResult;
}
export {};
//# sourceMappingURL=multi-combobox.d.ts.map