import { LitElement, html, css } from "lit";

class WexMainPane extends LitElement {
  static get properties() {
    return {};
  }

  // :host {
  //   display: flex;
  //   flex: 1;
  //   width: 100%;
  // }
  static get styles() {
    return css`
      .mainpane {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        align-items: center;
        justify-content: space-evenly;
        padding: 0rem 15% 0 15%;
        color: var(--font-color);
        height: 100%;
        max-height: 100%;
      }
      .mainpane > *:not(:last-child) {
        margin-bottom: 1rem;
      }
    `;
  }

  render() {
    return html`
      <section class="mainpane">
        <slot></slot>
      </section>
    `;
  }
}

customElements.define("wex-main-pane", WexMainPane);
