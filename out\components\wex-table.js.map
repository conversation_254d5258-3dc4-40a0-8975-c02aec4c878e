{"version": 3, "file": "wex-table.js", "sourceRoot": "", "sources": ["../../src/components/wex-table.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE5D,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAExC,OAAO,yCAAyC,CAAC;AACjD,OAAO,+CAA+C,CAAC;AACvD,OAAO,4CAA4C,CAAC;AACpD,OAAO,6CAA6C,CAAC;AACrD,OAAO,IAAI,MAAM,YAAY,CAAC;AAGvB,IAAM,QAAQ,GAAd,MAAM,QAAS,SAAQ,UAAU;IAAjC;;QACsB,YAAO,GAAU,EAAE,CAAC;QACpB,SAAI,GAAU,EAAE,CAAC;QACf,iBAAY,GAAG,KAAK,CAAC;QACrB,eAAU,GAAG,KAAK,CAAC;QACrB,cAAS,GAAU,EAAE,CAAC;QACrB,kBAAa,GAAG,EAAE,CAAC;QAI/C,8BAA8B;QACtB,WAAM,GAAU,EAAE,CAAC;QACnB,kBAAa,GAAQ,IAAI,CAAC;IAiepC,CAAC;IAheC,kDAAkD;IAElD,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAyIT,CAAC;IACJ,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,sBAAsB;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEpC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YAC1D,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,YAAY;QACV,8EAA8E;IAChF,CAAC;IAED,OAAO;QACL,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACvB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAClC,CAAC;IAED,WAAW,CAAC,KAAU;QACpB,sBAAsB;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,cAAc;QACZ,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC;IAClC,CAAC;IAED,WAAW,CAAC,CAAQ;QAClB,MAAM,MAAM,GAAG,CAAC,CAAC,aAA4B,CAAC;QAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAChD,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK;YAAE,OAAO;QAC7B,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;SACvC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,IAAS,EAAE,CAAc;QACrC,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAC/B,IAAI,CAAC,IAAI;YAAE,OAAO;QAClB,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,QAAQ,EAAE;YACxB,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;SACxB,CAAC,CACH,CAAC;IACJ,CAAC;IAED,gBAAgB,CAAC,CAAc;QAC7B,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACnC,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,WAAW,EAAE;YAC3B,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE;SACzB,CAAC,CACH,CAAC;IACJ,CAAC;IAED,YAAY,CAAC,IAAS,EAAE,CAAa;QACnC,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,YAAY;YAAE,OAAO;QAC9B,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE1B,MAAM,IAAI,GAAI,CAAC,CAAC,MAAc,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,OAAO,EAAE;YACvB,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI;gBACX,IAAI;gBACJ,QAAQ,EAAE,CAAC,CAAC,QAAQ;gBACpB,OAAO,EAAE,CAAC,CAAC,OAAO;aACnB;SACF,CAAC,CACH,CAAC;IACJ,CAAC;IAED,iBAAiB,CAAC,IAAS,EAAE,CAAc;QACzC,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,YAAY,EAAE;YAC5B,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;SACxB,CAAC,CACH,CAAC;IACJ,CAAC;IAED,uBAAuB,CAAC,CAAc;QACpC,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC;QACtB,IAAI,CAAC,IAAI;YAAE,OAAO;QAClB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,WAAW,EAAE;YAC3B,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;SAC3B,CAAC,CACH,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,IAAS,EAAE,CAAc;QACrC,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,MAAM,MAAM,GAAG,CAAC,CAAC,aAA4B,CAAC;QAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM;YAAE,OAAO;QACpB,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,aAAa,EAAE;YAC7B,MAAM,EAAE;gBACN,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,IAAI;aACZ;SACF,CAAC,CACH,CAAC;IACJ,CAAC;IAED,8BAA8B;IAC9B,wBAAwB;IACxB,yBAAyB;IAEzB,2BAA2B;IAC3B,6BAA6B;IAC7B,IAAI;IAEJ,+BAA+B;IAC/B,iCAAiC;IAEjC,oEAAoE;IACpE,oEAAoE;IACpE,gCAAgC;IAChC,MAAM;IACN,IAAI;IAEJ,wBAAwB;IACxB,4BAA4B;IAE5B,uFAAuF;IACvF,IAAI;IAEJ;;;;;;;;;OASG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE;YAC9B,IAAI,aAAa,GAAG,IAAI,CAAC;YAEzB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;oBAC9C,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;wBACnC,aAAa;4BACX,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBAChE,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;YACtC,CAAC;YAED,OAAO,aAAa,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa;YACvC,CAAC,CAAC,WAAW,IAAI,CAAC,aAAa,GAAG;YAClC,CAAC,CAAC,EAAE,CAAC;QAEP,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,wDAAwD;QAElG,MAAM,QAAQ,GAAG,IAAI,CAAA;mCACU,cAAc;;;mBAG9B,IAAI,CAAC,SAAS;;4BAEL,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC;;;2BAGxC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;6BACrB,IAAI,CAAC,IAAI,CAAC,MAAM;;;;;cAK/B,YAAY;YACZ,CAAC,CAAC,IAAI,CAAA;;;wCAGoB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;;kCAEtC,aAAa;gBACnB,CAAC,CAAC,iBAAiB;gBACnB,CAAC,CAAC,+BAA+B;;;;;;iBAM5C;YACH,CAAC,CAAC,IAAI;cACN,IAAI,CAAC,OAAO,CAAC,GAAG,CAChB,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAA;;;4BAGC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;sBAC5B,GAAG,CAAC,UAAU;YACd,CAAC,CAAC,IAAI,CAAA;;;2CAGe,GAAG,CAAC,IAAI,IAAI,KAAK;4CAChB,GAAG,CAAC,KAAK;;wCAEb,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;2CAQxB,GAAG,CAAC,IAAI,IAAI,MAAM;4CACjB,GAAG,CAAC,KAAK;;wCAEb,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;yBAQ1C;YACH,CAAC,CAAC,IAAI,CAAA,EAAE;;;eAGf,CACF;cACC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CACtB,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAA;;;mCAGQ,GAAG,CAAC,QAAQ;4BACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;kCAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;;;oBAGpD,YAAY;YACZ,CAAC,CAAC,IAAI,CAAA;;;;sCAIY,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;;;sCAGlC,GAAG,CAAC,QAAQ;gBAClB,CAAC,CAAC,iBAAiB;gBACnB,CAAC,CAAC,+BAA+B;;;;;uBAK1C;YACH,CAAC,CAAC,IAAI;oBACN,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACzB,IAAI,GAAG,CAAC,KAAK,IAAI,UAAU,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAA;;4BAEL,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;;uBAEvC,CAAC;YACJ,CAAC;YACD,IAAI,GAAG,CAAC,KAAK,IAAI,QAAQ,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAA;;;;sCAIK,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;;;;uCAIjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;;6CAEjB,GAAG,CAAC,MAAM;;;;uBAIhC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAA;;4BAGL,GAAG,CAAC,QAAQ;oBACV,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,4CAA4C;oBAC/E,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,kCAAkC;gBAC7D;;uBAEH,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;;eAEL,CACF;;;;;0BAKa,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;;;KAG5D,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AA5e4B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;yCAAqB;AACpB;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;sCAAkB;AACf;IAA5B,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;8CAAsB;AACrB;IAA5B,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;4CAAoB;AACrB;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;2CAAuB;AACrB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;+CAAoB;AACnB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;+CAAoB;AACnB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;+CAAoB;AARpC,QAAQ;IADpB,aAAa,CAAC,WAAW,CAAC;GACd,QAAQ,CA6epB", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\n\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\nimport { getLocalTime } from \"lib/util\";\r\n\r\nimport \"@ui5/webcomponents-compat/dist/Table.js\";\r\nimport \"@ui5/webcomponents-compat/dist/TableColumn.js\";\r\nimport \"@ui5/webcomponents-compat/dist/TableRow.js\";\r\nimport \"@ui5/webcomponents-compat/dist/TableCell.js\";\r\nimport _get from \"lodash.get\";\r\n\r\n@customElement(\"wex-table\")\r\nexport class WexTable extends LitElement {\r\n  @property({ type: Array }) columns: any[] = [];\r\n  @property({ type: Array }) rows: any[] = [];\r\n  @property({ type: Boolean }) enableSelect = false;\r\n  @property({ type: Boolean }) enableMenu = false;\r\n  @property({ type: Array }) menuItems: any[] = [];\r\n  @property({ type: String }) defaultHeight = \"\";\r\n  @property({ type: Object }) activeFilters: any;\r\n  @property({ type: Object }) cellRenderers: any;\r\n\r\n  // private state: object = {};\r\n  private string: any[] = [];\r\n  private _subscription: any = null;\r\n  // private contextMenu: HTMLElement | null = null;\r\n\r\n  static get styles() {\r\n    return css`\r\n      #container {\r\n        display: flex;\r\n        flex-direction: column;\r\n        min-width: 100%;\r\n        height: 100%;\r\n        background: #fff;\r\n        overflow-y: auto;\r\n\r\n        position: relative;\r\n      }\r\n      button {\r\n        margin: 0;\r\n        padding: 0;\r\n        border: 0;\r\n        background: transparent;\r\n        overflow: hidden;\r\n        cursor: pointer;\r\n      }\r\n      .column {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        min-width: 40px;\r\n      }\r\n      .column > *:not(:last-child) {\r\n        margin-right: 0.5rem;\r\n      }\r\n      .sort-container {\r\n        display: flex;\r\n        flex-direction: column;\r\n      }\r\n      .sort-container > *:not(:last-child) {\r\n        margin-bottom: 0.25rem;\r\n      }\r\n      .sort-container > button {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        height: 0.75rem;\r\n        color: var(--clr-gray-light);\r\n      }\r\n      .sort-container > button:not(:disabled):hover {\r\n        color: var(--clr-primary-ultra-light);\r\n      }\r\n      .sort-container > button[data-value=\"asc\"] {\r\n      }\r\n      .sort-container > button[data-value=\"desc\"] {\r\n      }\r\n      .sort-container > button:disabled {\r\n        cursor: default;\r\n        color: var(--clr-black);\r\n      }\r\n      .sort-icon {\r\n        width: 1rem;\r\n      }\r\n      iron-icon.red {\r\n        color: red !important;\r\n      }\r\n      .row[data-selected=\"true\"] {\r\n        --sapList_Background: var(--row-selected-background);\r\n      }\r\n\r\n      .action-icon {\r\n        width: 1rem;\r\n        opacity: 0.7;\r\n        cursor: pointer;\r\n      }\r\n      .action-icon:hover {\r\n        opacity: 1;\r\n      }\r\n\r\n      .file-icon[data-status=\"_rogue_lock\"] {\r\n        color: purple;\r\n      }\r\n      .file-icon[data-status=\"_locked_by_other\"] {\r\n        color: red;\r\n      }\r\n      .file-icon[data-status=\"_room_locked_by_user\"],\r\n      .file-icon[data-status=\"_sesh_locked_by_user\"] {\r\n        color: green;\r\n      }\r\n      .file-icon[data-status=\"_xdocs_locked_by_user\"] {\r\n        color: orange;\r\n      }\r\n      .file-icon[data-status=\"_locked_concurrent\"] {\r\n        color: blue;\r\n      }\r\n\r\n      .flex-spacer {\r\n        flex-grow: 1;\r\n      }\r\n\r\n      .popup-container {\r\n        z-index: 100;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        position: absolute;\r\n\r\n        box-sizing: border-box;\r\n\r\n        background-color: rgba(0, 0, 0, 0.3);\r\n        width: 100%;\r\n        height: 100%;\r\n        overflow: hidden;\r\n      }\r\n\r\n      .popup {\r\n        border: 1px solid #ccc;\r\n        border-radius: 5px;\r\n        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n        box-sizing: border-box;\r\n\r\n        padding: 3rem;\r\n        background-color: #fff;\r\n        width: 80%;\r\n        height: 80%;\r\n      }\r\n\r\n      .close-container {\r\n        display: flex;\r\n        justify-content: right;\r\n        position: absolute;\r\n        top: 9%;\r\n        right: 9%;\r\n      }\r\n\r\n      .close-container button {\r\n        background-color: #fff;\r\n        border-radius: 100%;\r\n        user-select: none;\r\n      }\r\n\r\n      .popup-container[hidden] {\r\n        display: none;\r\n      }\r\n    `;\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    // this.state = state;\r\n    this.string = state[state.langCode];\r\n\r\n    this._subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this._subscription);\r\n  }\r\n\r\n  firstUpdated() {\r\n    // this.contextMenu = this.shadowRoot?.querySelector(\"#context-menu\") ?? null;\r\n  }\r\n\r\n  updated() {\r\n    console.log(\"updated\");\r\n    console.log(this.activeFilters);\r\n  }\r\n\r\n  stateChange(state: any) {\r\n    // this.state = state;\r\n    this.string = state[state.langCode];\r\n  }\r\n\r\n  _isAllSelected() {\r\n    const res = this.rows.find((row) => !row.selected);\r\n    return this.rows.length && !res;\r\n  }\r\n\r\n  _handleSort(e: Event) {\r\n    const target = e.currentTarget as HTMLElement;\r\n    const field = target.getAttribute(\"data-field\");\r\n    const value = target.getAttribute(\"data-value\");\r\n    if (!field || !value) return;\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"sort\", {\r\n        detail: { field: field, value: value },\r\n      })\r\n    );\r\n  }\r\n\r\n  _handleSelect(item: any, e: CustomEvent) {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (!this.enableSelect) return;\r\n    if (!item) return;\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"select\", {\r\n        detail: { value: item },\r\n      })\r\n    );\r\n  }\r\n\r\n  _handleSelectAll(e: CustomEvent) {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (!this.enableSelect) return;\r\n    const bool = this._isAllSelected();\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"selectall\", {\r\n        detail: { value: !bool },\r\n      })\r\n    );\r\n  }\r\n\r\n  _handleClick(item: any, e: MouseEvent) {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (this.enableSelect) return;\r\n    if (!item) return;\r\n\r\n    console.log(\"row?\", item);\r\n\r\n    const icon = (e.target as any).icon;\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"click\", {\r\n        detail: {\r\n          value: item,\r\n          icon,\r\n          shiftKey: e.shiftKey,\r\n          ctrlKey: e.ctrlKey,\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  _handleRightClick(item: any, e: CustomEvent) {\r\n    e.preventDefault();\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"rightclick\", {\r\n        detail: { value: item },\r\n      })\r\n    );\r\n  }\r\n\r\n  _handleClickContextMenu(e: CustomEvent) {\r\n    const data = e.detail;\r\n    if (!data) return;\r\n    const { value } = data;\r\n    const { name: action } = value;\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"menuclick\", {\r\n        detail: { action: action },\r\n      })\r\n    );\r\n  }\r\n\r\n  _handleAction(item: any, e: CustomEvent) {\r\n    e.stopPropagation();\r\n    const target = e.currentTarget as HTMLElement;\r\n    const action = target.getAttribute(\"data-action\");\r\n    if (!action) return;\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"actionclick\", {\r\n        detail: {\r\n          action: action,\r\n          value: item,\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  // _handleInfoClick(item, e) {\r\n  //   e.preventDefault();\r\n  //   e.stopPropagation();\r\n\r\n  //   this.showPopup = true;\r\n  //   this.selectedRow = item;\r\n  // }\r\n\r\n  // _handleOutsideClick(event) {\r\n  //   if (!this.showPopup) return;\r\n\r\n  //   const popupContainer = this.shadowRoot.querySelector(\"#popup\");\r\n  //   if (popupContainer && !popupContainer.contains(event.target)) {\r\n  //     this._handleClosePopup();\r\n  //   }\r\n  // }\r\n\r\n  // _handleClosePopup() {\r\n  //   this.showPopup = false;\r\n\r\n  //   // document.removeEventListener(\"mousedown\", this._handleOutsideClick.bind(this));\r\n  // }\r\n\r\n  /**\r\n   * This incorporates filtering logic directly into wex table. If there are no filterValues passed\r\n   * to the table, it will just return all rows.\r\n   *\r\n   * The structure of the filterValues object should be\r\n   * {\r\n   *  key: [...filterStrings]\r\n   * }\r\n   * @returns filtered rows based on filterValues property input\r\n   */\r\n  _filterRows() {\r\n    return this.rows.filter((row) => {\r\n      let shouldInclude = true;\r\n\r\n      if (this.activeFilters) {\r\n        Object.keys(this.activeFilters).forEach((key) => {\r\n          if (this.activeFilters[key].length) {\r\n            shouldInclude =\r\n              shouldInclude && this.activeFilters[key].includes(row[key]);\r\n          }\r\n        });\r\n      }\r\n\r\n      if (!shouldInclude) {\r\n        console.log(\"filtered a row;\", row);\r\n      }\r\n\r\n      return shouldInclude;\r\n    });\r\n  }\r\n\r\n  /*\r\n   * * added dummy column and cell to handle unexpected padding-left on first column\r\n   */\r\n  render() {\r\n    const isAllSelected = this._isAllSelected();\r\n    const isSelectable = this.enableSelect;\r\n    const containerStyle = this.defaultHeight\r\n      ? `height: ${this.defaultHeight};`\r\n      : \"\";\r\n\r\n    const cellRenderers = this.cellRenderers; // || this._cellRenderers; // removed built-in renderers\r\n\r\n    const template = html`\r\n      <div id=\"container\" style=\"${containerStyle}\">\r\n        <vaadin-context-menu\r\n          id=\"context-menu\"\r\n          .items=${this.menuItems}\r\n          selector=\".has-menu\"\r\n          @item-selected=\"${this._handleClickContextMenu.bind(this)}\"\r\n        >\r\n          <ui5-table\r\n            no-data-text=${this.string[\"_nofiles\"]}\r\n            ?show-no-data=\"${this.rows.length}\"\r\n            sticky-column-header\r\n          >\r\n            <ui5-table-column slot=\"columns\" id=\"dummy-column\">\r\n            </ui5-table-column>\r\n            ${isSelectable\r\n              ? html`\r\n                  <ui5-table-column slot=\"columns\">\r\n                    <div class=\"column check-column\">\r\n                      <button @click=\"${this._handleSelectAll.bind(this)}\">\r\n                        <iron-icon\r\n                          icon=\"${isAllSelected\r\n                            ? \"icons:check-box\"\r\n                            : \"icons:check-box-outline-blank\"}\"\r\n                          class=\"action-icon check-icon\"\r\n                        ></iron-icon>\r\n                      </button>\r\n                    </div>\r\n                  </ui5-table-column>\r\n                `\r\n              : null}\r\n            ${this.columns.map(\r\n              (col) => html`\r\n                <ui5-table-column slot=\"columns\">\r\n                  <div class=\"column\">\r\n                    <span>${this.string[col.label]}</span>\r\n                    ${col.isSortable\r\n                      ? html`\r\n                          <div class=\"sort-container\">\r\n                            <button\r\n                              ?disabled=\"${col.sort == \"asc\"}\"\r\n                              data-field=\"${col.field}\"\r\n                              data-value=\"asc\"\r\n                              @click=\"${this._handleSort.bind(this)}\"\r\n                            >\r\n                              <iron-icon\r\n                                class=\"sort-icon\"\r\n                                icon=\"vaadin:caret-up\"\r\n                              ></iron-icon>\r\n                            </button>\r\n                            <button\r\n                              ?disabled=\"${col.sort == \"desc\"}\"\r\n                              data-field=\"${col.field}\"\r\n                              data-value=\"desc\"\r\n                              @click=\"${this._handleSort.bind(this)}\"\r\n                            >\r\n                              <iron-icon\r\n                                class=\"sort-icon\"\r\n                                icon=\"vaadin:caret-down\"\r\n                              ></iron-icon>\r\n                            </button>\r\n                          </div>\r\n                        `\r\n                      : html``}\r\n                  </div>\r\n                </ui5-table-column>\r\n              `\r\n            )}\r\n            ${this._filterRows().map(\r\n              (row) => html`\r\n                <ui5-table-row\r\n                  class=\"row has-menu\"\r\n                  data-selected=\"${row.selected}\"\r\n                  @click=\"${this._handleClick.bind(this, row)}\"\r\n                  @contextmenu=\"${this._handleRightClick.bind(this, row)}\"\r\n                >\r\n                  <ui5-table-cell id=\"dummy-cell\"> </ui5-table-cell>\r\n                  ${isSelectable\r\n                    ? html`\r\n                        <ui5-table-cell>\r\n                          <button\r\n                            data-action=\"select\"\r\n                            @click=\"${this._handleSelect.bind(this, row)}\"\r\n                          >\r\n                            <iron-icon\r\n                              icon=\"${row.selected\r\n                                ? \"icons:check-box\"\r\n                                : \"icons:check-box-outline-blank\"}\"\r\n                              class=\"action-icon check-icon\"\r\n                            ></iron-icon>\r\n                          </button>\r\n                        </ui5-table-cell>\r\n                      `\r\n                    : null}\r\n                  ${this.columns.map((col) => {\r\n                    if (col.field == \"lockDate\") {\r\n                      return html`\r\n                        <ui5-table-cell align=\"left\">\r\n                          ${getLocalTime(_get(row, col.field))}\r\n                        </ui5-table-cell>\r\n                      `;\r\n                    }\r\n                    if (col.field == \"status\") {\r\n                      return html`\r\n                        <ui5-table-cell>\r\n                          <button\r\n                            data-action=\"properties\"\r\n                            @click=\"${this._handleAction.bind(this, row)}\"\r\n                          >\r\n                            <iron-icon\r\n                              icon=\"vaadin:file-text-o\"\r\n                              title=\"${this.string[row.status]}\"\r\n                              class=\"action-icon file-icon\"\r\n                              data-status=\"${row.status}\"\r\n                            ></iron-icon>\r\n                          </button>\r\n                        </ui5-table-cell>\r\n                      `;\r\n                    } else {\r\n                      return html`\r\n                        <ui5-table-cell align=\"left\">\r\n                          ${\r\n                            col.renderer\r\n                              ? cellRenderers[col.renderer](row) // use the renderer callback from columns.js\r\n                              : _get(row, col.field) // Or just render the static value\r\n                          }\r\n                        </ui5-table-cell>\r\n                      `;\r\n                    }\r\n                  })}\r\n                </ui5-table-row>\r\n              `\r\n            )}\r\n          </ui5-table>\r\n        </vaadin-context-menu>\r\n        <div\r\n          class=\"flex-spacer has-menu\"\r\n          @contextmenu=\"${this._handleRightClick.bind(this, null)}\"\r\n        ></div>\r\n      </div>\r\n    `;\r\n    return template;\r\n  }\r\n}\r\n"]}