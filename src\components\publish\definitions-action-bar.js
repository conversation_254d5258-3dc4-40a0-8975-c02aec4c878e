import { LitElement, html, css } from "lit-element";
import { storeInstance } from "store/index.js";
import * as wexlib from "lib/wexlib.js";

import "../../components/base/col-item";
import "../../components/base/row-item";
import "../../components/base/icon";

class WexPublishDefinitionsActionBar extends LitElement {
  static get properties() {
    return {
      projects: { type: Array },
      pubdefs: { type: Array },
    };
  }

  static get styles() {
    return css``;
  }

  constructor() {
    super();

    this.projects = [];
  }

  async _editPubDef() {
    // console.log("edit clicked");
    const selectedPubDef = this.pubDefs.filter((pubDef) => pubDef.selected);
    // need to toast if multiples are selected; it only supports a single pubdef selection
    // console.log("selectedPubDef Array:", selectedPubDef);
    console.log("pubDef to edit", selectedPubDef[0]);
    storeInstance.dispatch("setState", {
      property: "publish_defs_create_dialog_open",
      value: {
        mode: "Edit",
        projects: this.projects,
        pubDef: selectedPubDef[0],
      },
    });
  }

  async _runPubDef() {
    try {
      const selected = this.pubDefs.filter((pubDef) => pubDef.selected);
      // bail out if anything is not totally complete

      // TODO Xdocs issue with sql tables adding unremovable items

      await wexlib.runPubDefs(selected);
      util.notifyUser("positive", this._string["_msg_pubish_run_success"]);
    } catch {
    } finally {
    }
  }

  async _deletePubDef() {
    console.log("delete pubdef with id", this._selectedPubDefId);
    try {
      const selected = this.pubDefs.filter((pubDef) => pubDef.selected);
      console.log("delete this:", selected);
      await wexlib.rmPubDef(selected[0].pubDefId);

      const pubDefs = await wexlib.getAllPubDefs();
      console.log("pubDefs after delete", pubDefs);
      if (pubDefs.length) {
        console.log("settings pubdefs", pubDefs);
        storeInstance.dispatch("setPubDefs", {
          pubDefs,
        });
        storeInstance.dispatch("touch"); // force state update
      }
    } catch (e) {
      console.error("delete", e);
    }
  }

  _handleSelectAllRows = () => {
    this.dispatchEvent(
      new CustomEvent("select-all-definitions", {
        bubbles: true,
        composed: true,
      })
    );
  };

  _handleClearSelectedRows = () => {
    this.dispatchEvent(
      new CustomEvent("clear-selected-definitions", {
        bubbles: true,
        composed: true,
      })
    );
  };

  render() {
    return html`
      <wex-row-item height="42px">
        <wex-col-item>
          <wex-row-item justifyContent="flex-start" alignItems="center">
            <wex-icon
              icon="icons:clear"
              class="pointer action-icon"
              title="Clear selection"
              @click="${this._handleClearSelectedRows}"
            ></wex-icon>
            <ui5-button @click="${this._handleSelectAllRows}"
              >Select all</ui5-button
            >
          </wex-row-item>
        </wex-col-item>

        <wex-col-item>
          <wex-row-item justifyContent="flex-end" alignItems="center">
            <ui5-button @click=${this._editPubDef.bind(this)}>Edit</ui5-button>
            <ui5-button @click="${this._runPubDef.bind(this)}">Run</ui5-button>
            <ui5-button
              @click="${this._deletePubDef.bind(this)}"
              design="Negative"
              >Delete</ui5-button
            >
          </wex-row-item>
        </wex-col-item>
      </wex-row-item>
    `;
  }
}

customElements.define(
  "wex-publish-definitions-action-bar",
  WexPublishDefinitionsActionBar
);
