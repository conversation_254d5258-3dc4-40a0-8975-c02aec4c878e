import { LitElement } from "lit";
import { html, render } from "lit/html.js";
import { storeInstance } from "store/index.js";
import "@ui5/webcomponents/dist/Select.js";
import "@ui5/webcomponents/dist/Option.js";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "@ui5/webcomponents/dist/Label.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/List.js";
// import "@ui5/webcomponents/dist/StandardListItem.js";
// import "@ui5/webcomponents/dist/CustomListItem.js";
// import "@ui5/webcomponents/dist/GroupHeaderListItem.js";
export class WexDialogProjectSelector extends LitElement {
    constructor() {
        super();
    }
    connectedCallback() {
        super.connectedCallback();
        this.subscription = storeInstance.subscribe((state) => {
            /*WexDialogProjectSelector*/
            this.stateChange(state);
        });
        this.state = storeInstance.state;
        this.string = this.state[this.state.langCode];
        this.projectCats = [];
        this.projectsList = [];
        this.open = false;
        this.myRender();
        this.projectDialog = this.shadowRoot.querySelector("#project-dialog");
    }
    disconnectedCallback() {
        storeInstance.unsubscribe(this.subscription);
    }
    stateChange(state) {
        this.state = state;
        this.string = this.state[this.state.langCode];
        if (this.state.project_dialog_open) {
            if (!this.open) {
                storeInstance.dispatch("getProjects");
                this.open = true;
                this.projectDialog.show();
            }
            if (this.state.global_projects) {
                this.prepData();
                this.myRender();
            }
        }
        else {
            if (this.open) {
                this.open = false;
                this.projectDialog.close();
            }
        }
    }
    prepData() {
        this.projectCats = [];
        this.projectCats.push({
            projCatId: -1,
            projCatName: this.string["_all_project_cats"],
        });
        this.state.global_projects.map((project) => {
            if (!this.projectCats.some((el) => el.projCatId === project.projCatId)) {
                this.projectCats.push({
                    projCatId: project.projCatId,
                    projCatName: project.projCatName,
                });
            }
        });
        this.projectsList = structuredClone(this.state.global_projects);
        this.selectedProjectCatIdx = 0;
        if (this.state.selectedProjectCat) {
            if (this.state.selectedProjectCat.projCatId != -1) {
                //preselect drop down
                for (var i = 0; i < this.projectCats.length; i++) {
                    if (this.projectCats[i].projCatId ==
                        this.state.selectedProjectCat.projCatId) {
                        this.selectedProjectCatIdx = i;
                    }
                }
                //filter projects
                this.projectsList = this.projectsList.filter(function (el) {
                    return el.projCatId == this.state.selectedProjectCat.projCatId;
                }.bind(this));
            }
        }
    }
    myRender() {
        render(this.projectSelectorDialogTemplate(), this.shadowRoot);
    }
    _projectCatSelectorSelected(val) {
        if (this.state.selectedProjectCat) {
            return this.state.selectedProjectCat.projCatId == val;
        }
        else {
            return false;
        }
    }
    projectSelectorDialogTemplate() {
        return html `
      <style>
        #project-dialog{
            width: 40%;
        }
        .project-form{
            flex-direction: column;
            justify-content: space-evenly;
            align-items: flex-start;
            padding:30px 15% 30px 15%;
        }
        .project-form > div {
            display: grid;

        }
        [inner-input] {
            color: inherit;
            font-style: normal;
            -webkit-appearance: none;
            line-height: normal;
            box-sizing: border-box;
            min-width: 3rem;
            text-overflow: ellipsis;
            font-size: inherit;
            font-family: inherit;
            background: transparent;
            border-width: initial;
            border-style: none;
            border-color: initial;
            border-image: initial;
            flex: 1 1 0%;
            outline: none;
        }
        .dialog-footer{
            display: flex;
            align-items: center;
            padding: .5rem;
            }
        }
        .hide{display:none!important;}
        #message{
            margin:5px;width:90%;
        }
        ui5-label{text-align:left;}
        #projectcatselector{width:100%;margin-bottom:30px;}
        ui5-button{
            margin:1rem;
            }
      </style>

      <ui5-dialog
        id="project-dialog"
        header-text="${this.string["_selectproject"]}"
      >
        <section class="project-form">
          <ui5-label>${this.string["_projectcatlable"]}</ui5-label>
          <ui5-select
            id="projectcatselector"
            value-state="Information"
            class="headeritem ui5-content-density-compact"
            @change="${this._setProjectCatSelected}"
          >
            ${this.projectCats.map((item) => html `<ui5-option
                  .item="${item}"
                  id=${`cat-${item.projCatId}`}
                  ?selected=${this._projectCatSelectorSelected(item.projCatId)}
                  >${item.projCatName}</ui5-option
                >`)}
          </ui5-select>

          <ui5-label>${this.string["_project"]}</ui5-label>
          <ui5-list
            id="projectsselector"
            class="full-width  ui5-content-density-compact"
            style="height: 300px"
            infinite-scroll
            @item-click="${this._selectProject.bind(this)}"
          >
            ${this._allOption()}
            ${this.projectsList.map((item) => html `<ui5-li
                  .item="${item}"
                  id=${`project-${item.projectId}`}
                  ?selected="${item == this.selectedProject}"
                  value="${item.projCatId}"
                  description="${item.branchName +
            ": " +
            item.languageCode +
            " : " +
            item.description}"
                  >${item.name}</ui5-li
                >`)}
          </ui5-list>
        </section>

        <div slot="footer" class="dialog-footer">
          <div style="flex: 1;"></div>
          <ui5-button
            id="selectbtn"
            design="Emphasized"
            ?disabled="${!this.selectedProject}"
            @click="${this._saveProject.bind(this)}"
            >${this.string["_ok"]}</ui5-button
          >
          <ui5-button
            id="closeDialogButton"
            design="Emphasized"
            @click="${this.closeDialog.bind(this)}"
            >${this.string["_cancel"]}</ui5-button
          >
        </div>
      </ui5-dialog>
    `;
    }
    _allOption() {
        return html `<ui5-li
      description="${this.string["_projects"]}"
      ?selected="${this.selectedProject == "all"}"
      >${this.string["_all"]}</ui5-li
    >`;
    }
    _setProjectCatSelected(e) {
        if (e.detail.selectedOption) {
            if (e.detail.selectedOption.item) {
                storeInstance.dispatch("setState", {
                    property: "selectedProjectCat",
                    value: e.detail.selectedOption.item,
                });
                //storeInstance.dispatch("setProjectCatFilter", e.detail.selectedOption.item);
                return;
            }
        }
        if (this.state.selectedProjectCat) {
            storeInstance.dispatch("setState", {
                property: "selectedProjectCat",
                value: e.detail.selectedOption.item,
            });
        }
        storeInstance.dispatch("setState", {
            property: "mru_projectcat",
            value: e.detail.selectedOption.item,
        });
    }
    _selectProject(e) {
        if (!e.detail.item.item) {
            this.selectedProject = "all";
        }
        else {
            this.selectedProject = e.detail.item.item;
        }
        this.myRender();
    }
    _saveProject(e) {
        if (this.state.project_dialog_open.hasOwnProperty("callback")) {
            this.state.project_dialog_open.callback(this.selectedProject == "all" ? null : this.selectedProject);
            this.closeDialog();
        }
        else {
            var tmpFor = this.state.project_dialog_open;
            this.closeDialog();
            storeInstance.dispatch("setProjectFilter", {
                for: tmpFor,
                project: this.selectedProject == "all" ? null : this.selectedProject,
            });
        }
        storeInstance.dispatch("setState", {
            property: "mru_project",
            value: this.selectedProject,
        });
    }
    closeDialog() {
        storeInstance.dispatch("setState", {
            property: "project_dialog_open",
            value: null,
        });
    }
}
// customElements.define("wex-dialog-project-selector", WexDialogProjectSelector);
//# sourceMappingURL=wex-dialog-project-selector.js.map