export const PHONES_JSON: DitamapNodeStructure = {
  title: "Phone 1 User Guide",
  type: "map",
  href: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
  rootElementName: "bookmap",
  mapPath: [],
  children: [
    {
      type: "map",
      title: "Submap Phones",
      mapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      mapPath: ["/Content/Phone1_Bookmap_xi1577_1_1.ditamap"],
      href: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      rootElementName: "map",
      children: [
        {
          type: "topicref",
          title: "Begin",
          href: "/Content/begin_xi1612_1_1.xml",
          mapName: "submap_phones_xi1609_1_1.ditamap",
          mapPath: [
            "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            "submap_phones_xi1609_1_1.ditamap",
          ],
          rootElementName: "topicref",
          children: [
            {
              type: "topicref",
              title: "Getting Started",
              href: "/Content/getting_started_xi1615_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Insert the SIM card",
              href: "/Content/insert_SIMcard_xi1616_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Charge Battery",
              href: "/Content/charge_battery_xi1613_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
          ],
        },
        {
          type: "topicref",
          title: "Basics",
          href: "/Content/basics_xi1610_1_1.xml",
          mapName: "submap_phones_xi1609_1_1.ditamap",
          mapPath: [
            "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            "submap_phones_xi1609_1_1.ditamap",
          ],
          rootElementName: "topicref",
          children: [
            {
              type: "topicref",
              title: "Overview of the handset",
              href: "/Content/overview_handset_xi1621_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Make and receive calls",
              href: "/Content/make_and_receive_calls_xi1619_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [
                {
                  type: "topicref",
                  title: "Make a call",
                  href: "/Content/make_calls_xi1620_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
                {
                  type: "topicref",
                  title: "Receive a call",
                  href: "/Content/receive_call_xi1625_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
                {
                  type: "topicref",
                  title: "End a call",
                  href: "/Content/reject_call_xi1626_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
              ],
            },
            {
              type: "topicref",
              title: "Retrieve Voicemail",
              href: "/Content/retrieve_voicemail_xi1631_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Take Photos",
              href: "/Content/taking_photos_xi1637_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [
                {
                  type: "topicref",
                  title: "Take a rear-facing photo",
                  href: "/Content/take_photos_xi1636_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
                {
                  type: "topicref",
                  title: "Take Front-facing Photo",
                  href: "/Content/take_front-facing_photo_xi1635_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
              ],
            },
            {
              type: "topicref",
              title: "Listen to music",
              href: "/Content/listen%20_music_xi1618_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Exchange Data",
              href: "/Content/exchange_data_xi1614_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Reset Phone",
              href: "/Content/reset_phone_xi1630_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
          ],
        },
        {
          type: "topicref",
          title: "Troubleshooting",
          href: "/Content/troubleshooting_xi1638_1_1.xml",
          mapName: "submap_phones_xi1609_1_1.ditamap",
          mapPath: [
            "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            "submap_phones_xi1609_1_1.ditamap",
          ],
          rootElementName: "topicref",
          children: [
            {
              type: "topicref",
              title: "Screen Frozen",
              href: "/Content/screen_frozen_xi1632_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Battery Drain",
              href: "/Content/battery_drain_xi1611_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
          ],
        },
        {
          type: "topicref",
          title: "Service Section",
          href: "/Content/service_section_xi1633_1_1.xml",
          mapName: "submap_phones_xi1609_1_1.ditamap",
          mapPath: [
            "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            "submap_phones_xi1609_1_1.ditamap",
          ],
          rootElementName: "topicref",
          children: [
            {
              type: "topicref",
              title: "Replace Battery",
              href: "/Content/replace_battery_xi1627_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Insert Battery",
              href: "/Content/insert_battery_xi1617_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
          ],
        },
        {
          type: "topicref",
          title: "Specifications",
          href: "/Content/specifications_xi1634_1_1.xml",
          mapName: "submap_phones_xi1609_1_1.ditamap",
          mapPath: [
            "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            "submap_phones_xi1609_1_1.ditamap",
          ],
          rootElementName: "topicref",
          children: [
            {
              type: "topicref",
              title: "Product Specifications",
              href: "",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
          ],
        },
        {
          type: "topicref",
          title: "QuickStart MP4 Handset",
          href: "/Content/QuickStart_MP4_Handset_xi1641_1_1.xml",
          mapName: "submap_phones_xi1609_1_1.ditamap",
          mapPath: [
            "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            "submap_phones_xi1609_1_1.ditamap",
          ],
          rootElementName: "topicref",
          children: [],
        },
        {
          type: "topichead",
          title: "Glossary",
          href: "",
          mapName: "submap_phones_xi1609_1_1.ditamap",
          mapPath: [
            "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
            "submap_phones_xi1609_1_1.ditamap",
          ],
          rootElementName: "topichead",
          children: [
            {
              type: "glossref",
              title: "Dual Band",
              href: "/Content/dual_band_xi1582_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "glossref",
              children: [],
            },
            {
              type: "glossref",
              title: "Earphones",
              href: "/Content/earphones_xi1584_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "glossref",
              children: [],
            },
            {
              type: "topicref",
              title: "NFC Antenna",
              href: "/Content/nfc_antenna_xi1588_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
          ],
        },
      ],
    },
  ],
};

interface DitamapNodeStructure {
  type: string;
  title: string;
  mapName?: string;
  mapPath: string[];
  href: string;
  rootElementName: string;
  children: DitamapNodeStructure[];
}
