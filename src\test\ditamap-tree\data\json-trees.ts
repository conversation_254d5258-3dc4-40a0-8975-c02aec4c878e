export const PHONES_JSON: DitamapNodeStructure = {
  title: "root",
  type: "root",
  mapPath: [],
  rootElementName: "",
  href: "",
  children: [
    {
      title: "Phone 1 User Guide",
      type: "map",
      href: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      rootElementName: "bookmap",
      mapPath: [],
      children: [
        {
          type: "map",
          title: "Submap Phones",
          mapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
          mapPath: ["/Content/Phone1_Bookmap_xi1577_1_1.ditamap"],
          href: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
          rootElementName: "map",
          children: [
            {
              type: "topicref",
              title: "Begin",
              href: "/Content/begin_xi1612_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topic",
              children: [
                {
                  type: "topicref",
                  title: "Getting Started",
                  href: "/Content/getting_started_xi1615_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topic",
                  children: [],
                },
                {
                  type: "topicref",
                  title: "Insert SIM Card",
                  href: "/Content/insert_SIMcard_xi1616_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topic",
                  children: [],
                },
                {
                  type: "topicref",
                  title: "Charge Battery",
                  href: "/Content/charge_battery_xi1613_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topic",
                  children: [],
                },
              ],
            },
            {
              type: "topicref",
              title: "Basics",
              href: "/Content/basics_xi1610_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [
                {
                  type: "topicref",
                  title: "Making Calls",
                  href: "/Content/making_calls_xi1616_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
                {
                  type: "topicref",
                  title: "Text Messaging",
                  href: "/Content/text_messaging_xi1617_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
                {
                  type: "topicref",
                  title: "Managing Contacts",
                  href: "/Content/managing_contacts_xi1618_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
              ],
            },
            {
              type: "topicref",
              title: "Advanced Features",
              href: "/Content/advanced_features_xi1620_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [
                {
                  type: "topicref",
                  title: "Camera",
                  href: "/Content/camera_xi1625_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [
                    {
                      type: "topicref",
                      title: "Taking Photos",
                      href: "/Content/taking_photos_xi1626_1_1.xml",
                      mapName: "submap_phones_xi1609_1_1.ditamap",
                      mapPath: [
                        "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                        "submap_phones_xi1609_1_1.ditamap",
                      ],
                      rootElementName: "topicref",
                      children: [],
                    },
                    {
                      type: "topicref",
                      title: "Video Recording",
                      href: "/Content/video_recording_xi1627_1_1.xml",
                      mapName: "submap_phones_xi1609_1_1.ditamap",
                      mapPath: [
                        "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                        "submap_phones_xi1609_1_1.ditamap",
                      ],
                      rootElementName: "topicref",
                      children: [],
                    },
                  ],
                },
                {
                  type: "topicref",
                  title: "Apps",
                  href: "/Content/apps_xi1628_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
              ],
            },
            {
              type: "topicref",
              title: "Troubleshooting",
              href: "/Content/troubleshooting_xi1638_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [
                {
                  type: "topicref",
                  title: "Common Issues",
                  href: "/Content/common_issues_xi1639_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
                {
                  type: "topicref",
                  title: "Factory Reset",
                  href: "/Content/factory_reset_xi1640_1_1.xml",
                  mapName: "submap_phones_xi1609_1_1.ditamap",
                  mapPath: [
                    "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                    "submap_phones_xi1609_1_1.ditamap",
                  ],
                  rootElementName: "topicref",
                  children: [],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};

interface DitamapNodeStructure {
  type: string;
  title: string;
  mapName?: string;
  mapPath: string[];
  href: string;
  rootElementName: string;
  children: DitamapNodeStructure[];
}
