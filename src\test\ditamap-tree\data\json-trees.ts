export const PHONES_JSON: DitamapNodeStructure = {
  title: "root",
  type: "root",
  mapPath: [],
  rootElementName: "",
  href: "",
  children: [
    {
      title: "Phone 1 User Guide",
      type: "map",
      href: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      rootElementName: "bookmap",
      mapPath: [],
      children: [
        {
          type: "map",
          title: "Submap Phones",
          mapName: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
          mapPath: ["/Content/Phone1_Bookmap_xi1577_1_1.ditamap"],
          href: "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
          rootElementName: "map",
          children: [
            {
              type: "topicref",
              title: "Begin",
              href: "/Content/begin_xi1612_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topic",
              children: [],
            },
            {
              type: "topicref",
              title: "Basics",
              href: "/Content/basics_xi1610_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
            {
              type: "topicref",
              title: "Troubleshooting",
              href: "/Content/troubleshooting_xi1638_1_1.xml",
              mapName: "submap_phones_xi1609_1_1.ditamap",
              mapPath: [
                "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
                "submap_phones_xi1609_1_1.ditamap",
              ],
              rootElementName: "topicref",
              children: [],
            },
          ],
        },
      ],
    },
  ],
};

interface DitamapNodeStructure {
  type: string;
  title: string;
  mapName?: string;
  mapPath: string[];
  href: string;
  rootElementName: string;
  children: DitamapNodeStructure[];
}
