{"version": 3, "file": "wex-editor-filepackage.js", "sourceRoot": "", "sources": ["../../src/components/wex-editor-filepackage.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAO,MAAM,KAAK,CAAC;AAE5C,OAAO,EAAE,aAAa,IAAI,KAAK,EAAE,MAAM,gBAAgB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,sCAAsC,CAAC;AAC9D,OAAO,KAAK,IAAI,MAAM,aAAa,CAAC;AACpC,OAAO,KAAK,MAAM,MAAM,eAAe,CAAC;AAExC,OAAO,yCAAyC,CAAC;AACjD,OAAO,4CAA4C,CAAC;AACpD,OAAO,6CAA6C,CAAC;AACrD,OAAO,+CAA+C,CAAC;AACvD,OAAO,iCAAiC,CAAC;AACzC,OAAO,oDAAoD,CAAC;AAC5D,OAAO,4CAA4C,CAAC;AACpD,OAAO,oCAAoC,CAAC;AAC5C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,qCAAqC,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAGzD,IAAM,oBAAoB,GAA1B,MAAM,oBAAqB,SAAQ,UAAU;IAW3C;QACE,KAAK,EAAE,CAAC;QATkB,WAAM,GAA2B,EAAE,CAAC;QAMpC,SAAI,GAAW,OAAO,CAAC;IAInD,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5C,sBAAsB;YACtB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACpC,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED,YAAY;QACV,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED,WAAW;QACT,KAAK,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAC1E,CAAC;IAED,WAAW,CAAC,KAAK;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAE9C,uBAAuB;QACvB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QAClC,CAAC;QACD,wBAAwB;QACxB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC;YAC7D,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC;QACnD,CAAC;IACH,CAAC;IAED,gBAAgB;QACd,gCAAgC;QAChC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,MAAM,CACzD,UAAU,EAAE;YACV,OAAO,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;QACtB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;QACF,sBAAsB;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,CAAC;YACX,CAAC;YACD,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;QACH,UAAU;QACV,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,aAAa;QACX,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,KAAK,CAAC,4BAA4B,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,CACxD,CAAC;YACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC;gBAChC,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;oBACpC,IACE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ,EACjE,CAAC;wBACD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;oBACjC,CAAC;gBACH,CAAC;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,WAAW,CAAC;gBACtC,CAAC;YACH,CAAC;YACD,sBAAsB;QACxB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CACnE,CAAC;IACJ,CAAC;IAED,gBAAgB;QACd,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YAC5D,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,eAAe;IACf,MAAM;QACJ,OAAO,IAAI,CAAA;;;;;;;;;;;;;;;;;;;;sBAoBO,IAAI,CAAC,KAAK,CAAC,mBAAmB;aACzC,4BAA4B;;;;;;;;;;;;qBAYlB,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,YAAY;YACxD,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,4BAA4B;gBAC1D,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,mBAAmB;oBAChD,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAsGS,CAAC,IAAI,CAAC,UAAU,EAAE;;kBAEvB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;qBACpB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;sBAC9B,IAAI,CAAC,KAAK,CAAC,kCAAkC;;mBAEhD,IAAI,CAAC,MAAM,CAAC,0BAA0B,CAAC;;;uBAGnC,CAAC,IAAI,CAAC,UAAU,EAAE;;kBAEvB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;qBACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;sBAC9B,CAAC,IAAI,CAAC,KAAK,CAAC,kCAAkC;;mBAEjD,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC;;;;2BAIjC,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;;wBAEhD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;uBAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;;;;;2BAKhB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;;wBAEhD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;uBAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;;;;;;;;uBAQpB,CAAC,IAAI,CAAC,UAAU,EAAE;;;oBAGrB,IAAI,CAAC,eAAe;4BACZ,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;4BAInC,IAAI,CAAC,MAAM,CACzB,IAAI,CAAC,KAAK,CAAC,kCAAkC;YAC3C,CAAC,CAAC,iBAAiB;YACnB,CAAC,CAAC,mBAAmB,CACxB;6BACgB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;;;cAGpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACzB,IAAI,GAAG,CAAC,SAAS,IAAI,MAAM,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAA;;;gCAGK,GAAG,CAAC,OAAO;;;mCAGR,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU;yBAC7C,GAAG,CAAC,OAAO;;;;;;wCAMI,GAAG,CAAC,SAAS;;kCAEnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;uCAEtB,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;uCAEhC,CAAC,IAAI,CAAC,UAAU,EAAE;;;;;wCAKjB,GAAG,CAAC,SAAS;;kCAEnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;wCAErB,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;uCAElC,CAAC,IAAI,CAAC,UAAU,EAAE;;;;;qCAKpB,CAAC;YACxB,CAAC;QACH,CAAC,CAAC;cACA,IAAI,CAAC,IAAI,CAAC,GAAG,CACb,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAA;+BACW,CAAC,IAAI,CAAC,UAAU,EAAE;4CACL,IAAI,CAAC,UAAU,EAAE;YACzC,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU;YAChD,CAAC,CAAC,aAAa;YACf,CAAC,CAAC,EAAE;2BACG,IAAI;+BACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;4BACjC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;kCAC3B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC;;oBAEpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;iCAC1C,CACpB;;;;KAIR,CAAC;IACJ,CAAC;IAED,cAAc,CAAC,CAAC;QACd,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO;QAE/B,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;SAC5B,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED,WAAW,CAAC,CAAC;QACX,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,IAAI,SAAS,GAAG,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC;QACtC,IAAI,CAAC,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YAChC,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAC9B,CAAC,CAAC,aAAa,CAAC,WAAW,EAC3B,SAAS,EACT,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CACvD,CAAC;YACF,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,4BAA4B;gBACtC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,YAAY,CAAC,GAAG,EAAE,GAAG;QACnB,sHAAsH;QACtH;;;;;;kBAMU;QACV,IAAI,GAAG,CAAC,SAAS,IAAI,MAAM,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAA,2CAA2C,CAAC,IAAI,CAAC,UAAU,EAAE;;;yBAGrD,CAAC,IAAI,CAAC,UAAU,EAAE;sBACrB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;qBAChC,GAAG;;oBAEJ,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,oBAAoB;;;yBAGhD,CAAC,IAAI,CAAC,UAAU,EAAE;;;8BAGb,GAAG,CAAC,SAAS;qBACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC;;;qBAG1B,GAAG;;qBAEH,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;qBACvB,GAAG,CAAC,QAAQ;;;;;qBAKZ,GAAG;wBACA,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,SAAS;;qBAE7C,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,SAAS;gBACjD,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC7B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;qBACf,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,SAAS;gBACjE,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM;;;;kBAInD,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC;;wBAEZ,CAAC;QACrB,CAAC,CAAC;;;eAGK;IACT,CAAC;IAED,eAAe,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,qBAAqB,CACxB,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAC/B,CAAC,CAAC,aAAa,CAAC,IAAI,CACrB,CAAC;IACJ,CAAC;IAED,qBAAqB,CAAC,YAAY,EAAE,YAAY;QAC9C,IAAI,OAAO,GAAG;YACZ,MAAM,EAAE,MAAM;YACd,aAAa,EAAE,YAAY,CAAC,aAAa;YACzC,SAAS,EAAE,YAAY,CAAC,MAAM;YAC9B,QAAQ,EAAE,YAAY,CAAC,QAAQ;YAC/B,QAAQ,EAAE,CAAC,YAAY,CAAC,QAAQ;SACjC,CAAC;QACF,KAAK,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,IAAI,cAAc,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACrC,cAAc,GAAG,CAAC,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC1C,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;oBAC5B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACX,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;oBACxC,CAAC;yBAAM,CAAC;wBACN,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;oBACzB,CAAC;oBACD,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;YAC5B,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;aACjC,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,IAAI,cAAc,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACrC,cAAc,GAAG,CAAC,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC1C,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;oBAC5B,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC9B,cAAc,GAAG,CAAC,CAAC;oBACrB,CAAC;yBAAM,CAAC;wBACN,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;oBACzB,CAAC;oBACD,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;YAC5B,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;aACjC,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,eAAe,CAAC,CAAC;QACf,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,oCAAoC;YAC9C,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ;SACrC,CAAC,CAAC;QACH,KAAK,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAC1E,CAAC;IAED,kBAAkB,CAAC,CAAC;QAClB,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;YAC3B,KAAK,SAAS;gBACZ,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACzB,QAAQ,EAAE,yBAAyB;oBACnC,KAAK,EAAE;wBACL,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;qBACzC;iBACF,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,YAAY;gBACf,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACzB,QAAQ,EAAE,oBAAoB;oBAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;iBACvC,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,YAAY;gBACf,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACzB,QAAQ,EAAE,yBAAyB;oBACnC,KAAK,EAAE;wBACL,MAAM,EAAE,iBAAiB;wBACzB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;qBACzC;iBACF,CAAC,CAAC;gBACH,MAAM;QACV,CAAC;IACH,CAAC;IAED,mBAAmB,CAAC,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC5B,KAAK,YAAY;gBACf,KAAK,CAAC,QAAQ,CACZ,uBAAuB,EACvB,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAChC,CAAC;gBACF,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,CAAC,EAAE,CACP,OAAO;oBACL,IAAI,CAAC,KAAK,CAAC,QAAQ;oBACnB,oBAAoB;oBACpB,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;oBACxC,OAAO;oBACP,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAChD,CAAC;gBACF,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,MAAM;YACR,KAAK,QAAQ;gBACX,0DAA0D;gBAC1D,MAAM;YACR,KAAK,cAAc;gBACjB,+BAA+B;gBAC/B,wCAAwC;gBACxC,uBAAuB;gBACvB,MAAM;gBACN,8DAA8D;gBAC9D,yBAAyB;gBACzB,2CAA2C;gBAC3C,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,MAAM;YACR,KAAK,kBAAkB;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;oBAClD,GAAG,EAAE,KAAK;oBACV,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;oBAClD,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;iBAC9B,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,kBAAkB;gBACrB,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAChE,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAChC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBACtE,8HAA8H;gBAC9H,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,CAAC,qBAAqB,CACxB,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAC/B,IAAI,CAAC,gBAAgB,CACtB,CAAC,CAAC,mHAAmH;gBACtH,MAAM;YACR,KAAK,cAAc;gBACjB,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE;oBACjC,MAAM,EAAE,cAAc;oBACtB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;iBACtC,CAAC,CAAC;gBACH,MAAM;QACV,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IACnE,CAAC;IAED,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QACnC,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACjB,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC1B,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,wBAAwB;gBAClC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;YACH,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;aACvC,CAAC,CAAC;YACH,MAAM,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;gBACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC9C,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,sBAAsB,CAAC,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,uCAAuC;QACvC,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;YAC7C,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAChC,CAAC,CAAC,aAAa,CAAC,IAAI,EACpB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAC1B,mBAAmB,EACnB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,EACpC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAClD,CAAC;YACF,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC;YAEnC,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;gBACpC,IACE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;oBACxC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAC7B,CAAC;oBACD,OAAO;gBACT,CAAC;YACH,CAAC;YACD,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;aAC5B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,iBAAiB,CAAC,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;QAC9B,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QACtE,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACnB,QAAQ,IAAI,CAAC,EAAE,EAAE,CAAC;gBAChB,KAAK,SAAS;oBACZ,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACzB,QAAQ,EAAE,sBAAsB;wBAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;qBAC5B,CAAC,CAAC;oBACH,MAAM,CAAC,EAAE,CACP,OAAO;wBACL,IAAI,CAAC,KAAK,CAAC,QAAQ;wBACnB,oBAAoB;wBACpB,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;wBACxC,OAAO;wBACP,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,aAAa;wBAC7C,oBAAoB,CACvB,CAAC;oBACF,MAAM;gBACR,KAAK,MAAM;oBACT,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACzB,QAAQ,EAAE,sBAAsB;wBAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;qBAC5B,CAAC,CAAC;oBACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;gBACR,KAAK,YAAY;oBACf,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACzB,QAAQ,EAAE,sBAAsB;wBAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;qBAC5B,CAAC,CAAC;oBACH,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;wBACzB,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;4BACpC,IACE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;gCACxC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAC7B,CAAC;gCACD,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oCACzB,QAAQ,EAAE,sBAAsB;oCAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;iCAC5B,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gCACzB,QAAQ,EAAE,sBAAsB;gCAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;6BAC5B,CAAC,CAAC;wBACL,CAAC;wBACD,KAAK,CAAC,QAAQ,CACZ,uBAAuB,EACvB,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAChC,CAAC;wBACF,MAAM;oBACR,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;oBACpC,IACE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;wBACxC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAC7B,CAAC;wBACD,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;4BACzB,QAAQ,EAAE,sBAAsB;4BAChC,KAAK,EAAE,IAAI;yBACZ,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;gBACH,CAAC;gBACD,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACzB,QAAQ,EAAE,sBAAsB;oBAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AArxB6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;oDAAqC;AACrC;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;qDAAS;AACR;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;kDAAM;AACJ;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;yDAAa;AACZ;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;6DAAiB;AAChB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wDAAY;AACX;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;kDAAwB;AAT/C,oBAAoB;IADzB,aAAa,CAAC,wBAAwB,CAAC;GAClC,oBAAoB,CAwxBzB", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { render } from \"lit/html.js\";\r\nimport { storeInstance as Store } from \"store/index.js\";\r\nimport { Router } from \"@vaadin/router/dist/vaadin-router.js\";\r\nimport * as util from \"lib/util.ts\";\r\nimport * as wexlib from \"lib/wexlib.js\";\r\n\r\nimport \"@ui5/webcomponents-compat/dist/Table.js\";\r\nimport \"@ui5/webcomponents-compat/dist/TableRow.js\";\r\nimport \"@ui5/webcomponents-compat/dist/TableCell.js\";\r\nimport \"@ui5/webcomponents-compat/dist/TableColumn.js\";\r\nimport \"@ui5/webcomponents/dist/Icon.js\";\r\nimport \"@vaadin/vaadin-context-menu/vaadin-context-menu.js\";\r\nimport \"@vaadin/vaadin-list-box/vaadin-list-box.js\";\r\nimport \"@vaadin/vaadin-item/vaadin-item.js\";\r\nimport \"@vaadin/vaadin-icons/vaadin-icons.js\";\r\nimport \"@ui5/webcomponents/dist/RadioButton\";\r\nimport { customElement, property } from \"lit/decorators\";\r\n\r\n@customElement(\"wex-editor-filepackage\")\r\nclass WexEditorFilepackage extends LitElement {\r\n  state: any;\r\n  subscription: any;\r\n  @property({ type: Object }) string: Record<string, string> = {};\r\n  @property({ type: Array }) columns;\r\n  @property({ type: Array }) rows;\r\n  @property({ type: Object }) contextMenu;\r\n  @property({ type: Object }) primaryRadioBtn;\r\n  @property({ type: Object }) taskInstId;\r\n  @property({ type: Object }) lang: string = \"en-US\";\r\n\r\n  constructor() {\r\n    super();\r\n  }\r\n\r\n  connectedCallback() {\r\n    this.subscription = Store.subscribe((state) => {\r\n      /*WexEditFilepackage*/\r\n      this.stateChange(state);\r\n    });\r\n    this.state = Store.state;\r\n    if (this.state.editor_selected_task) {\r\n      this.refreshData();\r\n    }\r\n  }\r\n\r\n  firstUpdated() {\r\n    this._marshellColumns();\r\n    this._marshellRows();\r\n    if (this.shadowRoot) {\r\n      this.contextMenu = this.shadowRoot.querySelector(\"#contextmenu\");\r\n      this.primaryRadioBtn = this.shadowRoot.querySelector(\"#primary\");\r\n    }\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    Store.unsubscribe(this.subscription);\r\n  }\r\n\r\n  refreshData() {\r\n    Store.dispatch(\"getEditorFilepackage\", this.state.editor_selected_task);\r\n  }\r\n\r\n  stateChange(state) {\r\n    this.state = state;\r\n    this.string = this.state[this.state.langCode];\r\n\r\n    //re localize condition\r\n    if (this.lang != this.state.langCode) {\r\n      this.lang = this.state.langCode;\r\n    }\r\n    //refresh data condition\r\n    if (this._isNewTask()) {\r\n      this.taskInstId = this.state.editor_selected_task.taskInstId;\r\n      this.refreshData();\r\n    }\r\n  }\r\n\r\n  _isNewTask() {\r\n    if (this.state.editor_selected_task) {\r\n      if (this.state.editor_selected_task.taskInstId != this.taskInstId) {\r\n        return true;\r\n      }\r\n    }\r\n  }\r\n\r\n  _isClaimed() {\r\n    if (this.state.editor_selected_task) {\r\n      return this.state.editor_selected_task.isClaimed;\r\n    }\r\n  }\r\n\r\n  _marshellColumns() {\r\n    //filter for active col headings\r\n    this.columns = this.state.editor_filepackage_columns.filter(\r\n      function (el) {\r\n        return el.order > 0;\r\n      }.bind(this)\r\n    );\r\n    //sort by column order\r\n    this.columns.sort(function (a, b) {\r\n      if (a.order < b.order) {\r\n        return -1;\r\n      }\r\n      if (a.order > b.order) {\r\n        return 1;\r\n      }\r\n      return 0;\r\n    });\r\n    //localize\r\n    this.columns.map((col) => (col.heading = this.string[col.title]));\r\n  }\r\n\r\n  _marshellRows() {\r\n    this.rows = [];\r\n    if (this.state.editor_filepackage_file_list) {\r\n      this.rows = JSON.parse(\r\n        JSON.stringify(this.state.editor_filepackage_file_list)\r\n      );\r\n      for (var i = 0; i < this.rows.length; i++) {\r\n        this.rows[i].isSelected = false;\r\n        if (this.state.editor_selected_file) {\r\n          if (\r\n            this.rows[i].resLblId == this.state.editor_selected_file.resLblId\r\n          ) {\r\n            this.rows[i].isSelected = true;\r\n          }\r\n        }\r\n        if (util.isDitamap(this.rows[i])) {\r\n          this.rows[i].mapClass = \" ditamap \";\r\n        }\r\n      }\r\n      //apply claimed filter\r\n    }\r\n    this.rows.sort(\r\n      util.columnSort(util.getSortColPropertyName(this.columns, \"name\"))\r\n    );\r\n  }\r\n\r\n  _disableNextPrev() {\r\n    if (this.rows.length > 1 && this.state.editor_selected_file) {\r\n      return false;\r\n    } else {\r\n      return true;\r\n    }\r\n  }\r\n\r\n  /* Templates */\r\n  render() {\r\n    return html`\r\n      <style>\r\n        .lockedbyother{color:red;}\r\n        .lockedbyselfhere{color:green;}\r\n        .lockedbyselfelsewhere{color:orange;}\r\n\r\n        .rowselected{\r\n            --sapList_Background: var(--row-selected-background);\r\n        }\r\n\r\n        #notasks{\r\n            font-style: oblique;\r\n            opacity: 0.5;\r\n            text-align:center;\r\n            margin-top:50px;\r\n        }\r\n        .datarow{cursor:pointer;}\r\n        .ditamap{font-weight:bold;}\r\n\r\n        #filepackageheader{\r\n            height: ${this.state.global_screen_specs\r\n          .edit_filepackageHeaderHeight}px !important;\r\n            color:#000;\r\n            text-align:center;\r\n            display:flex;\r\n            flex-direction: row;\r\n            justify-content: space-between;\r\n            flex-wrap: nowrap\r\n        }\r\n        #filepackageheader>div{flex-shrink: 1;}\r\n\r\n\r\n        #filepackagetablecontainer{\r\n            height:${this.state.global_screen_specs.layoutHeight -\r\n        (this.state.global_screen_specs.edit_filepackageHeaderHeight +\r\n          this.state.global_screen_specs.edit_controlsHeight *\r\n            2)}px; !important;\r\n            overflow: auto;\r\n        }\r\n\r\n\r\n        #filerole{\r\n            display: inline-block;;\r\n            margin-left:-12px;\r\n            cursor: pointer;\r\n            }\r\n\r\n\r\n        #filerole{\r\n            display: inline-block;;\r\n            margin-left:-12px;\r\n            cursor: pointer;\r\n            }\r\n\r\n\r\n        #nextprev{\r\n            line-height:30px;\r\n            padding-top:10px;\r\n            color:#32363A;\r\n            margin-right:-6px;\r\n        }\r\n\r\n        #nextprev{\r\n            line-height:30px;\r\n            padding-top:10px;\r\n            color:#32363A;\r\n            margin-right:-6px;\r\n        }\r\n\r\n        #nexticon{cursor:pointer;}\r\n        #previcon{cursor:pointer;}\r\n        #nexticon[disabled]{cursor:forbidden;color:#ccc;}\r\n        #previcon[disabled]{cursor:forbidden;color:#ccc;}\r\n\r\n        .reviewedicon{\r\n            --iron-icon-width:16px;\r\n        }\r\n        .hide{visibility: hidden;}\r\n        .sortbuttoncontainer{\r\n            display:flex;\r\n            flex-direction:column;\r\n        }\r\n        .sortbuttoncontainer iron-icon{\r\n            margin-left: .25rem;\r\n            --iron-icon-width:.9rem\r\n        }\r\n        iron-icon.asc{margin-bottom:-.6rem}\r\n        iron-icon.desc{margin-top:-.6rem}\r\n        iron-icon.muted{\r\n            color: #ccc;\r\n        }\r\n\r\n        .colhead{\r\n            line-height: 1.4rem;\r\n            display:flex;\r\n            flex-direction: row;\r\n            align-items:center;\r\n\r\n        }\r\n        .colhead>span:first-child{\r\n            font-size:.8rem;\r\n        }\r\n        .iconcontatiner{\r\n            height:1.4rem;\r\n            overflow:hidden;\r\n            }\r\n        .sidecomponent{\r\n        }\r\n        #sidecomponentparent{\r\n            display: flex;\r\n            justify-content: center;\r\n            background-color:#F2F2F2;\r\n            padding: 0.5rem 0 0;\r\n        }\r\n        ui5-table-cell>div{\r\n            white-space: nowrap;\r\n        }\r\n        .disabled{\r\n            color:#ccc;\r\n            cursor:forbidden;\r\n        }\r\n        *[disabled]{\r\n            color:#ccc;\r\n            cursor:forbidden;\r\n        }\r\n        #nextprev ui5-button{\r\n            height:1.5rem;\r\n            width:2rem;\r\n        }\r\n        ui5-button>iron-icon{\r\n            width:15px;\r\n        }\r\n\r\n        .fileicon, .previewicon, .editicon, .reviewedicon{width: 1rem;}\r\n        .fileicon:hover, .previewicon:hover, .editicon:hover, reviewedicon:hover{color:#000;}\r\n      </style>\r\n      <div id=\"sidecomponentparent\">\r\n        <ui5-radio-button\r\n          ?disabled=\"${!this._isClaimed()}\"\r\n          id=\"primary\"\r\n          text=\"${this.string[\"_primary\"]}\"\r\n          @change=\"${this._fileRoleChange.bind(this)}\"\r\n          ?checked=\"${this.state.editor_filepackage_display_primary}\"\r\n          name=\"GroupA\"\r\n          title=\"${this.string[\"_displayonlyprimaryfiles\"]}\"\r\n        ></ui5-radio-button>\r\n        <ui5-radio-button\r\n          ?disabled=\"${!this._isClaimed()}\"\r\n          id=\"refernce\"\r\n          text=\"${this.string[\"_reference\"]}\"\r\n          @change=\"${this._fileRoleChange.bind(this)}\"\r\n          ?checked=\"${!this.state.editor_filepackage_display_primary}\"\r\n          name=\"GroupA\"\r\n          title=\"${this.string[\"_displayonlyreferencefiles\"]}\"\r\n        ></ui5-radio-button>\r\n        <!-- <div id=\"nextprev\">\r\n            <ui5-button\r\n              ?disabled=\"${!this._isClaimed() || this._disableNextPrev()}\"\r\n              id=\"previcon\"\r\n              @click=\"${this._loadPrev.bind(this)}\"\r\n              title=\"${this.string[\"_prev\"]}\"\r\n            >\r\n              <iron-icon icon=\"vaadin:caret-left\"></iron-icon>\r\n            </ui5-button>\r\n            <ui5-button\r\n              ?disabled=\"${!this._isClaimed() || this._disableNextPrev()}\"\r\n              id=\"nexticon\"\r\n              @click=\"${this._loadNext.bind(this)}\"\r\n              title=\"${this.string[\"_next\"]}\"\r\n            >\r\n              <iron-icon icon=\"vaadin:caret-right\"></iron-icon>\r\n            </ui5-button>\r\n          </div> -->\r\n      </div>\r\n      <div id=\"filepackagetablecontainer\">\r\n        <vaadin-context-menu\r\n          ?disabled=\"${!this._isClaimed()}\"\r\n          id=\"contextmenu\"\r\n          selector=\".has-menu\"\r\n          .items=\"${this.fileContextNenu}\"\r\n          @item-selected=\"${this._contextmenuclicked.bind(this)}\"\r\n        >\r\n          <ui5-table\r\n            id=\"filepackagetable\"\r\n            no-data-text=\"${this.string[\r\n              this.state.editor_filepackage_display_primary\r\n                ? \"_noprimaryfiles\"\r\n                : \"_noreferencefiles\"\r\n            ]}\"\r\n            ?show-no-data=\"${this.rows.length == 0}\"\r\n            sticky-column-header\r\n          >\r\n            ${this.columns.map((col) => {\r\n              if (col.fieldname == \"name\") {\r\n                return html`<ui5-table-column\r\n                  class=\"foo\"\r\n                  slot=\"columns\"\r\n                  popin-text=\"${col.heading}\"\r\n                >\r\n                  <span class=\"colhead\">\r\n                    <span class=\"${this._isClaimed() ? \"\" : \"disabled\"}\"\r\n                      >${col.heading}</span\r\n                    >\r\n\r\n                    <span class=\"sortbuttoncontainer\">\r\n                      <span\r\n                        class=\"iconcontatiner\"\r\n                        .colproperty=\"${col.fieldname}\"\r\n                        title=\"asc\"\r\n                        @click=\"${this._sortColumn.bind(this)}\"\r\n                        ><iron-icon\r\n                          class=\"asc ${col.sort != \"asc\" ? \"muted\" : \"\"}\"\r\n                          icon=\"vaadin:caret-up\"\r\n                          ?disabled=\"${!this._isClaimed()}\"\r\n                        ></iron-icon\r\n                      ></span>\r\n                      <span\r\n                        class=\"iconcontatiner\"\r\n                        .colproperty=\"${col.fieldname}\"\r\n                        title=\"desc\"\r\n                        @click=\"${this._sortColumn.bind(this)}\"\r\n                        ><iron-icon\r\n                          class=\"desc ${col.sort != \"desc\" ? \"muted\" : \"\"}\"\r\n                          icon=\"vaadin:caret-down\"\r\n                          ?disabled=\"${!this._isClaimed()}\"\r\n                        ></iron-icon\r\n                      ></span>\r\n                    </span>\r\n                  </span>\r\n                </ui5-table-column> `;\r\n              }\r\n            })}\r\n            ${this.rows.map(\r\n              (item) =>\r\n                html` <ui5-table-row\r\n                  ?disabled=\"${!this._isClaimed()}\"\r\n                  class=\"datarow has-menu ${this._isClaimed()\r\n                    ? \"\"\r\n                    : \"disabled\"} ${item.mapClass} ${item.isSelected\r\n                    ? \"rowselected\"\r\n                    : \"\"}\"\r\n                  .item=\"${item}\"\r\n                  @dblclick=\"${this._defaultAction.bind(this)}\"\r\n                  @click=\"${this._handleRowClicked.bind(this)}\"\r\n                  @contextmenu=\"${this._handleRowRightClicked.bind(this)}\"\r\n                >\r\n                  ${this.columns.map((col) => this._rowTemplate(col, item))}\r\n                </ui5-table-row>`\r\n            )}\r\n          </ui5-table>\r\n        </vaadin-context-menu>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  _defaultAction(e) {\r\n    if (!this._isClaimed()) return;\r\n\r\n    Store.dispatch(\"setState\", {\r\n      property: \"editor_selected_file\",\r\n      value: e.currentTarget.item,\r\n    });\r\n    this._editSelectedFile();\r\n  }\r\n\r\n  _sortColumn(e) {\r\n    if (!this._isClaimed()) {\r\n      return;\r\n    }\r\n    var direction = e.currentTarget.title;\r\n    if (e.currentTarget.colproperty) {\r\n      var cols = util.changeColumnSort(\r\n        e.currentTarget.colproperty,\r\n        direction,\r\n        structuredClone(this.state.editor_filepackage_columns)\r\n      );\r\n      Store.dispatch(\"setState\", {\r\n        property: \"editor_filepackage_columns\",\r\n        value: cols,\r\n      });\r\n    }\r\n  }\r\n\r\n  _rowTemplate(col, itm) {\r\n    //<ui5-checkbox ?checked=\"${itm.reviewd}\" .item=\"${itm}\" @clicked=\"${this._toggleReviewed.bind(this)}\"></ui5-checkbox>\r\n    /*\r\n        if(col.fieldname=='reviewed'){\r\n            return html`<ui5-table-cell  align='center'  ?disabled=\"${!this._isClaimed()}\"><div class=\"reviewedicon\">\r\n                \r\n            </div></ui5-table-cell>`;\r\n        } else {\r\n            */\r\n    if (col.fieldname == \"name\") {\r\n      return html`<ui5-table-cell align=\"left\" ?disabled=\"${!this._isClaimed()}\"\r\n        ><div>\r\n          <iron-icon\r\n            ?disabled=\"${!this._isClaimed()}\"\r\n            @click=\"${this._toggleReviewed.bind(this)}\"\r\n            .item=\"${itm}\"\r\n            class=\"reviewedicon\"\r\n            icon=\"${itm.reviewed ? \"vaadin:circle\" : \"vaadin:circle-thin\"}\"\r\n          ></iron-icon>\r\n          <iron-icon\r\n            ?disabled=\"${!this._isClaimed()}\"\r\n            icon=\"vaadin:file-text-o\"\r\n            id=\"properties\"\r\n            class=\"fileicon ${itm.iconClass}\"\r\n            title=\"${this.string[itm.iconTitle]}\"\r\n          ></iron-icon>\r\n          <iron-icon\r\n            .item=\"${itm}\"\r\n            icon=\"vaadin:eye\"\r\n            title=\"${this.string[\"_preview\"]}\"\r\n            class=\"${itm.selected} previewicon\"\r\n            id=\"preview\"\r\n          >\r\n          </iron-icon>\r\n          <iron-icon\r\n            .item=\"${itm}\"\r\n            ?disabled=${!this.state.editor_selected_task.isClaimed}\r\n            icon=\"vaadin:edit\"\r\n            title=\"${!this.state.editor_selected_task.isClaimed\r\n              ? this.string[\"_claimtoedit\"]\r\n              : this.string[\"_edit\"]}  \"\r\n            class=\"${itm.selected} ${!this.state.editor_selected_task.isClaimed\r\n              ? \"disabled\"\r\n              : \"\"} editicon ${util.isFileEditable(itm) ? \"\" : \"hide\"}\"\r\n            id=\"edit\"\r\n          >\r\n          </iron-icon>\r\n          &nbsp;${itm[col.fieldname]}\r\n        </div>\r\n      </ui5-table-cell>`;\r\n    } /*else {\r\n                return html`<ui5-table-cell align='left'><div >&nbsp;${itm[col.fieldname]}</div></ui5-table-cell>`;\r\n         }       }\r\n         */\r\n  }\r\n\r\n  _toggleReviewed(e) {\r\n    if (!this._isClaimed()) {\r\n      return;\r\n    }\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    this._toggleReviewedAction(\r\n      this.state.editor_selected_task,\r\n      e.currentTarget.item\r\n    );\r\n  }\r\n\r\n  _toggleReviewedAction(selectedTask, selectedFile) {\r\n    var payload = {\r\n      origin: \"task\",\r\n      processInstId: selectedTask.processInstId,\r\n      TaskDefId: selectedTask.taskId,\r\n      resLblId: selectedFile.resLblId,\r\n      reviewed: !selectedFile.reviewed,\r\n    };\r\n    Store.dispatch(\"setTaskFileReviewedStatus\", payload);\r\n  }\r\n\r\n  _loadPrev() {\r\n    if (!this._isClaimed()) {\r\n      return;\r\n    }\r\n    var rowIdxToSelect = null;\r\n    if (!this.state.editor_selected_file) {\r\n      rowIdxToSelect = 0;\r\n    } else {\r\n      for (var i = 0; i < this.rows.length; i++) {\r\n        if (this.rows[i].isSelected) {\r\n          if (i == 0) {\r\n            rowIdxToSelect = this.rows.length - 1;\r\n          } else {\r\n            rowIdxToSelect = i - 1;\r\n          }\r\n          break;\r\n        }\r\n      }\r\n    }\r\n    if (rowIdxToSelect !== null) {\r\n      Store.dispatch(\"setState\", {\r\n        property: \"editor_selected_file\",\r\n        value: this.rows[rowIdxToSelect],\r\n      });\r\n      this._editSelectedFile();\r\n    }\r\n  }\r\n\r\n  _loadNext() {\r\n    if (!this._isClaimed()) {\r\n      return;\r\n    }\r\n    var rowIdxToSelect = null;\r\n    if (!this.state.editor_selected_file) {\r\n      rowIdxToSelect = 0;\r\n    } else {\r\n      for (var i = 0; i < this.rows.length; i++) {\r\n        if (this.rows[i].isSelected) {\r\n          if (i == this.rows.length - 1) {\r\n            rowIdxToSelect = 0;\r\n          } else {\r\n            rowIdxToSelect = i + 1;\r\n          }\r\n          break;\r\n        }\r\n      }\r\n    }\r\n    if (rowIdxToSelect !== null) {\r\n      Store.dispatch(\"setState\", {\r\n        property: \"editor_selected_file\",\r\n        value: this.rows[rowIdxToSelect],\r\n      });\r\n      this._editSelectedFile();\r\n    }\r\n  }\r\n\r\n  _fileRoleChange(e) {\r\n    Store.dispatch(\"setState\", {\r\n      property: \"editor_filepackage_display_primary\",\r\n      value: this.primaryRadioBtn.selected,\r\n    });\r\n    Store.dispatch(\"getEditorFilepackage\", this.state.editor_selected_task);\r\n  }\r\n\r\n  _filePackageAction(e) {\r\n    switch (e.currentTarget.id) {\r\n      case \"addfile\":\r\n        Store.dispatch(\"setState\", {\r\n          property: \"select_file_dialog_open\",\r\n          value: {\r\n            payload: this.state.editor_selected_task,\r\n          },\r\n        });\r\n        break;\r\n      case \"importfile\":\r\n        Store.dispatch(\"setState\", {\r\n          property: \"import_dialog_open\",\r\n          value: this.state.editor_selected_task,\r\n        });\r\n        break;\r\n      case \"createfile\":\r\n        Store.dispatch(\"setState\", {\r\n          property: \"create_file_dialog_open\",\r\n          value: {\r\n            target: \"editfilepackage\",\r\n            payload: this.state.editor_selected_task,\r\n          },\r\n        });\r\n        break;\r\n    }\r\n  }\r\n\r\n  _contextmenuclicked(e) {\r\n    if (!this._isClaimed()) {\r\n      return;\r\n    }\r\n    switch (e.detail.value.name) {\r\n      case \"properties\":\r\n        Store.dispatch(\r\n          \"filePropertiesRequest\",\r\n          this.state.editor_selected_file\r\n        );\r\n        break;\r\n      case \"preview\":\r\n        Router.go(\r\n          \"/wex/\" +\r\n            this.state.langCode +\r\n            \"/preview?resLblId=\" +\r\n            this.state.editor_selected_file.resLblId +\r\n            \"&aod=\" +\r\n            this.state.editor_selected_file.verCreateDate\r\n        );\r\n        break;\r\n      case \"edit\":\r\n        this._editSelectedFile();\r\n        break;\r\n      case \"remove\":\r\n        //Store.dispatch(\"removeFileFromPackage\", {ownOnly:true});\r\n        break;\r\n      case \"edit_ditamap\":\r\n        // Store.dispatch(\"setState\", {\r\n        //   property: \"editor_side_active_tab\",\r\n        //   value: \"ditamaps\",\r\n        // });\r\n        // var tmp = structuredClone(this.state.editor_selected_file);\r\n        // tmp.isDitabase = true;\r\n        // Store.dispatch(\"checkOutDitabase\", tmp);\r\n        this._editSelectedFile();\r\n        break;\r\n      case \"previewnewwindow\":\r\n        wexlib.previewNewWindow({\r\n          resLblId: this.state.editor_selected_file.resLblId,\r\n          aod: \"now\",\r\n          mimeType: this.state.editor_selected_file.mimeType,\r\n          langCode: this.state.langCode,\r\n        });\r\n        break;\r\n      case \"openfilelocation\":\r\n        var aTmp = this.state.editor_selected_file.resPathId.split(\"/\");\r\n        aTmp.pop();\r\n        var folderPath = aTmp.join(\"/\");\r\n        this._folderByPath(this.state.global_folders, folderPath, null, this);\r\n        //Store.dispatch(\"setState\", {property: \"select_file_highlight_folder\" , value: this.state.search_selected_file.folderPath } )\r\n        break;\r\n      case \"toggledone\":\r\n        this._toggleReviewedAction(\r\n          this.state.editor_selected_task,\r\n          this.itemRightClicked\r\n        ); //The reviewed status can be changed without updateing the item, so must use the captured item from the right click\r\n        break;\r\n      case \"open_ditamap\":\r\n        Store.dispatch(\"handleFileAction\", {\r\n          action: \"open_ditamap\",\r\n          file: this.state.editor_selected_file,\r\n        });\r\n        break;\r\n    }\r\n  }\r\n\r\n  _editSelectedFile() {\r\n    Store.dispatch(\"checkEditFile\", this.state.editor_selected_file);\r\n  }\r\n\r\n  _folderByPath(node, seekPath, path, c) {\r\n    if (path) {\r\n      node.path = path + node.name;\r\n    } else {\r\n      node.path = \"\";\r\n    }\r\n    if (node.path == seekPath) {\r\n      Store.dispatch(\"setState\", {\r\n        property: \"browse_selected_folder\",\r\n        value: node,\r\n      });\r\n      Store.dispatch(\"setState\", {\r\n        property: \"browse_selected_file\",\r\n        value: this.state.editor_selected_file,\r\n      });\r\n      Router.go(\"/wex/\" + this.state.langCode + \"/browse\");\r\n    } else {\r\n      if (node.hasOwnProperty(\"children\")) {\r\n        for (var i = 0; i < node.children.length; i++) {\r\n          c._folderByPath(node.children[i], seekPath, node.path + \"/\", c);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  _handleRowRightClicked(e) {\r\n    if (!this._isClaimed()) {\r\n      return;\r\n    }\r\n    //modify conditional context menu items\r\n    if (e.currentTarget.item) {\r\n      this.itemRightClicked = e.currentTarget.item;\r\n      var menuItems = util.buildFileMenu(\r\n        e.currentTarget.item,\r\n        this.state.menus.file_menu,\r\n        \"editorfilepackage\",\r\n        this.state.wex_user.globalCapability,\r\n        this.state.editor_selected_task.userWexCapability\r\n      );\r\n      menuItems.map((item) => (item.text = this.string[item.label]));\r\n      this.contextMenu.items = menuItems;\r\n\r\n      if (this.state.editor_selected_file) {\r\n        if (\r\n          this.state.editor_selected_file.resLblId ==\r\n          e.currentTarget.item.resLblId\r\n        ) {\r\n          return;\r\n        }\r\n      }\r\n      Store.dispatch(\"setState\", {\r\n        property: \"editor_selected_file\",\r\n        value: e.currentTarget.item,\r\n      });\r\n    }\r\n  }\r\n\r\n  _handleRowClicked(e) {\r\n    if (!this._isClaimed()) {\r\n      return;\r\n    }\r\n    const path = e.composedPath();\r\n    var iconClicked = path[0].tagName == \"IRON-ICON\" && !path[0].disabled;\r\n    if (iconClicked) {\r\n      var icon = path[0];\r\n      switch (icon.id) {\r\n        case \"preview\":\r\n          Store.dispatch(\"setState\", {\r\n            property: \"editor_selected_file\",\r\n            value: e.currentTarget.item,\r\n          });\r\n          Router.go(\r\n            \"/wex/\" +\r\n              this.state.langCode +\r\n              \"/preview?resLblId=\" +\r\n              this.state.editor_selected_file.resLblId +\r\n              \"&aod=\" +\r\n              this.state.editor_selected_file.verCreateDate +\r\n              \"&previewStyle=html\"\r\n          );\r\n          break;\r\n        case \"edit\":\r\n          Store.dispatch(\"setState\", {\r\n            property: \"editor_selected_file\",\r\n            value: e.currentTarget.item,\r\n          });\r\n          this._editSelectedFile();\r\n          break;\r\n        case \"properties\":\r\n          Store.dispatch(\"setState\", {\r\n            property: \"editor_selected_file\",\r\n            value: e.currentTarget.item,\r\n          });\r\n          if (e.currentTarget.item) {\r\n            if (this.state.editor_selected_file) {\r\n              if (\r\n                this.state.editor_selected_file.resLblId !=\r\n                e.currentTarget.item.resLblId\r\n              ) {\r\n                Store.dispatch(\"setState\", {\r\n                  property: \"editor_selected_file\",\r\n                  value: e.currentTarget.item,\r\n                });\r\n              }\r\n            } else {\r\n              Store.dispatch(\"setState\", {\r\n                property: \"editor_selected_file\",\r\n                value: e.currentTarget.item,\r\n              });\r\n            }\r\n            Store.dispatch(\r\n              \"filePropertiesRequest\",\r\n              this.state.editor_selected_file\r\n            );\r\n            break;\r\n          }\r\n      }\r\n    } else {\r\n      if (e.currentTarget.item) {\r\n        if (this.state.editor_selected_file) {\r\n          if (\r\n            this.state.editor_selected_file.resLblId ==\r\n            e.currentTarget.item.resLblId\r\n          ) {\r\n            Store.dispatch(\"setState\", {\r\n              property: \"editor_selected_file\",\r\n              value: null,\r\n            });\r\n            return;\r\n          }\r\n        }\r\n        Store.dispatch(\"setState\", {\r\n          property: \"editor_selected_file\",\r\n          value: e.currentTarget.item,\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n"]}