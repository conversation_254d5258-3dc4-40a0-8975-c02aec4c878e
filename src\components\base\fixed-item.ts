import { LitElement, html, css } from "lit";
import { customElement } from "lit/decorators.js";

@customElement("wex-fixed-item")
export class WexFixedItem extends LitElement {
  static styles = css`
    :host {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  `;

  render() {
    const template = html`<slot></slot>`;
    return template;
  }
}
