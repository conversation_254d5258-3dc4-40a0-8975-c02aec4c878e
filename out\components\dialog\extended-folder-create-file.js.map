{"version": 3, "file": "extended-folder-create-file.js", "sourceRoot": "", "sources": ["../../../src/components/dialog/extended-folder-create-file.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,KAAK,IAAI,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,aAAa,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,oDAAoD,CAAC;AAE5D,OAAO,mCAAmC,CAAC;AAC3C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,kCAAkC,CAAC;AAC1C,4CAA4C;AAC5C,OAAO,wCAAwC,CAAC;AAChD,8CAA8C;AAC9C,oDAAoD;AACpD,OAAO,qBAAqB,CAAC;AAC7B,OAAO,+BAA+B,CAAC;AACvC,OAAO,UAAU,CAAC;AAClB,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAE5D;;EAEE;AAGF,IAAM,iCAAiC,GAAvC,MAAM,iCAAkC,SAAQ,UAAU;IAMxD;QACE,KAAK,EAAE,CAAC;QANkB,mBAAc,GAAwB,EAAE,CAAC;QAC1C,iBAAY,GAAU,EAAE,CAAC;QAC1B,qBAAgB,GAAQ,IAAI,CAAC;QAC7B,mBAAc,GAAsB,IAAI,CAAC;QAIjE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAE7B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEpC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YACrD,6BAA6B;YAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG;YAChB,aAAa,EAAE,GAAG;YAClB,SAAS,EAAE,MAAM;YACjB,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,UAAU;SACtB,CAAC;IACJ,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,YAAY;QACV,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAChE,sBAAsB;IACxB,CAAC;IAED,WAAW,CAAC,KAAK;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEpC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAE3D,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,uCAAuC,CAAC;QAEpE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,sBAAsB;YACtB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,uBAAuB,CAAC;YAElD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;YACvD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC;YACpD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;YAE9C,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACzD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBAC7B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,0BAA0B;oBACpC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;iBAClC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC;YACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;YAExD,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;gBAC5C,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACvC,CAAC;YAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,mBAAmB,CAAC,cAAc,GAAG,GAAG,CAAC;YAEnE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACf,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,sBAAsB;gBACtB,2BAA2B;gBAE3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC;YAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,wBAAwB,CAAC;YACrD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,0BAA0B,CAAC;YACzD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,0BAA0B,CAAC;YAEzD,mBAAmB;QACrB,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBAC7B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;gBAC3B,uBAAuB;gBACvB,4BAA4B;gBAC5B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;gBAClB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,0BAA0B;oBACpC,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;gBACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,4BAA4B;oBACtC,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;gBACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,4BAA4B;oBACtC,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;gBACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,6CAA6C;oBACvD,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,WAAW;QACT,aAAa,CAAC,QAAQ,CAAC,iBAAiB,EAAE;YACxC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB,CAAC,CAAC;QACjB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,UAAU,CACR;YACE,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;YACpD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,mBAAmB,GACrB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;YACrD,IAAI,iBAAiB,GACnB,mBAAmB,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC;YACjE,IAAI,oBAAoB,GACtB,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC;YACpE,IACE,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC,GAAG;gBACxC,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC,MAAM,EAC3C,CAAC;gBACD,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC;oBAC3B,QAAQ,EAAE,MAAM;oBAChB,KAAK,EAAE,QAAQ;oBACf,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,GAAG,CACJ,CAAC;QAEF;;;;;;;;;;;;;;;;;0BAiBkB;IACpB,CAAC;IAED,YAAY;QACV,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,yCAAyC;YACnD,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,4BAA4B;YACtC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IAED,MAAM;QACJ,MAAM,QAAQ,GAAG,IAAI,CAAA;;;;;gBAKT,CAAC,CAAC,IAAI,CAAC,cAAc;wBACb,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;sBAC5B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;;UAEtC,IAAI,CAAC,WAAW,EAAE;;KAEvB,CAAC;QACF,OAAO,QAAQ,CAAC;QAChB,4CAA4C;QAC5C,iCAAiC;QACjC,gBAAgB;QAChB,yBAAyB;QACzB,2BAA2B;QAC3B,gDAAgD;QAChD,0BAA0B;QAC1B,iDAAiD;QACjD,iDAAiD;QACjD,MAAM;QACN,gBAAgB;QAChB,yBAAyB;QACzB,6BAA6B;QAC7B,0BAA0B;QAC1B,+CAA+C;QAC/C,4CAA4C;QAC5C,MAAM;IACR,CAAC;IAED,eAAe;QACb,MAAM,QAAQ,GAAG,IAAI,CAAA;;;uBAGF,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;;uCAEd,IAAI,CAAC,qBAAqB,EAAE;;KAE9D,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,cAAc;IACd,4BAA4B;IAC5B,qDAAqD;IACrD,sDAAsD;IACtD,sDAAsD;IACtD,IAAI;IACJ,8CAA8C;IAC9C,eAAe;IACf,wCAAwC;IACxC,0DAA0D;IAC1D,kBAAkB;IAClB,gBAAgB;IAEhB,gBAAgB;QACd,MAAM,QAAQ,GAAG,IAAI,CAAA;;qBAEJ,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;;;gCAGjB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;oBAKlD,IAAI,CAAC,UAAU;kBACjB,IAAI,CAAC,WAAW;wBACV,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;sBAKjC,IAAI,CAAC,WAAW;6BACT,IAAI,CAAC,cAAc;6BACnB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;;;WAGjD,CAAC;QACR,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAA;;;uBAGQ,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;;;2BAGzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;sBAC7B,IAAI,CAAC,QAAQ;;;;YAIvB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,aAAa;YAC7C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,WAAW;;;KAGpD,CAAC;IACJ,CAAC;IAED,WAAW;QACT,MAAM,QAAQ,GAAG,IAAI,CAAA;;UAEf,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;;QAEnD,IAAI,CAAC,aAAa,EAAE;KACvB,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;;;;;YAQQ;IAER,qBAAqB;QACnB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAClD,OAAO,IAAI,CAAA;;;;;UAKP,IAAI,CAAC,YAAY,CAAC,GAAG,CACrB,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAA;uBACO,IAAI;uBACJ,IAAI,CAAC,KAAK;2BACN,IAAI,IAAI,IAAI,CAAC,gBAAgB;uBACjC,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE;wBACtD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtC,IAAI,CAAC,QAAQ;cAChB,CACL;kBACS,CAAC;QACf,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAA;UACP,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;UACnC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,aAAa;UAC7C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,WAAW;;;;;;;;;aAS1C,CAAC;QACV,CAAC;IACH,CAAC;IAED,oBAAoB;QAClB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC/B,uCAAuC;QACvC,qCAAqC;QACrC,iEAAiE;QACjE,MAAM;IACR,CAAC;IAED,sBAAsB,CAAC,GAAG;QACxB,8BAA8B;QAC9B,kCAAkC;QAClC,sBAAsB;QACtB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,4BAA4B;YACtC,KAAK,EAAE,GAAG;SACX,CAAC,CAAC;QACH,IAAI,YAAY,GAAG,GAAG,CAAC,uBAAuB,CAAC;QAC/C,IAAI,IAAI,CAAC,mBAAmB,IAAI,YAAY,EAAE,CAAC;YAC7C,YAAY,GAAG,GAAG,CAAC,6BAA6B,CAAC;QACnD,CAAC;QACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,0BAA0B;YACpC,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;SAC5B,CAAC,CAAC;IACL,CAAC;IAED,aAAa,KAAI,CAAC;IAElB,eAAe,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,0BAA0B;gBACpC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,mBAAmB;QACjB,+EAA+E;QAC/E,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,eAAe,CAAC,CAAC;QACf,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,4BAA4B;YACtC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;SAC5B,CAAC,CAAC;IACL,CAAC;IAED,cAAc,CAAC,IAAI;QACjB,IAAI,MAAM,GAAG,KAAK,CAAC;QACnB,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,GAAG,GAAG,8BAA8B,CAAC,CAAC,yCAAyC;YACnF,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,4BAA4B;YAC7C,IAAI,GAAG,GAAG,yCAAyC,CAAC,CAAC,uBAAuB;YAC5E,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,cAAc,CAAC,CAAC;QACd,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;QACjD,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACnD,IAAI,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC9D,IAAI,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC1D,IAAI,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;YACpC,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACnC,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,KAAK,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,SAAS,EAAE,CAAC;gBACvE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,GAAG,GAAG,WAAW,CAAC;YACpE,CAAC;YAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC;gBAChD,qBAAqB;gBACrB,+LAA+L;YACjM,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC7B,KAAK,CACH,8CAA8C;gBAC5C,IAAI,CAAC,QAAQ;gBACb,UAAU;gBACV,IAAI,CAAC,MAAM;gBACX,mBAAmB;gBACnB,IAAI,CAAC,cAAc,CAAC,EAAE;gBACtB,WAAW;gBACX,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAC5C;iBACE,IAAI,CACH,UAAU,QAAQ;gBAChB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBACjB,MAAM,KAAK,EAAE,CAAC,CAAC,8BAA8B;gBAC/C,CAAC;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb;iBACA,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjB,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzB,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACb,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACf,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,EAAE,CAAC;oBAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC;gBACxD,CAAC;gBACD,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,CAAC,CAAC;iBACD,KAAK,CAAC,UAAU,KAAK,IAAG,CAAC,CAAC;iBAC1B,OAAO,CAAC;gBACP,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;YACnB,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;YAChC,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC1C,OAAO,CAAC,KAAK,CAAC,SAAS;oBACrB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;YAC9D,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC;YACrE,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,mBAAmB;gBAC7B,KAAK,EAAE,OAAO;aACf,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,mCAAmC;YACnC,QAAQ,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;gBACnC,KAAK,SAAS;oBACZ,aAAa,CAAC,QAAQ,CAAC,gBAAgB,EAAE;wBACvC,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK;wBACvC,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE;wBACpC,QAAQ,EAAE,IAAI,CAAC,gBAAgB;wBAC/B,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ;wBAC7C,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM;wBACvC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa;wBAC5C,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;qBACvC,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,QAAQ;oBACX,aAAa,CAAC,QAAQ,CAAC,kBAAkB,EAAE;wBACzC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK;wBAC1C,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE;wBAChC,IAAI,EAAE,IAAI,CAAC,gBAAgB;wBAC3B,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ;wBAC7C,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM;wBACvC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;qBACvC,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,SAAS;oBACZ,aAAa,CAAC,QAAQ,CAAC,qBAAqB,EAAE;wBAC5C,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK;wBAC1C,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE;wBAChC,IAAI,EAAE,IAAI,CAAC,gBAAgB;wBAC3B,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ;wBAC7C,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM;wBACvC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;qBACvC,CAAC,CAAC;oBACH,MAAM;YACV,CAAC;YAED,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QAED;;;;;;;cAOM;IACR,CAAC;IACD,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmET,CAAC;IACJ,CAAC;CACF,CAAA;AA9mB6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;yEAA0C;AAC1C;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;uEAA0B;AAC1B;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;2EAA8B;AAC7B;IAAzB,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;yEAA0C;AAJ/D,iCAAiC;IADtC,aAAa,CAAC,wCAAwC,CAAC;GAClD,iCAAiC,CA+mBtC", "sourcesContent": ["import * as util from \"lib/util.js\";\r\nimport { LitElement, html, css } from \"lit-element\";\r\nimport { storeInstance } from \"store/index.js\";\r\nimport \"@vaadin/vaadin-split-layout/vaadin-split-layout.js\";\r\n\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\nimport \"@ui5/webcomponents/dist/Input.js\";\r\nimport \"@ui5/webcomponents/dist/Label.js\";\r\n// import \"@ui5/webcomponents/dist/Icon.js\";\r\nimport \"@ui5/webcomponents-icons/dist/error.js\";\r\n// import \"@ui5/webcomponents/dist/Dialog.js\";\r\n// import \"@ui5/webcomponents/dist/FileUploader.js\";\r\nimport \"../common/dialog.js\";\r\nimport \"../common/project-selector.js\";\r\nimport \"bds-tree\";\r\nimport { property, customElement } from \"lit/decorators.js\";\r\nimport { Collection } from \"@bds/types\";\r\n/*\r\nhttps://localhost:8443/lwa/jrest/GetFileTemplates?branchId=1\r\n*/\r\n\r\n@customElement(\"wex-dialog-extended-folder-create-file\")\r\nclass WexDialogExtendedFolderCreateFile extends LitElement {\r\n  @property({ type: Object }) dialogOpenData: Record<string, any> = {};\r\n  @property({ type: Array }) templateList: any[] = [];\r\n  @property({type: Object}) selectedTemplate: any = null;\r\n  @property({type: Object}) selectedFolder: Collection | null = null;\r\n\r\n  constructor() {\r\n    super();\r\n    this.state = {};\r\n    this.string = {};\r\n    this._subscription = null;\r\n    this.dialogOpenData = null;\r\n\r\n    this.foldersData = {};\r\n    this.selectedTemplate = null;\r\n\r\n    this.wexGlobalCapability = null;\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n\r\n    this._subscription = storeInstance.subscribe((state) => {\r\n      /*WexDialogFolderCreateFile*/\r\n      this.stateChange(state);\r\n    });\r\n    this.foldersData = state.global_folders;\r\n    this.files = [];\r\n    this.open = false;\r\n    this.fileName = \"\";\r\n    this.fileNameExistsInCollection = null;\r\n    this.dialogHeight = 0;\r\n    this.displayBranch = \"\";\r\n    this.displayLang = \"\";\r\n    this.treeConfig = {\r\n      initialNodeId: \"2\",\r\n      labelPath: \"name\",\r\n      idPath: \"id\",\r\n      childPath: \"children\",\r\n    };\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this._subscription);\r\n  }\r\n\r\n  firstUpdated() {\r\n    this.filenameinput = this.shadowRoot.querySelector(\"#filename\");\r\n    // this.refreshData();\r\n  }\r\n\r\n  stateChange(state) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n\r\n    this.wexGlobalCapability = state.wex_user.globalCapability;\r\n\r\n    this.dialogOpenData = state.extended_folder_create_file_dialog_open;\r\n\r\n    if (this.dialogOpenData) {\r\n      // this.refreshData();\r\n      this.templateList = state.task_file_template_list;\r\n\r\n      this.lockedFolders = this.dialogOpenData.lockedFolders;\r\n      this.branchId = this.dialogOpenData.branch.branchId;\r\n      this.langId = this.dialogOpenData.lang.langId;\r\n\r\n      if (this.dialogOpenData.folder && !this.payloadprocessed) {\r\n        this.payloadprocessed = true;\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"folder_createfile_folder\",\r\n          value: this.dialogOpenData.folder,\r\n        });\r\n      }\r\n\r\n      this.displayBranch = this.dialogOpenData.branch.name;\r\n      this.displayLang = this.dialogOpenData.lang.description;\r\n\r\n      if (this.dialogOpenData.target == \"package\") {\r\n        util.log(this.dialogOpenData.target);\r\n      }\r\n\r\n      this.dialogHeight = state.global_screen_specs.viewportHeight * 0.7;\r\n\r\n      if (!this.open) {\r\n        this.uploadran = false;\r\n        // this.dialog.show();\r\n        // this.dialog.open = true;\r\n\r\n        this.open = true;\r\n        this.selectedTemplate = null;\r\n        this.refreshData();\r\n      }\r\n\r\n      this.selectedFolder = state.folder_createfile_folder;\r\n      this.selectedTemplate = state.folder_createfile_template;\r\n      this.selectedFilename = state.folder_createfile_filename;\r\n\r\n      // this.myRender();\r\n    } else {\r\n      if (this.open) {\r\n        this.selectedFolder = null;\r\n        this.selectedTemplate = null;\r\n        this.selectedFilename = \"\";\r\n        // this.dialog.close();\r\n        // this.dialog.open = false;\r\n        this.open = false;\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"folder_createfile_folder\",\r\n          value: null,\r\n        });\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"folder_createfile_template\",\r\n          value: null,\r\n        });\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"folder_createfile_filename\",\r\n          value: null,\r\n        });\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"folder_createfile_name_exists_in_collection\",\r\n          value: null,\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  refreshData() {\r\n    storeInstance.dispatch(\"getTemplateList\", {\r\n      branchId: this.branchId,\r\n      langId: this.langId,\r\n    });\r\n  }\r\n\r\n  _scrollToSelected(e) {\r\n    this.e = e;\r\n    setTimeout(\r\n      function () {\r\n        this.selectedY = e.detail.getBoundingClientRect().y;\r\n        if (!this.folders) {\r\n          this.folders = this.shadowRoot.querySelector(\"#folders\");\r\n        }\r\n        var folderContainerRect =\r\n          this.folders.parentElement.getBoundingClientRect();\r\n        var folderViewPortTop =\r\n          folderContainerRect.top + this.folders.parentElement.scrollTop;\r\n        var folderViewPortBottom =\r\n          folderContainerRect.bottom + this.folders.parentElement.scrollTop;\r\n        if (\r\n          this.selectedY < folderContainerRect.top ||\r\n          this.selectedY > folderContainerRect.bottom\r\n        ) {\r\n          this.e.detail.scrollIntoView({\r\n            behavior: \"auto\",\r\n            block: \"center\",\r\n            inline: \"nearest\",\r\n          });\r\n        }\r\n      }.bind(this),\r\n      300\r\n    );\r\n\r\n    /*\r\n            if(!this.state.project_dialog_open){\r\n                var selectedY = e.detail.getBoundingClientRect().y;\r\n                if(!this.folders){\r\n                    this.folders =  this.shadowRoot.querySelector(\"#folders\");\r\n                    }\r\n                var folderContainerRect = this.folders.parentElement.getBoundingClientRect();\r\n                \r\n                if(this.folders)\r\n                    \r\n                        if(selectedY <  this.folders.parentElement.scrollTop){\r\n                            this.folders.parentElement.scrollBy( {top: (selectedY- folderContainerRect.y)-100 ,left: 0,behavior: 'instant'}  );\r\n                            }\r\n                        if(selectedY >  this.folders.parentElement.scrollTop + folderContainerRect.height ){\r\n                            this.folders.parentElement.scrollBy({top: (selectedY- folderContainerRect.y)+100 ,left: 0,behavior: 'instant'});\r\n                            }\r\n                    }\r\n                    */\r\n  }\r\n\r\n  _closeDialog() {\r\n    this.payloadprocessed = false;\r\n    this.filenameinput.value = \"\";\r\n    this.sectionHeight = undefined;\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"extended_folder_create_file_dialog_open\",\r\n      value: null,\r\n    });\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"createfile_project_context\",\r\n      value: null,\r\n    });\r\n  }\r\n\r\n  render() {\r\n    const template = html`\r\n      <wex-dialog\r\n        width=\"80%\"\r\n        height=\"90vh\"\r\n        dialogOpenStateProperty=\"extended_folder_create_file_dialog_open\"\r\n        .open=${!!this.dialogOpenData}\r\n        confirmLabel=\"${this.string[\"_createfile\"]}\"\r\n        headerText=\"${this.string[\"_createfile\"]}\"\r\n      >\r\n        ${this._renderBody()}\r\n      </wex-dialog>\r\n    `;\r\n    return template;\r\n    // <div slot=\"footer\" class=\"dialog-footer\">\r\n    //   <div style=\"flex: 1;\"></div>\r\n    //   <ui5-button\r\n    //     class=\"dialogbtns\"\r\n    //     id=\"create-file-btn\"\r\n    //     ?disabled=\"${this._saveButtonDisabled()}\"\r\n    //     design=\"Emphasized\"\r\n    //     @click=\"${this._checkFileName.bind(this)}\"\r\n    //     >${this.string[\"_createfile\"]}</ui5-button\r\n    //   >\r\n    //   <ui5-button\r\n    //     class=\"dialogbtns\"\r\n    //     id=\"closeDialogButton\"\r\n    //     design=\"Emphasized\"\r\n    //     @click=\"${this._closeDialog.bind(this)}\"\r\n    //     >${this.string[\"_close\"]}</ui5-button\r\n    //   >\r\n  }\r\n\r\n  _renderLeftSide() {\r\n    const template = html`\r\n      <div id=\"left\">\r\n        <div class=\"lablerow\">\r\n          <ui5-label>${this.string[\"_selecttemplate\"]}</ui5-label><br />\r\n        </div>\r\n        <div id=\"template-container\">${this._templateListTemplate()}</div>\r\n      </div>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  // <ui5-button\r\n  //   id=\"jump-proj-fldr-btn\"\r\n  //   @click=\"${this._projectSelectDialog.bind(this)}\"\r\n  //   class=\"prj ui5button ui5-content-density-compact\"\r\n  //   title=\"${this.string[\"_jump_to_project_folder\"]}\"\r\n  // >\r\n  //   ${this.string[\"_jump_to_project_folder\"]}\r\n  //   <iron-icon\r\n  //     icon=\"vaadin:arrow-circle-down-o\"\r\n  //     title=\"${this.string[\"_jump_to_a_project_folder\"]}\"\r\n  //   ></iron-icon>\r\n  // </ui5-button>\r\n\r\n  _renderRightSide() {\r\n    const template = html` <div id=\"right\">\r\n      <div class=\"lablerow\">\r\n        <ui5-label>${this.string[\"_selectfolder\"]}</ui5-label>\r\n\r\n        <wex-project-selector\r\n          .jumpToFolderAction=${this._selectProjectCallback.bind(this)}\r\n        ></wex-project-selector>\r\n      </div>\r\n      <div id=\"folder-container\">\r\n        <bds-tree\r\n          .config=${this.treeConfig}\r\n          .root=${this.foldersData}\r\n          @treeClick=\"${this._folderSelected.bind(this)}\"\r\n        ></bds-tree>\r\n        <!-- <wex-folders\r\n          id=\"folders\"\r\n          name=\"selectfolder\"\r\n          .folders=\"${this.foldersData}\"\r\n          .selectedfolder=\"${this.selectedFolder}\"\r\n          @folderselected=\"${this._folderSelected.bind(this)}\"\r\n        ></wex-folders> -->\r\n      </div>\r\n    </div>`;\r\n    return template;\r\n  }\r\n\r\n  _renderBottom() {\r\n    return html`\r\n      <div id=\"filenamerow2\">\r\n        <div>\r\n          <ui5-label>${this.string[\"_enterfilename\"]}</ui5-label><br />\r\n          <ui5-input\r\n            id=\"filename\"\r\n            placeholder=\"${this.string[\"_filename\"]}\"\r\n            .value=\"${this.fileName}\"\r\n          ></ui5-input>\r\n        </div>\r\n        <div id=\"branchlang\">\r\n          ${this.string[\"_branch\"]}: ${this.displayBranch}<br />\r\n          ${this.string[\"_language\"]}: ${this.displayLang}\r\n        </div>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  _renderBody() {\r\n    const template = html`\r\n      <vaadin-split-layout id=\"layout\">\r\n        ${this._renderLeftSide()} ${this._renderRightSide()}\r\n      </vaadin-split-layout>\r\n      ${this._renderBottom()}\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  /*\r\n        <ui5-button @click=\"${this._projectSelectDialog.bind(this)}\" class='prj ui5button ui5-content-density-compact' title=\"${this.string['_filter_by_project']}\">&#8230;</ui5-button>\r\n                                        <ui5-button ?disabled='${!this.state.createfile_project_context}'  id=\"jump-proj-fldr-btn\" @click=\"${this._jumpToFolder.bind(this)}\" class='prj ui5button ui5-content-density-compact' title=\"${this.string['_jump_to_project_folder']}\">\r\n                                            <iron-icon icon=\"vaadin:arrow-circle-right-o\" title=\"${this.string['_jump_to_project_folder']}\"></iron-icon>\r\n                                        </ui5-button> \r\n\r\n                                    <ui5-input class='projectinput ui5-content-density-compact' readonly value=\"${this.state.createfile_project_context ? this.state.createfile_project_context.name : this.string[\"_jump_to_project_folder\"]}\" @click=\"${this._projectSelectDialog.bind(this)}\"></ui5-input>\r\n                                        \r\n        */\r\n\r\n  _templateListTemplate() {\r\n    if (this.templateList && this.templateList.length) {\r\n      return html`<ui5-list\r\n        id=\"templateselector\"\r\n        class=\"full-width  ui5-content-density-compact\"\r\n        infinite-scroll\r\n      >\r\n        ${this.templateList.map(\r\n          (item) =>\r\n            html`<ui5-li\r\n              .item=\"${item}\"\r\n              value=\"${item.resId}\"\r\n              ?selected=\"${item == this.selectedTemplate}\"\r\n              class=\"${item == this.selectedTemplate ? \"selectedtemplate\" : \"\"}\"\r\n              @click=\"${this._selectTemplate.bind(this)}\"\r\n              >${item.fileName}</ui5-li\r\n            >`\r\n        )}\r\n      </ui5-list>`;\r\n    } else {\r\n      return html`<div id=\"notemplates\">\r\n        ${this.string[\"_noTemplatesFoundFor\"]} <br />\r\n        ${this.string[\"_branch\"]}: ${this.displayBranch}<br />\r\n        ${this.string[\"_language\"]}: ${this.displayLang}\r\n        <div>\r\n          <ui5-list\r\n            id=\"templateselector\"\r\n            class=\"full-width  ui5-content-density-compact\"\r\n            infinite-scroll\r\n          >\r\n          </ui5-list>\r\n        </div>\r\n      </div>`;\r\n    }\r\n  }\r\n\r\n  _projectSelectDialog() {\r\n    console.log(\"jump to project\");\r\n    // storeInstance.dispatch(\"setState\", {\r\n    //   property: \"project_dialog_open\",\r\n    //   value: { callback: this._selectProjectCallback.bind(this) },\r\n    // });\r\n  }\r\n\r\n  _selectProjectCallback(prj) {\r\n    // prj.projectHomeCollectionId\r\n    // this.sectionHeight = undefined;\r\n    // this.firstOpen = 2;\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"createfile_project_context\",\r\n      value: prj,\r\n    });\r\n    var collectionId = prj.projectHomeCollectionId;\r\n    if (this.wexGlobalCapability == \"Contribute\") {\r\n      collectionId = prj.projectContributeCollectionId;\r\n    }\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"folder_createfile_folder\",\r\n      value: { id: collectionId },\r\n    });\r\n  }\r\n\r\n  _jumpToFolder() {}\r\n\r\n  _folderSelected(e) {\r\n    if (!this.lockedFolders) {\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"folder_createfile_folder\",\r\n        value: e.detail.node,\r\n      });\r\n    }\r\n  }\r\n\r\n  _saveButtonDisabled() {\r\n    //if template selected and folder selected and filename ok then false else true\r\n    var retval = false;\r\n    return retval;\r\n  }\r\n\r\n  _selectTemplate(e) {\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"folder_createfile_template\",\r\n      value: e.currentTarget.item,\r\n    });\r\n  }\r\n\r\n  _validFileName(name) {\r\n    var retval = false;\r\n    if (name) {\r\n      var rg1 = /^[^\\\\/:\\*\\?\"<>+%@#&,=~'\\|]+$/; // forbidden characters \\ / : * ? \" < > |\r\n      var rg2 = /^\\./; // cannot start with dot (.)\r\n      var rg3 = /^(nul|prn|con|lpt[0-9]|com[0-9])(\\.|$)/i; // forbidden file names\r\n      retval = rg1.test(name) && !rg2.test(name) && !rg3.test(name);\r\n    }\r\n    return retval;\r\n  }\r\n\r\n  _checkFileName(e) {\r\n    this.selectedFilename = this.filenameinput.value;\r\n    this.aMsg = [];\r\n    if (!this.selectedFolder) {\r\n      this.aMsg.push(this.string[\"_folderrequired\"]);\r\n    }\r\n    if (!this.selectedTemplate) {\r\n      this.aMsg.push(this.string[\"_templaterequired\"]);\r\n    }\r\n    if (!this.selectedFilename) {\r\n      this.aMsg.push(this.string[\"_filerequired\"]);\r\n    }\r\n\r\n    if (this.selectedFilename && this.selectedTemplate) {\r\n      var aTemplateType = this.selectedTemplate.fileName.split(\".\");\r\n      var templateExt = aTemplateType[aTemplateType.length - 1];\r\n      var tmpName = this.selectedFilename;\r\n      var aTmp = this.selectedFilename.split(\".\");\r\n      var tmpExt = aTmp[aTmp.length - 1];\r\n      if (tmpExt.toLowerCase() != \"xml\" && tmpExt.toLowerCase() != \"ditamap\") {\r\n        this.selectedFilename = this.selectedFilename + \".\" + templateExt;\r\n      }\r\n\r\n      this.validName = this._validFileName(tmpName);\r\n      if (!this.validName) {\r\n        this.aMsg.push(this.string[\"_invalidfilename\"]);\r\n        //open message dialog\r\n        //storeInstance.dispatch(\"setState\", {property:  \"folder_createfile_dialog_message\" ,  value: {type:'Negative', message: this.string[\"_invalidfilename\"] + \": \" + this.filenameinput.value} });\r\n      }\r\n    }\r\n    if (this.validName) {\r\n      PubSub.publish(\"requestout\");\r\n      fetch(\r\n        \"/lwa/jrest/DoesResourceExistInColl?branchId=\" +\r\n          this.branchId +\r\n          \"&langId=\" +\r\n          this.langId +\r\n          \"&intCollectionId=\" +\r\n          this.selectedFolder.id +\r\n          \"&resName=\" +\r\n          encodeURIComponent(this.selectedFilename)\r\n      )\r\n        .then(\r\n          function (response) {\r\n            if (!response.ok) {\r\n              throw Error(); //to bail out of promise chain\r\n            }\r\n            return response;\r\n          }.bind(this)\r\n        )\r\n        .then((response) => {\r\n          return response.json();\r\n        })\r\n        .then((data) => {\r\n          util.log(data);\r\n          if (data.exists != \"false\") {\r\n            this.aMsg.push(this.string[\"_filenamealreadyexists\"]);\r\n          }\r\n          this._nextSteps();\r\n        })\r\n        .catch(function (error) {})\r\n        .finally(function () {\r\n          PubSub.publish(\"requestin\");\r\n        });\r\n    } else {\r\n      this._nextSteps();\r\n    }\r\n  }\r\n\r\n  _nextSteps() {\r\n    if (this.aMsg.length > 0) {\r\n      var message = {};\r\n      message.error = {};\r\n      message.error.summary = \"Error\";\r\n      message.error.shortDesc = \"\";\r\n      for (var i = 0; i < this.aMsg.length; i++) {\r\n        message.error.shortDesc =\r\n          message.error.shortDesc + \"<li>\" + this.aMsg[i] + \"</li>\";\r\n      }\r\n      message.error.shortDesc = \"<ul>\" + message.error.shortDesc + \"</ul>\";\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"error_dialog_open\",\r\n        value: message,\r\n      });\r\n    } else {\r\n      // const obj = this.dialogOpenData;\r\n      switch (this.dialogOpenData.target) {\r\n        case \"package\":\r\n          storeInstance.dispatch(\"taskCreateFile\", {\r\n            templateId: this.selectedTemplate.resId,\r\n            collectionId: this.selectedFolder.id,\r\n            filename: this.selectedFilename,\r\n            branchid: this.dialogOpenData.branch.branchId,\r\n            langid: this.dialogOpenData.lang.langId,\r\n            task: this.dialogOpenData.task.querySelector,\r\n            callback: this.dialogOpenData.callback,\r\n          });\r\n          break;\r\n        case \"folder\":\r\n          storeInstance.dispatch(\"folderCreateFile\", {\r\n            templateResId: this.selectedTemplate.resId,\r\n            folderId: this.selectedFolder.id,\r\n            name: this.selectedFilename,\r\n            branchid: this.dialogOpenData.branch.branchId,\r\n            langid: this.dialogOpenData.lang.langId,\r\n            callback: this.dialogOpenData.callback,\r\n          });\r\n          break;\r\n        case \"ditamap\":\r\n          storeInstance.dispatch(\"navigatorCreateFile\", {\r\n            templateResId: this.selectedTemplate.resId,\r\n            folderId: this.selectedFolder.id,\r\n            name: this.selectedFilename,\r\n            branchid: this.dialogOpenData.branch.branchId,\r\n            langid: this.dialogOpenData.lang.langId,\r\n            callback: this.dialogOpenData.callback,\r\n          });\r\n          break;\r\n      }\r\n\r\n      this._closeDialog();\r\n    }\r\n\r\n    /*\r\n        if(tmpName){\r\n                storeInstance.dispatch(\"setState\", {property:  \"folder_createfile_name\" ,  value:  tmpName });\r\n                storeInstance.dispatch(\"folderCheckCreateFileName\", {name: tmpName, langId: this.state.browse_lang_filter.langId, branchId: this.state.browse_branch_filter.branchId, folderId: this.state.browse_selected_folder.id});    \r\n            } else {\r\n                storeInstance.commit(\"setState\", {property:  \"folder_createfile_name\" ,  value:  null });\r\n        }\r\n        */\r\n  }\r\n  static get styles() {\r\n    return css`\r\n      #main {\r\n        width: 100%;\r\n        margin: 0;\r\n        padding: 0;\r\n        display: flex;\r\n        flex-direction: column;\r\n        height: 100%;\r\n      }\r\n      #layout {\r\n        height: 100%;\r\n        display: flex;\r\n        flex: 1 1 auto;\r\n        overflow: hidden;\r\n        border-bottom: 1px solid lightgrey;\r\n      }\r\n\r\n      #left,\r\n      #right {\r\n        width: 50%;\r\n        overflow: hidden;\r\n        height: 100%;\r\n      }\r\n\r\n      .lablerow {\r\n        padding-left: 0.5rem;\r\n        line-height: 30px;\r\n        justify-content: space-between;\r\n        display: flex;\r\n        flex-direction: row;\r\n      }\r\n\r\n      #template-container {\r\n        height: 100%;\r\n        overflow: auto;\r\n      }\r\n\r\n      #folder-container {\r\n        height: 100%;\r\n        overflow: auto;\r\n      }\r\n\r\n      #filenamerow2 {\r\n        box-sizing: border-box;\r\n        padding-left: 4rem;\r\n        padding-top: 1rem;\r\n        background-color: #f8f8f8;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: center;\r\n      }\r\n\r\n      #branchlang {\r\n        margin: 1rem 0 0 1rem;\r\n      }\r\n\r\n      ui5-label {\r\n        text-align: left;\r\n      }\r\n\r\n      .selectedtemplate {\r\n        background-color: var(--row-selected-background);\r\n      }\r\n\r\n      #notemplates {\r\n        text-align: center;\r\n      }\r\n    `;\r\n  }\r\n}\r\n"]}