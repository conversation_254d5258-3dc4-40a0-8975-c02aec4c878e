var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { storeInstance } from "store/index.js";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/CheckBox.js";
import "@ui5/webcomponents/dist/Input.js";
import "../wex-folders.js";
import "../wex-select-file-files.js";
import "../common/dialog.js";
import "../common/project-selector.js";
import "./content/file-explorer.js";
import "../select/global_languages.js";
import "../select/global_branches.js";
import { customElement, property } from "lit/decorators.js";
let WexDialogSelectFile = class WexDialogSelectFile extends LitElement {
    static get properties() {
        return {
            dialogOpenData: { type: Object },
            selectedProject: { type: Object },
            searchTerm: { type: String },
            selectedFile: { type: Object },
        };
    }
    static get styles() {
        return css `
      wex-main-pane {
        height: 100%;
      }
      .top {
        width: 100%;
        flex: 1;
      }
      .middle {
        flex: 9;
      }
      .bottom {
        flex: 1;
        display: flex;
        flex-direction: row;
        flex-shrink: 0;
        height: 10%;
      }

      #proj {
        flex: 2;
      }

      #searchterm {
        clear: both;
        --_ui5_input_width: 8rem;
      }

      #display {
        flex: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .selectedfileinput {
        width: 100%;
      }
    `;
    }
    constructor() {
        super();
        this.dialogOpenData = {};
        this.selectedProject = null;
        this.searchTerm = "";
        this.selectedFile = null;
        this.tree = null;
        this._onConfirm = () => {
            // console.log("_onConfirm");
            this.state.select_file_dialog_open.callback(this.selectedFile);
        };
        this.state = {};
        this.string = {};
        this._subscription = null;
        this.dialogOpenData = null;
        this.selectedFile = null;
        this.selectedFilePathId = "";
        this.searchTerm = "";
        this.selectedProject = null;
        this.foldersData = {};
        this.fileExplorer = null;
    }
    connectedCallback() {
        super.connectedCallback();
        this.state = storeInstance.state;
        this.string = this.state[this.state.langCode];
        this._subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        // this._init();
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this._subscription);
    }
    firstUpdated() {
        this._init();
        this.searchTermField = this.shadowRoot.querySelector("#searchterm");
        this.tree = this.shadowRoot?.querySelector("bds-tree") || null;
    }
    _init() {
        storeInstance.dispatch("getCollectionTree"); // this sets a state var? Which one is updated here?
        this.fileExplorer = this.shadowRoot.querySelector("wex-dialog-content-file-explorer");
        // this.folderselement = this.shadowRoot.querySelector("#fileselectfolders");
        // if (this.folderselement) {
        //   this.folderselement.addEventListener(
        //     "selectedrendered",
        //     this._scrollToSelected.bind(this)
        //   );
        // }
        // TODO EJS add this to the dialog component
        // this.addEventListener("keydown", (e) => {
        //   if (e.key == "Escape") {
        //     this.closeDialog();
        //   }
        // });
        //this.fileExplorer.refreshData();
    }
    _updateSearchTerm() {
        // if there is a search term in state - why would a term be in state before the dialog is opened?
        if (this.state.select_file_dialog_search_term) {
            // set it to instance
            this.searchTerm = this.state.select_file_dialog_search_term;
        }
        else {
            // or set instance to nil
            this.searchTerm = "";
        }
    }
    _updateFolderData() {
        // this.foldersData = JSON.parse(JSON.stringify(this.state.global_folders));
        if (this.state.global_folders) {
            this.foldersData = JSON.parse(JSON.stringify(this.state.global_folders));
        }
    }
    _updateSelectedFile(state) {
        // state change from wex-select-file-files
        // if there is a selected file
        // console.log(
        //   "state.select_file_selected_file",
        //   state.select_file_selected_file
        // );
        if (state.select_file_selected_file) {
            // console.log("state file?", state.select_file_selected_file);
            // set it to instance
            this.selectedFile = state.select_file_selected_file;
            // set the path id to instance var
            this.selectedFilePathId = this.selectedFile.resPathId;
        }
        else {
            // null the vars (they should be null already in the constructor)
            this.selectedFile = null;
            this.selectedFilePathId = "";
        }
    }
    _setCurrentFolder() {
        // setCurrentFolder
        // const folderObj = this.state.select_file_dialog_open.folder;
        const folderObj = this.dialogOpenData.folder;
        // console.log("setCurrentFolder", folderObj);
        if (folderObj) {
            const folderId = folderObj.id;
            this._findFolderById(this.foldersData, folderId, this);
        }
    }
    _setCurrentProject() {
        // this.selectedProject = this.state.task_filter_project; // confusing; is there a global project selector? Can it inherit from the task page?
        // const project = this.state.select_file_dialog_open.project;
        const project = this.dialogOpenData.project || this.state.task_filter_project;
        if (project) {
            // this._selectProjectCallback(project);
            this.selectedProject = project;
        }
    }
    stateChange(state) {
        this.state = state;
        this.string = state[state.langCode];
        this.dialogOpenData = state.select_file_dialog_open;
        // if dialog is open
        if (this.dialogOpenData) {
            this._updateFolderData(); // outside open check in source
            this._updateSearchTerm();
            this._updateSelectedFile(state);
            this._setCurrentFolder();
            this._setCurrentProject();
        }
        // does this need to be moved to file explorer?
        this.contextMenu = JSON.parse(JSON.stringify(this.state.menus.folder_menu));
        this.contextMenu.map((item) => {
            item.text = this.string[item.label];
        });
    }
    _findFolderById(root, collId) {
        if (!this.tree || !root)
            return;
        console.log("FIND FOLDER", this.tree);
        this.tree.treeController.setNode(root, String(collId));
        const result = this.tree.treeController.findNodeByKey(root, String(collId), []);
        if (!result)
            return;
        const { node } = result;
        // found the right folder
        storeInstance.dispatch("setState", {
            property: "select_file_dialog_project",
            value: null,
        });
        storeInstance.dispatch("setState", {
            property: "select_file_dialog_folderitem",
            value: { ...node, path: node.collectionPathId },
        });
    }
    _clearSelectedFile() {
        this.selectedFile = null;
        this.selectedFilePathId = "";
    }
    // _getSearchTerm() {
    //   return this.searchTerm;
    // }
    // sub-dialog call
    // _projectSelectDialog() {
    //   storeInstance.dispatch("setState", {
    //     property: "project_dialog_open",
    //     value: { callback: this._selectProjectCallback.bind(this) },
    //   });
    // }
    // callback for opening project dialog... try to remove?
    // _selectProjectCallback(project) {
    //   console.log("selectProjectCallback");
    //   console.log("selectProjectCallback project", project);
    //   console.log("selectProjectCallback selectedProject", this.selectedProject);
    //   // this.selectedProject = project;
    //   // storeInstance.dispatch("setState", {
    //   //   property: "select_file_dialog_project",
    //   //   value: project,
    //   // });
    //   // this._findFolderById(this.foldersData, project.projectHomeCollectionId, this);
    // }
    // should be instance variables
    _clearFilters() {
        console.log("clear filters", this.state.default_lang_object);
        storeInstance.dispatch("setState", {
            property: "select_file_dialog_lang_filter",
            value: this.state.default_lang_object,
        });
        storeInstance.dispatch("setState", {
            property: "select_file_dialog_branch_filter",
            value: this.state.default_branch_object,
        });
        // this.searchTermField.value = "";
        storeInstance.dispatch("setState", {
            property: "select_file_dialog_search_subfolders",
            value: false,
        });
        storeInstance.dispatch("setState", {
            property: "select_file_dialog_search_term",
            value: "",
        });
        this._clearSelectedFile();
        this.fileExplorer.refreshFilesList();
    }
    _scrollToSelected(e) {
        // console.log("scrolling to selected");
        var selectedY = e.detail.getBoundingClientRect().y;
        var folderContainerRect = this.folderselement.parentElement.getBoundingClientRect();
        if (selectedY < this.folderselement.parentElement.scrollTop) {
            this.folderselement.parentElement.scrollBy(0, selectedY - folderContainerRect.y);
        }
        if (selectedY >
            this.folderselement.parentElement.scrollTop + folderContainerRect.height) {
            this.folderselement.parentElement.scrollBy(0, selectedY - folderContainerRect.y);
        }
    }
    // should be instance var
    // this.selectedFile;
    _clearSelectedFile() {
        storeInstance.dispatch("setState", {
            property: "select_file_selected_file",
            value: null,
        });
    }
    // should be instance var
    // this.folderItem;
    // _folderSelected(e) {
    //   if (this.folderselected != e.detail) {
    //     this._clearSelectedFile();
    //     storeInstance.dispatch("setState", {
    //       property: "select_file_dialog_folderitem",
    //       value: e.detail,
    //     });
    //     this.folderselected == e.detail;
    //   }
    // }
    _jumpToFolder() {
        console.log("jumpToFolder", this.selectedProject);
        // check only if there is a project vs all projects
        if (this.selectedProject) {
            // console.log("jumpToFolder");
            // console.log("jumpToFolder this.foldersData", this.foldersData);
            console.log("jumpToFolder this.selectedProject", this.selectedProject);
            this._findFolderById(this.foldersData, this.selectedProject.projectHomeCollectionId);
        }
    }
    // should be instance var
    _handleSearchClick() {
        console.log("dispatch search for ", this.searchTermField.value);
        storeInstance.dispatch("setState", {
            property: "select_file_dialog_search_term",
            value: this.searchTermField.value,
        });
        this._clearSelectedFile();
    }
    // should be instance var
    _handleCheckboxChange(e) {
        if (this.includesubfolders != e.currentTarget.checked) {
            this._clearSelectedFile();
            this.includesubfolders = e.currentTarget.checked;
            storeInstance.dispatch("setState", {
                property: "select_file_dialog_search_subfolders",
                value: e.currentTarget.checked,
            });
        }
    }
    _renderProjectControls() {
        return html `
      <wex-row-item id="proj" justifyContent="stretch" alignItems="center">
        <wex-project-selector
          .jumpToFolderAction=${this._jumpToFolder.bind(this)}
        ></wex-project-selector>

        <wex-select-global-languages
          @language-selected=${() => this.fileExplorer.refreshFilesList()}
          .filterTargets=${["select_file_dialog_lang_filter"]}
        ></wex-select-global-languages>
        <wex-select-global-branches
          @branch-selected=${() => this.fileExplorer.refreshFilesList()}
          .filterTargets=${["select_file_dialog_branch_filter"]}
        ></wex-select-global-branches>

        <ui5-button
          design="Transparent"
          @click="${this._clearFilters.bind(this)}"
          >${this.string["_clearfilters"]}</ui5-button
        >

        <ui5-checkbox
          id="search-chk"
          ?checked="${this.state.select_file_dialog_search_subfolders}"
          @change="${this._handleCheckboxChange.bind(this)}"
          text="${this.string["_includesubfolders"]}"
        ></ui5-checkbox>
      </wex-row-item>
    `;
    }
    _renderSearchControls() {
        return html `
      <wex-row-item id="srch" justifyContent="flex-end" alignItems="center">
        <ui5-input
          id="searchterm"
          type="Text"
          value="${this.searchTerm}"
          show-clear-icon
        ></ui5-input>

        <wex-icon
          icon="search"
          @click=${this._handleSearchClick.bind(this)}
        ></wex-icon>
      </wex-row-item>
    `;
    }
    _renderTop() {
        return html `
      <wex-row-item class="top" justifyContent="space-between">
        ${this._renderProjectControls()} ${this._renderSearchControls()}
      </wex-row-item>
    `;
    }
    _renderMiddle() {
        return html `
      <wex-row-item class="middle">
        <wex-dialog-content-file-explorer></wex-dialog-content-file-explorer>
      </wex-row-item>
    `;
    }
    _renderBottom() {
        return html `
      <wex-row-item class="bottom">
        <wex-col-item id="display">
          <ui5-input
            class="selectedfileinput ui5-content-density-compact"
            readonly
            value="${this.selectedFilePathId}"
          ></ui5-input>
        </wex-col-item>
      </wex-row-item>
    `;
    }
    // render select file dialog (main component)
    render() {
        return html `
      <wex-dialog
        id="select-file-dialog"
        .open="${!!this.dialogOpenData}"
        .dialogSettings="${this.dialogSettings}"
        .onConfirm="${this._onConfirm}"
        confirmLabel="Ok"
        cancelLabel="Close"
        headerText="Select a File"
        dialogOpenStateProperty="select_file_dialog_open"
        width="90%"
        height="90%"
      >
        <wex-col-item>
          ${this._renderTop()} ${this._renderMiddle()} ${this._renderBottom()}
        </wex-col-item>
      </wex-dialog>
    `;
    }
};
__decorate([
    property({ type: Object })
], WexDialogSelectFile.prototype, "dialogOpenData", void 0);
__decorate([
    property({ type: Object })
], WexDialogSelectFile.prototype, "selectedProject", void 0);
__decorate([
    property({ type: String })
], WexDialogSelectFile.prototype, "searchTerm", void 0);
__decorate([
    property({ type: Object })
], WexDialogSelectFile.prototype, "selectedFile", void 0);
WexDialogSelectFile = __decorate([
    customElement("wex-dialog-select-file")
], WexDialogSelectFile);
//# sourceMappingURL=select-file.js.map