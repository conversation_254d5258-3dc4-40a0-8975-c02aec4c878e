{"version": 3, "file": "value-notifier.js", "sources": ["../src/lib/value-notifier.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ContextCallback} from './context-request-event.js';\n\n/**\n * A disposer function\n */\ntype Disposer = () => void;\n\ninterface CallbackInfo {\n  disposer: Disposer;\n  consumerHost: Element;\n}\n\n/**\n * A simple class which stores a value, and triggers registered callbacks when\n * the value is changed via its setter.\n *\n * An implementor might use other observable patterns such as MobX or Redux to\n * get behavior like this. But this is a pretty minimal approach that will\n * likely work for a number of use cases.\n */\nexport class ValueNotifier<T> {\n  protected readonly subscriptions = new Map<\n    ContextCallback<T>,\n    CallbackInfo\n  >();\n  private _value!: T;\n  get value(): T {\n    return this._value;\n  }\n  set value(v: T) {\n    this.setValue(v);\n  }\n\n  setValue(v: T, force = false) {\n    const update = force || !Object.is(v, this._value);\n    this._value = v;\n    if (update) {\n      this.updateObservers();\n    }\n  }\n\n  constructor(defaultValue?: T) {\n    if (defaultValue !== undefined) {\n      this.value = defaultValue;\n    }\n  }\n\n  updateObservers = (): void => {\n    for (const [callback, {disposer}] of this.subscriptions) {\n      callback(this._value, disposer);\n    }\n  };\n\n  addCallback(\n    callback: ContextCallback<T>,\n    consumerHost: Element,\n    subscribe?: boolean\n  ): void {\n    if (!subscribe) {\n      // just call the callback once and we're done\n      callback(this.value);\n      return;\n    }\n    if (!this.subscriptions.has(callback)) {\n      this.subscriptions.set(callback, {\n        disposer: () => {\n          this.subscriptions.delete(callback);\n        },\n        consumerHost,\n      });\n    }\n    const {disposer} = this.subscriptions.get(callback)!;\n    callback(this.value, disposer);\n  }\n\n  clearCallbacks(): void {\n    this.subscriptions.clear();\n  }\n}\n"], "names": ["ValueNotifier", "value", "this", "_value", "v", "setValue", "force", "update", "Object", "is", "updateObservers", "constructor", "defaultValue", "subscriptions", "Map", "callback", "disposer", "undefined", "addCallback", "consumerHost", "subscribe", "has", "set", "delete", "get", "clearCallbacks", "clear"], "mappings": ";;;;;MA0BaA,EAMX,SAAIC,GACF,OAAOC,KAAKC,CACb,CACD,SAAIF,CAAMG,GACRF,KAAKG,SAASD,EACf,CAED,QAAAC,CAASD,EAAME,GAAQ,GACrB,MAAMC,EAASD,IAAUE,OAAOC,GAAGL,EAAGF,KAAKC,GAC3CD,KAAKC,EAASC,EACVG,GACFL,KAAKQ,iBAER,CAED,WAAAC,CAAYC,GApBOV,KAAAW,cAAgB,IAAIC,IA0BvCZ,KAAeQ,gBAAG,KAChB,IAAK,MAAOK,GAAUC,SAACA,MAAcd,KAAKW,cACxCE,EAASb,KAAKC,EAAQa,EACvB,OARoBC,IAAjBL,IACFV,KAAKD,MAAQW,EAEhB,CAQD,WAAAM,CACEH,EACAI,EACAC,GAEA,IAAKA,EAGH,YADAL,EAASb,KAAKD,OAGXC,KAAKW,cAAcQ,IAAIN,IAC1Bb,KAAKW,cAAcS,IAAIP,EAAU,CAC/BC,SAAU,KACRd,KAAKW,cAAcU,OAAOR,EAAS,EAErCI,iBAGJ,MAAMH,SAACA,GAAYd,KAAKW,cAAcW,IAAIT,GAC1CA,EAASb,KAAKD,MAAOe,EACtB,CAED,cAAAS,GACEvB,KAAKW,cAAca,OACpB"}