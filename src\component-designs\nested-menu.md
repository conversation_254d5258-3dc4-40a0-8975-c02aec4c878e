# Nested Menu

A complex menu display.

```ts
class WexNestedMenu extends LitElement {
  @property({ type: Array }) data: any[] = [];

  render() {}
}
```

Project Selector:

```ts
class ProjectSelector extends LitElement {
  @property({ type: Object }) selectedProject: Project | null = null;

  render() {
    return html`<bds-nested-menu @item-selected=${this._handleItemSelected} .data=${this.data}> </bds-nested-menu>`;
  }
}

data = [
  {
    label: "All Projects",
    value: "all",
  },
  {
    label: "Project 1",
    value: "project1",
    children: [
      {
        label: "Branch 1",
        value: "branch1",
      },
      {
        label: "Branch 2",
        value: "branch2",
      },
    ],
  },
];
```

Editor Task Manager:

```ts
class EditorTaskManager extends LitElement {
  @property({ type: Array }) tasks: Task[] = [];

  render() {
    return html`<bds-nested-menu .data=${this.data}>
      <template>
        <div>Task Name</div>
      </template>
    </bds-nested-menu>`;
  }
}
```
