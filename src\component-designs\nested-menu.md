# Nested Menu

A complex menu display.

- We want to make all item displays customizable.

[Future Optimization - Can only fetch if there is metadata about children count]

- We want to make children fetchable.
- If a child can be fetched, we need it to include:
  - A fetch function.
  - A key in the cache.
  - A renderer function.

```ts
class WexNestedMenu extends LitElement {
  @property({ type: Array }) data: any[] = [];

  render() {}
}

class WexNestedMenuItem<T extends NestedMenuItem> extends LitElement {
  @property({ type: Object }) item: T = {};
  @property({ type: Boolean }) isOpen: boolean = false;
  @property({ type: Object }) children: T[] = [];

  dispatchMenuItemSelected() {
    this.dispatchEvent(
      new CustomEvent("menu-item-selected", {
        detail: this.item,
      })
    );
  }

  render() {
    return html`<li @click=${this.dispatchMenuItemSelected}>
      ${item.renderer ? item.renderer() : item.label}
      ${this.isOpen &&
      this.children.map(
        (child) =>
          html`<wex-nested-menu-item .item=${child}></wex-nested-menu-item>`
      )}
    </li>`;
  }
}
```

Project Selector:

```ts
class ProjectSelector extends LitElement {
  @property({ type: Object }) selectedProject: Project | null = null;

  render() {
    return html`<bds-nested-menu
      @item-selected=${this._handleItemSelected}
      .data=${this.data}
    >
    </bds-nested-menu>`;
  }
}

data = [
  {
    label: "All Projects",
    value: "all",
    renderer: () =>
      html`<div @click=${this._handleAllProjectsClick}>All Projects</div>`,
  },
  {
    label: "Project 1",
    value: "project1",
    shouldOpen: () => this.selectedProject?.projectId === "project1",
    children: [],
  },
];
```

Editor Task Manager:

```ts
class EditorTaskManager extends LitElement {
  @property({ type: Array }) tasks: Task[] = [];

  render() {
    return html`<bds-nested-menu .data=${this.data}>
      <template>
        <div>Task Name</div>
      </template>
    </bds-nested-menu>`;
  }
}
```
