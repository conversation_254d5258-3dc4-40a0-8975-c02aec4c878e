# Nested Menu

A complex menu display.

- We want to make all item displays customizable.
- We want to make children fetchable.
- If a child can be fetched, we need it to include:
  - A fetch function.
  - A key in the cache.
  - A renderer function.

```ts
class WexNestedMenu extends LitElement {
  @property({ type: Array }) data: any[] = [];

  render() {}
}

class WexNestedMenuItem extends LitElement {
  @property({ type: Object }) item: NestedMenuItem = {};
  @property({ type: Boolean }) hasChildren?: boolean = false;

  fetchChildren() {
    this.item.fetchChildren();
  }

  render() {
    if (item.renderer) {
      return item.renderer(item);
    }
    return html`<div>${item.label}</div>`;
  }
}
```

Project Selector:

```ts
class ProjectSelector extends LitElement {
  @property({ type: Object }) selectedProject: Project | null = null;

  render() {
    return html`<bds-nested-menu
      @item-selected=${this._handleItemSelected}
      .data=${this.data}
    >
    </bds-nested-menu>`;
  }
}

data = [
  {
    label: "All Projects",
    value: "all",
    renderer: () =>
      html`<div @click=${this._handleAllProjectsClick}>All Projects</div>`,
  },
  {
    label: "Project 1",
    value: "project1",
    shouldOpen: () => this.selectedProject?.projectId === "project1",
    children: [],
  },
];
```

Editor Task Manager:

```ts
class EditorTaskManager extends LitElement {
  @property({ type: Array }) tasks: Task[] = [];

  render() {
    return html`<bds-nested-menu .data=${this.data}>
      <template>
        <div>Task Name</div>
      </template>
    </bds-nested-menu>`;
  }
}
```
