import { LitElement, html, css } from "lit-element";
import { storeInstance } from "store/index.js";
// import * as wexlib from "lib/wexlib.js";

import "../../components/base/col-item.ts";
import "../../components/base/row-item.ts";
import "../../components/base/fixed-item.ts";
import "../../components/base/icon.ts";
import "../../components/base/multi-combobox.ts";

class WexPublishDefinitionsFilterBar extends LitElement {
  static get properties() {
    return {
      pubDefs: { type: Array },
      projects: { type: Array },
      categories: { type: Array },
    };
  }

  static get styles() {
    return css`
      :host {
        flex: 1;
      }
    `;
  }

  constructor() {
    super();
    this._state = null;
    this._string = null;

    this.pubDefs = null;

    this.categories = null;
    this.projects = null;

    this.filterOptions = null;
    this._cachedFilterOptions = null;
    this.activeFilters = {};
  }

  connectedCallback() {
    super.connectedCallback();
    this._state = storeInstance.state;
    this._string = this._state[this._state.langCode];
    this.subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  stateChange(state) {
    this._state = state;
    this._string = state[state.langCode];
  }

  async _init() {
    try {
      this.projects = await storeInstance.waitFor(
        "_state.pages.publish.projects"
      );
      this.categories = await storeInstance.waitFor(
        "_state.pages.publish.categories"
      );
      this._generateFilterOptions();
      // console.log("this.categories", this.categories);
      // console.log("this.projects", this.projects);
    } catch (error) {
      console.error("Error during initialization:", error);
    }
  }

  _generateFilterOptions() {
    // console.log("generating filter options", this.pubDefs);
    const filterOptions = {
      category: {
        placeholder: "Category",
        // mcbItems: ["asdf", "qwer"],
        mcbItems: this.categories.map((category) => category.name),
      },
      projects: {
        placeholder: "Project Name",
        // mcbItems: ["rgewrger", "ergwergre"],
        mcbItems: this.projects.map((project) => project.name),
      },
    };
    // console.log("filterOptions", filterOptions);
    // const keysToInclude = [
    //   { key: "category", label: "Category" },
    //   { key: "projectName", label: "Project Name" },
    // ];

    // filterOptions["category"].placeholder = "Category";
    // filterOptions["projectName"].placeholder = "Project Name";

    // filterOptions.category.

    // keysToInclude.forEach(({ key, label }) => {
    //   const values = [...new Set(this.pubDefs.map((row) => row[key]))];
    //   filterOptions[key] = {
    //     placeholder: label,
    //     mcbItems: values,
    //   };
    // });

    // keysToInclude.forEach(({ key, label }) => {
    //   const values = [...new Set(this.categories.map((row) => row[key]))];
    //   filterOptions[key] = {
    //     placeholder: label,
    //     mcbItems: values,
    //   };
    // });

    this.filterOptions = filterOptions;
  }

  _createPubDef() {
    console.log("click", this.projects);
    storeInstance.dispatch("setState", {
      property: "publish_defs_create_dialog_open",
      value: this.projects,
    });
  }

  _handleFilterChange = (e) => {
    const { selectedItems, comboBoxGroup } = e.detail;

    let activeFilters = {};

    // this works for separate comboboxes
    if (comboBoxGroup) {
      activeFilters = { ...this.activeFilters };
      activeFilters[comboBoxGroup] = selectedItems.map((item) => item.text);
    } else {
      // this works for grouped comboboxes
      // activeFilters = {};
      activeFilters = selectedItems.reduce((acc, item) => {
        const group = item.dataset.group;
        const text = item.text;

        if (!acc[group]) acc[group] = [];
        acc[group].push(text);
        return acc;
      }, {});
    }

    this.activeFilters = activeFilters;
    this.dispatchEvent(
      new CustomEvent("set-active-filters", {
        detail: { activeFilters },
        bubbles: true,
        composed: true,
      })
    );
  };

  _handleCategorySelect = (e) => {
    console.log("NYI: category select needs setup and testing", e);
  };

  render() {
    return html`
      <wex-row-item>
        <wex-fixed-item>
          <wex-icon
            icon="add"
            title="${this._string["_create_pubdef"]}"
            @click="${this._createPubDef.bind(this)}"
          ></wex-icon>
        </wex-fixed-item>
        <wex-col-item>
          <wex-multi-combobox
            .mcbData="${this.filterOptions}"
            @selection-change="${this._handleCategorySelect}"
            dataGroup="category"
            .initialSelectedItems=${this.activeFilters}
          >
          </wex-multi-combobox>
        </wex-col-item>
        <wex-col-item>
          <wex-multi-combobox
            .mcbData="${this.filterOptions}"
            @selection-change="${this._handleFilterChange}"
            dataGroup="projects"
            .initialSelectedItems=${this.activeFilters}
          >
          </wex-multi-combobox>
        </wex-col-item>
      </wex-row-item>
    `;
  }
}

customElements.define(
  "wex-publish-definitions-filter-bar",
  WexPublishDefinitionsFilterBar
);
