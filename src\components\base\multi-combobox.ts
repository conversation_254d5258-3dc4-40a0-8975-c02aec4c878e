import { LitElement, html, css, PropertyValues } from "lit-element";
import { customElement, property, state } from "lit/decorators.js";

import "@ui5/webcomponents/dist/MultiComboBox.js";
import "@ui5/webcomponents/dist/MultiComboBoxItem.js";
import "@ui5/webcomponents/dist/MultiComboBoxItemGroup.js";

/**
 * MultiComboBox component using UI5 Web Components.
 *
 * comboBoxGroup is either the group target, or a single MCB
 *
 */

interface McbData {
  [mcbTargetGroup: string]: McbGroupData;
}

interface McbGroupData {
  placeholder: string;
  mcbItems: string[];
}

interface SelectionChangeDetail {
  selectedItems: string[];
  comboBoxGroup: string;
}

@customElement("wex-multi-combobox")
export class WexMultiComboBox extends LitElement {
  @property({ type: Object }) mcbData: McbData | null = null;
  @property({ type: Array }) mcbItems: string[] = [];
  @property({ type: Object }) initialSelectedItems: McbGroupData | null = null;
  @property({ type: String }) dataGroup: string | null = null;
  @property({ type: String }) placeholder: string = "Select...";

  @state() private _selectedMcbItems: string[] = [];

  static styles = css`
    ui5-multi-combobox {
      width: 100%;
    }
  `;

  connectedCallback() {
    super.connectedCallback();
    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
  }

  updated(changedProperties: PropertyValues) {
    if (changedProperties.has("mcbData") && this.mcbData && this.dataGroup) {
      const { mcbItems, placeholder } = this.mcbData[this.dataGroup];
      this.mcbItems = mcbItems;
      this.placeholder = placeholder;
    }
  }

  _init() {
    if (this.initialSelectedItems) {
      this._selectedMcbItems = Object.values(this.initialSelectedItems).flat();
    }
  }

  _handleSelectionChange(e: CustomEvent) {
    const comboBox = e.target as HTMLElement & {
      items: { selected: boolean; text: string }[];
    };
    const selectedItems = comboBox.items
      .filter((item) => item.selected)
      .map((item) => item.text);

    console.log("_handleSelectionChange: selectedItems", selectedItems);

    this.dispatchEvent(
      new CustomEvent<SelectionChangeDetail>("selection-change", {
        detail: { selectedItems, comboBoxGroup: this.dataGroup || "" },
        bubbles: true,
        composed: true,
      })
    );

    this._selectedMcbItems = selectedItems;
    console.log(
      "_handleSelectionChange: this._selectedMcbItems",
      this._selectedMcbItems
    );
  }

  _renderItems() {
    return this.mcbItems?.map((item) => {
      return html`
        <ui5-mcb-item
          text="${item}"
          data-group="${this.dataGroup || ""}"
          ?selected=${this._selectedMcbItems.includes(item)}
        >
        </ui5-mcb-item>
      `;
    });
  }

  _renderGroupedItems() {
    if (!this.mcbData) return null;
    return Object.entries(this.mcbData).map(
      ([groupName, group]) => html`
        <ui5-mcb-group-item text="${group.placeholder}"></ui5-mcb-group-item>
        ${group.mcbItems.map(
          (item) => html`
            <ui5-mcb-item
              text="${item}"
              data-group="${groupName}"
              ?selected=${this._selectedMcbItems.includes(item)}
            ></ui5-mcb-item>
          `
        )}
      `
    );
  }

  render() {
    return html`
      <ui5-multi-combobox
        placeholder=${this.placeholder}
        data-group=${this.dataGroup || ""}
        @selection-change=${this._handleSelectionChange}
      >
        ${this.dataGroup ? this._renderItems() : this._renderGroupedItems()}
      </ui5-multi-combobox>
    `;
  }
  // additional-text="${item.additionalText}"
}
