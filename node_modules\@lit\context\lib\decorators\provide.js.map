{"version": 3, "file": "provide.js", "sources": ["../../src/lib/decorators/provide.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ReactiveElement} from '@lit/reactive-element';\nimport {Context} from '../create-context.js';\nimport {ContextProvider} from '../controllers/context-provider.js';\n\n/*\n * IMPORTANT: For compatibility with t<PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\n/**\n * A property decorator that adds a ContextProvider controller to the component\n * making it respond to any `context-request` events from its children consumer.\n *\n * @param context A Context identifier value created via `createContext`\n *\n * @example\n *\n * ```ts\n * import {provide} from '@lit/context';\n * import {Logger} from 'my-logging-library';\n * import {loggerContext} from './logger-context.js';\n *\n * class MyElement {\n *   @provide({context: loggerContext})\n *   logger = new Logger();\n * }\n * ```\n * @category Decorator\n */\nexport function provide<ValueType>({\n  context: context,\n}: {\n  context: Context<unknown, ValueType>;\n}): ProvideDecorator<ValueType> {\n  return ((\n    protoOrTarget: ClassAccessorDecoratorTarget<ReactiveElement, ValueType>,\n    nameOrContext:\n      | PropertyKey\n      | ClassAccessorDecoratorContext<ReactiveElement, ValueType>\n  ) => {\n    // Map of instances to controllers\n    const controllerMap = new WeakMap<\n      ReactiveElement,\n      ContextProvider<Context<unknown, ValueType>>\n    >();\n    if (typeof nameOrContext === 'object') {\n      // Standard decorators branch\n      return {\n        get(this: ReactiveElement) {\n          return protoOrTarget.get.call(this);\n        },\n        set(this: ReactiveElement, value: ValueType) {\n          controllerMap.get(this)!.setValue(value);\n          return protoOrTarget.set.call(this, value);\n        },\n        init(this: ReactiveElement, value: ValueType) {\n          controllerMap.set(\n            this,\n            new ContextProvider(this, {context, initialValue: value})\n          );\n          return value;\n        },\n      };\n    } else {\n      // Experimental decorators branch\n      (protoOrTarget.constructor as typeof ReactiveElement).addInitializer(\n        (element: ReactiveElement): void => {\n          controllerMap.set(element, new ContextProvider(element, {context}));\n        }\n      );\n      // proxy any existing setter for this property and use it to\n      // notify the controller of an updated value\n      const descriptor = Object.getOwnPropertyDescriptor(\n        protoOrTarget,\n        nameOrContext\n      );\n      let newDescriptor: PropertyDescriptor;\n      if (descriptor === undefined) {\n        const valueMap = new WeakMap<ReactiveElement, ValueType>();\n        newDescriptor = {\n          get(this: ReactiveElement) {\n            return valueMap.get(this);\n          },\n          set(this: ReactiveElement, value: ValueType) {\n            controllerMap.get(this)!.setValue(value);\n            valueMap.set(this, value);\n          },\n          configurable: true,\n          enumerable: true,\n        };\n      } else {\n        const oldSetter = descriptor.set;\n        newDescriptor = {\n          ...descriptor,\n          set(this: ReactiveElement, value: ValueType) {\n            controllerMap.get(this)!.setValue(value);\n            oldSetter?.call(this, value);\n          },\n        };\n      }\n      Object.defineProperty(protoOrTarget, nameOrContext, newDescriptor);\n      return;\n    }\n  }) as ProvideDecorator<ValueType>;\n}\n\n/**\n * Generates a public interface type that removes private and protected fields.\n * This allows accepting otherwise compatible versions of the type (e.g. from\n * multiple copies of the same package in `node_modules`).\n */\ntype Interface<T> = {\n  [K in keyof T]: T[K];\n};\n\ntype ProvideDecorator<ContextType> = {\n  // legacy\n  <\n    K extends PropertyKey,\n    Proto extends Interface<Omit<ReactiveElement, 'renderRoot'>>,\n  >(\n    protoOrDescriptor: Proto,\n    name?: K\n  ): FieldMustMatchContextType<Proto, K, ContextType>;\n\n  // standard\n  <\n    C extends Interface<Omit<ReactiveElement, 'renderRoot'>>,\n    V extends ContextType,\n  >(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): void;\n};\n\n// Note TypeScript requires the return type of a decorator to be `void | any`\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype DecoratorReturn = void | any;\n\ntype FieldMustMatchContextType<Obj, Key extends PropertyKey, ContextType> =\n  // First we check whether the object has the property as a required field\n  Obj extends Record<Key, infer ProvidingType>\n    ? // Ok, it does, just check whether it's ok to assign the\n      // provided type to the consuming field\n      [ProvidingType] extends [ContextType]\n      ? DecoratorReturn\n      : {\n          message: 'providing field not assignable to context';\n          context: ContextType;\n          provided: ProvidingType;\n        }\n    : // Next we check whether the object has the property as an optional field\n      Obj extends Partial<Record<Key, infer Providing>>\n      ? // Check assignability again. Note that we have to include undefined\n        // here on the providing type because it's optional.\n        [Providing | undefined] extends [ContextType]\n        ? DecoratorReturn\n        : {\n            message: 'providing field not assignable to context';\n            context: ContextType;\n            consuming: Providing | undefined;\n          }\n      : // Ok, the field isn't present, so either someone's using provide\n        // manually, i.e. not as a decorator (maybe don't do that! but if you do,\n        // you're on your own for your type checking, sorry), or the field is\n        // private, in which case we can't check it.\n        DecoratorReturn;\n"], "names": ["provide", "context", "protoOrTarget", "nameOrContext", "controllerMap", "WeakMap", "get", "call", "this", "set", "value", "setValue", "init", "ContextProvider", "initialValue", "constructor", "addInitializer", "element", "descriptor", "Object", "getOwnPropertyDescriptor", "newDescriptor", "undefined", "valueMap", "configurable", "enumerable", "oldSetter", "defineProperty"], "mappings": ";;;;;YAqCgBA,GACdC,QAASA,IAIT,MAAQ,CACNC,EACAC,KAKA,MAAMC,EAAgB,IAAIC,QAI1B,GAA6B,iBAAlBF,EAET,MAAO,CACL,GAAAG,GACE,OAAOJ,EAAcI,IAAIC,KAAKC,KAC/B,EACD,GAAAC,CAA2BC,GAEzB,OADAN,EAAcE,IAAIE,MAAOG,SAASD,GAC3BR,EAAcO,IAAIF,KAAKC,KAAME,EACrC,EACD,IAAAE,CAA4BF,GAK1B,OAJAN,EAAcK,IACZD,KACA,IAAIK,EAAgBL,KAAM,CAACP,UAASa,aAAcJ,KAE7CA,CACR,GAEE,CAEJR,EAAca,YAAuCC,gBACnDC,IACCb,EAAcK,IAAIQ,EAAS,IAAIJ,EAAgBI,EAAS,CAAChB,YAAU,IAKvE,MAAMiB,EAAaC,OAAOC,yBACxBlB,EACAC,GAEF,IAAIkB,EACJ,QAAmBC,IAAfJ,EAA0B,CAC5B,MAAMK,EAAW,IAAIlB,QACrBgB,EAAgB,CACd,GAAAf,GACE,OAAOiB,EAASjB,IAAIE,KACrB,EACD,GAAAC,CAA2BC,GACzBN,EAAcE,IAAIE,MAAOG,SAASD,GAClCa,EAASd,IAAID,KAAME,EACpB,EACDc,cAAc,EACdC,YAAY,EAEf,KAAM,CACL,MAAMC,EAAYR,EAAWT,IAC7BY,EAAgB,IACXH,EACH,GAAAT,CAA2BC,GACzBN,EAAcE,IAAIE,MAAOG,SAASD,GAClCgB,GAAWnB,KAAKC,KAAME,EACvB,EAEJ,CAED,YADAS,OAAOQ,eAAezB,EAAeC,EAAekB,EAErD,CACF,CACH"}