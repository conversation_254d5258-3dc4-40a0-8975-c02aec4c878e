var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import PubSub from "pubsub-js";
import { LitElement, html, css } from "lit";
import "@ui5/webcomponents/dist/Toast.js";
import { customElement, property } from "lit/decorators";
let WexToast = class WexToast extends LitElement {
    constructor() {
        super(...arguments);
        this.type = "";
        this.message = "";
        this.duration = 0;
        this.toast = null;
    }
    firstUpdated() {
        this.toast = this.shadowRoot.querySelector("ui5-toast");
    }
    connectedCallback() {
        super.connectedCallback();
        PubSub.subscribe("notifyUser", this._notifyUser.bind(this));
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        PubSub.unsubscribe("notifyUser", this._notifyUser.bind(this));
    }
    _notifyUser(label, params) {
        if (!this.toast)
            return;
        this.type = params.type.toLowerCase();
        this.message = params.message;
        this.duration = params.duration * 1000;
        this.toast.open = true;
    }
    render() {
        return html `
      <ui5-toast class="fit-bottom ${this.type}" duration="${this.duration}">
        ${this.message}
      </ui5-toast>
    `;
    }
    static get styles() {
        return css `
      .positive {
        --_ui5-v2-11-0_toast_background: green;
        --sapContent_ContrastTextColor: white;
      }
      .negative {
        --_ui5-v2-11-0_toast_background: red;
        --sapContent_ContrastTextColor: white;
      }
      .info {
        --_ui5-v2-11-0_toast_background: blue;
        --sapContent_ContrastTextColor: yellow;
      }
      .warn {
        --_ui5-v2-11-0_toast_background: #FFC95C;
        --sapContent_ContrastTextColor: black;
      }
    `;
    }
};
__decorate([
    property({ type: String })
], WexToast.prototype, "type", void 0);
__decorate([
    property({ type: String })
], WexToast.prototype, "message", void 0);
__decorate([
    property({ type: Number })
], WexToast.prototype, "duration", void 0);
WexToast = __decorate([
    customElement("wex-toast")
], WexToast);
//# sourceMappingURL=wex-toast.js.map