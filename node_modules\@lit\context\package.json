{"name": "@lit/context", "version": "1.1.6", "description": "Helpers and controllers for using Context protocol", "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://lit.dev/", "repository": {"type": "git", "url": "git+https://github.com/lit/lit.git", "directory": "packages/context"}, "type": "module", "main": "index.js", "module": "index.js", "typings": "index.d.ts", "directories": {"test": "test"}, "exports": {".": {"types": "./development/index.d.ts", "development": "./development/index.js", "default": "./index.js"}}, "files": ["/src/", "!/src/test/", "/lib/", "/development/", "!/development/test/", "/index.{d.ts,d.ts.map,js,js.map}"], "scripts": {"build": "wireit", "build:ts": "wireit", "build:ts:std-decorators-tests": "wireit", "build:ts:lit2-tests": "wireit", "build:ts:lit2": "wireit", "build:ts:types": "wireit", "build:rollup": "wireit", "test": "wireit", "test:dev": "wireit", "test:prod": "wireit", "checksize": "wireit"}, "wireit": {"build": {"dependencies": ["build:rollup", "build:ts", "build:ts:std-decorators-tests", "build:ts:types", "../lit:build", "../reactive-element:build", "../labs/testing:build"]}, "build:ts": {"command": "tsc --build --pretty", "dependencies": ["../lit:build:ts:types", "../reactive-element:build:ts:types"], "clean": "if-file-deleted", "files": ["tsconfig.json", "src/**/*.ts", "!src/test/std-decorators"], "output": ["development", "!development/test/std-decorators", "tsconfig.tsbuildinfo"]}, "build:ts:std-decorators-tests": {"#comment": "This is a separate script from build:ts because it needs a tsconfig without experimentalDecorators.", "command": "tsc --pretty --project tsconfig.std-decorators-tests.json", "clean": "if-file-deleted", "dependencies": ["build:ts"], "files": ["src/test/std-decorators/**/*.ts", "tsconfig.std-decorators-tests.json"], "output": ["development/test/std-decorators", "tsconfig.std-decorators-tests.tsbuildinfo"]}, "build:ts:lit2-tests": {"#comment": "Run this to install and build additional cross-version type tests.", "command": "tsc --pretty --project tsconfig.lit2-tests.json", "clean": "if-file-deleted", "dependencies": ["build:ts:lit2", "install:lit2"], "files": ["src/test/**/*.ts", "!src/test/std-decorators/**/*.ts", "tsconfig.lit2-tests.json"], "output": ["lit2/context/test", "tsconfig.lit2-tests.tsbuildinfo"]}, "install:lit2": {"command": "cd lit2/lit2-scope && npm ci", "clean": "if-file-deleted", "dependencies": [], "files": ["lit2/lit2-scope/package.json", "lit2/lit2-scope/package-lock.json"], "output": []}, "build:ts:lit2": {"#comment": "This is a separate script from build:ts because it needs a tsconfig that maps in lit@2.", "command": "tsc --pretty --project tsconfig.lit2.json", "clean": "if-file-deleted", "dependencies": ["build:ts"], "files": ["src/**/*.ts", "!src/test/**/*.ts", "tsconfig.lit2.json"], "output": ["lit2/lit2-scope/context", "lit2/lit2-scope/tsconfig.lit2.tsbuildinfo"]}, "build:ts:types": {"command": "treemirror development . \"**/*.d.ts{,.map}\" \"!test\"", "dependencies": ["../internal-scripts:build", "build:ts"], "files": [], "output": ["*.d.ts{,.map}", "lib/**/*.d.ts{,.map}"]}, "build:rollup": {"command": "rollup -c", "dependencies": ["build:ts"], "files": ["rollup.config.js", "../../../rollup-common.js"], "output": ["index.js{,.map}", "lib/**/*.js{,.map}"]}, "checksize": {"command": "rollup -c --environment=CHECKSIZE", "dependencies": ["build:ts"], "files": ["rollup.config.js", "../../../rollup-common.js"], "output": []}, "test": {"dependencies": ["test:dev", "test:prod"]}, "test:dev": {"command": "MODE=dev node ../tests/run-web-tests.js \"development/**/*_test.js\" --config ../tests/web-test-runner.config.js", "dependencies": ["build:ts", "build:ts:std-decorators-tests", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}, "test:prod": {"command": "MODE=prod node ../tests/run-web-tests.js \"development/**/*_test.js\" --config ../tests/web-test-runner.config.js", "dependencies": ["build", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}}, "author": "Google LLC", "dependencies": {"@lit/reactive-element": "^1.6.2 || ^2.1.0"}, "devDependencies": {"chai": "^5.1.0", "@lit-internal/scripts": "^1.0.1", "@lit-labs/testing": "^0.2.2", "@types/trusted-types": "^2.0.2", "lit": "^3.3.0"}, "publishConfig": {"access": "public"}}