export class DitamapNode {
    constructor() {
        this.id = null;
        this.type = "root";
        this.href = null;
        this.title = null;
        this.containingMap = null;
        this.mapPath = [];
        this.embeddedElement = null;
        this.children = [];
    }
    /** Tag name at the root of the XML element */
    get tagName() {
        return this.xmlElement.tagName;
    }
}
//# sourceMappingURL=ditamap-node.js.map