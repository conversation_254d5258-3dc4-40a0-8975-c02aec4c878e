export class DitamapNode {
    constructor() {
        this.id = null;
        this.type = "root";
        this.href = null;
        this.title = null;
        this.mapName = null;
        this.mapPath = [];
        this.embeddedElement = null;
        this.children = [];
    }
    /** Tag name at the root of the XML element */
    get tagName() {
        return this.domElement.tagName;
    }
}
//# sourceMappingURL=ditamap-node.js.map