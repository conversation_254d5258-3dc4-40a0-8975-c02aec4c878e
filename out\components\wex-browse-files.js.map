{"version": 3, "file": "wex-browse-files.js", "sourceRoot": "", "sources": ["../../src/components/wex-browse-files.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AACtC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAC3C,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,MAAM,MAAM,WAAW,CAAC;AAC/B,OAAO,KAAK,IAAI,MAAM,aAAa,CAAC;AAGpC,OAAO,iCAAiC,CAAC;AACzC,4CAA4C;AAC5C,gDAAgD;AAEhD,MAAM,cAAe,SAAQ,UAAU;IACrC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoGT,CAAC;IACJ,CAAC;IAED;QACE,KAAK,EAAE,CAAC;QACR,EAAE;QACF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,EAAE;QACF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,EAAE;QACF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YACpD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,qBAAqB;IACvB,CAAC;IAED,oBAAoB;QAClB,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,WAAW;QACT,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB;YAC7C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ;YACxC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB;YACjD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,IAAI;YACtC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC;QAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC;gBACjE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,YAAY,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC;oBAC9C,IAAI,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;oBAClE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACxC,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;gBAC1C,CAAC;YACH,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,UAAU;YACb,oHAAoH,CAAC;QAEvH,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,MAAM,GAAG,GAAG,CAAC;QACjB,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;YACtC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI;gBAC7C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI;gBACxC,CAAC,CAAC,GAAG,CAAC;QACV,CAAC;QAED,IAAI,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB;YACvD,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,GAAG;YAC/D,CAAC,CAAC,EAAE,CAAC;QAEP,YAAY;YACV,mBAAmB;gBACnB,MAAM;gBACN,kDAAkD;gBAClD,iBAAiB,CAAC;QAEpB,WAAW,CAAC,IAAI,CACd,+CAA+C;YAC7C,IAAI,CAAC,UAAU;YACf,gBAAgB;YAChB,IAAI,CAAC,YAAY;YACjB,cAAc;YACd,IAAI,CAAC,QAAQ;YACb,sCAAsC;YACtC,YAAY;YACZ,UAAU;YACV,IAAI,CAAC,UAAU,CAClB,CAAC;QACF,kEAAkE;QAElE,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,UAAU,EAAE,cAAc;YAC1B,OAAO,EAAE,CAAC;SACX,CAAC,CAAC;IACL,CAAC;IAED,QAAQ;QACN,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IACnE,CAAC;IAED,WAAW,CAAC,KAAK;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAE9C,8BAA8B;QAC9B,0DAA0D;QAC1D,2BAA2B;QAC3B,qDAAqD;QACrD,IAAI;QACJ,4EAA4E;QAC5E,2BAA2B;QAC3B,uEAAuE;QACvE,IAAI;QAEJ,wBAAwB;QACxB,yCAAyC;QACzC,gCAAgC;QAChC,mBAAmB;QACnB,QAAQ;QACR,wBAAwB;QACxB,IAAI;QAEJ,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;YACpC,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,KAAK;aACb,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YAC5B,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAED,gBAAgB;QACd,gCAAgC;QAChC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAC7C,UAAU,EAAE;YACV,OAAO,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;QACtB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;QACF,sBAAsB;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,CAAC;YACX,CAAC;YACD,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;QACH,UAAU;QACV,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,aAAa;QACX,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YAC5B,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC;gBAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;oBACpC,IACE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ,EACjE,CAAC;wBACD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,eAAe,CAAC;oBAC1C,CAAC;gBACH,CAAC;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,WAAW,CAAC;gBACtC,CAAC;YACH,CAAC;YACD,sBAAsB;QACxB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CACnE,CAAC;IACJ,CAAC;IAED,eAAe;IACf,aAAa;QACX,OAAO,IAAI,CAAA;;;;;;4BAMa,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;4BAIvC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;6BACtB,IAAI,CAAC,IAAI,CAAC,MAAM;;;cAG/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAChB,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAA;;8BAEG,GAAG,CAAC,OAAO;;;0BAGf,GAAG,CAAC,OAAO;;;;;sCAKC,GAAG,CAAC,SAAS;;gCAEnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;qCAEtB,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;;;;;sCAM/B,GAAG,CAAC,SAAS;;gCAEnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;sCAErB,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;;;;;kCAMrC,CACrB;;;;4BAIe,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;;;0BAIzB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;;;;cAKnC,IAAI,CAAC,IAAI,CAAC,GAAG,CACb,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAA;0CACc,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;yBAC/C,IAAI;6BACA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;0BAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;gCACtB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;;kBAE/C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;;;+BAI1C,IAAI;;+BAEJ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;+BACvB,IAAI,CAAC,QAAQ;;;;;;+BAMb,IAAI;;+BAEJ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;+BACpB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACjD,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,MAAM;;;;;;;+BAOD,CAClB;;;;4BAIe,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;KAIxD,CAAC;IACJ,CAAC;IAED,YAAY,CAAC,GAAG,EAAE,IAAI;QACpB,uHAAuH;QACvH,IAAI,GAAG,CAAC,SAAS,IAAI,UAAU,EAAE,CAAC;YAChC,OAAO,IAAI,CAAA;;;sBAGK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;qBAChC,IAAI;oBACL,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,oBAAoB;;yBAEjD,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,CAAC,SAAS,IAAI,MAAM,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAA;;;;;iCAKc,IAAI,CAAC,SAAS;uBACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;6BACrB,IAAI,CAAC,MAAM;;oBAEpB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;;0BAEb,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAA;;mBAEA,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;;SAE7B,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,WAAW,CAAC,CAAC;QACX,IAAI,SAAS,GAAG,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC;QACtC,IAAI,CAAC,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YAChC,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAC9B,CAAC,CAAC,aAAa,CAAC,WAAW,EAC3B,SAAS,EACT,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAC3C,CAAC;YACF,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,eAAe,CAAC,CAAC;QACf,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;QAClC,aAAa,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,uDAAuD;IACvD,+BAA+B;IAC/B,YAAY,CAAC,CAAC;QACZ,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC;QAC7C,mCAAmC;QACnC,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;QAC9B,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QACtE,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACnB,QAAQ,IAAI,CAAC,EAAE,EAAE,CAAC;gBAChB,KAAK,SAAS;oBACZ,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBACvB,MAAM;gBACR,KAAK,YAAY;oBACf,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;wBACzB,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;4BACpC,IACE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;gCACxC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAC7B,CAAC;gCACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oCACjC,QAAQ,EAAE,sBAAsB;oCAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;iCAC5B,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gCACjC,QAAQ,EAAE,sBAAsB;gCAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;6BAC5B,CAAC,CAAC;wBACL,CAAC;wBACD,aAAa,CAAC,QAAQ,CACpB,uBAAuB,EACvB,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAChC,CAAC;wBACF,MAAM;oBACR,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;oBACpC,IACE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;wBACxC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAC7B,CAAC;wBACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;4BACjC,QAAQ,EAAE,sBAAsB;4BAChC,KAAK,EAAE,IAAI;yBACZ,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;gBACH,CAAC;gBACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,sBAAsB;oBAChC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,sCAAsC;IACtC,iBAAiB,CAAC,CAAC;QACjB,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,IAAI,IAAI,CAAC;QAC1C,wCAAwC;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAC1D,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAChC,IAAI,EACJ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAC1B,QAAQ,EACR,YAAY,CACb,CAAC;QACF,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE/D,oDAAoD;QACpD,oCAAoC;QACpC,MAAM,QAAQ,GACZ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;QACtE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC;YACxD,IAAI,GAAG,IAAI,CAAC;gBAAE,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC;QAEnC,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;gBACpC,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAC9D,OAAO;gBACT,CAAC;YACH,CAAC;YACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,qDAAqD;IACrD,uBAAuB,CAAC,CAAC;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC;QAC7C,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC5B,KAAK,YAAY,CAAC;YAClB,KAAK,MAAM,CAAC;YACZ,KAAK,cAAc,CAAC;YACpB,KAAK,cAAc,CAAC;YACpB,KAAK,SAAS,CAAC;YACf,KAAK,kBAAkB;gBACrB,aAAa,CAAC,QAAQ,CAAC,kBAAkB,EAAE;oBACzC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;oBAC3B,IAAI,EAAE,IAAI;iBACX,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,SAAS;gBACZ,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACvB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,4BAA4B;oBACtC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;iBACvC,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,cAAc;gBACjB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,0BAA0B;oBACpC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;iBACvC,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB;oBACjC,MAAM,CAAC,OAAO,CAAC,gCAAgC,EAAE;wBAC/C,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;wBAChD,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,IAAI;wBAC9C,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;qBACnD,CAAC,CAAC;gBACL,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;oBACnC,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACjC,QAAQ,EAAE,qBAAqB;wBAC/B,KAAK,EAAE,IAAI;qBACZ,CAAC,CAAC;gBACL,CAAC;gBACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,oBAAoB;oBAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;iBACvC,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;oBAClC,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACjC,QAAQ,EAAE,oBAAoB;wBAC9B,KAAK,EAAE,IAAI;qBACZ,CAAC,CAAC;gBACL,CAAC;gBACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,qBAAqB;oBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;iBACvC,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,MAAM,EAAE,MAAM,CAAC;gBACnB,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;oBAClC,MAAM,GAAG,KAAK,CAAC;oBACf,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC;gBAClD,CAAC;qBAAM,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;oBAC1C,MAAM,GAAG,MAAM,CAAC;oBAChB,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CAAC;gBACnD,CAAC;gBACD,aAAa,CAAC,QAAQ,CAAC,iBAAiB,EAAE;oBACxC,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE;iBAC/C,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,QAAQ;gBACX,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,oBAAoB;oBACjC,MAAM,CAAC,OAAO,CAAC,gCAAgC,EAAE;wBAC/C,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;wBACrC,QAAQ,EAAE,SAAS;qBACpB,CAAC,CAAC;gBACL,MAAM;QACV,CAAC;IACH,CAAC;IAED,YAAY;QACV,MAAM,CAAC,EAAE,CACP,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,QAAQ;YACnB,oBAAoB;YACpB,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;YACxC,OAAO;YACP,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,aAAa;YAC7C,oBAAoB,CACvB,CAAC;IACJ,CAAC;IAED,WAAW,CAAC,IAAI;QACd,aAAa,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;CACF;AAED,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { LitElement, css } from \"lit\";\r\nimport { html, render } from \"lit/html.js\";\r\nimport { storeInstance } from \"store/index.js\";\r\nimport { Router } from \"@vaadin/router\";\r\nimport pubsub from \"pubsub-js\";\r\nimport * as util from \"lib/util.ts\";\r\nimport * as wexlib from \"lib/wexlib.js\";\r\n\r\nimport \"@ui5/webcomponents/dist/Icon.js\";\r\n// import \"@ui5/webcomponents/dist/Tree.js\";\r\n// import \"@ui5/webcomponents/dist/TreeItem.js\";\r\n\r\nclass WexBrowseFiles extends LitElement {\r\n  static get styles() {\r\n    return css`\r\n      .file-icon[data-status=\"_locked_by_other\"] {\r\n        color: red;\r\n      }\r\n      .file-icon[data-status=\"_room_locked_by_user\"],\r\n      .file-icon[data-status=\"_sesh_locked_by_user\"] {\r\n        color: green;\r\n      }\r\n      .file-icon[data-status=\"_xdocs_locked_by_user\"] {\r\n        color: orange;\r\n      }\r\n      .file-icon[data-status=\"_locked_concurrent\"] {\r\n        color: blue;\r\n      }\r\n\r\n      .colhead {\r\n        cursor: pointer;\r\n      }\r\n      .rowselected {\r\n        --sapList_Background: var(--row-selected-background);\r\n      }\r\n      #notasks {\r\n        font-style: oblique;\r\n        opacity: 0.5;\r\n        text-align: center;\r\n        margin-top: 50px;\r\n      }\r\n      .datarow {\r\n        cursor: pointer;\r\n      }\r\n      .ditamap {\r\n        font-weight: bold;\r\n      }\r\n      .hide {\r\n        visibility: hidden;\r\n      }\r\n      .sortbuttoncontainer {\r\n        display: flex;\r\n        flex-direction: column;\r\n      }\r\n      .sortbuttoncontainer iron-icon {\r\n        margin-left: 0.25rem;\r\n        --iron-icon-width: 0.9rem;\r\n      }\r\n      iron-icon.asc {\r\n        margin-bottom: -0.6rem;\r\n      }\r\n      iron-icon.desc {\r\n        margin-top: -0.6rem;\r\n      }\r\n      iron-icon.muted {\r\n        color: #ccc;\r\n      }\r\n\r\n      .colhead {\r\n        line-height: 1.4rem;\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n      }\r\n\r\n      .iconcontatiner {\r\n        height: 1.4rem;\r\n        overflow: hidden;\r\n      }\r\n\r\n      .file-icon {\r\n        width: 1rem;\r\n        opacity: 0.7;\r\n      }\r\n      .file-icon:hover {\r\n        opacity: 1;\r\n      }\r\n\r\n      .rowicons iron-icon {\r\n        width: 1rem;\r\n        padding-right: 30px;\r\n        display: none;\r\n      }\r\n      .rowicons iron-icon:hover {\r\n        color: #000;\r\n      }\r\n\r\n      .rowicons iron-icon.rowselected {\r\n        display: inline-block;\r\n      }\r\n\r\n      #wex-browse-files {\r\n        height: 100%;\r\n      }\r\n\r\n      #contextmenu {\r\n        display: flex;\r\n        flex-direction: column;\r\n        height: 100%;\r\n      }\r\n\r\n      .flex-spacer {\r\n        flex-grow: 1;\r\n      }\r\n    `;\r\n  }\r\n\r\n  constructor() {\r\n    super();\r\n    //\r\n    this.state = null;\r\n    this.string = [];\r\n    //\r\n    this.folder = null;\r\n    this.searchterm = null;\r\n    this.subfolders = null;\r\n    //\r\n    this.contextMenu = null;\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this.state = storeInstance.state;\r\n    this.string = this.state[this.state.langCode];\r\n    this.subscription = storeInstance.subscribe((state) => {\r\n      this.stateChange(state);\r\n    });\r\n    //this.refreshData();\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  refreshData() {\r\n    var searchQuery = [];\r\n    this.searchlang = this.state.browse_lang_filter\r\n      ? this.state.browse_lang_filter.langCode\r\n      : this.state.default_lang_object.langCode;\r\n    this.searchbranch = this.state.browse_branch_filter\r\n      ? this.state.browse_branch_filter.name\r\n      : this.state.default_branch_object.name;\r\n    this.asOfDate = this.state.default_asof;\r\n    if (this.state.browse_asof_filter) {\r\n      if (this.state.browse_asof_filter.hasOwnProperty(\"gmtLabelDate\")) {\r\n        this.asOfDate = this.state.browse_asof_filter.gmtLabelDate;\r\n      } else {\r\n        if (Date.parse(this.state.browse_asof_filter)) {\r\n          var tmpDate = new Date(Date.parse(this.state.browse_asof_filter));\r\n          this.asOfDate = tmpDate.toISOString();\r\n        } else {\r\n          this.asOfDate = this.state.default_asof;\r\n        }\r\n      }\r\n    }\r\n\r\n    //XPathValueDB('//title')\r\n    this.fieldNames =\r\n      \" Title, Name, LockOwner,  CreateDate, ActiveProcesses, FileStatus, NumComments, Size, FileId, FolderPath, MimeType\";\r\n\r\n    var folderClause = \"\";\r\n    var folder = \"/\";\r\n    if (this.state.browse_selected_folder) {\r\n      folder = this.state.browse_selected_folder.path\r\n        ? this.state.browse_selected_folder.path\r\n        : \"/\";\r\n    }\r\n\r\n    var addMimeTypeClause = this.state.browse_mimetype_filter\r\n      ? \" AND MimeType = '\" + this.state.browse_mimetype_filter + \"'\"\r\n      : \"\";\r\n\r\n    folderClause =\r\n      \" WHERE InFolder('\" +\r\n      folder +\r\n      \"') AND NOT (InFolderDesc('/Content/KbContent')) \" +\r\n      addMimeTypeClause;\r\n\r\n    searchQuery.push(\r\n      \"VIEW := PropertyView  CONTEXT (LanguageCode='\" +\r\n        this.searchlang +\r\n        \"',BranchName='\" +\r\n        this.searchbranch +\r\n        \"',AsOfDate='\" +\r\n        this.asOfDate +\r\n        \"', HeadVersionOnly='true') FOR EACH \" +\r\n        folderClause +\r\n        \" RETURN \" +\r\n        this.fieldNames\r\n    );\r\n    //storeInstance.dispatch(\"getBrowseFiles\",  searchQuery.join(\"\") )\r\n\r\n    storeInstance.dispatch(\"getFiles\", {\r\n      query: searchQuery.join(\"\"),\r\n      statefield: \"browse_files\",\r\n      maxrows: 0,\r\n    });\r\n  }\r\n\r\n  myRender() {\r\n    render(this.filesTemplate(), this.shadowRoot);\r\n    this.contextMenu = this.shadowRoot.querySelector(\"#contextmenu\");\r\n  }\r\n\r\n  stateChange(state) {\r\n    this.state = state;\r\n    this.string = this.state[this.state.langCode];\r\n\r\n    // var refreshRquired = false;\r\n    // if (this.folder != this.state.browse_selected_folder) {\r\n    //   refreshRquired = true;\r\n    //   this.folder = this.state.browse_selected_folder;\r\n    // }\r\n    // if (this.subfolders != this.state.select_file_dialog_search_subfolders) {\r\n    //   refreshRquired = true;\r\n    //   this.subfolders = this.state.select_file_dialog_search_subfolders;\r\n    // }\r\n\r\n    // if (refreshRquired) {\r\n    //   storeInstance.dispatch(\"setState\", {\r\n    //     property: \"browse_files\",\r\n    //     value: null,\r\n    //   });\r\n    //   this.refreshData();\r\n    // }\r\n\r\n    if (this.state.browse_refresh_files) {\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_refresh_files\",\r\n        value: false,\r\n      });\r\n      this.refreshData();\r\n    }\r\n\r\n    if (this.state.browse_files) {\r\n      this._marshellColumns();\r\n      this._marshellRows();\r\n      this.myRender();\r\n    }\r\n  }\r\n\r\n  _marshellColumns() {\r\n    //filter for active col headings\r\n    this.columns = this.state.browse_columns.filter(\r\n      function (el) {\r\n        return el.order > 0;\r\n      }.bind(this)\r\n    );\r\n    //sort by column order\r\n    this.columns.sort(function (a, b) {\r\n      if (a.order < b.order) {\r\n        return -1;\r\n      }\r\n      if (a.order > b.order) {\r\n        return 1;\r\n      }\r\n      return 0;\r\n    });\r\n    //localize\r\n    this.columns.map((col) => (col.heading = this.string[col.title]));\r\n  }\r\n\r\n  _marshellRows() {\r\n    this.rows = [];\r\n    if (this.state.browse_files) {\r\n      this.rows = structuredClone(this.state.browse_files);\r\n      for (var i = 0; i < this.rows.length; i++) {\r\n        this.rows[i].selected = \"\";\r\n        if (this.state.browse_selected_file) {\r\n          if (\r\n            this.rows[i].resLblId == this.state.browse_selected_file.resLblId\r\n          ) {\r\n            this.rows[i].selected = \" rowselected \";\r\n          }\r\n        }\r\n        if (util.isDitamap(this.rows[i])) {\r\n          this.rows[i].mapClass = \" ditamap \";\r\n        }\r\n      }\r\n      //apply claimed filter\r\n    }\r\n    this.rows.sort(\r\n      util.columnSort(util.getSortColPropertyName(this.columns, \"name\"))\r\n    );\r\n  }\r\n\r\n  /* Templates */\r\n  filesTemplate() {\r\n    return html`\r\n      <div id=\"wex-browse-files\">\r\n        <vaadin-context-menu\r\n          id=\"contextmenu\"\r\n          .items=\"[]\"\r\n          selector=\".has-menu\"\r\n          @item-selected=\"${this._handleContextMenuClick.bind(this)}\"\r\n        >\r\n          <ui5-table\r\n            id=\"filestable\"\r\n            no-data-text=\"${this.string[\"_nofiles\"]}\"\r\n            ?show-no-data=\"${this.rows.length}\"\r\n            sticky-column-header\r\n          >\r\n            ${this.columns.map(\r\n              (col) => html`<ui5-table-column\r\n                slot=\"columns\"\r\n                popin-text=\"${col.heading}\"\r\n              >\r\n                <span class=\"colhead\">\r\n                  <span>${col.heading}</span>\r\n\r\n                  <span class=\"sortbuttoncontainer\">\r\n                    <span\r\n                      class=\"iconcontatiner\"\r\n                      .colproperty=\"${col.fieldname}\"\r\n                      title=\"asc\"\r\n                      @click=\"${this._sortColumn.bind(this)}\"\r\n                      ><iron-icon\r\n                        class=\"asc ${col.sort != \"asc\" ? \"muted\" : \"\"}\"\r\n                        icon=\"vaadin:caret-up\"\r\n                      ></iron-icon\r\n                    ></span>\r\n                    <span\r\n                      class=\"iconcontatiner\"\r\n                      .colproperty=\"${col.fieldname}\"\r\n                      title=\"desc\"\r\n                      @click=\"${this._sortColumn.bind(this)}\"\r\n                      ><iron-icon\r\n                        class=\"desc ${col.sort != \"desc\" ? \"muted\" : \"\"}\"\r\n                        icon=\"vaadin:caret-down\"\r\n                      ></iron-icon\r\n                    ></span>\r\n                  </span>\r\n                </span>\r\n              </ui5-table-column>`\r\n            )}\r\n            <ui5-table-column\r\n              slot=\"columns\"\r\n              min-width=\"800\"\r\n              popin-text=\"${this.string[\"_actions\"]}\"\r\n            >\r\n              <span style=\"line-height: 1.4rem\">\r\n                <span class=\"colhead\">\r\n                  <span>${this.string[\"_actions\"]}</span>\r\n                </span>\r\n              </span>\r\n            </ui5-table-column>\r\n\r\n            ${this.rows.map(\r\n              (item) => html`<ui5-table-row\r\n                class=\"datarow has-menu ${item.mapClass} ${item.selected}\"\r\n                .item=\"${item}\"\r\n                @dblclick=\"${this._handleDblClick.bind(this)}\"\r\n                @click=\"${this._handleClick.bind(this)}\"\r\n                @contextmenu=\"${this._handleRightClick.bind(this)}\"\r\n              >\r\n                ${this.columns.map((col) => this._rowTemplate(col, item))}\r\n                <ui5-table-cell align=\"left\"\r\n                  ><div class=\"rowicons\">\r\n                    <iron-icon\r\n                      .item=\"${item}\"\r\n                      icon=\"vaadin:eye\"\r\n                      title=\"${this.string[\"_preview\"]}\"\r\n                      class=\"${item.selected}\"\r\n                      id=\"preview\"\r\n                    >\r\n                    </iron-icon>\r\n\r\n                    <iron-icon\r\n                      .item=\"${item}\"\r\n                      icon=\"vaadin:edit\"\r\n                      title=\"${this.string[\"_edit\"]}\"\r\n                      class=\"${item.selected} ${util.isFileEditable(item)\r\n                        ? \"\"\r\n                        : \"hide\"}\"\r\n                      id=\"edit\"\r\n                    >\r\n                    </iron-icon>\r\n\r\n                    <ui5-icon name=\"activate\"></ui5-icon></div\r\n                ></ui5-table-cell>\r\n              </ui5-table-row>`\r\n            )}\r\n          </ui5-table>\r\n          <div\r\n            class=\"flex-spacer has-menu\"\r\n            @contextmenu=\"${this._handleRightClick.bind(this)}\"\r\n          ></div>\r\n        </vaadin-context-menu>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  _rowTemplate(col, item) {\r\n    //<ui5-checkbox ?checked=\"${item.reviewd}\" .item=\"${itm}\" @clicked=\"${this._toggleReviewed.bind(this)}\"></ui5-checkbox>\r\n    if (col.fieldname == \"reviewed\") {\r\n      return html`<ui5-table-cell align=\"center\"\r\n        ><div class=\"reviewedicon\">\r\n          <iron-icon\r\n            @click=\"${this._toggleReviewed.bind(this)}\"\r\n            .item=\"${item}\"\r\n            icon=\"${item.reviewed ? \"vaadin:circle\" : \"vaadin:circle-thin\"}\"\r\n          ></iron-icon></div\r\n      ></ui5-table-cell>`;\r\n    } else {\r\n      if (col.fieldname == \"name\") {\r\n        return html`<ui5-table-cell align=\"left\"\r\n          ><div>\r\n            <iron-icon\r\n              icon=\"vaadin:file-text-o\"\r\n              id=\"properties\"\r\n              class=\"file-icon ${item.iconClass}\"\r\n              title=\"${this.string[item.iconTitle]}\"\r\n              data-status=\"${item.status}\"\r\n            ></iron-icon>\r\n            &nbsp;${item[col.fieldname]}\r\n          </div>\r\n        </ui5-table-cell>`;\r\n      } else {\r\n        return html`\r\n          <ui5-table-cell align=\"left\">\r\n            <div>${item[col.fieldname]}</div>\r\n          </ui5-table-cell>\r\n        `;\r\n      }\r\n    }\r\n  }\r\n\r\n  _sortColumn(e) {\r\n    var direction = e.currentTarget.title;\r\n    if (e.currentTarget.colproperty) {\r\n      var cols = util.changeColumnSort(\r\n        e.currentTarget.colproperty,\r\n        direction,\r\n        structuredClone(this.state.browse_columns)\r\n      );\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_columns\",\r\n        value: cols,\r\n      });\r\n    }\r\n  }\r\n\r\n  _handleDblClick(e) {\r\n    const file = e.currentTarget.item;\r\n    storeInstance.dispatch(\"checkEditFile\", file);\r\n  }\r\n\r\n  // handler for click on actions column icon on row item\r\n  // note, emitted upon any click\r\n  _handleClick(e) {\r\n    const file = this.state.browse_selected_file;\r\n    // make sure icon is clicked target\r\n    const path = e.composedPath();\r\n    var iconClicked = path[0].tagName == \"IRON-ICON\" && !path[0].disabled;\r\n    if (iconClicked) {\r\n      var icon = path[0];\r\n      switch (icon.id) {\r\n        case \"preview\":\r\n          this._gotoPreview();\r\n          break;\r\n        case \"edit\":\r\n          this._handleEdit(file);\r\n          break;\r\n        case \"properties\":\r\n          if (e.currentTarget.item) {\r\n            if (this.state.browse_selected_file) {\r\n              if (\r\n                this.state.browse_selected_file.resLblId !=\r\n                e.currentTarget.item.resLblId\r\n              ) {\r\n                storeInstance.dispatch(\"setState\", {\r\n                  property: \"browse_selected_file\",\r\n                  value: e.currentTarget.item,\r\n                });\r\n              }\r\n            } else {\r\n              storeInstance.dispatch(\"setState\", {\r\n                property: \"browse_selected_file\",\r\n                value: e.currentTarget.item,\r\n              });\r\n            }\r\n            storeInstance.dispatch(\r\n              \"filePropertiesRequest\",\r\n              this.state.browse_selected_file\r\n            );\r\n            break;\r\n          }\r\n      }\r\n    } else {\r\n      if (e.currentTarget.item) {\r\n        if (this.state.browse_selected_file) {\r\n          if (\r\n            this.state.browse_selected_file.resLblId ==\r\n            e.currentTarget.item.resLblId\r\n          ) {\r\n            storeInstance.dispatch(\"setState\", {\r\n              property: \"browse_selected_file\",\r\n              value: null,\r\n            });\r\n            return;\r\n          }\r\n        }\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"browse_selected_file\",\r\n          value: e.currentTarget.item,\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  // handler for right click on row item\r\n  _handleRightClick(e) {\r\n    const file = e.currentTarget.item || null;\r\n    // modify conditional context menu items\r\n    const effectiveCap = this.state.wex_user.globalCapability;\r\n    let menuItems = util.buildFileMenu(\r\n      file,\r\n      this.state.menus.file_menu,\r\n      \"browse\",\r\n      effectiveCap\r\n    );\r\n    menuItems.map((item) => (item.text = this.string[item.label]));\r\n\r\n    // TEMP -- disable paste if nothing is copied or cut\r\n    // need to find a different solution\r\n    const canPaste =\r\n      !!this.state.browse_file_to_copy || !!this.state.browse_file_to_cut;\r\n    if (!canPaste) {\r\n      let idx = menuItems.findIndex((x) => x.name == \"paste\");\r\n      if (idx >= 0) menuItems[idx].disabled = true;\r\n    }\r\n\r\n    this.contextMenu.items = menuItems;\r\n\r\n    if (file) {\r\n      if (this.state.browse_selected_file) {\r\n        if (this.state.browse_selected_file.resLblId == file.resLblId) {\r\n          return;\r\n        }\r\n      }\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_selected_file\",\r\n        value: file,\r\n      });\r\n    } else {\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_selected_file\",\r\n        value: null,\r\n      });\r\n    }\r\n  }\r\n\r\n  // handler for click on right click context menu item\r\n  _handleContextMenuClick(e) {\r\n    const file = this.state.browse_selected_file;\r\n    switch (e.detail.value.name) {\r\n      case \"properties\":\r\n      case \"edit\":\r\n      case \"edit_ditamap\":\r\n      case \"open_ditamap\":\r\n      case \"preview\":\r\n      case \"previewnewwindow\":\r\n        storeInstance.dispatch(\"handleFileAction\", {\r\n          action: e.detail.value.name,\r\n          file: file,\r\n        });\r\n        break;\r\n      case \"publish\":\r\n        console.log(\"publish\");\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"browse_publish_dialog_open\",\r\n          value: this.state.browse_selected_file,\r\n        });\r\n        break;\r\n      case \"export_files\":\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"export_files_dialog_open\",\r\n          value: this.state.browse_selected_file,\r\n        });\r\n        break;\r\n      case \"delete\":\r\n        if (this.state.browse_selected_file)\r\n          pubsub.publish(\"dialog-browse-delete-file-open\", {\r\n            fileId: this.state.browse_selected_file.resLblId,\r\n            fileName: this.state.browse_selected_file.name,\r\n            branchId: this.state.browse_branch_filter.branchId,\r\n          });\r\n        break;\r\n      case \"cut\":\r\n        if (this.state.browse_file_to_copy) {\r\n          storeInstance.dispatch(\"setState\", {\r\n            property: \"browse_file_to_copy\",\r\n            value: null,\r\n          });\r\n        }\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"browse_file_to_cut\",\r\n          value: this.state.browse_selected_file,\r\n        });\r\n        break;\r\n      case \"copy\":\r\n        if (this.state.browse_file_to_cut) {\r\n          storeInstance.dispatch(\"setState\", {\r\n            property: \"browse_file_to_cut\",\r\n            value: null,\r\n          });\r\n        }\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"browse_file_to_copy\",\r\n          value: this.state.browse_selected_file,\r\n        });\r\n        break;\r\n      case \"paste\":\r\n        let action, fileId;\r\n        if (this.state.browse_file_to_cut) {\r\n          action = \"cut\";\r\n          fileId = this.state.browse_file_to_cut.resLblId;\r\n        } else if (this.state.browse_file_to_copy) {\r\n          action = \"copy\";\r\n          fileId = this.state.browse_file_to_copy.resLblId;\r\n        }\r\n        storeInstance.dispatch(\"browsePasteFile\", {\r\n          action: action,\r\n          fileId: fileId,\r\n          folderId: this.state.browse_selected_folder.id,\r\n        });\r\n        break;\r\n      case \"rename\":\r\n        const fileNames = this.rows.map((x) => x.name);\r\n        if (this.state.browse_selected_file)\r\n          pubsub.publish(\"dialog-browse-rename-file-open\", {\r\n            file: this.state.browse_selected_file,\r\n            siblings: fileNames,\r\n          });\r\n        break;\r\n    }\r\n  }\r\n\r\n  _gotoPreview() {\r\n    Router.go(\r\n      \"/wex/\" +\r\n        this.state.langCode +\r\n        \"/preview?resLblId=\" +\r\n        this.state.browse_selected_file.resLblId +\r\n        \"&aod=\" +\r\n        this.state.browse_selected_file.verCreateDate +\r\n        \"&previewStyle=html\"\r\n    );\r\n  }\r\n\r\n  _handleEdit(file) {\r\n    storeInstance.dispatch(\"checkEditFile\", file);\r\n  }\r\n}\r\n\r\ncustomElements.define(\"wex-browse-files\", WexBrowseFiles);\r\n"]}