{"version": 3, "file": "wex-task-filepackage.js", "sourceRoot": "", "sources": ["../../src/components/wex-task-filepackage.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AACvC,OAAO,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAC;AAEnD,OAAO,KAAK,IAAI,MAAM,aAAa,CAAC;AAEpC,OAAO,yCAAyC,CAAC;AACjD,OAAO,4CAA4C,CAAC;AACpD,OAAO,6CAA6C,CAAC;AACrD,OAAO,+CAA+C,CAAC;AACvD,OAAO,oDAAoD,CAAC;AAC5D,OAAO,4CAA4C,CAAC;AACpD,OAAO,oCAAoC,CAAC;AAC5C,OAAO,qCAAqC,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAG5D,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAGhC,IAAM,kBAAkB,GAAxB,MAAM,kBAAmB,SAAQ,UAAU;IAA3C;;QAML,WAAM,GAA2B,EAAE,CAAC;QACpC,oBAAe,GAAmB,IAAI,CAAC;QAEvC,qBAAgB,GAAe,EAAE,CAAC;QAElC,sBAAiB,GAAY,IAAI,CAAC;QAClC,gBAAW,GAAmB,IAAI,CAAC;QAEnC;;WAEG;QAEH,SAAI,GAAuB,MAAM,CAAC;QAClC;;WAEG;QAEH,iBAAY,GAAgB,IAAI,CAAC;QACjC;;WAEG;QAEH,iBAAY,GAAW,SAAS,CAAC;QAGjC,iBAAY,GAAW,EAAE,CAAC;QAE1B,YAAO,GAAa,EAAE,CAAC;QAEvB,SAAI,GAAe,EAAE,CAAC;QAEtB,aAAQ,GAAe,EAAE,CAAC;QAE1B,iBAAY,GAAoB,IAAI,CAAC;IAk0BvC,CAAC;IAh0BC,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YACtD,sBAAsB;YACtB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC;IAClE,CAAC;IAED,oBAAoB;QAClB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED,OAAO,CAAC,iBAAmC;QACzC,IAAI,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBACzD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,CACtC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CACpC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,YAAY;QACV,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED,WAAW,CAAC,KAAU;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAClD,CAAC,IAAU,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAC/B,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;QACjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;QAClD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC;QAChE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,gBAAgB;QACd,gCAAgC;QAChC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,MAAM,CACvD,UAAU,EAAE;YACV,OAAO,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;QACtB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;QACF,sBAAsB;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,CAAC;YACX,CAAC;YACD,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;QACH,UAAU;QACV,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAElE;;cAEM;QACN,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,mDAAmD;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,MAAM,EAAE,CAAC;gBACnC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC;oBAClC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAChD,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,YAAY,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBACtD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,aAAa;QACX,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC;gBAC3B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;wBACxD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,eAAe,CAAC;oBAC1C,CAAC;gBACH,CAAC;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,WAAW,CAAC;gBACtC,CAAC;YACH,CAAC;YACD,sBAAsB;QACxB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CACnE,CAAC;IACJ,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,gBAAgB,CAAC,EAAE,CAAC;YAClD,OAAO,IAAI,CAAA;;kBAEC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;;WAEzC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QAC1B,CAAC;QACL,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,mBAAmB,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAA;;kBAEC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;;WAEzC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAC7B,CAAC;QACL,CAAC;IACH,CAAC;IACD,iBAAiB;QACf,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,mBAAmB,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAA;;kBAEC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;;WAEzC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAC7B,CAAC;QACL,CAAC;IACH,CAAC;IAED,WAAW,CAAC,GAAG;QACb,IAAI,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC;QACzB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,eAAe;IACf,MAAM;QACJ,OAAO,IAAI,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAqCK,IAAI,CAAC,KAAK,CAAC,mBAAmB;aACrC,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAgFtB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;uBACpB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;wBAC9B,IAAI,CAAC,iBAAiB;;;;;oBAK1B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;uBACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;wBAC9B,CAAC,IAAI,CAAC,iBAAiB;;;UAGrC,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,iBAAiB,EAAE;UACjD,IAAI,CAAC,iBAAiB,EAAE;;;;;;;oBAOd,IAAI,CAAC,gBAAgB;4BACb,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;4BAInC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;6BACtB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;;;cAGpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAChB,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAA;;;;;gCAKK,GAAG,CAAC,OAAO;;;;8BAIb,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;;;;;0CAKT,GAAG,CAAC,SAAS;;oCAEnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;yCAEtB,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;;;;;0CAM/B,GAAG,CAAC,SAAS;;oCAEnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;0CAErB,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;;;;;;;eAQ5D,CACF;;;;;4BAKe,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;;;0BAIzB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;;;;cAKnC,IAAI,CAAC,IAAI,CAAC,GAAG,CACb,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAA;kCACc,IAAI,CAAC,QAAQ;4CACH,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;2BAChD,IAAI;+BACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;4BACjC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;kCAC3B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC;;oBAEpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;;;;iCAK1C,IAAI;;iCAEJ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;iCACvB,IAAI,CAAC,QAAQ;;;;;iCAKb,IAAI;;iCAEJ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;iCACpB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACjD,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,MAAM;;;;;;iCAMD,CACpB;;;;KAIR,CAAC;IACJ,CAAC;IAED,WAAW,CAAC,CAAC;QACX,IAAI,SAAS,GAAG,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC;QACtC,IAAI,CAAC,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YAChC,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAC9B,CAAC,CAAC,aAAa,CAAC,WAAW,EAC3B,SAAS,EACT,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CACrD,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAC9B,QAAQ,EAAE,0BAA0B;gBACpC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,YAAY,CAAC,GAAG,EAAE,GAAG;QACnB,sHAAsH;QACtH,IAAI,GAAG,CAAC,SAAS,IAAI,UAAU,EAAE,CAAC;YAChC,OAAO,IAAI,CAAA;;;sBAGK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;qBAChC,GAAG;oBACJ,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,oBAAoB;;yBAEhD,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,CAAC,SAAS,IAAI,MAAM,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAA;;;;;gCAKa,GAAG,CAAC,SAAS;uBACtB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;;;oBAG7B,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC;;0BAEZ,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAA;wBACK,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC;UAChC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,eAAe,CAAC,CAAC;QACf,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACtE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,qBAAqB,CAAC,YAAkB,EAAE,YAAsB;QAC9D,IAAI,OAAO,GAAG;YACZ,MAAM,EAAE,MAAM;YACd,aAAa,EAAE,YAAY,CAAC,aAAa;YACzC,SAAS,EAAE,YAAY,CAAC,MAAM;YAC9B,QAAQ,EAAE,YAAY,CAAC,QAAQ;YAC/B,QAAQ,EAAE,CAAC,YAAY,CAAC,QAAQ;SACjC,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAED,eAAe;QACb,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC9B,QAAQ,EAAE,6BAA6B;YACvC,KAAK,EAAE,CAAC,IAAI,CAAC,iBAAiB;SAC/B,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IAC3D,CAAC;IAED,eAAe,CAAC,OAAO;QACrB,IAAI,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE;YACtC,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,IAAI,CAAC,YAAY;SACxB,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC9B,QAAQ,EAAE,yBAAyB;YACnC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB;QAChB,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CACjD,UAAU,GAAG;YACX,OAAO,GAAG,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;QACtD,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;QACF,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;YACnB,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,+BAA+B,CAAC,EAAE,CAAC;gBACnE,IAAI,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC;YAClE,CAAC;QACH,CAAC;QACD,IAAI,OAAO,GAAG;YACZ,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;YACzC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,KAAK;YACrD,YAAY,EAAE,YAAY;SAC3B,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC9B,QAAQ,EAAE,yBAAyB;YACnC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB,CAAC,CAAC;QAClB,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAChC,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;gBAC3B,KAAK,SAAS;oBACZ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;wBAC9B,QAAQ,EAAE,yBAAyB;wBACnC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;qBACrD,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,YAAY;oBACf,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;wBAC9B,QAAQ,EAAE,oBAAoB;wBAC9B,KAAK,EAAE,IAAI,CAAC,YAAY;qBACzB,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,YAAY;oBACf,IAAI,cAAc,GAAG,KAAK,CAAC;oBAE3B,IAAI,IAAI,CAAC,YAAY,CAAC,iBAAiB,IAAI,YAAY,EAAE,CAAC;wBACxD,cAAc,GAAG,IAAI,CAAC;oBACxB,CAAC;oBACD,IAAI,GAAG,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;oBAC5C,IAAI,IAAI,GAAG,IAAI,CAAC;oBAChB,IAAI,GAAG,EAAE,CAAC;wBACR,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAChC,IAAI,CAAC,KAAK,CAAC,cAAc,EACzB,GAAG,CAAC,6BAA6B,CAClC,CAAC;wBACF,4FAA4F;oBAC9F,CAAC;oBAED,IAAI,IAAI,EAAE,CAAC;wBACT,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;4BAC9B,QAAQ,EAAE,yCAAyC;4BACnD,KAAK,EAAE;gCACL,MAAM,EAAE,SAAS;gCACjB,MAAM,EAAE,IAAI;gCACZ,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAChC,IAAI,CAAC,KAAK,CAAC,eAAe,EAC1B,GAAG,CAAC,QAAQ,CACb;gCACD,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAC5B,IAAI,CAAC,KAAK,CAAC,YAAY,EACvB,GAAG,CAAC,UAAU,CACf;gCACD,aAAa,EAAE,cAAc;gCAC7B,IAAI,EAAE,IAAI,CAAC,YAAY;gCACvB,WAAW,EAAE,IAAI,CAAC,iBAAiB;6BACpC;yBACF,CAAC,CAAC;oBACL,CAAC;oBACD;;;;;;8BAMU;oBACV,MAAM;YACV,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,cAAc,CAAC,CAAC;QACd,IAAI,QAAQ,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC9B,QAAQ,EAAE,oBAAoB;YAC9B,KAAK,EAAE,QAAQ;SAChB,CAAC,CAAC;QACH,UAAU,CACR;YACE,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;gBAChC,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,GAAG,CACJ,CAAC;IACJ,CAAC;IAED,mBAAmB,CAAC,CAAC;QACnB,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC;QAC/B,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,YAAY,CAAC;YAClB,KAAK,SAAS,CAAC;YACf,KAAK,kBAAkB,CAAC;YACxB,KAAK,cAAc,CAAC;YACpB,KAAK,kBAAkB;gBACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE;oBACtC,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,IAAI;iBACX,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,cAAc,CAAC;YACpB,KAAK,MAAM;gBACT,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;oBAChC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;gBACtD,CAAC;gBACD,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,uBAAuB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBAChE,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;oBAChC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,mHAAmH;gBAC3L,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;gBACtD,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAED,WAAW;QACT,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC9B,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;SAC1C,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC9B,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;SAC1C,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC9B,QAAQ,EAAE,wBAAwB;YAClC,KAAK,EAAE,OAAO;SACf,CAAC,CAAC;QACH,2CAA2C;QAC3C,yBAAyB;QACzB,4BAA4B;QAC5B,wBAAwB;QACxB,OAAO;QACP,sCAAsC;QACtC,0CAA0C;QAC1C,yBAAyB;QACzB,QAAQ;QACR,WAAW;QACX,yBAAyB;QACzB,uBAAuB;QACvB,wBAAwB;QACxB,OAAO;QACP,sCAAsC;QACtC,0CAA0C;QAC1C,sBAAsB;QACtB,QAAQ;QACR,IAAI;QACJ,sDAAsD;QACtD,gCAAgC;IAClC,CAAC;IAED,sBAAsB,CAAC,CAAC;QACtB,uCAAuC;QACvC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC;YAC7C,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAChC,CAAC,CAAC,aAAa,CAAC,IAAI,EACpB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAC1B,aAAa,EACb,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,EACpC,IAAI,CAAC,YAAY,CAAC,iBAAiB,EACnC,IAAI,CAAC,YAAY,CAAC,SAAS,CAC5B,CAAC;YACF,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAC/C,GAAG,IAAI;gBACP,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;aAC9B,CAAC,CAAC,CAAC;YAEJ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChE,OAAO;gBACT,CAAC;YACH,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAC9B,QAAQ,EAAE,oBAAoB;gBAC9B,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;aAC5B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,iBAAiB,CAAC,CAAC;QACjB,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;QAC9B,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC;YAC/B,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,SAAS;oBACZ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE;wBACtC,MAAM,EAAE,MAAM;wBACd,IAAI,EAAE,IAAI;wBACV,OAAO,EAAE,IAAI;qBACd,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;wBAChC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;oBACtD,CAAC;oBACD,MAAM;gBACR,KAAK,YAAY;oBACf,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;wBACzB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;4BACtB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gCAChE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oCAC9B,QAAQ,EAAE,oBAAoB;oCAC9B,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;iCAC5B,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gCAC9B,QAAQ,EAAE,oBAAoB;gCAC9B,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;6BAC5B,CAAC,CAAC;wBACL,CAAC;wBACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE;4BACtC,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI;4BACV,OAAO,EAAE,IAAI;yBACd,CAAC,CAAC;wBACH,MAAM;oBACR,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAChE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;4BAC9B,QAAQ,EAAE,oBAAoB;4BAC9B,KAAK,EAAE,IAAI;yBACZ,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;gBACH,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oBAC9B,QAAQ,EAAE,oBAAoB;oBAC9B,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,0BAA0B;QACxB,IAAI,GAAG,GAAG,IAAI,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3D,IACE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EACtE,CAAC;gBACD,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM;YACR,CAAC;QACH,CAAC;QACD,IAAI,GAAG,EAAE,CAAC;YACR,OAAO,GAAG,CAAC;QACb,CAAC;IACH,CAAC;IAED,0BAA0B;QACxB,IAAI,YAAY,GAAG,IAAI,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3D,IACE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EACtE,CAAC;gBACD,YAAY;oBACV,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC;gBAC9D,MAAM;YACR,CAAC;QACH,CAAC;QACD,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,sBAAsB,CAChC,IAAI,CAAC,KAAK,CAAC,cAAc,EACzB,YAAY,CACb,CAAC;YACF,uEAAuE;QACzE,CAAC;IACH,CAAC;IAED,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;QACzB,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,MAAM,GAAG,IAAI,CAAC;YAClB,IAAI,IAAI,CAAC,EAAE,IAAI,MAAM,EAAE,CAAC;gBACtB,MAAM,GAAG,IAAI,CAAC;gBACd,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBAC9C,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC/D,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;gBAChB,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;IACH,CAAC;IAED,eAAe,CAAC,IAAI,EAAE,MAAM;QAC1B,IAAI,KAAK,GAAG,EAAE,EACZ,IAAI,EACJ,EAAE,CAAC;QACL,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YACnB,IAAI,IAAI,CAAC,EAAE,IAAI,MAAM,EAAE,CAAC;gBACtB,YAAY;gBACZ,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACjD,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC;oBAChD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;QACD,+BAA+B;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AAv2BC;IADC,OAAO,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAC,CAAC;iDAC5B;AAOX;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;4DACQ;AAElC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;6DACM;AAOlC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;gDACO;AAKlC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wDACM;AAKjC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wDACM;AAGjC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wDACD;AAE1B;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;mDACH;AAEvB;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;gDACJ;AAEtB;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;oDACA;AAE1B;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wDACU;AAvC1B,kBAAkB;IAD9B,aAAa,CAAC,sBAAsB,CAAC;GACzB,kBAAkB,CAy2B9B", "sourcesContent": ["import { LitElement, html } from \"lit\";\r\nimport { globalStoreContext } from \"store/context\";\r\nimport { Router } from \"@vaadin/router/dist/vaadin-router.js\";\r\nimport * as util from \"lib/util.ts\";\r\n\r\nimport \"@ui5/webcomponents-compat/dist/Table.js\";\r\nimport \"@ui5/webcomponents-compat/dist/TableRow.js\";\r\nimport \"@ui5/webcomponents-compat/dist/TableCell.js\";\r\nimport \"@ui5/webcomponents-compat/dist/TableColumn.js\";\r\nimport \"@vaadin/vaadin-context-menu/vaadin-context-menu.js\";\r\nimport \"@vaadin/vaadin-list-box/vaadin-list-box.js\";\r\nimport \"@vaadin/vaadin-item/vaadin-item.js\";\r\nimport \"@ui5/webcomponents/dist/RadioButton\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\nimport { Task } from \"@bds/types\";\r\nimport { FileMeta } from \"@bds/types\";\r\nimport { consume } from \"@lit/context\";\r\n\r\n@customElement(\"wex-task-filepackage\")\r\nexport class WexTaskFilepackage extends LitElement {\r\n  @consume({context: globalStoreContext})\r\n  store: any;\r\n\r\n  subscription: any;\r\n  state: any;\r\n  string: Record<string, string> = {};\r\n  primaryRadioBtn: Element | null = null;\r\n  @property({ type: Array })\r\n  contextMenuItems: MenuItem[] = [];\r\n  @property({ type: Boolean })\r\n  isPrimarySelected: boolean = true;\r\n  contextMenu: Element | null = null;\r\n\r\n  /**\r\n   * Currently selected workflow tab\r\n   */\r\n  @property({ type: String })\r\n  view: \"task\" | \"process\" = \"task\";\r\n  /**\r\n   * Task selected in the active tasks table\r\n   */\r\n  @property({ type: Object })\r\n  selectedTask: Task | null = null;\r\n  /**\r\n   * author: 4,  contributor: 3,  review: 2,  default: 1\r\n   */\r\n  @property({ type: String })\r\n  effectiveCap: string = \"default\";\r\n\r\n  @property({ type: Object })\r\n  claimedTasks: Task[] = [];\r\n  @property({ type: Array })\r\n  columns: Column[] = [];\r\n  @property({ type: Array })\r\n  rows: FileMeta[] = [];\r\n  @property({ type: Array })\r\n  fileList: FileMeta[] = [];\r\n  @property({ type: Object })\r\n  selectedFile: FileMeta | null = null;\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this.subscription = this.store.subscribe((state: any) => {\r\n      /*WexTaskFilepackage*/\r\n      this.stateChange(state);\r\n    });\r\n    this.state = this.store.state;\r\n    this.isPrimarySelected = this.state.filepackage_display_primary;\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    this.store.unsubscribe(this.subscription);\r\n  }\r\n\r\n  updated(changedProperties: Map<string, any>) {\r\n    if (changedProperties.has(\"selectedTask\")) {\r\n      if (this.selectedTask) {\r\n        this.store.dispatch(\"getFilepackage\", this.selectedTask);\r\n        this.effectiveCap = util.getEffectiveCap(\r\n          this.selectedTask.userWexCapability\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  firstUpdated() {\r\n    this._marshellColumns();\r\n    this._marshellRows();\r\n    if (this.shadowRoot) {\r\n      this.contextMenu = this.shadowRoot.querySelector(\"#contextmenu\");\r\n      this.primaryRadioBtn = this.shadowRoot.querySelector(\"#primary\");\r\n    }\r\n  }\r\n\r\n  stateChange(state: any) {\r\n    this.state = state;\r\n    this.string = this.state[this.state.langCode];\r\n    this.lang = this.state.langCode;\r\n    this.claimedTasks = this.state.task_task_list.filter(\r\n      (task: Task) => task.isClaimed\r\n    );\r\n    this.fileList = this.state.filepackage_file_list;\r\n    this.selectedFile = this.state.task_selected_file;\r\n    this.isPrimarySelected = this.state.filepackage_display_primary;\r\n    this._marshellColumns();\r\n    this._marshellRows();\r\n  }\r\n\r\n  _marshellColumns() {\r\n    //filter for active col headings\r\n    this.columns = this.state.task_filepackage_columns.filter(\r\n      function (el) {\r\n        return el.order > 0;\r\n      }.bind(this)\r\n    );\r\n    //sort by column order\r\n    this.columns.sort(function (a, b) {\r\n      if (a.order < b.order) {\r\n        return -1;\r\n      }\r\n      if (a.order > b.order) {\r\n        return 1;\r\n      }\r\n      return 0;\r\n    });\r\n    //localize\r\n    this.columns.map((col) => (col.heading = this.string[col.title]));\r\n\r\n    /*  find sort column\r\n                first set the defaults\r\n        */\r\n    this.sortProperty = \"taskInstId\";\r\n    /*      Then check is sort is set on any columns */\r\n    for (var i = 0; i < this.columns.length; i++) {\r\n      if (this.columns[i].sort != \"none\") {\r\n        if (this.columns[i].sort == \"asc\") {\r\n          this.sortProperty = this.columns[i].fieldname;\r\n        } else {\r\n          this.sortProperty = \"-\" + this.columns[i].fieldname;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  _marshellRows() {\r\n    this.rows = [];\r\n    if (this.fileList) {\r\n      this.rows = structuredClone(this.fileList);\r\n      for (var i = 0; i < this.rows.length; i++) {\r\n        this.rows[i].selected = \"\";\r\n        if (this.selectedFile) {\r\n          if (this.rows[i].resLblId == this.selectedFile.resLblId) {\r\n            this.rows[i].selected = \" rowselected \";\r\n          }\r\n        }\r\n        if (util.isDitamap(this.rows[i])) {\r\n          this.rows[i].mapClass = \" ditamap \";\r\n        }\r\n      }\r\n      //apply claimed filter\r\n    }\r\n    this.rows.sort(\r\n      util.columnSort(util.getSortColPropertyName(this.columns, \"name\"))\r\n    );\r\n  }\r\n\r\n  _isClaimed() {\r\n    return this.selectedTask.isClaimed;\r\n  }\r\n\r\n  _addFileButton() {\r\n    if (util.can(this.effectiveCap, \"filePackageAdd\")) {\r\n      return html`<ui5-button\r\n        id=\"addfile\"\r\n        @click=\"${this._filePackageAction.bind(this)}\"\r\n        class=\" ui5button ui5-content-density-compact\"\r\n        >${this.string[\"_addfile\"]}</ui5-button\r\n      >`;\r\n    }\r\n  }\r\n\r\n  _importFileButton() {\r\n    if (util.can(this.effectiveCap, \"filePackageImport\")) {\r\n      return html`<ui5-button\r\n        id=\"importfile\"\r\n        @click=\"${this._filePackageAction.bind(this)}\"\r\n        class=\"ui5button ui5-content-density-compact\"\r\n        >${this.string[\"_importfile\"]}</ui5-button\r\n      >`;\r\n    }\r\n  }\r\n  _createFileButton() {\r\n    if (util.can(this.effectiveCap, \"filePackageCreate\")) {\r\n      return html`<ui5-button\r\n        id=\"createfile\"\r\n        @click=\"${this._filePackageAction.bind(this)}\"\r\n        class=\"ui5button ui5-content-density-compact\"\r\n        >${this.string[\"_createfile\"]}</ui5-button\r\n      >`;\r\n    }\r\n  }\r\n\r\n  _colHeading(col) {\r\n    var retval = col.heading;\r\n    return retval;\r\n  }\r\n\r\n  /* Templates */\r\n  render() {\r\n    return html`\r\n      <style>\r\n        .lockedbyother {\r\n          color: red;\r\n        }\r\n        .lockedbyselfhere {\r\n          color: green;\r\n        }\r\n        .lockedbyselfelsewhere {\r\n          color: orange;\r\n        }\r\n\r\n        .rowselected {\r\n          --sapList_Background: var(--row-selected-background);\r\n        }\r\n        #notasks {\r\n          font-style: oblique;\r\n          opacity: 0.5;\r\n          text-align: center;\r\n          margin-top: 50px;\r\n        }\r\n        .datarow {\r\n          cursor: pointer;\r\n        }\r\n        .ditamap {\r\n          font-weight: bold;\r\n        }\r\n\r\n        #filepackageheader {\r\n          padding: var(--spacing-xs);\r\n          background-color: var(--secondary);\r\n          color: #fff;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n        }\r\n        #filepackagetablecontainer {\r\n          height: ${this.state.global_screen_specs\r\n            .task_tabsSectionContentHeight}px !important;\r\n          overflow-y: auto;\r\n        }\r\n        #filerole {\r\n          display: inline-block;\r\n          margin-right: 3rem;\r\n        }\r\n        #filerole ui5-radio-button {\r\n          --sapContent_LabelColor: #fff;\r\n        }\r\n        .reviewedicon {\r\n          --iron-icon-width: 16px;\r\n        }\r\n        .hide {\r\n          visibility: hidden;\r\n        }\r\n        .sortbuttoncontainer {\r\n          display: flex;\r\n          flex-direction: column;\r\n        }\r\n        .sortbuttoncontainer iron-icon {\r\n          margin-left: 0.25rem;\r\n          --iron-icon-width: 0.9rem;\r\n        }\r\n        iron-icon.asc {\r\n          margin-bottom: -0.6rem;\r\n        }\r\n        iron-icon.desc {\r\n          margin-top: -0.6rem;\r\n        }\r\n        iron-icon.muted {\r\n          color: #ccc;\r\n        }\r\n\r\n        .colhead {\r\n          line-height: 1.4rem;\r\n          display: flex;\r\n          flex-direction: row;\r\n          align-items: center;\r\n        }\r\n\r\n        .fileicon {\r\n          width: 1rem;\r\n        }\r\n        .fileicon:hover {\r\n          color: #000;\r\n        }\r\n\r\n        .rowicons iron-icon {\r\n          width: 1rem;\r\n          padding-right: 30px;\r\n          display: none;\r\n        }\r\n        .rowicons iron-icon:hover {\r\n          color: #000;\r\n        }\r\n\r\n        .iconcontatiner {\r\n          height: 1.4rem;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .rowicons iron-icon.rowselected {\r\n          display: inline-block;\r\n        }\r\n\r\n        .rowicons iron-icon.disabled {\r\n          cursor: default;\r\n          color: #cfd4db;\r\n        }\r\n        .rowicons iron-icon.disabled:hover {\r\n          color: #cfd4db;\r\n        }\r\n      </style>\r\n\r\n      <div id=\"filepackageheader\">\r\n        <div id=\"filerole\">\r\n          <ui5-radio-button\r\n            id=\"primary\"\r\n            name=\"filerole\"\r\n            text=\"${this.string[\"_primary\"]}\"\r\n            @change=\"${this._fileRoleChange.bind(this)}\"\r\n            ?checked=\"${this.isPrimarySelected}\"\r\n          ></ui5-radio-button>\r\n          <ui5-radio-button\r\n            id=\"refernce\"\r\n            name=\"filerole\"\r\n            text=\"${this.string[\"_reference\"]}\"\r\n            @change=\"${this._fileRoleChange.bind(this)}\"\r\n            ?checked=\"${!this.isPrimarySelected}\"\r\n          ></ui5-radio-button>\r\n        </div>\r\n        ${this._addFileButton()} ${this._importFileButton()}\r\n        ${this._createFileButton()}\r\n      </div>\r\n\r\n      <div id=\"filepackagetablecontainer\">\r\n        <vaadin-context-menu\r\n          id=\"contextmenu\"\r\n          selector=\".has-menu\"\r\n          .items=\"${this.contextMenuItems}\"\r\n          @item-selected=\"${this._contextmenuclicked.bind(this)}\"\r\n        >\r\n          <ui5-table\r\n            id=\"filepackagetable\"\r\n            no-data-text=\"${this.string[\"_nofiles\"]}\"\r\n            ?show-no-data=\"${this.rows.length == 0}\"\r\n            sticky-column-header\r\n          >\r\n            ${this.columns.map(\r\n              (col) => html`\r\n                <ui5-table-column\r\n                  class=\"foo\"\r\n                  slot=\"columns\"\r\n                  min-width=\"800\"\r\n                  popin-text=\"${col.heading}\"\r\n                >\r\n                  <span style=\"line-height: 1.4rem\">\r\n                    <span class=\"colhead\">\r\n                      <span>${this._colHeading(col)}</span>\r\n\r\n                      <span class=\"sortbuttoncontainer\">\r\n                        <span\r\n                          class=\"iconcontatiner\"\r\n                          .colproperty=\"${col.fieldname}\"\r\n                          title=\"asc\"\r\n                          @click=\"${this._sortColumn.bind(this)}\"\r\n                          ><iron-icon\r\n                            class=\"asc ${col.sort != \"asc\" ? \"muted\" : \"\"}\"\r\n                            icon=\"vaadin:caret-up\"\r\n                          ></iron-icon\r\n                        ></span>\r\n                        <span\r\n                          class=\"iconcontatiner\"\r\n                          .colproperty=\"${col.fieldname}\"\r\n                          title=\"desc\"\r\n                          @click=\"${this._sortColumn.bind(this)}\"\r\n                          ><iron-icon\r\n                            class=\"desc ${col.sort != \"desc\" ? \"muted\" : \"\"}\"\r\n                            icon=\"vaadin:caret-down\"\r\n                          ></iron-icon\r\n                        ></span>\r\n                      </span>\r\n                    </span>\r\n                  </span>\r\n                </ui5-table-column>\r\n              `\r\n            )}\r\n            <ui5-table-column\r\n              class=\"foo\"\r\n              slot=\"columns\"\r\n              min-width=\"800\"\r\n              popin-text=\"${this.string[\"_actions\"]}\"\r\n            >\r\n              <span style=\"line-height: 1.4rem\">\r\n                <span class=\"colhead\">\r\n                  <span>${this.string[\"_actions\"]}</span>\r\n                </span>\r\n              </span>\r\n            </ui5-table-column>\r\n\r\n            ${this.rows.map(\r\n              (item) =>\r\n                html` <ui5-table-row\r\n                  data-reslblid=${item.resLblId}\r\n                  class=\"datarow has-menu ${item.mapClass}  ${item.selected}\"\r\n                  .item=\"${item}\"\r\n                  @dblclick=\"${this._defaultAction.bind(this)}\"\r\n                  @click=\"${this._handleRowClicked.bind(this)}\"\r\n                  @contextmenu=\"${this._handleRowRightClicked.bind(this)}\"\r\n                >\r\n                  ${this.columns.map((col) => this._rowTemplate(col, item))}\r\n\r\n                  <ui5-table-cell align=\"left\"\r\n                    ><div class=\"rowicons\">\r\n                      <iron-icon\r\n                        .item=\"${item}\"\r\n                        icon=\"vaadin:eye\"\r\n                        title=\"${this.string[\"_preview\"]}\"\r\n                        class=\"${item.selected}\"\r\n                        id=\"preview\"\r\n                      >\r\n                      </iron-icon>\r\n                      <iron-icon\r\n                        .item=\"${item}\"\r\n                        icon=\"vaadin:edit\"\r\n                        title=\"${this.string[\"_edit\"]}  \"\r\n                        class=\"${item.selected} ${util.isFileEditable(item)\r\n                          ? \"\"\r\n                          : \"hide\"}\"\r\n                        id=\"edit\"\r\n                      >\r\n                      </iron-icon>\r\n                      <ui5-icon name=\"activate\"></ui5-icon></div\r\n                  ></ui5-table-cell>\r\n                </ui5-table-row>`\r\n            )}\r\n          </ui5-table>\r\n        </vaadin-context-menu>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  _sortColumn(e) {\r\n    var direction = e.currentTarget.title;\r\n    if (e.currentTarget.colproperty) {\r\n      var cols = util.changeColumnSort(\r\n        e.currentTarget.colproperty,\r\n        direction,\r\n        structuredClone(this.state.task_filepackage_columns)\r\n      );\r\n      this.store.dispatch(\"setState\", {\r\n        property: \"task_filepackage_columns\",\r\n        value: cols,\r\n      });\r\n    }\r\n  }\r\n\r\n  _rowTemplate(col, itm) {\r\n    //<ui5-checkbox ?checked=\"${itm.reviewd}\" .item=\"${itm}\" @clicked=\"${this._toggleReviewed.bind(this)}\"></ui5-checkbox>\r\n    if (col.fieldname == \"reviewed\") {\r\n      return html`<ui5-table-cell align=\"center\"\r\n        ><div class=\"reviewedicon\">\r\n          <iron-icon\r\n            @click=\"${this._toggleReviewed.bind(this)}\"\r\n            .item=\"${itm}\"\r\n            icon=\"${itm.reviewed ? \"vaadin:circle\" : \"vaadin:circle-thin\"}\"\r\n          ></iron-icon></div\r\n      ></ui5-table-cell>`;\r\n    } else {\r\n      if (col.fieldname == \"name\") {\r\n        return html`<ui5-table-cell align=\"left\"\r\n          ><div>\r\n            <iron-icon\r\n              icon=\"vaadin:file-text-o\"\r\n              id=\"properties\"\r\n              class=\"fileicon ${itm.iconClass}\"\r\n              title=\"${this.string[\"_properties\"]}\"\r\n            >\r\n            </iron-icon>\r\n            &nbsp;${itm[col.fieldname]}\r\n          </div>\r\n        </ui5-table-cell>`;\r\n      } else {\r\n        return html`<ui5-table-cell align=\"left\"\r\n          ><div>&nbsp;${itm[col.fieldname]}</div></ui5-table-cell\r\n        >`;\r\n      }\r\n    }\r\n  }\r\n\r\n  _toggleReviewed(e) {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n\r\n    if (!this.selectedTask) {\r\n      util.notifyUser(\"warn\", \"Task not selected\");\r\n      return;\r\n    }\r\n\r\n    if (this.selectedTask.isClaimed) {\r\n      this._toggleReviewedAction(this.selectedTask, e.currentTarget.item);\r\n    } else {\r\n      util.notifyUser(\"warn\", this.string[\"_notclaimed\"]);\r\n    }\r\n  }\r\n\r\n  _toggleReviewedAction(selectedTask: Task, selectedFile: FileMeta) {\r\n    var payload = {\r\n      origin: \"task\",\r\n      processInstId: selectedTask.processInstId,\r\n      TaskDefId: selectedTask.taskId,\r\n      resLblId: selectedFile.resLblId,\r\n      reviewed: !selectedFile.reviewed,\r\n    };\r\n    this.store.dispatch(\"setTaskFileReviewedStatus\", payload);\r\n  }\r\n\r\n  _fileRoleChange() {\r\n    this.store.dispatch(\"setState\", {\r\n      property: \"filepackage_display_primary\",\r\n      value: !this.isPrimarySelected,\r\n    });\r\n    this.store.dispatch(\"getFilepackage\", this.selectedTask);\r\n  }\r\n\r\n  addFileCallBack(addFile) {\r\n    var fileRole = this.isPrimarySelected ? 1 : 2;\r\n    this.store.dispatch(\"addFileToPackage\", {\r\n      fileRole: fileRole,\r\n      file: addFile,\r\n      task: this.selectedTask,\r\n    });\r\n    this.store.dispatch(\"setState\", {\r\n      property: \"select_file_dialog_open\",\r\n      value: null,\r\n    });\r\n  }\r\n\r\n  createFileCallBack() {\r\n    var taskProject = this.state.global_projects.filter(\r\n      function (prj) {\r\n        return prj.projectId == this.selectedTask.projectId;\r\n      }.bind(this)\r\n    );\r\n    if (taskProject[0]) {\r\n      if (taskProject[0].hasOwnProperty(\"projectContributeCollectionId\")) {\r\n        var collectionId = taskProject[0].projectContributeCollectionId;\r\n      }\r\n    }\r\n    var payload = {\r\n      filename: this.state.task_createfile_name,\r\n      templateId: this.state.task_createfile_template.resId,\r\n      collectionId: collectionId,\r\n    };\r\n    this.store.dispatch(\"taskCreateFile\", payload);\r\n    this.store.dispatch(\"setState\", {\r\n      property: \"create_file_dialog_open\",\r\n      value: null,\r\n    });\r\n  }\r\n\r\n  _filePackageAction(e) {\r\n    if (this.selectedTask.isClaimed) {\r\n      switch (e.currentTarget.id) {\r\n        case \"addfile\":\r\n          this.store.dispatch(\"setState\", {\r\n            property: \"select_file_dialog_open\",\r\n            value: { callback: this.addFileCallBack.bind(this) },\r\n          });\r\n          break;\r\n        case \"importfile\":\r\n          this.store.dispatch(\"setState\", {\r\n            property: \"import_dialog_open\",\r\n            value: this.selectedTask,\r\n          });\r\n          break;\r\n        case \"createfile\":\r\n          var tmpLockFolders = false;\r\n\r\n          if (this.selectedTask.userWexCapability == \"Contribute\") {\r\n            tmpLockFolders = true;\r\n          }\r\n          var prj = this._getProjectFromCurrentTask();\r\n          var fldr = null;\r\n          if (prj) {\r\n            fldr = util._getFolderObjectFromId(\r\n              this.state.global_folders,\r\n              prj.projectContributeCollectionId\r\n            );\r\n            //fldr = this._findFolderTest(this.state.global_folders, prj.projectContributeCollectionId);\r\n          }\r\n\r\n          if (fldr) {\r\n            this.store.dispatch(\"setState\", {\r\n              property: \"extended_folder_create_file_dialog_open\",\r\n              value: {\r\n                target: \"package\",\r\n                folder: fldr,\r\n                branch: util.getBranchObjectFromId(\r\n                  this.state.global_branches,\r\n                  prj.branchId\r\n                ),\r\n                lang: util.getLangObjectFromId(\r\n                  this.state.global_langs,\r\n                  prj.languageId\r\n                ),\r\n                lockedFolders: tmpLockFolders,\r\n                task: this.selectedTask,\r\n                primaryrole: this.isPrimarySelected,\r\n              },\r\n            });\r\n          }\r\n          /*\r\n                    var tmpFolder = this._getrFolderFromCurrentTask();\r\n                    if(this.tmpFolder){\r\n                        this.store.dispatch(\"setState\", {property: \"extended_folder_create_file_dialog_open\", value:{target: \"package\", payload: this.tmpFolder } } )\r\n                    }\r\n                    //this.store.dispatch(\"setState\", {property: \"create_file_dialog_open\" , value:  {callback: this.createFileCallBack.bind(this) }} )\r\n                  */\r\n          break;\r\n      }\r\n    } else {\r\n      util.notifyUser(\"warn\", this.string[\"_notclaimed\"]);\r\n    }\r\n  }\r\n\r\n  _defaultAction(e) {\r\n    var fileItem = e.currentTarget.item;\r\n    this.store.dispatch(\"setState\", {\r\n      property: \"task_selected_file\",\r\n      value: fileItem,\r\n    });\r\n    setTimeout(\r\n      function () {\r\n        if (this.selectedTask.isClaimed) {\r\n          this._editAction();\r\n        } else {\r\n          util.notifyUser(\"warn\", this.string[\"_notclaimed\"]);\r\n        }\r\n      }.bind(this),\r\n      100\r\n    );\r\n  }\r\n\r\n  _contextmenuclicked(e) {\r\n    const action = e.detail.value.name;\r\n    const file = this.selectedFile;\r\n    switch (action) {\r\n      case \"properties\":\r\n      case \"preview\":\r\n      case \"previewnewwindow\":\r\n      case \"open_ditamap\":\r\n      case \"openfilelocation\":\r\n        this.store.dispatch(\"handleFileAction\", {\r\n          action: action,\r\n          file: file,\r\n        });\r\n        break;\r\n      case \"edit_ditamap\":\r\n      case \"edit\":\r\n        if (this.selectedTask.isClaimed) {\r\n          this._editAction();\r\n        } else {\r\n          util.notifyUser(\"warn\", this.string[\"_notclaimed\"]);\r\n        }\r\n        break;\r\n      case \"remove\":\r\n        this.store.dispatch(\"removeFileFromPackage\", { ownOnly: true });\r\n        break;\r\n      case \"toggledone\":\r\n        if (this.selectedTask.isClaimed) {\r\n          this._toggleReviewedAction(this.selectedTask, this.itemRightClicked); //The reviewed status can be changed without updateing the item, so must use the captured item from the right click\r\n        } else {\r\n          util.notifyUser(\"warn\", this.string[\"_notclaimed\"]);\r\n        }\r\n        break;\r\n    }\r\n  }\r\n\r\n  _editAction() {\r\n    this.store.dispatch(\"setState\", {\r\n      property: \"editor_selected_task\",\r\n      value: structuredClone(this.selectedTask),\r\n    });\r\n    this.store.dispatch(\"setState\", {\r\n      property: \"editor_selected_file\",\r\n      value: structuredClone(this.selectedFile),\r\n    });\r\n    this.store.dispatch(\"checkEditFile\", this.selectedFile);\r\n    this.store.dispatch(\"setState\", {\r\n      property: \"editor_side_active_tab\",\r\n      value: \"tasks\",\r\n    });\r\n    // if (util.isDitamap(this.selectedFile)) {\r\n    //   this.store.dispatch(\r\n    //     \"editDitamapRequest\",\r\n    //     this.selectedFile\r\n    //   );\r\n    //   this.store.dispatch(\"setState\", {\r\n    //     property: \"editor_side_active_tab\",\r\n    //     value: \"ditamaps\",\r\n    //   });\r\n    // } else {\r\n    //   this.store.dispatch(\r\n    //     \"checkEditFile\",\r\n    //     this.selectedFile\r\n    //   );\r\n    //   this.store.dispatch(\"setState\", {\r\n    //     property: \"editor_side_active_tab\",\r\n    //     value: \"tasks\",\r\n    //   });\r\n    // }\r\n    // Router.go(\"/wex/\" + this.state.langCode + \"/edit\");\r\n    // this.store.dispatch(\"touch\");\r\n  }\r\n\r\n  _handleRowRightClicked(e) {\r\n    //modify conditional context menu items\r\n    console.log(e.currentTarget.item);\r\n    if (e.currentTarget.item) {\r\n      this.itemRightClicked = e.currentTarget.item;\r\n      var menuItems = util.buildFileMenu(\r\n        e.currentTarget.item,\r\n        this.state.menus.file_menu,\r\n        \"filepackage\",\r\n        this.state.wex_user.globalCapability,\r\n        this.selectedTask.userWexCapability,\r\n        this.selectedTask.isClaimed\r\n      );\r\n      this.contextMenuItems = menuItems.map((item) => ({\r\n        ...item,\r\n        text: this.string[item.label],\r\n      }));\r\n\r\n      if (this.selectedFile) {\r\n        if (this.selectedFile.resLblId == e.currentTarget.item.resLblId) {\r\n          return;\r\n        }\r\n      }\r\n      this.store.dispatch(\"setState\", {\r\n        property: \"task_selected_file\",\r\n        value: e.currentTarget.item,\r\n      });\r\n    }\r\n  }\r\n\r\n  _handleRowClicked(e) {\r\n    const path = e.composedPath();\r\n    var iconClicked = path[0].tagName == \"IRON-ICON\" && !path[0].disabled;\r\n    console.log(iconClicked);\r\n    if (iconClicked) {\r\n      const action = path[0].id;\r\n      const file = this.selectedFile;\r\n      switch (action) {\r\n        case \"preview\":\r\n          this.store.dispatch(\"handleFileAction\", {\r\n            action: action,\r\n            file: file,\r\n            options: null,\r\n          });\r\n          break;\r\n        case \"edit\":\r\n          if (this.selectedTask.isClaimed) {\r\n            this._editAction();\r\n          } else {\r\n            util.notifyUser(\"warn\", this.string[\"_notclaimed\"]);\r\n          }\r\n          break;\r\n        case \"properties\":\r\n          if (e.currentTarget.item) {\r\n            if (this.selectedFile) {\r\n              if (this.selectedFile.resLblId != e.currentTarget.item.resLblId) {\r\n                this.store.dispatch(\"setState\", {\r\n                  property: \"task_selected_file\",\r\n                  value: e.currentTarget.item,\r\n                });\r\n              }\r\n            } else {\r\n              this.store.dispatch(\"setState\", {\r\n                property: \"task_selected_file\",\r\n                value: e.currentTarget.item,\r\n              });\r\n            }\r\n            this.store.dispatch(\"handleFileAction\", {\r\n              action: action,\r\n              file: file,\r\n              options: null,\r\n            });\r\n            break;\r\n          }\r\n      }\r\n    } else {\r\n      if (e.currentTarget.item) {\r\n        if (this.selectedFile) {\r\n          if (this.selectedFile.resLblId == e.currentTarget.item.resLblId) {\r\n            this.store.dispatch(\"setState\", {\r\n              property: \"task_selected_file\",\r\n              value: null,\r\n            });\r\n            return;\r\n          }\r\n        }\r\n        this.store.dispatch(\"setState\", {\r\n          property: \"task_selected_file\",\r\n          value: e.currentTarget.item,\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  _getProjectFromCurrentTask() {\r\n    var prj = null;\r\n    for (var i = 0; i < this.state.global_projects.length; i++) {\r\n      if (\r\n        this.state.global_projects[i].projectId == this.selectedTask.projectId\r\n      ) {\r\n        prj = this.state.global_projects[i];\r\n        break;\r\n      }\r\n    }\r\n    if (prj) {\r\n      return prj;\r\n    }\r\n  }\r\n\r\n  _getrFolderFromCurrentTask() {\r\n    var collectionId = null;\r\n    for (var i = 0; i < this.state.global_projects.length; i++) {\r\n      if (\r\n        this.state.global_projects[i].projectId == this.selectedTask.projectId\r\n      ) {\r\n        collectionId =\r\n          this.state.global_projects[i].projectContributeCollectionId;\r\n        break;\r\n      }\r\n    }\r\n    if (collectionId) {\r\n      return util._getFolderObjectFromId(\r\n        this.state.global_folders,\r\n        collectionId\r\n      );\r\n      //return this._findFolderTest(this.state.global_folders, collectionId )\r\n    }\r\n  }\r\n\r\n  _folderById(node, collId, c) {\r\n    if (!c.fldrFound) {\r\n      var retval = null;\r\n      if (node.id == collId) {\r\n        retval = node;\r\n        c.fldrFound = node;\r\n      } else {\r\n        if (node.children) {\r\n          for (var i = 0; i < node.children.length; i++) {\r\n            return (retval = c._folderById(node.children[i], collId, c));\r\n          }\r\n        }\r\n      }\r\n      if (c.fldrFound) {\r\n        return retval;\r\n      }\r\n    }\r\n  }\r\n\r\n  _findFolderTest(root, collId) {\r\n    var stack = [],\r\n      node,\r\n      ii;\r\n    stack.push(root);\r\n\r\n    while (stack.length > 0) {\r\n      node = stack.pop();\r\n      if (node.id == collId) {\r\n        // Found it!\r\n        return node;\r\n      } else if (node.children && node.children.length) {\r\n        for (ii = 0; ii < node.children.length; ii += 1) {\r\n          stack.push(node.children[ii]);\r\n        }\r\n      }\r\n    }\r\n    // Didn't find it. Return null.\r\n    return null;\r\n  }\r\n}\r\n\r\ninterface Column {\r\n  title: string;\r\n  fieldname: string;\r\n  order: number;\r\n  sort: \"asc\" | \"desc\" | \"\" | \"muted\";\r\n  heading: string;\r\n}\r\n\r\ninterface MenuItem {\r\n  name: string;\r\n  label: string;\r\n}\r\n"]}