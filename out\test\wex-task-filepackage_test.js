import { WexTaskFilepackage } from "../components/wex-task-filepackage";
import { fixture, assert, html } from "@open-wc/testing";
import "./store-wrapper";
import Store from 'store/beedle.js';
suite("renders", () => {
    test("is defined", async () => {
        const el = await fixture("<store-wrapper><wex-task-filepackage></wex-task-filepackage></store-wrapper>");
        assert.instanceOf(el, WexTaskFilepackage);
    });
    test("renders a radio group", async () => {
        const el = await fixture(html `<wex-task-filepackage
        .view="task"
        .selectedTask=${testTask}
      ></wex-task-filepackage>`);
    });
});
let testTask = {
    isClaimed: false,
    processDefName: "",
    processInstDescription: "",
    processInstId: 0,
    projectId: 0,
    projectName: "",
    taskActiveDate: "",
    taskDefDescription: "",
    taskDefId: "",
    taskDefName: "",
    taskId: 0,
    taskInstComment: "",
    taskInstId: 0,
    taskStartDate: "",
    userWexCapability: "Author",
};
const mockStore = new Store(actions, mutations, initialState);
//# sourceMappingURL=wex-task-filepackage_test.js.map