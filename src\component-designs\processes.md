# Process

Get the processes from the store and display. Fetch in the background to refresh.
If there are no processes show loading screen while fetching.

## Process Page
- Fetches the processes
- Passes processes to the process tiles
- Manages the process filters
- Handles process actions

Loading:
- If there are no processes and the fetch is pending show loading screen.
- If there is an error and the fetch is pending show loading screen.
- If there are processes in the cache and fetch is pending show processes and the updating spinner
- If there are no processes and the fetch is compmlete show no processes message.
- If there is an error and the fetch is complete show error message.

## Process Tile

- Displays process information
- Displays properties, history, and diagram
- Dispatches selected process up to the process page