{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/store/context/index.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAE7C,MAAM,CAAC,MAAM,kBAAkB,GAAG,aAAa,CAAQ,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC", "sourcesContent": ["import Store from \"../index.js\";\r\nimport { createContext } from \"@lit/context\";\r\n\r\nexport const globalStoreContext = createContext<Store>(Symbol(\"store\"));\r\n"]}