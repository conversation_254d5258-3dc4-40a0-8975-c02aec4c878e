import { LitElement, html, css } from "lit";
import { storeInstance } from "store/index.js";
import * as wexlib from "lib/wexlib.js";

import "../../components/base/col-item";
import "../../components/base/row-item";

class WexDefinitionViewPane extends LitElement {
  static get properties() {
    return {
      selectedProjectId: { type: Number },
      selectedPubDefId: { type: Number },
      _pubDef: { type: Object },
    };
  }

  static get styles() {
    return css`
      :host {
        display: flex;
        flex: 1;
        width: 100%;
        overflow: hidden;
        max-height: 70vh;
      }

      .loading {
        position: absolute;
        width: inherit;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: rgba(var(--clr-rgb-gray-ultra-light), 0.7);
        z-index: 10;
      }

      .dev-view {
        flex-direction: column;
        width: 70%;
        border-radius: 5px;
        border: 1px solid #556b81;
        padding: 1rem;
        overflow: auto;
        height: 100%;
      }

      .dev-view > *:not(:last-child) {
        margin-bottom: 1rem;
      }

      ul {
        flex-grow: 1;
        margin: 0;
        padding: 0;
        list-style: none;
        border: 1px solid var(--clr-gray-light);
        overflow-y: auto;
      }

      li {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 1rem;
        height: 2.25rem;
        border-bottom: 1px solid var(--clr-gray-light);
      }
      li > span {
        flex-grow: 1;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li > .icon-container {
        position: relative;
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
      }
      li > .icon-container:hover {
        background: var(--clr-white);
      }
      li > *:not(:last-child) {
        margin-right: 1rem;
      }
      li[active] {
        background: var(--row-selected-background);
      }
      li[active] {
        background: var(--row-selected-background);
      }
      li:not([active]):hover {
        background: var(--clr-gray-ultra-light);
      }

      .bold {
        font-style: bold;
      }
      .italic {
        font-style: italic;
      }

      .pointer {
        cursor: pointer;
      }

      .action-icon,
      .more-icon {
        width: 1.5rem;
        height: 1.5rem;
        padding: 0.25rem;
        border-radius: 0.25rem;
      }
      .action-icon:hover {
        background-color: var(--clr-primary-ultra-light);
      }

      ui5-input {
        width: 100%;
      }
    `;
    // .more-icon:hover {
    //   background-color: var(--clr-white);
    // }
  }

  constructor() {
    super();
    this._state = null;
    this._string = [];
    this._subscription = null;

    this.selectedProjectId = null; // project id selected in the main pane
    this.selectedPubDefId = null; // pubdef id selected in the main pane

    // private properties

    this._pubDef = null;
    this._isLoading = false;
  }

  connectedCallback() {
    super.connectedCallback();
    this._state = storeInstance.state;
    this._string = storeInstance.state[storeInstance.state.langCode];

    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  stateChange(state) {
    this._state = state;
    this._string = state[state.langCode];
  }

  async updated(changedProps) {
    if (changedProps.has("selectedPubDefId")) {
      if (this.selectedPubDefId) {
        this._pubDef = await wexlib.getPubDef(this.selectedPubDefId);
      } else {
        this._pubDef = null;
      }
    }

    // if (changedProps.has("selectedProjectId")) {
    //   this._selectedProjectId = this.selectedProjectId;
    // }
  }

  _init() {}

  _renderLoading() {
    const loading = html`
      <div class="loading">
        <ui5-busy-indicator active> </ui5-busy-indicator>
      </div>
    `;
    if (this._isLoading) return loading;
  }

  _closePubDef() {
    this.selectedPubDefId = null;
    this._pubDef = null;
  }

  // change handler for pubdef name // TODO
  async _handleChange(type = null, e) {
    try {
      if (!type || !e) return;
      if (type == "pubdef-name") {
        const name = e.currentTarget.value;
        await wexlib.setPubDefName(this._pubDef.pubDefId, name);
      }
      this._pubDef = await wexlib.getPubDef(item.pubDefId);
      this.requestUpdate();
    } catch {}
  }

  async _editPubContent() {
    if (!this.selectedProjectId) return;
    const curr = this._pubDef.lstPubContentDtos;
    const res = await wexlib.getProjPubContents(this.selectedProjectId);
    res.forEach((x) => {
      const exist = curr.find((y) => y.pubContentId == x.pubContentId);
      if (exist) x.selected = true;
      return x;
    });

    // console.log("pubdef", this._pubDef);

    storeInstance.dispatch("setState", {
      property: "wex_select_dialog_open",
      value: {
        pubDefId: this.selectedPubDefId,
        pubDef: this._pubDef, // pass entire pubDef instead of just ID
        headerText: "Edit Publication Content",
        updateTargetKey: "lstPubContentDtos",
        data: res,
        // closeCallback: (closeData) => wexlib.setPubDefPubContents(closeData),
        closeCallback: (closeData) => wexlib.editPubDef(closeData),
        refreshCallback: async (id) => {
          const updated = await wexlib.getPubDef(id);
          this._pubDef = updated;
        },
      },
    });
  }

  async _editPubLangs() {
    if (!this.selectedProjectId) return;
    const curr = this._pubDef.lstLanguages;
    curr.forEach((x) => (x.name = x.description));
    const res = await wexlib.getProjLanguages(this.selectedProjectId);
    res.forEach((x) => {
      const exist = curr.find((y) => y.langId == x.langId);
      if (exist) x.selected = true;
      x.name = x.description;
      return x;
    });

    storeInstance.dispatch("setState", {
      property: "wex_select_dialog_open",
      value: {
        pubDefId: this.selectedPubDefId,
        headerText: "Edit Languages",
        data: res,
        closeCallback: (closeData) => wexlib.setPubDefLanguages(closeData),
        refreshCallback: async (id) => {
          const updated = await wexlib.getPubDef(id);
          this._pubDef = updated;
        },
      },
    });
  }

  async _editPubOutputs() {
    const res = await wexlib.getPubEngineOutputs();
    const curr = this._pubDef.lstOutputFormats;
    res.forEach((x) => {
      const exist = curr.find(
        (y) =>
          y.publishEngineId === x.publishEngineId &&
          y.outputTypeId === x.outputTypeId
      );
      if (exist) x.selected = true;
      return x;
    });

    // console.log("edit outputs", res);

    storeInstance.dispatch("setState", {
      property: "wex_select_dialog_open",
      value: {
        pubDefId: this.selectedPubDefId,
        headerText: "Edit Output Formats",
        data: res,
        closeCallback: (closeData) => wexlib.setPubDefEngineOutputs(closeData),
        refreshCallback: async (id) => {
          const updated = await wexlib.getPubDef(id);
          this._pubDef = updated;
        },
      },
    });
  }

  _renderCloseButton() {
    return html` <wex-row-item justifyContent="right">
      <iron-icon
        icon="clear"
        class="pointer action-icon"
        title="${this._string["_close"]}"
        @click="${this._closePubDef.bind(this)}"
      ></iron-icon>
    </wex-row-item>`;
  }

  _renderProjectName() {
    return html`
      <wex-row-item alignItems="flex-start" height="7rem">
        <wex-col-item>
          <h3>${this._string["_project_name"]}</h3>
          <ui5-input value="${this._pubDef.projectName}" readonly></ui5-input>
        </wex-col-item>
        <wex-col-item>
          <h3>${this._string["_pubdef_name"]}</h3>
          <ui5-input
            readonly
            value="${this._pubDef.name}"
            @change="${this._handleChange.bind(this, "pubdef-name")}"
          ></ui5-input>
        </wex-col-item>
      </wex-row-item>
    `;
  }

  _renderContent() {
    return html`
      <wex-row-item justifyContent="space-between">
        <h3>${this._string["_pub_content"]}</h3>
        <iron-icon
          icon="create"
          class="pointer action-icon"
          title="${this._string["_edit_x"](this._string["_pub_content"])}"
          @click="${this._editPubContent.bind(this)}"
        ></iron-icon>
      </wex-row-item>
      <ul class="mainpane-list">
        ${this._pubDef.lstPubContentDtos.length > 0
          ? this._pubDef.lstPubContentDtos.map(
              (x) => html` <li><span>${x.name}</span></li> `
            )
          : html`
              <li>
                <span class="italic"> ${this._string["_no_item"]} </span>
              </li>
            `}
      </ul>
    `;
  }

  _renderLanguages() {
    return html` <wex-row-item justifyContent="space-between">
        <h3>${this._string["_languages"]}</h3>
        <iron-icon
          icon="create"
          class="pointer action-icon"
          title="${this._string["_edit_x"](this._string["_languages"])}"
          @click="${this._editPubLangs.bind(this)}"
        ></iron-icon>
      </wex-row-item>
      <ul class="mainpane-list">
        ${this._pubDef.lstLanguages.length > 0
          ? this._pubDef.lstLanguages.map(
              (x) => html` <li><span>${x.description}</span></li> `
            )
          : html`
              <li>
                <span class="italic"> ${this._string["_no_item"]} </span>
              </li>
            `}
      </ul>
      .`;
  }

  _renderOutputs() {
    return html`
      <wex-row-item justifyContent="space-between">
        <h3>${this._string["_output_format"]}</h3>
        <iron-icon
          icon="create"
          class="pointer action-icon"
          title="${this._string["_edit_x"](this._string["_output_format"])}"
          @click="${this._editPubOutputs.bind(this)}"
        ></iron-icon>
      </wex-row-item>
      <ul class="mainpane-list">
        ${this._pubDef.lstOutputFormats.length > 0
          ? this._pubDef.lstOutputFormats.map(
              (x) => html` <li><span>${x.name}</span></li> `
            )
          : html`
              <li>
                <span class="italic"> ${this._string["_no_item"]} </span>
              </li>
            `}
      </ul>
    `;
  }

  _renderPubDef() {
    if (!this._pubDef)
      return html`
        <wex-row-item justifyContent="center">
          <span>${this._string["_publish_select_pubdef_inst"]}</span>
        </wex-row-item>
      `;

    const pubDef = html`
      ${this._renderCloseButton()} ${this._renderProjectName()}
      ${this._renderContent()} ${this._renderLanguages()}
      ${this._renderOutputs()}
    `;

    return pubDef;
  }

  _renderDefinitionView() {
    return html`
      <section class="dev-view" ?loading="${this._isLoading}">
        ${this._renderLoading()} ${this._renderPubDef()}
      </section>
    `;
  }

  render() {
    return html`
      <wex-col-item
        justifyContent="flex-start"
        alignItems="center"
        height="500px"
      >
        ${this._renderDefinitionView()}
      </wex-col-item>
    `;
  }
}

customElements.define("wex-definition-view-pane", WexDefinitionViewPane);
