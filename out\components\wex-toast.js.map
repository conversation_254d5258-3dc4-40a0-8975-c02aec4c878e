{"version": 3, "file": "wex-toast.js", "sourceRoot": "", "sources": ["../../src/components/wex-toast.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,MAAM,MAAM,WAAW,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAGzD,IAAM,QAAQ,GAAd,MAAM,QAAS,SAAQ,UAAU;IAAjC;;QAC8B,SAAI,GAAG,EAAE,CAAC;QACV,YAAO,GAAG,EAAE,CAAC;QACb,aAAQ,GAAG,CAAC,CAAC;QACzC,UAAK,GAAuB,IAAI,CAAC;IAoDnC,CAAC;IAlDC,YAAY;QACV,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IAC3D,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,WAAW,CAAC,KAAK,EAAE,MAAM;QACvB,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO;QACxB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QACtC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IACzB,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAA;qCACsB,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,QAAQ;UAChE,IAAI,CAAC,OAAO;;KAEjB,CAAC;IACJ,CAAC;IAED,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;KAiBT,CAAC;IACJ,CAAC;CACF,CAAA;AAvD6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;sCAAW;AACV;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;yCAAc;AACb;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;0CAAc;AAHrC,QAAQ;IADb,aAAa,CAAC,WAAW,CAAC;GACrB,QAAQ,CAwDb", "sourcesContent": ["import PubSub from \"pubsub-js\";\r\nimport { LitElement, html, css } from \"lit\";\r\nimport \"@ui5/webcomponents/dist/Toast.js\";\r\nimport { customElement, property } from \"lit/decorators\";\r\n\r\n@customElement(\"wex-toast\")\r\nclass WexToast extends LitElement {\r\n  @property({ type: String }) type = \"\";\r\n  @property({ type: String }) message = \"\";\r\n  @property({ type: Number }) duration = 0;\r\n  toast: HTMLElement | null = null;\r\n\r\n  firstUpdated() {\r\n    this.toast = this.shadowRoot!.querySelector(\"ui5-toast\");\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    PubSub.subscribe(\"notifyUser\", this._notifyUser.bind(this));\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    PubSub.unsubscribe(\"notifyUser\", this._notifyUser.bind(this));\r\n  }\r\n\r\n  _notifyUser(label, params) {\r\n    if (!this.toast) return;\r\n    this.type = params.type.toLowerCase();\r\n    this.message = params.message;\r\n    this.duration = params.duration * 1000;\r\n    this.toast.open = true;\r\n  }\r\n\r\n  render() {\r\n    return html`\r\n      <ui5-toast class=\"fit-bottom ${this.type}\" duration=\"${this.duration}\">\r\n        ${this.message}\r\n      </ui5-toast>\r\n    `;\r\n  }\r\n\r\n  static get styles() {\r\n    return css`\r\n      .positive {\r\n        --_ui5-v2-11-0_toast_background: green;\r\n        --sapContent_ContrastTextColor: white;\r\n      }\r\n      .negative {\r\n        --_ui5-v2-11-0_toast_background: red;\r\n        --sapContent_ContrastTextColor: white;\r\n      }\r\n      .info {\r\n        --_ui5-v2-11-0_toast_background: blue;\r\n        --sapContent_ContrastTextColor: yellow;\r\n      }\r\n      .warn {\r\n        --_ui5-v2-11-0_toast_background: #FFC95C;\r\n        --sapContent_ContrastTextColor: black;\r\n      }\r\n    `;\r\n  }\r\n}\r\n"]}