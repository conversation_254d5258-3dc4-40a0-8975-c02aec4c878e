import { LitElement, html, css } from "lit";
import { customElement, property } from "lit/decorators.js";

@customElement("wex-col-item")
export class WexColItem extends LitElement {
  @property({ type: String }) alignItems?: string;
  @property({ type: String }) justifyContent?: string;
  @property({ type: String }) flex?: string;
  @property({ type: String }) width?: string;

  static styles = css`
    :host {
      display: flex;
      flex: 1;
      height: 100%;
    }

    .wrapper {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 40px;
    }
    .wrapper > *:not(:last-child) {
      margin-bottom: 0.5rem;
    }
  `;

  render() {
    const width = this.width ?? "auto";
    const justifyContent = this.justifyContent ?? "space-evenly";
    const alignItems = this.alignItems ?? "stretch";
    const flex = this.flex ?? "1";
    return html`
      <div
        class="wrapper"
        style="width: ${width}; justify-content: ${justifyContent}; align-items: ${alignItems}; flex: ${flex};"
      >
        <slot></slot>
      </div>
    `;
  }

  // protected createRenderRoot(): HTMLElement | DocumentFragment {
  //   return this;
  // }
}
