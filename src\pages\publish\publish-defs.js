import { LitElement, html, css } from "lit";
import { storeInstance } from "store/index.js";
import * as wexlib from "lib/wexlib.js";

import "../../components/base/row-item";
import "../../components/common/mainpane.js";

import "../../components/publish/publish-definitions-pane.js";
import "../../components/publish/definition-view-pane.js";

import "../../components/header/publish";

// TODO -- add to initial state
const DEBOUNCE_DURATION = 1000;

class WexPagePublishDefs extends LitElement {
  static get properties() {
    return {
      _selectedProjectId: { type: Number },
      _selectedPubDefId: { type: Number },
    };
  }

  static get styles() {
    return css`
      #publish-defs-container {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        max-height: 100%;
      }
      .mainpane {
        display: flex;
        flex-direction: row;
        align-items: stretch;
        justify-content: space-evenly;
        justify-content: center;
        color: var(--font-color);
        height: 100%;
        max-height: 100%;
      }
      .mainpane > *:not(:last-child) {
        margin-bottom: 1rem;
        border: 1px solid #f00;
      }
      .split-layout {
        width: 100%;
        height: 100%;
      }
      .right,
      .left {
        padding: 1rem;
      }
    `;
  }

  constructor() {
    super();
    this.state = null;
    this.string = [];
    this._subscription = null;

    this._selectedProjectId = null;
    this._selectedPubDefId = null;

    this.contextMenu = null;

    this._dialogOpen = null;
  }

  connectedCallback() {
    super.connectedCallback();
    const state = storeInstance.state;
    this.state = state;
    this.string = state[state.langCode];
    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  stateChange(state) {
    // Save old dialog state for comparison
    const oldDialogOpen = this._dialogOpen;
    this._dialogOpen = state.publish_defs_create_dialog_open;

    this.state = state;
    this.string = state[state.langCode];

    // Detect any change to dialog open flag (open or close)
    if (oldDialogOpen !== this._dialogOpen) {
      // this._refreshAllData();
    }
  }

  // async _refreshAllData() {
  //   // console.log("refreshing ALL DATA HAHAHA");
  //   try {
  //     const projects = await wexlib.getProjectsByProjCategoryId();
  //     const pubDefs = await wexlib.getAllPubDefs();

  //     storeInstance.dispatch("setPublishProjects", { projects });
  //     storeInstance.dispatch("setPubDefs", { pubDefs });
  //   } catch (e) {
  //     console.error("Error refreshing data after dialog change", e);
  //   }
  // }

  async _init() {
    try {
      const categories = await wexlib.getProjCategories();
      if (categories.length) {
        storeInstance.dispatch("setPublishCategories", {
          categories,
        });
      }

      const projects = await wexlib.getProjectsByProjCategoryId();
      if (projects.length) {
        storeInstance.dispatch("setPublishProjects", {
          projects,
        });
      }

      const pubDefs = await wexlib.getAllPubDefs();
      if (pubDefs.length) {
        storeInstance.dispatch("setPubDefs", {
          pubDefs,
        });
      }
    } catch (error) {
      console.error("Error initializing publish definitions:", error);
    }
  }

  _handleSinglePubDefSelected(e) {
    this._selectedProjectId = e.detail.projectId;
    this._selectedPubDefId = e.detail.pubDefId;
  }

  _handleResetSelectedPubDef() {
    this.selectedProjectId = null;
    this._selectedPubDefId = null;
  }

  _renderPubDefsPane() {
    const template = html`
      <wex-publish-definitions-pane
        class="left"
        @reset-selected-pubdef=${this._handleResetSelectedPubDef.bind(this)}
        @single-pubdef-selected=${this._handleSinglePubDefSelected.bind(this)}
      ></wex-publish-definitions-pane>
    `;
    return template;
  }

  _renderDefViewPane() {
    const template = html`
      <wex-definition-view-pane
        class="right"
        .selectedProjectId=${this._selectedProjectId}
        .selectedPubDefId=${this._selectedPubDefId}
      ></wex-definition-view-pane>
    `;
    return template;
  }

  _renderMainPane() {
    // <wex-row-item>
    const template = html`
      <section class="mainpane">
        <vaadin-split-layout class="split-layout">
          ${this._renderPubDefsPane()} ${this._renderDefViewPane()}
        </vaadin-split-layout>
      </section>
    `;
    // </wex-row-item>
    return template;
  }

  render() {
    return html`
      <div id="publish-defs-container">
        <wex-publish-header></wex-publish-header>
        ${this._renderMainPane()}
      </div>
    `;
  }
}

customElements.define("wex-page-publish-defs", WexPagePublishDefs);
