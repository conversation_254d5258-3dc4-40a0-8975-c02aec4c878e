import jsMapElement from "lib/jsDitamap/jsMapElement.js";
import generator from "./nodeGenerator";
import * as util from "lib/util.ts";
import { createInsertAction } from "../util";
import pubsub from "pubsub-js";
import * as wexlib from "lib/wexlib.js";

export default class jsDitamap {
  //PUBLIC PROPERTIES
  get jsDitamap() {
    return this._jsMap;
  }

  get rootDitaMapId() {
    return this._rootDitaMapId;
  }

  get options() {
    return this._options;
  }

  set activeUser(strActiveUser) {
    this._activeUser = strActiveUser;
  }
  set options(options) {
    this._options = options;
    if (!this._requiredParamsPresent()) {
      console.log("required params are missing from options object");
    }
  }

  //CONSTRUCTOR
  constructor() {}

  _init() {
    this._getResourceEndpoint = this._options.apiUri + "/GetTopicRenderXml";
    this._getCheckOutEndpoint = this._options.apiUri + "/CheckOutRes";
    this._getCheckInEndpoint = this._options.apiUri + "/UnLockReachableMaps";
    this._saveResourceEndpoint = this._options.apiUri + "/PutTopicRenderXml";
    this._getResourceMetaEndpoint =
      this._options.apiUri + "/GetMetaDataForResList";
    this._setCommentEndpoint = this._options.apiUri + "/SetResComment";
    this._resourceCredentials = ""; //userName="+ this._options.apiUsername +"&accessToken="+ this._options.apiPassword
    this._domList = {}; //Array of each ditamap an xml dom obj
    this._resList = {}; //Array for resMeta for each ditamap
    this._titleHrefs = []; //hrefs for topics...used to lookup titles
    this._titles = []; //array of titles returned by 'GetMetaDataForResList'
    this._httpRequestsOut = []; //Tracks outstanding http requests
    this._httpCheckoutCounter = 0; //Tracks outstanding http requests
    this._httpCheckinCounter = 0; //Tracks outstanding http requests
    this._httpGetResMetaForMapCounter = 0;
    this._id = 0; //Incrementor for unique ids for tree nodes
    this._domElements = []; //Actual DOM Elements represented by each tree node
    this._jsMap = new jsMapElement(); //JS version of the dita map
    this.rootMapLockStatus = undefined;
    this.lockedByUser = undefined;
    this.lockedbyotherbtn = undefined;
    this.addedElements = [];
  }

  unload() {
    this._init();
  }

  //PUBLIC INITIATOR
  //returns true if conversion started
  //returns false if missing params and writes error to console
  //throw err
  load(rootDitaMapId, rootDitaMapTitle, aod) {
    this._init();
    var retval = true;
    if (aod) {
      this.aod = aod;
    } else {
      this.aod = "now";
    }
    if (this._requiredParamsPresent()) {
      if (!rootDitaMapId) {
        retval = false;
      }
      if (retval) {
        this._rootDitaMapId = rootDitaMapId;
        this._rootDitaMapTitle = rootDitaMapTitle;
        this._init();
        this._resList[rootDitaMapId] = {};
        this._loadDitamapXML(this._rootDitaMapId);
      }
    } else {
      retval = false;
    }
    this.loaded = retval;
    return retval;
  }

  anyMapsLockedByUser() {
    var retval = false;
    for (var map in this._resList) {
      if (this._resList[map].lockStatus == "lockedbyuser") {
        retval = true;
      }
    }
    return retval;
  }

  mapLockStatus(resLblId) {
    //lockedbyuser, lockedbyother, unlocked
    try {
      return this._resList[resLblId].lockStatus;
    } catch (err) {
      //console.log(err)
    }
  }

  isDirty(resLblId) {
    //lockedbyuser, lockedbyother, unlocked
    if (this._domList[resLblId].dirty) {
      return true;
    } else {
      return false;
    }
  }

  mapName(resLblId) {
    return this._resList[resLblId].name;
  }

  lockResource(resLblId) {
    var prom = this._lockMap(resLblId);
    prom.then(
      function (details) {
        this._resList[JSON.parse(details).resLblId].lockOwner =
          this._activeUser;
        this._setStatuses();
        if (this._options.onLockedResponse) {
          this._options.onLockedResponse(true);
        }
      }.bind(this),
      function (details) {
        if (this._options.onLockedResponse) {
          this._options.onLockedResponse(false);
        }
      }.bind(this)
    );
  }

  lockMaps() {
    var prom;
    var fails = 0;
    for (var resLblId in this._domList) {
      prom = this._lockMap(resLblId);
      prom.then(
        function (details) {
          //console.log(details)
          if (this._httpCheckoutCounter == 0) {
            this._mapFilesCheckedOut(!fails);
          }
        }.bind(this),
        function (details) {
          if (this._resList[resLblId].lockOwner) {
            //already locked....see if it is me
            if (this._resList[resLblId].lockOwner != this._activeUser) {
              //File already locked by someone else, so fail
              fails++;
            }
          }
          if (this._httpCheckoutCounter == 0) {
            this._mapFilesCheckedOut(!fails);
          }
        }.bind(this)
      );
    }
  }

  unlockMap(resLblId) {
    var prom = this._unlockMap(resLblId);
    prom.then(
      function (details) {
        var resMeta = JSON.parse(details);
        this._resList[resMeta.resLblId] = resMeta;
        this._resList[resMeta.resLblId].lockStatus = "unlocked";
        if (this._options.onUnlockedResponse) {
          this._options.onUnlockedResponse(true);
        }
      }.bind(this),
      function (details) {
        if (this._options.onUnlockedResponse) {
          this._options.onUnlockedResponse(false);
        }
      }.bind(this)
    );
  }

  unlockAllMaps() {
    var prom;
    var fails = 0;
    for (var resLblId in this._domList) {
      if (this._resList[resLblId].lockStatus != "unlocked") {
        prom = this._unlockMap(resLblId);
        prom.then(
          function (details) {
            var resMeta = JSON.parse(details);
            this._resList[resMeta.resLblId] = resMeta;
            this._resList[resMeta.resLblId].lockStatus = "unlocked";
            this._httpCheckinCounter--;
            if (this._httpCheckinCounter == 0) {
              this._mapFilesUnlockedAll(!fails);
            }
          }.bind(this),
          function (details) {
            fails++;
            this._httpCheckinCounter--;
            if (this._httpCheckinCounter == 0) {
              this._mapFilesUnlockedAll(false);
            }
          }.bind(this)
        );
      }
    }
  }

  anyMapsDirty() {
    return this._anyMapsDirty();
  }

  _anyMapsDirty() {
    var retval = false;
    for (var map in this._domList) {
      if (this._domList[map].dirty) {
        retval = true;
      }
    }
    return retval;
  }

  numMapsDirty() {
    var retval = 0;
    for (var map in this._domList) {
      if (this._domList[map].dirty) {
        retval++;
      }
    }
    return retval;
  }

  getdomList() {
    return this._domList;
  }
  getResList() {
    return this._resList;
  }

  saveAllMaps(comment) {
    var prom;
    var fails = 0;
    this._httpSavedCounter = 0;
    for (var resLblId in this._domList) {
      if (this._domList[resLblId].dirty) {
        prom = this._saveMap(resLblId);
        prom.then(
          function (savedResLblId) {
            console.log(savedResLblId);
            //console.log(savedResLblId)
            if (comment) {
              var commentProm = this._setComment(savedResLblId, comment);
            }
            if (this._domList[savedResLblId]) {
              this._domList[savedResLblId].dirty = false;
            }

            if (!this._anyMapsDirty()) {
              this._allMapFilesSaved(true);
            }
            /*
						if(this._httpSavedCounter==0){
							if(fails>0){
								this._allMapFilesSaved(false)
							} else {
								this._allMapFilesSaved(true)
								}
							}
						*/
          }.bind(this),
          function (failedResLblId) {
            fails++;
            if (this._httpSavedCounter == 0) {
              this._allMapFilesSaved(false);
            }
          }.bind(this)
        );
      }
    }
  }

  reload(rootDitaMapTitle) {
    this._init();
    if (this._rootDitaMapId) {
      this.load(this._rootDitaMapId, rootDitaMapTitle);
    }
  }

  reloadSubMap(resLblId) {
    this._getSubMapXML(resLblId).then(function (details) {
      //console.log(details)
    });
  }

  _getSubMapXML(resLblId) {
    if (resLblId) {
      return new Promise(
        function (resolve, reject) {
          var xhttp = new XMLHttpRequest();
          xhttp.timeout = 5000;
          xhttp.docPath = resLblId;
          xhttp.onreadystatechange = function () {
            if (xhttp.readyState == 4) {
              this._httpSavedCounter--;
              switch (xhttp.status) {
                case 200:
                  resolve(xhttp.responseText);
                  break;
                case 403:
                  reject(xhttp.responseText);
                  break;
                case 0:
                  break;
                default:
                  reject(xhttp.responseText);
                  break;
              }
            }
          }.bind(this);
          //xhttp.open("GET", this._getResourceEndpoint + resLblId + '?' + this._resourceCredentials  + "&aod=" + this.aod , true);
          xhttp.open(
            "GET",
            this._getResourceEndpoint + resLblId + "?aod=" + this.aod,
            true
          );

          this._httpRequestsOut.push(resLblId); //using an array of doc names instead of a counter for debugging puposes only.
          xhttp.send();
        }.bind(this)
      );
    }
  }

  _getResource(resLblId) {
    if (resLblId) {
      return new Promise(
        function (resolve, reject) {
          var xhttp = new XMLHttpRequest();
          xhttp.timeout = 5000;
          xhttp.onreadystatechange = function () {
            if (xhttp.readyState == 4) {
              this._httpSavedCounter--;
              switch (xhttp.status) {
                case 200:
                  resolve(xhttp.responseText);
                  break;
                case 403:
                  reject(xhttp.responseText);
                  break;
                case 0:
                  break;
                default:
                  reject(xhttp.responseText);
                  break;
              }
            }
          }.bind(this);
          xhttp.open("POST", this._getResourceMetaEndpoint, true);
          xhttp.setRequestHeader(
            "Content-type",
            "application/x-www-form-urlencoded"
          );
          var params =
            "cdlResLblIds=" + resLblId + "&" + this._resourceCredentials;
          xhttp.send(params);
        }.bind(this)
      );
    }
  }

  _cloneNode(node, parent) {
    let newNode = new jsMapElement();
    let newIntNodeId = this._id++;
    newNode.id = `node${newIntNodeId}`;
    newNode.intId = newIntNodeId;
    newNode.type = node.type;
    newNode.href = node.href;
    newNode.title = node.title;
    newNode.domElement = node.domElement.cloneNode(true); //https://developer.mozilla.org/fr/docs/Web/API/Node/cloneNode
    newNode.embeddedElement = node.embeddedElement.cloneNode(true);
    newNode.icon = node.icon;
    newNode.keyscope = node.keyscope;
    newNode.rootElementName = node.rootElementName;

    // Handle parent
    if (parent) {
      newNode.mapName = this._isSubMap(parent.domElement)
        ? parent.href
        : parent.mapName;
      newNode.parent = parent;
    }

    if (Array.isArray(node.nodes)) {
      for (let child of node.nodes) {
        newNode.nodes.push(this._cloneNode(child, newNode));
      }
    }
    this._domElements.push(newNode);
    return newNode;
  }

  _transferState(oldTree, newTree) {
    let Q = [[oldTree, newTree]];
    while (Array.isArray(Q) && Q.length > 0) {
      let [oldTree, newTree] = Q.shift();
      newTree.open = oldTree.open;
      if (Array.isArray(oldTree.nodes)) {
        for (let i = 0; i < oldTree.nodes.length; i++) {
          Q.push([oldTree.nodes[i], newTree.nodes[i]]);
        }
      }
    }
  }

  _hasDirtyNode() {
    return this._checkDirtyDfs(this._jsMap);
  }

  _checkDirtyDfs(node) {
    if (node.dirty) {
      return true;
    }

    if (Array.isArray(node.nodes)) {
      for (let child of node.nodes) {
        return this._checkDirtyDfs(child);
      }
    }

    return false;
  }

  _findParentMapNode(node, mapName) {
    if (!node) {
      throw new Error("Parent Not Found");
    }

    if (node.href === mapName) {
      return node;
    }

    return this._findParentMapNode(node.parent, mapName);
  }

  loadPasteBuffer(targetNode) {
    this.pasteBuffer = this._cloneNode(targetNode, targetNode.parent);
  }

  // loadPasteBuffer(targetNode) {
  //   var de = null;
  //   var retval = false;
  //   var mapName = targetNode.mapName;
  //   this._domElements.forEach(function (el) {
  //     if (el.id == targetNode.id) {
  //       de = el;
  //     }
  //   });
  //   if (de) {
  //     let jsonCln = structuredClone(de);
  //     jsonCln.domElement = de.domElement;
  //     let newIntNodeId = this._id++;
  //     let newNodeId = "node" + newIntNodeId;
  //     jsonCln.id = newNodeId;
  //     jsonCln.intId = newIntNodeId;
  //     this.pasteBuffer = jsonCln;
  //     retval = true;
  //   }
  //   return retval;
  // }

  removeMapElement(targetNode) {
    var retval;
    retval = true;
    try {
      this.pasteBuffer = targetNode;
      targetNode.embeddedElement.remove();
      let parent = this._findParentMapNode(targetNode, targetNode.mapName);
      parent.dirty = true;
      this._domList[targetNode.mapName].dirty = true;
    } catch (err) {
      util.notifyUser("warn", err);
      retval = false;
    }

    if (this._options.onNodeRemoved) {
      this._options.onNodeRemoved(retval, targetNode.mapName);
    }
  }

  pasteFromBuffer(targetnode) {
    let retval = false;
    if (this.pasteBuffer) retval = true;
    if (retval) {
      let newNode = this.convertNode(targetnode, this.pasteBuffer);
      this._addTreeElement(newNode, targetnode);
      this._domList[targetnode.mapName].dirty = true;
    }

    if (this._options.onPasteFromBuffer) {
      this._options.onPasteFromBuffer(retval, targetnode.mapName);
    }
  }

  // Convert copy from map -> part & vice versa if necessary
  convertNode(targetNode, node) {
    if (
      this._isBookMap(targetNode.domElement) !==
      this._isBookMap(node.parent.domElement)
    ) {
      let newNode = this._makeNewMapElementFromNode(node, targetNode);
      if (!this._isSubMap(node.domElement)) {
        if (this._isBookMap(targetNode.domElement)) {
          newNode.embeddedElement.replaceChildren(...node.domElement.children);
        } else {
          newNode.domElement.replaceChildren(...node.domElement.children);
        }
      }
      return newNode;
    }
    return node;
  }

  // pasteFromBuffer(targetNode, mode) {
  //   console.log("BUFFER", this.pasteBuffer);
  //   if (this.pasteBuffer) {
  //     var de;
  //     var retval;
  //     var mapName = targetNode.mapName;
  //     this._domElements.forEach(function (el) {
  //       if (el.id == targetNode.id) {
  //         de = el;
  //       }
  //     });
  //     retval = true;
  //     try {
  //       switch (mode) {
  //         case "firstChild":
  //           //insert before first child
  //           var tmpEl = de.domElement;
  //           if (tmpEl.nodeName == "#document") {
  //             tmpEl = tmpEl.documentElement;
  //             for (var i = 0; i < tmpEl.children.length; i++) {
  //               if (tmpEl.children[i].nodeName == "topicref") {
  //                 //tmpEl = tmpEl.children[i]
  //                 break;
  //               }
  //             }
  //           }

  //           if (tmpEl.children.length > 0) {
  //             tmpEl.insertBefore(this.pasteBuffer.domElement, tmpEl.firstChild);
  //           } else {
  //             tmpEl.appendChild(this.pasteBuffer.domElement);
  //           }
  //           break;
  //         case "nextSibling":
  //           de.domElement.parentElement.insertBefore(
  //             this.pasteBuffer.domElement,
  //             de.domElement.nextElementSibling
  //           );
  //           break;
  //       }
  //       var newId = this.pasteBuffer.intId;
  //       // this.pasteBuffer = undefined;
  //     } catch (err) {
  //       retval = false;
  //     }
  //   } else {
  //     retval = false;
  //   }

  //   if (retval) {
  //     this._domList[targetNode.mapName].dirty = true;
  //   }

  //   if (this._options.onPasteFromBuffer) {
  //     this._options.onPasteFromBuffer(retval, mapName);
  //   }
  // }

  _setStatuses() {
    //first see if all maps are already checked out
    for (var resMeta in this._resList) {
      if (this._resList[resMeta].lockOwner) {
        if (this._resList[resMeta].lockOwner == this._activeUser) {
          this._resList[resMeta].lockStatus = "lockedbyuser";
        } else {
          this._resList[resMeta].lockStatus = "lockedbyother";
        }
      } else {
        this._resList[resMeta].lockStatus = "unlocked";
      }

      if (resMeta == this.rootDitaMapId) {
        this._jsMap.lockStatus = this._resList[resMeta].lockStatus;
        this.rootMapLockStatus = this._resList[resMeta].lockStatus;
      }

      for (var n in this._jsMap.nodes) {
        if (resMeta == this._jsMap.nodes[n].href) {
          this._jsMap.nodes[n].lockStatus = this._resList[resMeta].lockStatus;
        }
      }
    }
    //this.lockedByUser = lockedByUser
    //this.lockedByOtherUser = lockedByOtherUser
    if (this._options.onjsDitamapResMetaReady) {
      this._options.onjsDitamapResMetaReady();
    }
  }

  _addTreeElement(newNode, parent) {
    var retval = false;
    let insertAction, embeddedElement;

    if (parent) {
      this._domElements.push(newNode);
      if (this._isBookMap(parent.domElement)) {
        let result = this._insertBookmapElement(parent.mapName, newNode);
        if (result) {
          ({ insertAction, embeddedElement } = result);
          newNode.embeddedElement = embeddedElement;
        } else {
          insertAction = "Failed";
          embeddedElement = null;
          retval = false;
        }
      } else {
        newNode.embeddedElement = parent.domElement.appendChild(
          newNode.domElement
        );
        insertAction = createInsertAction("appendChild", parent.domElement);
      }
      retval = true;
    }

    if (retval) {
      if (this._isSubMap(parent.domElement)) {
        this._domList[parent.href].dirty = true;
      } else {
        this._domList[parent.mapName].dirty = true;
      }
    }

    if (this._options.onNodeAdded) {
      this._options.onNodeAdded(retval, parent, newNode, insertAction);
    }
  }

  addMapElementFromExistingFile(resLblId, parentNode, navigator) {
    let prom = this._getResource(resLblId);
    prom.then(
      async function (details) {
        let file = JSON.parse(details)[0];
        let newNode = await this._makeNewMapElementFromFile(file, parentNode);
        console.log({ hasTitles: newNode });
        if (this.isValidNode(newNode)) {
          this._addTreeElement(newNode, parentNode);
          if (
            file.rootElementName == "bookmap" ||
            file.rootElementName == "map"
          ) {
            await wexlib.lockReachableMaps(resLblId);
          }
        }
      }.bind(this)
    );
  }

  dispatchLockRequest(navigator) {
    navigator.dispatchEvent(
      new CustomEvent("navigator_lock_request", {
        bubbles: true,
        composed: true,
        detail: {},
      })
    );
  }

  isValidNode(node) {
    if (node.mapPath.find((map) => map === node.href)) {
      util.notifyUser("negative", "Cannot have self-referrential map.");
      return false;
    }

    if (this.hasBookmapAncestor(node) && node.rootElementName == "bookmap") {
      util.notifyUser("negative", "Cannot add bookmap within a bookmap");
      return false;
    }

    return true;
  }

  hasBookmapAncestor(node) {
    if (!node.parent) {
      return false;
    }

    if (node.parent.rootElementName == "bookmap") {
      return true;
    }

    return this.hasBookmapAncestor(node.parent);
  }

  _generateNewElementAttributes(parentTagName, newNodeTagName) {
    switch (parentTagName) {
      case "bookmap":
        return generator.bookmap(newNodeTagName);
      default:
        return generator.default(newNodeTagName);
    }
  }

  /**
   *
   * @param {jsMapElement} node - copied node
   * @param {jsMapElement} target - target node
   * @returns
   */
  _makeNewMapElementFromNode(node, target) {
    let attr = this._generateNewElementAttributes(
      target.domElement.tagName,
      node.domElement.tagName
    );

    let newInt = this._id++;
    let newNodeId = "node" + newInt;
    let newIntNodeId = newInt;
    //NewElement
    let tr = document.createElement(attr.elementTagName);
    tr.setAttribute("format", attr.formatValue);
    tr.setAttribute("class", attr.classValue);
    tr.setAttribute("href", node.href);

    let newNode = new jsMapElement();
    newNode.id = newNodeId;
    newNode.intId = newIntNodeId;
    newNode.title = node.title;
    newNode.nodes = node.nodes;
    newNode.type = node.domElement.tagName;
    newNode.rootElementName = node.rootElementName;
    newNode.href = node.href;
    newNode.icon = attr.icon;
    newNode.mapName = this._isSubMap(target.domElement)
      ? target.href
      : target.mapName;
    newNode.dirty = true;

    if (this._isBookMap(target.domElement)) {
      newNode.domElement = node.domElement;
      newNode.embeddedElement = tr;
    } else {
      newNode.domElement = tr;
      newNode.embeddedElement = node.domElement;
    }

    return newNode;
  }

  async _makeNewMapElementFromFile(file, parent) {
    let attr = this._generateNewElementAttributes(
      parent.rootElementName,
      file.rootElementName
    );

    let newInt = this._id++;
    let newNodeId = "node" + newInt;
    let newIntNodeId = newInt;

    //NewElement
    let tr = document.createElement(attr.elementTagName);
    tr.setAttribute("format", attr.formatValue);
    tr.setAttribute("class", attr.classValue);
    tr.setAttribute("href", file.resLblId);

    let newNode = new jsMapElement();
    newNode.id = newNodeId;
    newNode.intId = newIntNodeId;
    newNode.title = file.title;
    newNode.type = file.rootElementName;
    newNode.rootElementName = file.rootElementName;
    newNode.href = file.resLblId;
    newNode.icon = attr.icon;
    newNode.embeddedElement = tr;
    newNode.domElement = tr;
    newNode.parent = parent;
    newNode.mapName = this._isSubMap(parent.domElement)
      ? parent.href
      : parent.mapName;
    newNode.mapPath =
      parent.mapPath.at(-1) === newNode.mapName
        ? parent.mapPath
        : parent.mapPath.concat([newNode.mapName]);
    newNode.dirty = true;

    if (file.rootElementName == "map" || file.rootElementName == "bookmap") {
      this._fetchMap(file.resLblId).then(() => {
        let mapElement = this._domList[file.resLblId].documentElement;
        newNode.domElement = mapElement;
        let tree = this._makeJsTree(mapElement, newNode);
        console.log({ tree });
        newNode.nodes = tree.nodes;
        this._fetchTitles();
      });
    }

    console.log({ newNode });
    return newNode;
  }

  _insertBookmapElement(mapName, newNode) {
    try {
      let bookmap = this._domList[mapName].documentElement;
      let part = bookmap.querySelector("part");
      let backmatter = bookmap.querySelector("backmatter");
      let appendix =
        bookmap.querySelector("appendix") ||
        bookmap.querySelector("appendices");

      if (part) {
        return {
          insertAction: createInsertAction("insertBefore", part),
          embeddedElement: bookmap.insertBefore(newNode.embeddedElement, part),
        };
      }

      if (appendix) {
        return {
          insertAction: createInsertAction(
            "appendChild",
            newNode.parent ? newNode.parent.domElement : newNode.domElement
          ),
          embeddedElement: bookmap.insertBefore(
            newNode.embeddedElement,
            appendix
          ),
        };
      }

      if (backmatter) {
        return {
          insertAction: createInsertAction(
            "appendChild",
            newNode.parent ? newNode.parent.domElement : newNode.domElement
          ),
          embeddedElement: bookmap.insertBefore(
            newNode.embeddedElement,
            backmatter
          ),
        };
      }
    } catch (e) {
      util.notifyUser("negative", e);
      throw new Error(e);
    }
  }

  // DEPRECATED
  // _addMapElement(file, parentNode) {
  //   let data = this._generateNewElementAttributes(
  //     parentNode.rootElementName,
  //     file.rootElementName
  //   );
  //   let newNode = this._makeNewMapElement(data, file, parentNode);

  //   var retval = false;

  //   if (parentNode) {
  //     this._domElements.push(newNode);
  //     if (this._isBookMap(parentNode.domElement)) {
  //       this.addedElements.push(
  //         this._insertBookmapElement(parentNode.mapName, newNode)
  //       );
  //     } else {
  //       this.addedElements.push(
  //         parentNode.domElement.appendChild(newNode.domElement)
  //       );
  //     }

  //     retval = true;
  //   }

  //   if (retval) {
  //     if (this._isSubMap(parentNode.domElement)) {
  //       this._domList[parentNode.href].dirty = true;
  //     } else {
  //       this._domList[parentNode.mapName].dirty = true;
  //     }
  //   }
  //   if (this._options.onNodeAdded) {
  //     this._options.onNodeAdded(
  //       retval,
  //       parentNode,
  //       newNode,
  //       this._isSubMap(parentNode.domElement)
  //     );
  //   }
  // }

  _getNodeById(id, node) {
    if (node.id == id) {
      return node;
    }
    for (var n in node.nodes) {
      return this._getNodeById(id, node.nodes[n]);
    }
  }

  _requiredParamsPresent() {
    var retval = true;
    if (!this._options.apiUri) {
      console.log("Missing API end point");
      retval = false;
    }

    return retval;
  }

  saveMap(resLblId, comment) {
    var prom = this._saveMap(resLblId);
    prom.then(
      function (savedResLblId) {
        if (comment) {
          var commentProm = this._setComment(savedResLblId, comment);
        }
        if (this._domList[savedResLblId]) {
          this._domList[savedResLblId].dirty = false;
        }

        if (this._options.onSavedResponse) {
          this._options.onSavedResponse(true, savedResLblId);
        }
      }.bind(this),
      function (failedResLblId) {
        this._options.onSavedResponse(false, savedResLblId);
      }.bind(this)
    );
  }

  _setComment(resLblId, comment) {
    //SetResComment
    return new Promise(
      function (resolve, reject) {
        var xhttp = new XMLHttpRequest();
        xhttp.timeout = 5000;
        xhttp.onreadystatechange = function () {
          if (xhttp.readyState == 4) {
            this._httpCommentsCounter--;
            switch (xhttp.status) {
              case 200:
                resolve(xhttp.responseText);
                break;
              case 204:
                resolve(xhttp.responseText);
                break;
              case 403:
                reject(xhttp.responseText);
                break;
              case 0:
                break;
              default:
                reject(xhttp.responseText);
                break;
            }
          }
        }.bind(this);

        xhttp.open("POST", this._setCommentEndpoint, true);
        xhttp.setRequestHeader(
          "Content-type",
          "application/x-www-form-urlencoded"
        );
        var params =
          "resLBLId=" +
          encodeURI(resLblId) +
          "&comment=" +
          comment +
          "&" +
          this._resourceCredentials;
        xhttp.send(params);
        this._httpCommentsCounter++;
      }.bind(this)
    );
  }

  async _saveMap(resLblId) {
    let doc = this._domList[resLblId].documentElement.outerHTML;
    doc = doc.replace(/ xmlns="[^"]*"/g, "");
    doc = doc.replace(/ glossref="[^"]*"/g, "");
    let content = this._doctypeToString(this._domList[resLblId].doctype) + doc;
    let data = new FormData();
    data.set("resLBLId", resLblId.slice(1));
    data.set("content", content);

    try {
      const response = await fetch("/lwa/jrest/PutTopicRenderXml", {
        method: "put",
        body: data,
      });
      if (!response.ok) {
        let error = await response.json();
        if (error) {
          util.notifyUser(
            "negative",
            `${error.httpRetCode} Error: An error occured.`
          );
        }
        throw new Error();
      }
      return resLblId;
    } catch (e) {
      console.log(e);
    }

    this._httpSavedCounter++;
  }

  // _saveMap(resLblId) {
  //   return new Promise(
  //     function (resolve, reject) {
  //       var xhttp = new XMLHttpRequest();
  //       xhttp.timeout = 5000;
  //       xhttp.onreadystatechange = function () {
  //         if (xhttp.readyState == 4) {
  //           this._httpSavedCounter--;
  //           switch (xhttp.status) {
  //             case 200:
  //               //resolve(xhttp.responseText);   <--Frank gives me a blank response so have to pull resLblb from request
  //               resolve(
  //                 xhttp.responseURL.substr(
  //                   xhttp.responseURL.indexOf("/PutTopicRenderXml") + 18
  //                 )
  //               );
  //               break;
  //             case 204:
  //               //resolve(xhttp.responseText);   <--Frank gives me a blank response so have to pull resLblb from request
  //               resolve(resLblId);
  //               break;
  //             case 403:
  //               reject(xhttp.responseText);
  //               break;
  //             case 0:
  //               break;
  //             default:
  //               reject(xhttp.responseText);
  //               break;
  //           }
  //         }
  //       }.bind(this);
  //       xhttp.open("PUT", this._saveResourceEndpoint, true);
  //       var doc = this._domList[resLblId].documentElement.outerHTML;
  //       doc = doc.replace(/ xmlns="[^"]*"/g, "");
  //       doc = doc.replace(/ glossref="[^"]*"/g, "");
  //       var content =
  //         this._doctypeToString(this._domList[resLblId].doctype) + doc;
  //       let data = new FormData();
  //       data.set("resLBLId", resLblId.slice(1));
  //       data.set("content", content);
  //       // var params = "content=" + content + "&" + this._resourceCredentials;
  //       xhttp.send(data);
  //       this._httpSavedCounter++;
  //     }.bind(this)
  //   );
  // }

  _doctypeToString(doctype) {
    var retval =
      "<!DOCTYPE " +
      doctype.name +
      (doctype.publicId ? ' PUBLIC "' + doctype.publicId + '"' : "") +
      (!doctype.publicId && doctype.systemId ? " SYSTEM" : "") +
      (doctype.systemId ? ' "' + doctype.systemId + '"' : "") +
      ">";
    return retval;
  }

  _lockMap(resLblId) {
    return new Promise(
      function (resolve, reject) {
        var xhttp = new XMLHttpRequest();
        xhttp.timeout = 5000;
        xhttp.onreadystatechange = function () {
          if (xhttp.readyState == 4) {
            this._httpCheckoutCounter--;
            switch (xhttp.status) {
              case 200:
                resolve(xhttp.responseText);
                break;
              case 403:
                reject(xhttp.responseText);
                break;
              case 0:
                break;
              default:
                reject(xhttp.responseText);
                break;
            }
          }
        }.bind(this);
        xhttp.open(
          "GET",
          this._getCheckOutEndpoint +
            "?resLBLId=" +
            resLblId +
            "&" +
            this._resourceCredentials,
          true
        );
        xhttp.send();
        this._httpCheckoutCounter++;
      }.bind(this)
    );
  }

  _unlockMap(resLblId) {
    this._httpCheckinCounter++;
    return new Promise(
      function (resolve, reject) {
        var xhttp = new XMLHttpRequest();
        xhttp.timeout = 5000;
        xhttp.onreadystatechange = function () {
          if (xhttp.readyState == 4) {
            switch (xhttp.status) {
              case 200:
                resolve(xhttp.responseText);
                break;
              case 403:
                reject(xhttp.responseText);
                break;
              case 0:
                break;
              default:
                reject(xhttp.responseText);
                break;
            }
          }
        }.bind(this);
        xhttp.open(
          "GET",
          this._getCheckInEndpoint +
            "?resLBLIdMap=" +
            resLblId +
            "&" +
            this._resourceCredentials,
          true
        );
        xhttp.send();
      }.bind(this)
    );
  }

  /******
	 load rootMap and push the domDocument into a flat array and parse the document for submaps.  Repeat recursivly to find all submaps. 
	 Flat array '_domList' contains resLblId of document, and the parsed xml dom document itself.
	 ******/
  _loadDitamapXML(docPath, hardReset = false) {
    var xhttp = new XMLHttpRequest();
    xhttp.timeout = 5000;
    xhttp.docPath = docPath;
    xhttp.onreadystatechange = function () {
      if (xhttp.readyState == 4 && xhttp.status == 200) {
        this._httpRequestsOut.splice(
          this._httpRequestsOut.splice(
            this._httpRequestsOut.indexOf(xhttp.docPath),
            1
          )
        );
        this._domList[xhttp.docPath] = xhttp.responseXML;

        //find the title of the map
        // var iterator = xhttp.responseXML.evaluate(
        //   "//mainbooktitle",
        //   xhttp.responseXML.documentElement,
        //   null,
        //   XPathResult.ORDERED_NODE_ITERATOR_TYPE,
        //   null

        try {
          var bookTitle = this._getSubmapTitle(xhttp.responseXML);
          this._bookTitle = bookTitle;
          //this._domList[xhttp.docPath].title = bookTitle;
        } catch (e) {}

        //rootmap keyscope
        /*
				if(this._domList[this._rootDitaMapId].documentElement.getAttribute("keyscope")){
			        this._rootMapKeyscope = this._domList[this._rootDitaMapId].documentElement.getAttribute("keyscope");
		        }
				*/
        //now find any submaps
        var iterator = xhttp.responseXML.evaluate(
          '//*[@format="ditamap"]',
          xhttp.responseXML.documentElement,
          null,
          XPathResult.ORDERED_NODE_ITERATOR_TYPE,
          null
        );
        try {
          var thisNode = iterator.iterateNext();
          //Might have to open this in the side to confirm its the correct type of map to load. eg no loading of
          //"subjectScheme"
          while (thisNode) {
            if (thisNode.getAttribute("href")) {
              this._loadDitamapXML(thisNode.getAttribute("href"), hardReset);
            }
            thisNode = iterator.iterateNext();
          }
        } catch (e) {
          console.log("Error: Document tree modified during iteration " + e);
        }

        if (this._httpRequestsOut.length == 0) {
          clearTimeout(this.to);
          this.to = setTimeout(
            function () {
              this._assembleTree1(hardReset);
            }.bind(this),
            1000
          );
        }
      } else {
        if (this.to) {
          clearTimeout(this.to);
        }
      }
    }.bind(this);

    xhttp.open(
      "GET",
      this._getResourceEndpoint +
        docPath +
        "?" +
        this._resourceCredentials +
        "&aod=" +
        this.aod,
      true
    );
    this._httpRequestsOut.push(docPath); //using an array of doc names instead of a counter for debugging puposes only.
    xhttp.send();
  }

  async _fetchMap(reslblId) {
    if (this._domList.hasOwnProperty(reslblId)) {
      return this._domList[reslblId];
    }

    pubsub.publish("requestout");

    try {
      const response = await fetch(`/lwa/jrest/GetTopicRenderXml${reslblId}`);
      if (!response.ok) {
        throw Error();
      }

      let textResponse = await response.text();
      if (!textResponse) {
        throw Error();
      }

      let parser = new DOMParser();
      let xml = parser.parseFromString(textResponse, "application/xml");
      this._domList[reslblId] = xml;

      // Find submaps
      let iterator = xml.evaluate(
        '//*[@format="ditamap"]',
        xml.documentElement,
        null,
        XPathResult.ORDERED_NODE_ITERATOR_TYPE,
        null
      );

      let thisNode = iterator.iterateNext();
      while (thisNode) {
        if (thisNode.getAttribute("href")) {
          this._fetchMap(thisNode.getAttribute("href"));
        }
        thisNode = iterator.iterateNext();
      }
    } catch (e) {
      console.log(e);
    } finally {
      pubsub.publish("requestin");
    }
  }

  _assembleTree1(hardReset = false) {
    //build root domelement

    var newNode = new jsMapElement();
    //test1
    //newNode.nodes=[]
    newNode.title = "ROOTELEMENT";
    newNode.id = "rootnode";
    newNode.mapName = this._rootDitaMapId;
    newNode.href = this._rootDitaMapId;
    newNode.rootElementName = this._getRootElementName(
      this._domList[this._rootDitaMapId]
    );
    let me2 = structuredClone(newNode);
    me2.domElement = this._domList[this._rootDitaMapId].documentElement;
    this._domElements.push(me2);

    // this._buildTree(
    //   this._domList[this._rootDitaMapId],
    //   this._jsMap,
    //   this._rootDitaMapId
    // );

    // if (this._jsMap.nodes) {
    //   this._fetchTitles();
    // } else {
    //   this._createRootNode();
    //   this.loadLockStatus();
    //   this._jsDitmapReady();
    // }
    this._id = 0;
    if (this._jsMap.id !== null && !hardReset) {
      let newTree = this._makeJsTree(this._domList[this._rootDitaMapId]);
      try {
        this._transferState(this._jsMap, newTree);
      } catch (e) {
        console.log(e);
      }
      this._jsMap = newTree;
    } else {
      this._jsMap = this._makeJsTree(this._domList[this._rootDitaMapId]);
    }

    if (this._jsMap.nodes) {
      this._fetchTitles();
    } else {
      this._createRootNode();
      this.loadLockStatus();
      this._jsDitmapReady();
    }

    /*
		_buildTree walks each dom in _domList and builds the n-deep tree array and collects hrefs for title look up
		_fetchTitles calls '/jrest/GetMetaDataForResList' passing the list of hrefs collected in _buildTree
		_fetchTitles is an async call.  When it is complete it calls _assembleTree2 passing in the Resource Metadata
		*/
  }

  _assembleTree2(resMetadata, shouldCreateRoot = true) {
    JSON.parse(resMetadata).forEach(
      function (topic) {
        this._assignTitles(topic, this._jsMap);
      }.bind(this)
    );
    if (shouldCreateRoot) {
      this._createRootNode();
    }
    this.loadLockStatus();
    //this._jsDitmapReady()
  }

  _createRootNode() {
    var rootNode = new jsMapElement();
    rootNode.id = "rootnode";
    rootNode.title = this._bookTitle; //should be title of rootMap
    rootNode.title = this._rootDitaMapTitle;
    rootNode.mapName = this._rootDitaMapId;
    rootNode.href = this._rootDitaMapId;
    rootNode.type = "rootmap";
    rootNode.icon = "ditamap";
    rootNode.mapPath = [];
    rootNode.domElement = this._domList[this._rootDitaMapId].documentElement;
    rootNode.rootElementName = this._getRootElementName(
      this._domList[this._rootDitaMapId]
    );
    rootNode.keyscope =
      this._domList[this._rootDitaMapId].documentElement.getAttribute(
        "keyscope"
      );
    rootNode.nodes = this._jsMap.nodes;
    this._jsMap = rootNode;
  }

  loadLockStatus() {
    for (var resLblId in this._resList) {
      this._getResMetaForMap(resLblId);
    }
  }

  _getResMetaForMap(mapResLblId) {
    var prom;
    this._httpGetResMetaForMapCounter++;
    prom = this._getResource(mapResLblId);
    prom.then(
      function (details) {
        var objDetails = JSON.parse(details);
        this._resList[objDetails[0].resLblId] = objDetails[0];
        this._httpGetResMetaForMapCounter--;
        if (this._httpGetResMetaForMapCounter == 0) {
          this._setStatuses();
          this._jsDitmapReady();
        }
      }.bind(this),
      function (details) {
        this._httpGetResMetaForMapCounter--;
        if (this._httpGetResMetaForMapCounter == 0) {
          this._setStatuses();
          this._jsDitmapReady();
        }
      }.bind(this)
    );
  }

  _validTreeNode(el) {
    if (el) {
      switch (el.nodeName.toLowerCase()) {
        case "topicref":
          return true;
          break;
        case "topichead":
          return true;
          break;
        case "reltable":
          return false;
          break;
        default:
          return true;
          break;
      }
    } else {
      return false;
    }
  }

  // --- Refactored buildTree ---

  /**
   * Builds a js Tree from a ditamap
   * @param {HTMLElement} root
   * @param {jsMapElement} parent
   */
  _makeJsTree(root, parent = null) {
    let tree = parent ? parent : new jsMapElement();
    let startingMapName = parent ? parent.href : this._getMapName(root);
    let Q = [[root, startingMapName, tree]];
    while (Array.isArray(Q) && Q.length > 0) {
      let [element, mapName, tree] = Q.shift(); // HTML / XML element, name of map, jsMap pointer
      let docEl = this._getHtmlElement(element);

      for (let child of docEl.children) {
        if (this._isSubMap(child)) {
          let newNode = this._processSubMap(child, mapName, tree);
          let newMapName = this._getMapName(child);
          tree.nodes.push(newNode);
          let currentNodeIndex = tree.nodes.length - 1;
          Q.push([
            this._domList[newMapName],
            newMapName,
            tree.nodes[currentNodeIndex],
          ]);
          this._domElements.push(newNode);
        } else if (this._isContentNode(child)) {
          let newNode = this._processContentNode(child, mapName, tree);
          if (child.children) {
            tree.nodes.push(newNode);
            let currentNodeIndex = tree.nodes.length - 1;
            Q.push([child, mapName, tree.nodes[currentNodeIndex]]);
          }
          this._domElements.push(newNode);
        }
      }
    }
    return tree;
  }

  _getRootElementName(element) {
    let htmlEl = this._getHtmlElement(element);
    return htmlEl.tagName;
  }

  _getHtmlElement(element) {
    if (element instanceof Document) {
      return element.documentElement;
    }
    return element;
  }

  _getMapName(element) {
    if (element instanceof Document) {
      let rootEl = element.documentElement;
      if (rootEl.tagName.toLowerCase() === "bookmap") {
        return this._rootDitaMapId;
      }
      return "";
    }

    if (element.getAttribute("href")) {
      return element.getAttribute("href");
    }
  }

  /**
   * @param {HTMLElement} child - current element being processed from xml tree
   * @param {String} map
   * @param {jsMapElement} parent - parent node in js tree
   */
  _processSubMap(child, map, parent) {
    if (this._options.renderSubMaps) {
      let newMapName = child.getAttribute("href");
      let mapXml = this._domList[newMapName]; // point to submap xml
      this._resList[newMapName] = {};

      let newNode = new jsMapElement();
      newNode.intId = this._id++;
      newNode.id = "node" + newNode.intId;
      newNode.type = "SubMap";
      newNode.href = newMapName;
      newNode.mapName = map;
      newNode.mapPath = parent.mapPath.concat([map]);
      newNode.icon = "ditamap";
      newNode.rootElementName = mapXml.documentElement.tagName;
      newNode.domElement = mapXml.documentElement;
      newNode.embeddedElement = child;
      newNode.title = this._removeXmlNoise(this._getSubmapTitle(mapXml));
      newNode.keyscope = mapXml.documentElement.getAttribute("keyscope");
      //zTree breaks if a domObject is in the tree.  So clone me and add to lookup arrary of dom elements
      // let nodeCopy = structuredClone(newNode);
      // nodeCopy.domElement = mapXml.documentElement;
      return newNode;
    }
  }

  /**
   * @param {HTMLElement} child - current element being processed from xml tree
   * @param {String} map
   * @param {jsMapElement} parent - parent node is js tree
   */
  _processContentNode(child, map, parent) {
    let newNode = new jsMapElement();
    newNode.intId = this._id++;
    newNode.id = "node" + newNode.intId;
    newNode.type = child.nodeName;
    newNode.keyscope = child.getAttribute("keyscope");
    newNode.mapPath =
      parent.mapPath.at(-1) === map
        ? parent.mapPath
        : parent.mapPath.concat([map]);
    newNode.mapName = map;
    newNode.rootElementName = this._getRootElementName(child);
    this._setContentData(newNode, child);
    //zTree breaks if a domObject is in the tree.  So clone me and add to lookup arrary of dom elements
    let nodeCopy = structuredClone(newNode);
    nodeCopy.domElement = child;
    nodeCopy.embeddedElement = child;
    return nodeCopy;
  }

  _removeXmlNoise(str) {
    if (str) return str.replace("<?xm-replace_text Main Book Title ?>", "");
  }

  _getSubmapTitle(node) {
    node = node instanceof Document ? node.documentElement : node;
    let mainbooktitleEl = node.querySelector("mainbooktitle");
    if (mainbooktitleEl) return mainbooktitleEl.innerHTML;

    let titleEl = node.querySelector("title");
    if (titleEl) return titleEl.innerText || titleEl.innerHTML;

    let titleAtt = node.getAttribute("title");
    if (titleAtt) return titleAtt;

    return node.tagName;
  }

  _setContentData(newNode, node) {
    let tmpHref = node.getAttribute("href");
    let navtitle = node.getAttribute("navtitle");
    if (tmpHref) {
      if (tmpHref.indexOf(" ") >= 0) {
        tmpHref = encodeURI(tmpHref);
      }
      newNode.href = tmpHref;
      newNode.icon = "topicref";
      //collect list of href's for title lookups
      this._titleHrefs.push(newNode.href);
    } else if (navtitle) {
      newNode.title = navtitle;
      newNode.icon = "topichead";
    } else {
      newNode.title = node.tagName;
      newNode.icon = "topichead";
    }
  }

  // --- End refactor ---

  _buildTree(element, treeNode, map) {
    // filter out reltable contents
    if (this._validTreeNode(element)) {
      //for(var child in element.children){
      for (var i = 0; i < element.children.length; i++) {
        var child = element.children[i];
        //var map = child.baseURI.substr(child.baseURI.indexOf('/GetTopicRenderXml')+18);
        if (map.substr(map.length - 1) == "?") {
          map = map.substr(0, map.length - 1);
        }

        if (this._isSubMap(child)) {
          if (this._options.renderSubMaps) {
            map = child.getAttribute("href");
            child = this._domList[map];
            this._resList[map] = {};
            //break point if not wanting to render subMaps
            var newNode = new jsMapElement();

            //test1
            if (child.children.length > 0) {
              newNode.nodes = [];
            }

            newNode.id = "node" + this._id++;
            if (child.documentElement) {
              newNode.title = child.documentElement.getAttribute("title");
              newNode.keyscope = child.documentElement.getAttribute("keyscope");
            } else {
              newNode.title = child.getAttribute("title");
              newNode.keyscope = child.getAttribute("keyscope");
            }
            if (!newNode.title) {
              if (child.children[0]) newNode.title = child.children[0].nodeName;
            }
            newNode.type = "SubMap";
            newNode.href = map;
            newNode.mapName = map;
            newNode.icon = "ditamap";

            if (!treeNode.nodes) {
              treeNode.nodes = [];
            }

            //newNode.keyscope = this._domList[this._rootDitaMapId].documentElement.getAttribute("keyscope");

            treeNode.nodes.push(newNode);
            //zTree breaks if a domObject is in the tree.  So clone me and add to lookup arrary of dom elements
            let me2 = structuredClone(newNode);
            me2.domElement = child;
            this._domElements.push(me2);
            //recurse into
            this._buildTree(
              child,
              treeNode.nodes[treeNode.nodes.length - 1],
              map
            );
          }

          if (this._options.onNewSubMap) {
            this._options.onNewSubMap(map);
          }
        } else {
          if (this._isContentNode(child)) {
            var newNode = new jsMapElement();
            //test1
            if (child.children.length > 0) {
              newNode.nodes = [];
            }
            newNode.id = "node" + this._id++;
            newNode.intId = this._id++;
            newNode.type = child.nodeName;
            newNode.keyscope = child.getAttribute("keyscope");

            var tmpHref = child.getAttribute("href");
            if (tmpHref) {
              if (tmpHref.indexOf(" ") >= 0) {
                tmpHref = encodeURI(tmpHref);
              }
              newNode.href = tmpHref;
              newNode.icon = "topicref";
              //collect list of href's for title lookups
              this._titleHrefs.push(newNode.href);
            } else {
              if (child.hasAttribute("navtitle")) {
                newNode.title = child.getAttribute("navtitle");
              } else {
                newNode.title = this._getNavTitle(child);
              }
              newNode.icon = "topichead";
            }

            newNode.mapName = map;
            // newNode.mapName = "/Content/submap_phones_xi1605_1_1.ditamap"

            //zTree breaks if a domObject is in the tree.  So clone me and add to lookup arrary of dom elements
            let me2 = structuredClone(newNode);
            me2.domElement = child;
            this._domElements.push(me2);

            if (!treeNode.nodes) {
              treeNode.nodes = [];
            }
            treeNode.nodes.push(newNode);
            this._buildTree(
              child,
              treeNode.nodes[treeNode.nodes.length - 1],
              map
            );
          } else {
            this._buildTree(child, treeNode, map);
          }
        }
      }
    }
  }

  // _getNavTitle(el) {
  //   for (var i = 0; i < el.children.length; i++) {
  //     if (el.children[i].nodeName == "topicmeta") {
  //       for (var k = 0; k < el.children[i].children.length; k++) {
  //         if (el.children[i].children[k].nodeName == "navtitle") {
  //           return el.children[i].children[k].innerHTML;
  //         }
  //       }
  //     }
  //   }
  //   return el.tagName;
  // }

  _fetchTitles() {
    if (this._titleHrefs.length > 0) {
      var xhttp = new XMLHttpRequest();
      xhttp.timeout = 5000;
      xhttp.onreadystatechange = function () {
        if (xhttp.readyState == 4 && xhttp.status == 200) {
          this._assembleTree2(xhttp.responseText);
        }
      }.bind(this);
      xhttp.open("POST", this._getResourceMetaEndpoint, true);
      xhttp.setRequestHeader(
        "Content-type",
        "application/x-www-form-urlencoded"
      );
      this._httpRequestsOut.push("titles"); //using an array of doc names instead of a counter for debugging puposes only.
      var params =
        "cdlResLblIds=" +
        this._titleHrefs.join() +
        "&" +
        this._resourceCredentials;
      xhttp.send(params);
    } else {
      this._createRootNode();
      this._jsDitmapReady();
    }
  }

  _assignTitles(topic, node) {
    if (node.href) {
      if (node.href == topic.resLblId) {
        node.title = topic.title.trim();
        //return;
      }
    }
    if (node.nodes) {
      node.nodes.forEach(
        function (childNode) {
          this._assignTitles(topic, childNode);
        }.bind(this)
      );
    }
  }

  _isBookMap(element) {
    return element.tagName === "bookmap";
  }

  _isSubMap(element) {
    const isBookmap = element.tagName === "bookmap";
    const isDitamap = element.tagName === "map";
    const hasDitamapFormat = element.getAttribute("format") == "ditamap";
    const hasHref = element.getAttribute("href");
    return isDitamap || isBookmap || (hasDitamapFormat && hasHref);
  }

  _isContentNode(element) {
    //add support for "mapgroup-d/topichead" with no
    var hasHref = false;
    var notExternalScope = false;
    var hasTopicRef = false;
    var hasTopicHead = false;
    var specialCBCase = false;
    var notKeydef = true;
    var notResourceOnly = true;

    if (element.classList) {
      hasTopicRef = element.classList.contains("map/topicref");
      hasTopicHead = element.classList.contains("mapgroup-d/topichead");
    }

    notResourceOnly = element.getAttribute("resource-only") != "resource-only";
    notKeydef = element.tagName.toLowerCase() != "keydef";

    hasHref = element.getAttribute("href") != null;

    if (
      element.nodeName == "part" ||
      element.nodeName == "topicref" ||
      element.nodeName == "chapter"
    ) {
      if (!hasHref) {
        specialCBCase = true;
      }
    }

    notExternalScope = element.getAttribute("scope") != "external";

    if (
      (hasTopicRef &&
        hasHref &&
        notExternalScope &&
        notResourceOnly & notKeydef) ||
      hasTopicHead ||
      specialCBCase
    ) {
      return true;
    } else {
      return false;
    }
  }

  _deleteEmptyNodeAttributes(el) {
    if (el.nodes) {
      if (el.nodes.length == 0) {
        delete el.nodes;
      } else {
        for (var i = 0; i < el.nodes.length; i++) {
          this._deleteEmptyNodeAttributes(el.nodes[i]);
        }
      }
    }
  }

  //CALL BACKS

  _jsDitmapReady() {
    //consider removing empty node collections here
    this._deleteEmptyNodeAttributes(this._jsMap);
    if (this._options.onjsDitmapReady) {
      this._options.onjsDitmapReady();
    }
  }

  _mapFilesCheckedOut(bSuccess) {
    if (bSuccess) {
      this.lockedByOtherUser = false;
      this.lockedByUser = true;
    }
    if (this._options.onLockedResponse) {
      this._options.onLockedResponse(bSuccess);
    }
  }

  _mapFilesUnlockedAll(bSuccess) {
    if (bSuccess) {
    }
    if (this._options.onUnlockedAllResponse) {
      this._options.onUnlockedAllResponse(bSuccess);
    }
  }

  _allMapFilesSaved(bSuccess) {
    if (this._options.onSavedAllResponse) {
      this._options.onSavedAllResponse(bSuccess);
    }
  }
}
