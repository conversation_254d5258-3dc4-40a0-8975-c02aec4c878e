var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property } from "lit/decorators.js";
let WexIcon = class WexIcon extends LitElement {
    constructor() {
        super(...arguments);
        this.icon = "";
        this.pointer = true;
    }
    render() {
        return html `
      <iron-icon
        icon="${this.icon ?? "more-horiz"}"
        class="${this.pointer ? "pointer" : ""} icon"
      ></iron-icon>
    `;
    }
};
WexIcon.styles = css `
    .icon {
      width: 1.5rem;
      height: 1.5rem;
      padding: 0.25rem;
      border-radius: 0.25rem;
    }
    .icon:hover {
      background-color: var(--clr-primary-ultra-light);
    }

    .pointer {
      cursor: pointer;
    }
  `;
__decorate([
    property({ type: String })
], WexIcon.prototype, "icon", void 0);
__decorate([
    property({ type: Boolean })
], WexIcon.prototype, "pointer", void 0);
WexIcon = __decorate([
    customElement("wex-icon")
], WexIcon);
export { WexIcon };
//# sourceMappingURL=icon.js.map