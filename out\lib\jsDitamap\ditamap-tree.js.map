{"version": 3, "file": "ditamap-tree.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-tree.ts"], "names": [], "mappings": "AAEA,MAAM,OAAO,WAAW;IAItB,YAAY,WAAmB,EAAE,KAAgC;QAFjE,SAAI,GAAuB,IAAI,CAAC;QAG9B,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;IAED,SAAS,CAAC,OAAe;QACvB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;IAC1B,CAAC;CACF", "sourcesContent": ["import { DitamapNode } from \"./ditamap-node\";\r\n\r\nexport class DitamapTree {\r\n  cache: Map<string, HTMLElement>;\r\n  root: DitamapNode | null = null;\r\n\r\n  constructor(rootMapName: string, files?: Map<string, HTMLElement>) {\r\n    this.cache = files ?? new Map();\r\n    this.root = this.buildTree(rootMapName);\r\n  }\r\n\r\n  buildTree(mapName: string): DitamapNode | null {\r\n    let mapEl = this.cache.get(mapName);\r\n    if (!mapEl) return null;\r\n  }\r\n}\r\n"]}