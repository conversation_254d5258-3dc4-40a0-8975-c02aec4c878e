{"version": 3, "file": "ditamap-tree.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-tree.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAE7C,MAAM,OAAO,WAAW;IActB,YAAY,WAAmB,EAAE,KAAgC;QATjE;;WAEG;QACH,SAAI,GAAuB,IAAI,CAAC;QAChC;;WAEG;QACH,gBAAW,GAAkB,IAAI,CAAC;QAGhC,IAAI,CAAC,SAAS,GAAG,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;QACvC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,SAAS,CAAC,OAAe,EAAE,MAA2B;QACpD,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,CAAC,OAAe,IAAyB,CAAC;IAEnD,mBAAmB,CAAC,IAAiB,EAAE,MAA0B,IAAG,CAAC;IAErE;;;OAGG;IACH,QAAQ,CAAC,QAAqB;QAC5B,IAAI,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;QAE3B,IAAI,CAAC,GAAgB,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;QACxC,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAe,CAAC;YAExD,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,QAAyC,EAAE,CAAC;gBACpE,IAAI,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;gBAEhC,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oBACtC,IAAI,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;oBACjE,IAAI,YAAY,GAAG,UAAU,IAAI,OAAO,CAAC;oBACzC,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;oBAC/C,CAAC;oBACD,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;oBACvC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChC,CAAC;gBAED,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;oBAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB;IACnB;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,OAAoB;QAC/B,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC;QAChD,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC;QAC5C,MAAM,gBAAgB,GAAG,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC;QACrE,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC7C,OAAO,SAAS,IAAI,SAAS,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,SAAS,CAAC,OAAoB;QACnC,OAAO,OAAO,CAAC,OAAO,KAAK,UAAU,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC;IACzE,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,OAAoB,EAAE,MAA0B,EAAE,OAAsB;QACvF,IAAI,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;QAC/B,OAAO,IAAI,CAAC;IAEd,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,OAAoB,EAAE,WAAoB;QAC1D,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAW,CAAC;QAChD,CAAC;QAED,IACE,WAAW;YACX,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,SAAS;gBAC1C,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,EAC1C,CAAC;YACD,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAoB;QACrC,IAAI,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAC7D,IAAI,eAAe;YAAE,OAAO,eAAe,CAAC,SAAS,CAAC;QAEtD,IAAI,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,OAAO;YAAE,OAAO,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;QAE3D,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,QAAQ;YAAE,OAAO,QAAQ,CAAC;QAE9B,OAAO,OAAO,CAAC,OAAO,CAAC;IACzB,CAAC;IAED,eAAe,CAAC,GAAW;QACzB,IAAI,GAAG;YAAE,OAAO,GAAG,CAAC,OAAO,CAAC,sCAAsC,EAAE,EAAE,CAAC,CAAC;QACxE,OAAO,GAAG,CAAC;IACb,CAAC;CACF", "sourcesContent": ["import { DitamapNode } from \"./ditamap-node\";\r\n\r\nexport class DitamapTree {\r\n  /**\r\n   * Map of all the stored ditamap xml\r\n   */\r\n  workspace: Map<string, HTMLElement>;\r\n  /**\r\n   * Underlying tree data structure\r\n   */\r\n  root: DitamapNode | null = null;\r\n  /**\r\n   * Name of the root map\r\n   */\r\n  rootMapName: string | null = null;\r\n\r\n  constructor(rootMapName: string, files?: Map<string, HTMLElement>) {\r\n    this.workspace = files ?? new Map();\r\n    this.root = this.buildTree(rootMapName)\r\n    this.rootMapName = rootMapName;\r\n  }\r\n\r\n  buildTree(mapName: string, parent?: DitamapNode | null): DitamapNode | null {\r\n    if (this.workspace.has(mapName)) {\r\n      return this._treeBfs(this.root);\r\n    }\r\n    return null;\r\n  }\r\n\r\n  _fetchMap(mapName: string): Promise<HTMLElement> {}\r\n\r\n  _attachNodeToParent(node: DitamapNode, parent: DitamapNode | null) {}\r\n\r\n  /**\r\n   * Builds json tree from xml\r\n   * @param rootHtml - parsed xml element\r\n   */\r\n  _treeBfs(rootHtml: HTMLElement): DitamapNode {\r\n    let tree = new DitamapNode();\r\n    tree.domElement = rootHtml;\r\n\r\n    let Q: QueueItem[] = [[rootHtml, tree]];\r\n    while (Array.isArray(Q) && Q.length > 0) {\r\n      let [element, parent, mapName] = Q.shift() as QueueItem;\r\n\r\n      for (let child of element.children as HTMLCollectionOf<HTMLElement>) {\r\n        let newNode = new DitamapNode();\r\n\r\n        if (DitamapTree.isMap(child)) {\r\n          this._decorateMapNode(child, newNode);\r\n          let newMapName = DitamapTree.getMapName(child, this.rootMapName);\r\n          let mapNameToUse = newMapName ?? mapName;\r\n          if (!newMapName) {\r\n            console.warn(\"No map name found for\", child);\r\n          }\r\n          Q.push([child, newNode, mapNameToUse]);\r\n          parent.children.push(newNode);\r\n        }\r\n\r\n        if (DitamapTree.isContent(child)) {\r\n          Q.push([child, newNode, mapName]);\r\n          parent.children.push(newNode);\r\n        }\r\n      }\r\n    }\r\n\r\n    return tree;\r\n  }\r\n\r\n  //---- Helpers ----\r\n  /**\r\n   * Checks if an html element is a ditamap\r\n   * @param element\r\n   */\r\n  static isMap(element: HTMLElement): boolean {\r\n    const isBookmap = element.tagName === \"bookmap\";\r\n    const isDitamap = element.tagName === \"map\";\r\n    const hasDitamapFormat = element.getAttribute(\"format\") == \"ditamap\";\r\n    const hasHref = element.getAttribute(\"href\");\r\n    return isDitamap || isBookmap || Boolean(hasDitamapFormat && hasHref);\r\n  }\r\n\r\n  /**\r\n   * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)\r\n   * @param element\r\n   */\r\n  static isContent(element: HTMLElement): boolean {\r\n    return element.tagName === \"topicref\" || element.tagName === \"chapter\";\r\n  }\r\n\r\n  /**\r\n   * Decorates a tree map node with information from the xml\r\n   * @param element - xml element\r\n   * @param parent - parent node in the json tree\r\n   */\r\n  _decorateMapNode(element: HTMLElement, parent: DitamapNode | null, mapName: string | null) {\r\n    let node = new DitamapNode();\r\n    node.href = element.getAttribute(\"href\");\r\n    node.containingMap = mapName;\r\n    node.title = this._removeXmlNoise(DitamapTree.getMapTitle(element));\r\n    node.domElement = element;\r\n    node.embeddedElement = element;\r\n    return node;\r\n\r\n  }\r\n\r\n  static getMapName(element: HTMLElement, rootMapName?: string) : string | undefined {\r\n    if (element.getAttribute(\"href\")) {\r\n      return element.getAttribute(\"href\") as string;\r\n    }\r\n\r\n    if (\r\n      rootMapName &&\r\n      (element.tagName.toLowerCase() === \"bookmap\" ||\r\n        element.tagName.toLowerCase() === \"map\")\r\n    ) {\r\n      return rootMapName;\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  static getMapTitle(element: HTMLElement) {\r\n    let mainbooktitleEl = element.querySelector(\"mainbooktitle\");\r\n    if (mainbooktitleEl) return mainbooktitleEl.innerHTML;\r\n\r\n    let titleEl = element.querySelector(\"title\");\r\n    if (titleEl) return titleEl.innerText || titleEl.innerHTML;\r\n\r\n    let titleAtt = element.getAttribute(\"title\");\r\n    if (titleAtt) return titleAtt;\r\n\r\n    return element.tagName;\r\n  }\r\n\r\n  _removeXmlNoise(str: string) {\r\n    if (str) return str.replace(\"<?xm-replace_text Main Book Title ?>\", \"\");\r\n    return str;\r\n  }\r\n}\r\n\r\ntype QueueItem = [HTMLElement, DitamapNode, (string | undefined)?];\r\n"]}