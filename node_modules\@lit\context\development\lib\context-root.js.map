{"version": 3, "file": "context-root.js", "sourceRoot": "", "sources": ["../../src/lib/context-root.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAkB,mBAAmB,EAAC,MAAM,4BAA4B,CAAC;AAGhF;;;;;;;GAOG;AACH,MAAM,OAAO,WAAW;IAAxB;QACU,2BAAsB,GAAG,IAAI,GAAG,EAgBrC,CAAC;QAuBI,sBAAiB,GAAG,CAC1B,KAAsD,EACtD,EAAE;YACF,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC1E,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBACrC,oDAAoD;gBACpD,OAAO;YACT,CAAC;YAED,wEAAwE;YACxE,qCAAqC;YACrC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAElD,wEAAwE;YACxE,MAAM,EAAC,QAAQ,EAAC,GAAG,kBAAkB,CAAC;YACtC,KAAK,MAAM,EAAC,UAAU,EAAE,WAAW,EAAC,IAAI,QAAQ,EAAE,CAAC;gBACjD,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnC,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;gBAErC,IAAI,OAAO,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACpD,qCAAqC;gBACvC,CAAC;qBAAM,CAAC;oBACN,wDAAwD;oBACxD,OAAO,CAAC,aAAa,CACnB,IAAI,mBAAmB,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAChE,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEM,qBAAgB,GAAG,CACzB,KAAqD,EACrD,EAAE;YACF,yDAAyD;YACzD,IAAI,KAAK,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;gBAC7B,OAAO;YACT,CAAC;YAED,iDAAiD;YACjD,uEAAuE;YACvE,4BAA4B;YAC5B,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,aAAa;gBAClC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAgB,CAAC;YAC1C,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;YAEhC,IAAI,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5E,IAAI,sBAAsB,KAAK,SAAS,EAAE,CAAC;gBACzC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAC7B,KAAK,CAAC,OAAO,EACb,CAAC,sBAAsB,GAAG;oBACxB,SAAS,EAAE,IAAI,OAAO,EAAE;oBACxB,QAAQ,EAAE,EAAE;iBACb,CAAC,CACH,CAAC;YACJ,CAAC;YAED,IAAI,SAAS,GAAG,sBAAsB,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC9D,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5B,sBAAsB,CAAC,SAAS,CAAC,GAAG,CAClC,OAAO,EACP,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC,CAC5B,CAAC;YACJ,CAAC;YAED,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,oDAAoD;gBACpD,OAAO;YACT,CAAC;YAED,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACxB,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACnC,UAAU,EAAE,IAAI,OAAO,CAAC,OAAO,CAAC;gBAChC,WAAW,EAAE,IAAI,OAAO,CAAC,QAAQ,CAAC;aACnC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAhGC;;;;;OAKG;IACH,MAAM,CAAC,OAAoB;QACzB,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACnE,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACvE,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,OAAoB;QACzB,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACtE,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC1E,CAAC;CA6EF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {Context} from './create-context.js';\nimport {ContextCallback, ContextRequestEvent} from './context-request-event.js';\nimport {ContextProviderEvent} from './controllers/context-provider.js';\n\n/**\n * A ContextRoot can be used to gather unsatisfied context requests and\n * re-dispatch them when new providers which satisfy matching context keys are\n * available.\n *\n * This allows providers to be added to a DOM tree, or upgraded, after the\n * consumers.\n */\nexport class ContextRoot {\n  private pendingContextRequests = new Map<\n    Context<unknown, unknown>,\n    {\n      // The WeakMap lets us detect if we're seen an element/callback pair yet,\n      // without needing to iterate the `requests` array\n      callbacks: WeakMap<HTMLElement, WeakSet<ContextCallback<unknown>>>;\n\n      // Requests lets us iterate over every element/callback that we need to\n      // replay context events for\n      // Both the element and callback must be stored in WeakRefs because the\n      // callback most likely has a strong ref to the element.\n      requests: Array<{\n        elementRef: WeakRef<HTMLElement>;\n        callbackRef: WeakRef<ContextCallback<unknown>>;\n      }>;\n    }\n  >();\n\n  /**\n   * Attach the ContextRoot to a given element to intercept `context-request` and\n   * `context-provider` events.\n   *\n   * @param element an element to add event listeners to\n   */\n  attach(element: HTMLElement): void {\n    element.addEventListener('context-request', this.onContextRequest);\n    element.addEventListener('context-provider', this.onContextProvider);\n  }\n\n  /**\n   * Removes the ContextRoot event listeners from a given element.\n   *\n   * @param element an element from which to remove event listeners\n   */\n  detach(element: HTMLElement): void {\n    element.removeEventListener('context-request', this.onContextRequest);\n    element.removeEventListener('context-provider', this.onContextProvider);\n  }\n\n  private onContextProvider = (\n    event: ContextProviderEvent<Context<unknown, unknown>>\n  ) => {\n    const pendingRequestData = this.pendingContextRequests.get(event.context);\n    if (pendingRequestData === undefined) {\n      // No pending requests for this context at this time\n      return;\n    }\n\n    // Clear our list. Any still unsatisfied requests will re-add themselves\n    // when we dispatch the events below.\n    this.pendingContextRequests.delete(event.context);\n\n    // Loop over all pending requests and re-dispatch them from their source\n    const {requests} = pendingRequestData;\n    for (const {elementRef, callbackRef} of requests) {\n      const element = elementRef.deref();\n      const callback = callbackRef.deref();\n\n      if (element === undefined || callback === undefined) {\n        // The element was GC'ed. Do nothing.\n      } else {\n        // Re-dispatch if we still have the element and callback\n        element.dispatchEvent(\n          new ContextRequestEvent(event.context, element, callback, true)\n        );\n      }\n    }\n  };\n\n  private onContextRequest = (\n    event: ContextRequestEvent<Context<unknown, unknown>>\n  ) => {\n    // Events that are not subscribing should not be buffered\n    if (event.subscribe !== true) {\n      return;\n    }\n\n    // Note, it's important to use the initial target\n    // since that's the requesting element and the event may be re-targeted\n    // to an outer host element.\n    const element = (event.contextTarget ??\n      event.composedPath()[0]) as HTMLElement;\n    const callback = event.callback;\n\n    let pendingContextRequests = this.pendingContextRequests.get(event.context);\n    if (pendingContextRequests === undefined) {\n      this.pendingContextRequests.set(\n        event.context,\n        (pendingContextRequests = {\n          callbacks: new WeakMap(),\n          requests: [],\n        })\n      );\n    }\n\n    let callbacks = pendingContextRequests.callbacks.get(element);\n    if (callbacks === undefined) {\n      pendingContextRequests.callbacks.set(\n        element,\n        (callbacks = new WeakSet())\n      );\n    }\n\n    if (callbacks.has(callback)) {\n      // We're already tracking this element/callback pair\n      return;\n    }\n\n    callbacks.add(callback);\n    pendingContextRequests.requests.push({\n      elementRef: new WeakRef(element),\n      callbackRef: new WeakRef(callback),\n    });\n  };\n}\n"]}