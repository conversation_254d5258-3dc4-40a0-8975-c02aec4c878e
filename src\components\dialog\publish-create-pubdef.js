// deprecated; publish-defs-create
import { LitElement, html, css } from "lit";
import { storeInstance as Store } from "store/index.js";
import pubsub from "pubsub-js";
import * as util from "lib/util.ts";

import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "@ui5/webcomponents/dist/Label.js";

class WexDialogCreatePubDef extends LitElement {
  static get properties() {
    return {
      _name: { type: String },
      _desc: { type: String },
      _errors: { type: Object },
    };
  }

  static get styles() {
    return css`
      * {
        box-sizing: border-box;
      }

      #dialog {
        width: min(90vw, 300px);
      }
      .dialog-content {
        display: flex;
        flex-direction: column;
        padding: 1rem;
      }
      .dialog-content * {
        color: var(--font-color);
      }
      .dialog-content > *:not(:last-child) {
        margin-bottom: 1rem;
      }
      .dialog-footer {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1rem;
      }
      .dialog-footer > .dialogbtns:not(:last-child) {
        margin-right: 1rem;
      }

      .dialog-item {
        display: flex;
        flex-direction: column;
      }
      .dialog-item > * {
        width: 100%;
      }
      .dialog-item > *:not(:last-child) {
        margin-bottom: 0.5rem;
      }

      .error-msg {
        font-style: italic;
        color: var(--clr-negative);
      }
    `;
  }

  constructor() {
    super();
    this._string = [];
    this._subscription = null;
    this._dialog = null;

    // properties
    this._name = "";
    this._desc = "";
    this._errors = {
      name: null,
      desc: null,
    };

    this._cbMsg = null;
  }

  connectedCallback() {
    super.connectedCallback();
    this._string = Store.state[Store.state.langCode];

    this._subscription = Store.subscribe((state) => {
      this.stateChange(state);
    });
    pubsub.subscribe("dialog-publish-create-pubdef", this._handler);
  }

  disconnectedCallback() {
    Store.unsubscribe(this._subscription);
  }

  firstUpdated() {
    this._dialog = this.shadowRoot.querySelector("#dialog");
  }

  stateChange(state) {
    this._string = state[state.langCode];
  }

  _handler = (msg, data) => {
    const { action, cbMsg } = data;
    if (action == "open") {
      this._cbMsg = cbMsg;
      // this._dialog.show();
      this._dialog.open = true;
    } else if (action == "close") {
      this._close();
    }
  };

  _close() {
    // clear properties
    this._name = "";
    this._desc = "";
    this._errors = {};
    this._cbMsg = null;
    // this._dialog.close();
    this._dialog.open = false;
  }

  _handleChange(type = null, e) {
    if (!type) return;
    const value = e.currentTarget.value;
    this[`_${type}`] = value;
    if (!value.length) {
      this._errors[`${type}`] = this._string["_error_empty_value"];
    } else if (!this._isValidString(value)) {
      this._errors[`${type}`] = this._string["_error_invalid_value"];
    } else {
      this._errors[`${type}`] = null;
    }
    this.requestUpdate();
  }

  _isValidString(str) {
    if (!str) return false;

    const rg1 = /^[^\\/:\*\?"<>+%@#&,=~'\|]+$/; // forbidden characters \ / : * ? " < > |
    const rg2 = /^[^\.]/; // cannot start with dot (.)
    const rg3 = /^(nul|prn|con|lpt[0-9]|com[0-9])(\.|$)/i; // forbidden folder names
    return rg1.test(str) && rg2.test(str) && !rg3.test(str);
  }

  _confirm() {
    if (!this._name.length)
      this._errors.name = this._string["_error_empty_value"];
    if (!this._desc.length)
      this._errors.desc = this._string["_error_empty_value"];
    if (!!this._errors.name || !!this._errors.desc) return this.requestUpdate();

    if (this._cbMsg) {
      const data = {
        name: this._name,
        desc: this._desc,
      };
      pubsub.publish(this._cbMsg, data);
    }
    this._close();
  }

  render() {
    const template = html`
      <ui5-dialog id="dialog" header-text="${this._string["_create_pubdef"]}">
        <div class="dialog-content">
          <div class="dialog-item">
            <ui5-label required>${this._string["_name"]}</ui5-label>
            <ui5-input
              value="${this._name}"
              @change="${this._handleChange.bind(this, "name")}"
            >
            </ui5-input>
            ${!!this._errors.name
              ? html` <span class="error-msg">${this._errors.name}</span> `
              : html``}
          </div>
          <div class="dialog-item">
            <ui5-label required>${this._string["_description"]}</ui5-label>
            <ui5-input
              value="${this._desc}"
              @change="${this._handleChange.bind(this, "desc")}"
            >
            </ui5-input>
            ${!!this._errors.desc
              ? html` <span class="error-msg">${this._errors.desc}</span> `
              : html``}
          </div>
        </div>

        <div class="dialog-footer">
          <ui5-button
            class="dialogbtns"
            data-type="create"
            design="Emphasized"
            @click="${this._confirm.bind(this)}"
          >
            ${this._string["_confirm"]}
          </ui5-button>
          <ui5-button
            class="dialogbtns"
            data-type="close"
            design="Emphasized"
            @click="${this._close.bind(this)}"
          >
            ${this._string["_cancel"]}
          </ui5-button>
        </div>
      </ui5-dialog>
    `;
    return template;
  }

  renderError() {
    this._errors.length
      ? html`
          <div class="error-msgs">
            ${this._errors.map((x) => html`<span>${x}</span>`)}
          </div>
        `
      : html``;
  }
}

customElements.define("wex-dialog-create-pubdef", WexDialogCreatePubDef);
