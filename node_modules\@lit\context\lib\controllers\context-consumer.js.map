{"version": 3, "file": "context-consumer.js", "sources": ["../../src/lib/controllers/context-consumer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {\n  ContextCallback,\n  ContextRequestEvent,\n} from '../context-request-event.js';\nimport type {Context, ContextType} from '../create-context.js';\nimport type {\n  Reactive<PERSON>ontroller,\n  ReactiveControllerHost,\n} from '@lit/reactive-element';\n\nexport interface Options<C extends Context<unknown, unknown>> {\n  context: C;\n  callback?: (value: ContextType<C>, dispose?: () => void) => void;\n  subscribe?: boolean;\n}\n\n/**\n * A ReactiveController which adds context consuming behavior to a custom\n * element by dispatching `context-request` events.\n *\n * When the host element is connected to the document it will emit a\n * `context-request` event with its context key. When the context request\n * is satisfied the controller will invoke the callback, if present, and\n * trigger a host update so it can respond to the new value.\n *\n * It will also call the dispose method given by the provider when the\n * host element is disconnected.\n */\nexport class ContextConsumer<\n  C extends Context<unknown, unknown>,\n  HostElement extends ReactiveC<PERSON>rollerHost & HTMLElement,\n> implements ReactiveController\n{\n  protected host: HostElement;\n  private context: C;\n  private callback?: (value: ContextType<C>, dispose?: () => void) => void;\n  private subscribe = false;\n\n  private provided = false;\n\n  value?: ContextType<C> = undefined;\n\n  constructor(host: HostElement, options: Options<C>);\n  /** @deprecated Use new ContextConsumer(host, options) */\n  constructor(\n    host: HostElement,\n    context: C,\n    callback?: (value: ContextType<C>, dispose?: () => void) => void,\n    subscribe?: boolean\n  );\n  constructor(\n    host: HostElement,\n    contextOrOptions: C | Options<C>,\n    callback?: (value: ContextType<C>, dispose?: () => void) => void,\n    subscribe?: boolean\n  ) {\n    this.host = host;\n    // This is a potentially fragile duck-type. It means a context object can't\n    // have a property name context and be used in positional argument form.\n    if ((contextOrOptions as Options<C>).context !== undefined) {\n      const options = contextOrOptions as Options<C>;\n      this.context = options.context;\n      this.callback = options.callback;\n      this.subscribe = options.subscribe ?? false;\n    } else {\n      this.context = contextOrOptions as C;\n      this.callback = callback;\n      this.subscribe = subscribe ?? false;\n    }\n    this.host.addController(this);\n  }\n\n  private unsubscribe?: () => void;\n\n  hostConnected(): void {\n    this.dispatchRequest();\n  }\n\n  hostDisconnected(): void {\n    if (this.unsubscribe) {\n      this.unsubscribe();\n      this.unsubscribe = undefined;\n    }\n  }\n\n  private dispatchRequest() {\n    this.host.dispatchEvent(\n      new ContextRequestEvent(\n        this.context,\n        this.host,\n        this._callback,\n        this.subscribe\n      )\n    );\n  }\n\n  // This function must have stable identity to properly dedupe in ContextRoot\n  // if this element connects multiple times.\n  private _callback: ContextCallback<ContextType<C>> = (value, unsubscribe) => {\n    // some providers will pass an unsubscribe function indicating they may provide future values\n    if (this.unsubscribe) {\n      // if the unsubscribe function changes this implies we have changed provider\n      if (this.unsubscribe !== unsubscribe) {\n        // cleanup the old provider\n        this.provided = false;\n        this.unsubscribe();\n      }\n      // if we don't support subscription, immediately unsubscribe\n      if (!this.subscribe) {\n        this.unsubscribe();\n      }\n    }\n\n    // store the value so that it can be retrieved from the controller\n    this.value = value;\n    // schedule an update in case this value is used in a template\n    this.host.requestUpdate();\n\n    // only invoke callback if we are either expecting updates or have not yet\n    // been provided a value\n    if (!this.provided || this.subscribe) {\n      this.provided = true;\n      if (this.callback) {\n        this.callback(value, unsubscribe);\n      }\n    }\n\n    this.unsubscribe = unsubscribe;\n  };\n}\n"], "names": ["ContextConsumer", "constructor", "host", "contextOrOptions", "callback", "subscribe", "this", "provided", "value", "undefined", "_callback", "unsubscribe", "requestUpdate", "context", "options", "addController", "hostConnected", "dispatchRequest", "hostDisconnected", "dispatchEvent", "ContextRequestEvent"], "mappings": ";;;;;SAkCaA,EAsBX,WAAAC,CACEC,EACAC,EACAC,EACAC,GAKA,GAvBMC,KAASD,WAAG,EAEZC,KAAQC,UAAG,EAEnBD,KAAKE,WAAoBC,EA0DjBH,KAAAI,EAA6C,CAACF,EAAOG,KAEvDL,KAAKK,cAEHL,KAAKK,cAAgBA,IAEvBL,KAAKC,UAAW,EAChBD,KAAKK,eAGFL,KAAKD,WACRC,KAAKK,eAKTL,KAAKE,MAAQA,EAEbF,KAAKJ,KAAKU,gBAILN,KAAKC,WAAYD,KAAKD,YACzBC,KAAKC,UAAW,EACZD,KAAKF,UACPE,KAAKF,SAASI,EAAOG,IAIzBL,KAAKK,YAAcA,CAAW,EAvE9BL,KAAKJ,KAAOA,OAGqCO,IAA5CN,EAAgCU,QAAuB,CAC1D,MAAMC,EAAUX,EAChBG,KAAKO,QAAUC,EAAQD,QACvBP,KAAKF,SAAWU,EAAQV,SACxBE,KAAKD,UAAYS,EAAQT,YAAa,CACvC,MACCC,KAAKO,QAAUV,EACfG,KAAKF,SAAWA,EAChBE,KAAKD,UAAYA,IAAa,EAEhCC,KAAKJ,KAAKa,cAAcT,KACzB,CAID,aAAAU,GACEV,KAAKW,iBACN,CAED,gBAAAC,GACMZ,KAAKK,cACPL,KAAKK,cACLL,KAAKK,iBAAcF,EAEtB,CAEO,eAAAQ,GACNX,KAAKJ,KAAKiB,cACR,IAAIC,EACFd,KAAKO,QACLP,KAAKJ,KACLI,KAAKI,EACLJ,KAAKD,WAGV"}