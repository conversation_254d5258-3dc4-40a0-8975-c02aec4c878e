{"version": 3, "file": "ditamap_test.js", "sourceRoot": "", "sources": ["../../../src/test/ditamap-tree/ditamap_test.ts"], "names": [], "mappings": "AAaA,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAC5D,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;IAC3C,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAC5B,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C,gBAAgB,EAChB,iBAAiB,CAClB,CAAC;QACF,IAAI,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC;QACnC,IAAI,QAAQ,GAAG,IAAI,GAAG,EAAuB,CAAC;QAC9C,QAAQ,CAAC,GAAG,CAAC,4CAA4C,EAAE,GAAG,CAAC,CAAC;QAChE,IAAI,IAAI,GAAG,IAAI,WAAW,CACxB,4CAA4C,EAC5C,QAAQ,CACT,CAAC;QAEF,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE;YACnB,MAAM;YACN,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,SAAS;SACV,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACnD,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;0CACoC,EACpC,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;QACrD,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;qDAC+C,EAC/C,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IACjE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC1B,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C,2EAA2E,EAC3E,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iBAAiB,EAAE,GAAG,EAAE;QACzB,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C,8FAA8F,EAC9F,iBAAiB,CAClB,CAAC;QACF,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IACrE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;QAC1D,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC,eAAe,CAC5C;;4CAEsC,EACtC,iBAAiB,CAClB,CAAC;QACF,MAAM,CACJ,WAAW,CAAC,UAAU,CACpB,QAAQ,CAAC,eAAe,EACxB,4CAA4C,CAC7C,CACF,CAAC,EAAE,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,EAAE,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC;AAEH,MAAM,SAAS,GAAG,CAChB,OAA2B,EAC3B,MAAmB,EACnB,EAAE;IACF,IAAI,CAAC,OAAO;QAAE,OAAO,IAAI,CAAC;IAE1B,IAAI,MAAM,GAAM,EAAO,CAAC;IAExB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACvB,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,SAAS;YAAE,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAC5E,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC", "sourcesContent": ["/**\r\n * The ditamap editor involves many parts\r\n *  1. Storing a hashmap of the files\r\n *  2. Building a JSON tree from the xml\r\n *  3. Displaying the tree\r\n *  4. Editing the tree\r\n *  5. Saving the tree\r\n *  6. Validating the tree\r\n */\r\nimport {\r\n  DitamapNode,\r\n  DitamapNodeStructure,\r\n} from \"../../lib/jsDitamap/ditamap-node\";\r\nimport { DitamapTree } from \"../../lib/jsDitamap/ditamap-tree\";\r\nimport { PHONES_TREE } from \"./data/json-trees\";\r\nimport { PHONES_1_BOOKMAP } from \"./files/phones_1_bookmap\";\r\nimport { expect } from \"@open-wc/testing\";\r\n\r\ndescribe(\"Building JSON tree from xml\", () => {\r\n  it(\"builds phones tree\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      PHONES_1_BOOKMAP,\r\n      \"application/xml\"\r\n    );\r\n    let xml = document.documentElement;\r\n    let registry = new Map<string, HTMLElement>();\r\n    registry.set(\"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\", xml);\r\n    let tree = new DitamapTree(\r\n      \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\",\r\n      registry\r\n    );\r\n\r\n    expect(PHONES_TREE).to.deep.equal(\r\n      pruneTree(tree.root, [\r\n        \"type\",\r\n        \"title\",\r\n        \"href\",\r\n        \"mapName\",\r\n        \"mapPath\",\r\n        \"tagName\",\r\n      ])\r\n    );\r\n  });\r\n  it.skip(\"builds a tree from a simple bookmap\");\r\n});\r\n\r\ndescribe(\"Map reference\", () => {\r\n  it(\"part with href and format=ditamap => true\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<part href=\"/Content/submap_phones_xi1609_1_1.ditamap\" format=\"ditamap\"\r\n\t\tclass=\"- map/topicref bookmap/part \" />`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapTree.isMap(document.documentElement)).to.be.true;\r\n  });\r\n\r\n  it(\"mapref with href and format=ditamap => true\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<mapref format=\"ditamap\" href=\"/Content/subject_scheme_Atts_sample_xi1608_1_1.ditamap\"\r\n        class=\"+ map/topicref mapgroup-d/mapref \" />`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapTree.isMap(document.documentElement)).to.be.true;\r\n  });\r\n});\r\n\r\ndescribe(\"Content reference\", () => {\r\n  it(\"topicref => true\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<topicref href=\"/Content/begin_xi1612_1_1.xml\" class=\"- map/topicref \" />`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapTree.isContent(document.documentElement)).to.be.true;\r\n  });\r\n\r\n  it(\"chapter => true\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<chapter href=\"/Content/Introduction_xi1674_1_1.xml\" class=\"- map/topicref bookmap/chapter\">`,\r\n      \"application/xml\"\r\n    );\r\n    expect(DitamapTree.isContent(document.documentElement)).to.be.true;\r\n  });\r\n});\r\n\r\ndescribe(\"Get map name (href)\", () => {\r\n  it(\"If the element is a bookmap, use the rootMapName\", () => {\r\n    let document = new DOMParser().parseFromString(\r\n      `<bookmap xmlns:ditaarch=\"http://dita.oasis-open.org/architecture/2005/\"\r\n        id=\"xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0\" xml:lang=\"en-US\"\r\n        class=\"- map/map bookmap/bookmap \">`,\r\n      \"application/xml\"\r\n    );\r\n    expect(\r\n      DitamapTree.getMapName(\r\n        document.documentElement,\r\n        \"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\"\r\n      )\r\n    ).to.equal(\"/Content/Phone1_Bookmap_xi1577_1_1.ditamap\");\r\n  });\r\n});\r\n\r\ndescribe(\"Bookmap quirks\", () => {\r\n  it.skip(\"doesn't add a part to a part\");\r\n});\r\n\r\nconst pruneTree = <T extends DitamapNodeStructure>(\r\n  current: DitamapNode | null,\r\n  fields: (keyof T)[]\r\n) => {\r\n  if (!current) return null;\r\n\r\n  let pruned: T = {} as T;\r\n\r\n  fields.forEach((field) => {\r\n    if (current[field] != undefined) pruned[field] = current[field];\r\n  });\r\n\r\n  pruned.children = current.children.map((child) => pruneTree(child, fields));\r\n  return pruned;\r\n};\r\n"]}