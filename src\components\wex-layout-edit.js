import { LitElement, html, css } from "lit";
import { render } from "lit/html.js";
import { storeInstance as Store } from "store/index.js";
import pubsub from "pubsub-js";
import * as util from "lib/util.ts";
import * as wexlib from "lib/wexlib.js";

import "./wex-editor-filepackage.ts";
import "./editor/controls.js";
import "./wex-fileinfo.js";
import "./wex-editor-mru.ts";
import "./wex-navigators.js";

import "@vaadin/vaadin-split-layout/vaadin-split-layout.js";
import "@vaadin/vaadin-tabs/vaadin-tabs.js";
// import "@ui5/webcomponents/dist/assets.js";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "@ui5/webcomponents/dist/Label.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/Icon.js";
import "@vaadin/vaadin-icons/vaadin-icons.js";
import "@ui5/webcomponents/dist/Label.js";

export class WexLayoutEdit extends LitElement {
  static get properties() {
    return {
      activeTab: { type: String },
      showEditorPage: { type: Boolean },
      editors: { type: Array },
      queryParams: { type: Object },
      isClaimed: { type: Boolean },
      selectedTask: { type: Object },
      taskList: { type: Array },
    };
  }

  constructor() {
    super();
    this._subscription = null;

    this.editorSelectList = [];
    this.navigatorSelectList = [];
    this.claimedTasks = [];

    this.activeTab = "";
    this.showEditorPage = false;
    this.editors = [];
    this.queryParams = new URLSearchParams();
    this.wexUser = {};
    this.taskList = [];
  }

  connectedCallback() {
    super.connectedCallback();
    this.state = Store.state;
    this.string = Store.state[Store.state.langCode];
    this.showEditorPage = this.state.show_editor_page;
    this.activeTab = this.state.editor_side_active_tab;
    this.editors = this.state.editors;
    this.taskList = this.state.task_task_list;
    this.selectedTask = this.state.editor_selected_task;
    this._subscription = Store.subscribe((state) => {
      /*WexEditLayout*/
      this.stateChange(state);
    });
    this.wexUser = Store.state.wex_user;

    this.parseQueryString();
    window.document.addEventListener(
      "oxywex-open-link",
      this._handleOpenLink.bind(this)
    );
  }

  disconnectedCallback() {
    Store.unsubscribe(this._subscription);
    window.document.removeEventListener(
      "oxywex-open-link",
      this._handleOpenLink.bind(this)
    );
  }

  firstUpdated() {
    if (this.state.editors.length > 0) {
      this._loadEditorIframe(this.state.editors.length - 1);
    }
    Store.dispatch("getActiveTasks");
    this.taskPane = this.shadowRoot.querySelector("#taskpane");
  }

  updated(changedProperties) {
    if (changedProperties.has("taskList")) {
      this.updateSelectedTask();
    }

    if (changedProperties.has("selectedTask")) {
      this.isClaimed = this.selectedTask ? this.selectedTask.isClaimed : false;
    }
  }

  async getFileData(resourceId) {
    if (Number.parseInt(resourceId)) {
      return await wexlib.requestFileInfoBySeqId(resourceId);
    } else {
      return await wexlib.requestFileInfoById(resourceId);
    }
  }

  getEditorModeFromCapability(capability) {
    switch (capability) {
      case "Author":
      case "Contribute":
        return "author";
      case "Review":
        return "review";
      default:
        return "readonly";
    }
  }

  getLockModeFromAllowed(allowedLockmodes, currentLockMode) {
    if (
      currentLockMode &&
      allowedLockmodes.some((mode) => mode == currentLockMode)
    ) {
      return currentLockMode;
    }

    if (allowedLockmodes.some((mode) => mode == "concurrent")) {
      return "concurrent";
    }
    return "exclusive";
  }

  async parseQueryString() {
    this.queryParams = new URLSearchParams(window.location.search);
    let editMode = this.queryParams.get("editMode");
    let lockMode = this.queryParams.get("editAccess") || "concurrent";
    let processId = this.queryParams.get("processInstId");
    let resourceId = this.queryParams.get("resourceId");

    if (!resourceId) {
      return;
    }

    // -- Need to handle if file not found
    let file = await this.getFileData(resourceId);

    // non-author cannot access files outside of task; note no file capability => no user capability
    if (!file.capability) {
      return util.notifyUser(
        "warn",
        `You do not have the capability to view ${file.name}`
      );
    }

    editMode = this.getEditorModeFromCapability(file.capability.capability);

    if (file.capability.workflowBased) {
      if (!processId) {
        return util.notifyUser(
          "warn",
          `You need to claim the task containing: ${file.name}`
        );
      }

      let processes = await wexlib.getProcessInstances(true, false);
      Store.dispatch("processInstances", processes);

      let currentProcess = processes.find(
        (candidate) => candidate.processInstId == processId
      );

      if (!currentProcess) {
        return util.notifyUser("warn", `Could not find process: ${processId}`);
      }

      this.activeTab = "tasks";

      Store.dispatch("setState", {
        property: "editor_side_active_tab",
        value: this.activeTab,
      });

      Store.dispatch("setState", {
        property: "editor_selected_task",
        value: currentProcess.activeParallelTasks[0],
      });

      Store.dispatch("setState", {
        property: "editor_selected_file",
        value: file,
      });

      if (!currentProcess.activeParallelTasks[0].isClaimed) {
        return util.notifyUser(
          "warn",
          `You need to claim: ${currentProcess.activeParallelTasks[0].taskDefName}`
        );
      }

      const allowedLockmodes = await wexlib.reqGetProperty({
        property: "wex.allowed.lockmodes",
        taskId: currentProcess.activeParallelTasks[0].taskId,
      });

      if (allowedLockmodes) {
        lockMode = this.getLockModeFromAllowed(allowedLockmodes, lockMode);
      }
    }

    switch (file.capability.capability.toLowerCase()) {
      case "review":
        editMode = editMode === "author" ? "review" : editMode;
        break;
      case "default":
        return util.notifyUser(
          "warn",
          `You do not have the capability to view ${file.name}`
        );
    }

    // Open File
    Store.dispatch("handleEditFile", {
      file: file,
      modes: { editMode, lockMode },
    });
  }

  // handle links within the document to open within wex instead of a new oxygen web author window
  async _handleOpenLink(e) {
    const { id } = e.detail;
    if (!id) return;

    try {
      const res = await wexlib.requestFileInfoById(id);
      // TEMP -- deprecated
      // res.fromMap = this.file.isDitabase;
      Store.dispatch("checkEditFile", res);
    } catch (err) {
      console.error(err);
    } finally {
      pubsub.publish("requestin");
    }
  }

  stateChange(state) {
    this.state = state;
    this.string = this.state[this.state.langCode];
    this.showEditorPage = this.state.show_editor_page;
    this.activeTab = this.state.editor_side_active_tab;
    this.editors = this.state.editors;
    this.wexUser = Store.state.wex_user;
    this.taskList = this.state.task_task_list;
    this.selectedTask = this.state.editor_selected_task;
    this._setSideTabVisibility();
  }

  // setActiveTask() {
  //   this.selectedTask = this.taskList.find(
  //     (el) =>
  //       el.taskInstId == this.state.editor_selected_task.taskInstId &&
  //       el.isClaimed != this.state.editor_selected_task.isClaimed
  //   );
  //   if (this.selectedTask) {
  //     Store.dispatch("setState", {
  //       property: "editor_selected_task",
  //       value: this.selectedTask,
  //     });
  //   }
  // }

  // Sync task data - e.g isClaimed
  updateSelectedTask() {
    if (!this.selectedTask) {
      // console.log("No selected task");
      return;
    }

    if (!Array.isArray(this.taskList) || this.taskList.length < 1) {
      // console.log("No tasks");
      return;
    }

    let currentTask = this.taskList.find(
      (task) => task.taskInstId == this.selectedTask?.taskInstId
    );

    if (!currentTask) {
      Store.dispatch("setState", {
        property: "editor_selected_task",
        value: this.taskList[0],
      });
      return;
    }

    Store.dispatch("setState", {
      property: "editor_selected_task",
      value: currentTask,
    });
  }

  _setSideTabVisibility() {
    if (this.taskPane) {
      if (this.activeTab == "tasks") {
        this.taskPane.classList.add("show");
        this.taskPane.classList.remove("hide");
      } else {
        this.taskPane.classList.add("hide");
        this.taskPane.classList.remove("show");
      }
    }
  }

  _loadEditorIframe(idx) {
    for (var i = 0; i < this.state.editors.length; i++) {
      this.state.editors[i].editor.classList.add("hide");
      this.state.editors[i].editor.classList.remove("show");
    }
    this.state.editors[idx].editor.classList.remove("hide");
    this.state.editors[idx].editor.classList.add("show");
    // this.myRender();
  }

  _hideEditor() {
    if (this.state.editors.length > 0) {
      if (!this.dragstart) {
        pubsub.publish("hideactiveeditor", {});
      }
      this.dragstart = true;
    }
  }

  _taskSelected(taskInstId) {
    return this.selectedTask.taskInstId == taskInstId;
  }

  _openFile() {
    if (this.state.wex_user.globalCapability != "Default") {
      Store.dispatch("setState", {
        property: "select_file_dialog_open",
        value: { callback: this._openFileAction.bind(this) },
      });
    }
  }
  _openFileAction(file) {
    Store.dispatch("checkEditFile", file);
    Store.dispatch("setState", {
      property: "select_file_dialog_open",
      value: null,
    });
  }

  _activeTabContent() {
    switch (this.activeTab) {
      case "tasks":
        return html`<div id="taskpane">${this._taskPaneTemplate()}</div>`;
        break;
      case "fileinfo":
        //return html`<div id='infopane'>${this._fileinfoPaneTemplate()}</div>`;
        return html`<wex-fileinfo></wex-fileinfo>`;
        break;
      case "mru":
        return html`<wex-editor-mru></wex-editor-mru>`;
        break;
      case "ditamaps":
        if (this.lastTab != "ditamaps") {
          var tmp = this.shadowRoot.querySelector("#ditamaps");
          if (tmp) {
            tmp.click();
          }
        }
        break;
    }
    this.lastTab = this.activeTab;
  }

  //${this._displayFilePackage()}
  _taskPaneTemplate() {
    if (this.taskList.length > 0) {
      return html`<div id="taskselectorcontainer">
                            ${this._taskSelectorTemplate()}
                        </div>
                        <div id='taskdetailscontainer'</div>
                            <div id="taskdetails" class="sidecomponent">
                                ${this._taskDetailsTemplate()}
                            </div>
                        </div>
                        <div id="filepackagecontainer">
                            <wex-editor-filepackage></wex-editor-filepackage>
                        </div>`;
    } else {
      return html`
        <div id="blankheader">&nbsp;</div>
        <div id="blanksubheader">&nbsp;</div>
        <div>
          <div class="noitem">${this.string["_notasks"]}</div>
        </div>
      `;
    }
  }

  _tabSelected(e) {
    let newTab = e.currentTarget.id;
    if (newTab === "close") {
      this.activeTab = "";
    } else {
      this.activeTab = newTab;
    }
    Store.dispatch("setState", {
      property: "editor_side_active_tab",
      value: this.activeTab,
    });
  }

  _hideEditorTab() {}

  _displayFilePackage() {
    if (this.taskList.length > 0) {
      return html`<wex-editor-filepackage></wex-editor-filepackage>`;
    }
  }

  _taskSelectorTemplate() {
    return html` <ui5-select
      id="claimedtasklist"
      class="headeritem ui5-content-density-compact"
      @change="${this._selectTask.bind(this)}"
      title="${this.string}"
    >
      ${this.taskList.map(
        (task) =>
          html`<ui5-option
            ?selected="${this.selectedTask.taskInstId == task.taskInstId}"
            .task="${task}"
            >${task.processInstId} - ${task.taskDefName}
            ${task.processInstDescription
              ? `- ${task.processInstDescription}`
              : null}
          </ui5-option>`
      )}
    </ui5-select>`;
  }

  _taskDetailsTemplate() {
    if (this.taskList.length > 0) {
      if (this.selectedTask) {
        return html`
          <div id="claimedcontrol">
            <ui5-button @click="${this._claimToggle.bind(this)}">
              ${this.isClaimed
                ? this.string["_unclaimtask"]
                : this.string["_claimtask"]}
            </ui5-button>
          </div>

          <ui5-button
            design="Emphasized"
            ?disabled="${!this.isClaimed}"
            @click="${this._finishTask.bind(this)}"
          >
            ${this.string["_finishtask"]}
          </ui5-button>

          <ui5-button
            id="taskdetailicon"
            title="${this.string["_viewtaskdetails"]}"
            @click="${this._taskdetails.bind(this)}"
          >
            <iron-icon icon="vaadin:info-circle-o"></iron-icon>
          </ui5-button>
        `;
      }
    }
  }

  _finishTask() {
    wexlib._finishTaskAction(this.selectedTask);
  }

  _claimToggle(e) {
    wexlib._claimToggle(this.selectedTask);
  }

  _selectTask(e) {
    Store.dispatch("setState", {
      property: "editor_selected_task",
      value: e.detail.selectedOption.task,
    });
  }

  _taskdetails(e) {
    Store.dispatch("setState", {
      property: "task_properties_dialog",
      value: this.selectedTask,
    });
  }

  /* Templates */
  render() {
    const globalCap = this.state.wex_user
      ? this.state.wex_user.globalCapability
      : null;
    const sidebarWidthPercentage = this.activeTab ? "1 1 25%;" : "1 1 0";
    const sidebarMaxWidth = this.activeTab ? "30rem" : 0;
    const navigatorsDisplay =
      this.activeTab && this.activeTab == "ditamaps" ? "block" : "none";
    return html`
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        #sidelayout {
          /* width: ${
            this.state.global_screen_specs.edit_leftSideWidthPx
          }px !important; */
          display: flex;
          height: 100%;
          justify-content: start;
          overflow: hidden;
        }
        #sidebar-content {
          width: calc(100% - 3.125rem);
        }

        #mainlayout {
          display: ${this.showEditorPage ? "flex" : "none"};
          height: calc(100vh - 30px);
        
        }
        #left {
          min-width: 3.125rem;
        }
        #right {
          display: flex;
          flex-direction: column;
          overflow: auto;
        }
        #right iframe {
          width: 99%;
          /* height: ${this.state.global_screen_specs.layoutHeight - 9}px; */
        }
        #editorlist {
          --_ui5_input_width: 25rem;
        }
        .show {
          display: block;
        }
        .hide {
          display: none;
        }
        #taskpane {
          /*background-color: #fff;*/
        }
        #taskselectorcontainer {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          background-color: var(--subheader-color);
          padding: var(--spacing-md);
        }

        #claimedtasklist {
          width: 100%;
        }

        #taskdetailscontainer {
          display: flex;
          background-color: var(--subheader-color);
          justify-content: center;
        }
        #taskdetails {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          gap: var(--spacing-sm);
        }
        #claimedcontrol {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          cursor: pointer;
        }
        ui5-checkbox[checked] {
        }

        #noclaimedtasksmsg {
          font-style: italic;
        }
        #editorlistlabel,
        #tasklistlabel,
        #navigatorlistlabel {
          color: var(--on-primary-color);
        }

        .nav-tabs {
          /*background: url(/wex/background-gradient-vertical.svg);*/
          background-size: cover;
          background-color: #f2f2f2;
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
          -webkit-box-align: start;
          -ms-flex-align: start;
          align-items: flex-start;
        }
        .nav-tabs > div {
          -webkit-writing-mode: vertical-lr;
          -ms-writing-mode: tb-lr;
          writing-mode: vertical-lr;
          -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
          -webkit-transform-origin: center center;
          transform-origin: center center;
          list-style-type: none;
        }
        .nav-tabs > div {
          display: block;
          padding: 0.6rem 1rem;
          color: var(--sapContent_LabelColor);
          cursor: pointer;
          --iron-icon-height: 1rem;
          --iron-icon-width: 1rem;
          letter-spacing: 1px;
          text-transform: uppercase;
        }
        .nav-tabs > div:hover {
          color: #000;
        }
        .nav-tabs > div.active {
          color: var(--_ui5_tc_headeritem_text_selected_color);
          background-color: var(--clr-bg);
          border: 1px solid #e4eaee;
        }
        .noscroll {
          scroll: none;
        }
        #claimtoggle {
        }
        .finishbtn {
          cursor: pointer;
        }
        *[disabled] {
          color: #ccc;
          cursor: forbidden;
        }
        .tabopen {
          color: var(--_ui5_tc_headeritem_text_selected_color);
        }

        #blankheader {
          height: 50px;
        }
        #blanksubheader {
          background-color: var(--subheader-color);
          vertical-align: middle;
          padding-left: 2rem;
        }
        .noitem {
          text-align: center;
          font-style: italic;
          margin: auto;
          padding: 1rem;
        }
        #blankdocument {
          flex-grow: 1;
          background-color: #fbfbfb;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        #blankdocument > div {
          width: calc(100% - 40px);
          height: calc(100% - 100px);
          border: 1px solid rgb(198, 198, 198);
          background-color: #fff;
        }
        #blanksubheader iron-icon {
          margin-left: 2rem;
          color: var(--_ui5_tc_headeritem_text_selected_color);
        }
        #blanksubheader iron-icon[disabled] {
          color: #ccc;
          cursor: not-allowed;
        }
        .menuicon {
          cursor: pointer;
        }
        .PositiveColor {
          color: var(--clr-positive);
        }
        .NegativeColor {
          color: var(--clr-negative);
        }
      </style>
      <vaadin-split-layout id="mainlayout">
        <div
          style="flex: ${sidebarWidthPercentage}%; max-width: ${sidebarMaxWidth};"
          id="left"
        >
          <div id="sidelayout">
            <div class="nav nav-tabs">
              <div>
                <iron-icon
                  icon="vaadin:angle-double-right"
                  title="${this.string["_close"]}"
                  id="close"
                  class="${this.tabOpen ? "tabopen" : ""}"
                  @click="${this._tabSelected.bind(this)}"
                ></iron-icon>
              </div>
              ${this.state.menus.editor_tabs.map(
                (item) => html`
                  <div
                    id="${item.name}"
                    @click="${this._tabSelected.bind(this)}"
                    class="${this.activeTab == item.name ? "active" : ""}"
                  >
                    ${this.string[item.label]}
                  </div>
                `
              )}
            </div>

            <wex-navigators style="width: calc(100% - 3.125rem);display: ${navigatorsDisplay}"></wex-navigators>

            ${
              this.activeTab !== "close" && this.activeTab !== "ditamaps"
                ? html` <div id="sidebar-content">
                    ${this._activeTabContent()}
                  </div>`
                : null
            }
          </div>
        </div>
        <div id="right" style="width: ${100 - sidebarWidthPercentage}%">
        <wex-editors></wex-editors>
          ${
            this.editors.length > 0
              ? html`<wex-editors></wex-editors>`
              : html`<div id="blankheader"></div>
                  <div id="blanksubheader">
                    <div id="blankdocument">
                      <div></div>
                    </div>
                    <iron-icon
                      icon="vaadin:folder-open"
                      ?disabled="${globalCap == "Default"}"
                      title="${globalCap == "Default"
                        ? this.string["_insufficientpermsopenfile"]
                        : this.string["_openfile"]}"
                      class="menuicon"
                      @click="${this._openFile.bind(this)}"
                    ></iron-icon>
                  </div>`
          }
          
          </div>
        </div>
      </vaadin-split-layout>
    `;
  }
}

customElements.define("wex-layout-edit", WexLayoutEdit);
