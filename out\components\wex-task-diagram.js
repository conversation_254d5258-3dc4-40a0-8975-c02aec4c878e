var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html } from "lit";
import { customElement, property } from "lit/decorators.js";
/**
 * Displays workflow diagram for selected task
 */
let WexTaskDiagram = class WexTaskDiagram extends LitElement {
    constructor() {
        super(...arguments);
        this.selectedTask = null;
    }
    /* Templates */
    render() {
        return html `
      <img
        id="diagram"
        src="/lwa/jrest/GetTaskDiagram?processInstId=${this.selectedTask
            ?.processInstId}&amp;cachebuster=${Date.now()}"
        style="display: block;margin-left: auto; margin-right: auto;"
      />
    `;
    }
};
__decorate([
    property({ type: Object, attribute: false })
], WexTaskDiagram.prototype, "selectedTask", void 0);
WexTaskDiagram = __decorate([
    customElement("wex-task-diagram")
], WexTaskDiagram);
export { WexTaskDiagram };
//# sourceMappingURL=wex-task-diagram.js.map