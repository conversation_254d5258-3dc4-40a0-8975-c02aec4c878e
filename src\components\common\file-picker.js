import { LitElement, html, property } from "lit";
import { storeInstance } from "store/index.js";
import { httpError } from "../../store/httpError";
import pubsub from "pubsub-js";
import * as util from "../../lib/util";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "../wex-folders";
import "../wex-table";

/**
 * Select content from xdocs file system
 */
class FilePicker extends LitElement {
  static get properties() {
    return {
      enableMultiselect: { type: Boolean },
      dialogOpenData: { Object },
      includeSubfolders: { Boolean },
      folders: { Array },
      files: { Array },
      selectedFiles: { Array },
      selectedProj: { type: Object },
      selectedFolder: { type: Object },
      callback: { type: Function },
      cancelCallback: { type: Function },
    };
  }

  constructor() {
    super();
    this.files = [];
    this.selectedFiles = [];
    this.includeSubfolders = false;
    this.treeConfig = {
      labelPath: "name",
      idPath: "id",
      childPath: "children",
    };
  }

  connectedCallback() {
    super.connectedCallback();
    this.subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });
    this.state = storeInstance.state;
    this.string = this.state[this.state.langCode];
    this.dialogOpenData = this.state.file_picker_dialog_open;
    this.folders = this.state.global_folders;
    this.columns = this.state.file_picker_columns;
  }

  disconnectedCallback() {
    storeInstance.unsubscribe(this.subscription);
    this.disconnectedCallback();
  }

  stateChange(state) {
    this.state = state;
    this.string = this.state[this.state.langCode];
    this.dialogOpenData = this.state.file_picker_dialog_open;
    this.folders = this.state.global_folders;
  }

  firstUpdated() {
    this.dialog = this.shadowRoot.querySelector("ui5-dialog");
  }

  updated(changedProperties) {
    if (changedProperties.has("dialogOpenData")) {
      if (this.dialogOpenData) {
        // this.dialog.show();
        this.dialog.open = true;

        this.callback = this.dialogOpenData.callback;
        this.cancelCallback = this.dialogOpenData.cancelCallback;
      } else {
        // this.dialog.close();
        this.dialog.open = false;
      }
    }
  }

  reset() {
    this.selectedFiles = [];
    this.selectedProj = null;
  }

  generateSearchExpr() {
    let searchQuery = [];
    searchQuery.push("VIEW := PropertyView ");
    this.searchlang = this.state.select_file_dialog_lang_filter
      ? this.state.select_file_dialog_lang_filter.langCode
      : this.state.default_lang_object.langCode;
    this.searchbranch = this.state.select_file_dialog_branch_filter
      ? this.state.select_file_dialog_branch_filter.name
      : this.state.default_branch_object.name;

    this.asOfDate = "Now";
    this.fieldNames =
      " Title, Name, LockOwner,  CreateDate, ActiveProcesses, FileStatus, NumComments, Size, FileId, FolderPath ";

    let folderClause = "";
    let folder = "/";
    console.log("selected folder", this.selectedFolder);
    if (this.selectedFolder) {
      folder = this.selectedFolder.collectionPathId;
      if (!folder) {
        folder = "/";
      }
    }

    if (this.includeSubfolders) {
      folderClause =
        " WHERE InFolderDesc('" +
        folder +
        "') AND NOT (InFolderDesc('/Content/KbContent'))";
    } else {
      folderClause =
        " WHERE InFolder('" +
        folder +
        "') AND NOT (InFolderDesc('/Content/KbContent')) ";
    }

    searchQuery.push(
      "CONTEXT (LanguageCode='" +
        this.searchlang +
        "',BranchName='" +
        this.searchbranch +
        "',AsOfDate='" +
        this.asOfDate +
        "',HeadVersionOnly='true') FOR EACH " +
        folderClause +
        " RETURN " +
        this.fieldNames
    );

    return searchQuery.join("");
  }

  async fetchFiles(searchExpr) {
    pubsub.publish("requestout");
    try {
      const response = await fetch(
        "/lwa/jrest/GetSearchResults?maxRows=100&searchExpr=" + searchExpr
      );
      if (!response.ok) {
        httpError(response);
        throw Error();
      }

      this.files = util.transformSearchResults(await response.json());
    } catch (e) {
      console.log(e);
    } finally {
      pubsub.publish("requestin");
    }
  }

  jumpToFolder() {
    if (this.selectedProj) {
      let foundFolder = this.findFolderByCollectionId(
        this.folders,
        this.selectedProj.projectHomeCollectionId
      );
      if (foundFolder) {
        this.handleFolderSelected({ detail: foundFolder });
        return;
      }
      return util.notifyUser("warn", "Project home not found in folders.");
    }
  }

  handleProjectSelectorClick() {
    storeInstance.dispatch("setState", {
      property: "project_dialog_open",
      value: {
        callback: (selectedProj) => {
          this.selectedProj = selectedProj;
          this.jumpToFolder();
        },
      },
    });
  }

  projectSelectorTemplate() {
    return html` <style>
        .project-selector-container {
          display: flex;
        }
      </style>
      <div class="project-selector-container">
        <ui5-button @click=${this.handleProjectSelectorClick}
          >${this.selectedProj
            ? this.selectedProj.name
            : "All Projects"}</ui5-button
        >
        <ui5-button
          ?disabled="${!this.selectedProj}"
          id="jump-proj-fldr-btn"
          @click="${this.handleJumpToFolder}"
          class="ui5button ui5-content-density-compact"
          title="${this.string["_jump_to_project_folder"]}"
        >
          <iron-icon
            icon="vaadin:arrow-circle-right-o"
            title="${this.string["_jump_to_project_folder"]}"
          ></iron-icon>
        </ui5-button>
      </div>`;
  }

  _clearSelectedFile() {
    storeInstance.dispatch("setState", {
      property: "select_file_selected_file",
      value: null,
    });
  }

  findFolderByCollectionId(node, collectionId) {
    if (!node) return null;

    if (node.id == collectionId) {
      return node;
    }

    if (Array.isArray(node.children)) {
      for (let child of node.children) {
        let found = this.findFolderByCollectionId(child, collectionId);
        if (found) return found;
      }
    }

    return null;
  }

  handleFolderSelected(e) {
    console.log(e.detail.node);
    if (this.selectedFolder != e.detail.node) {
      this._clearSelectedFile();
      this.selectedFolder = e.detail.node;
      this.selectedFiles = [];
      let query = this.generateSearchExpr();
      this.fetchFiles(query);
    }
  }

  handleClickRow(e) {
    if (!e.detail.value) {
      return;
    }

    const currentFile = e.detail.value;
    const files = [...this.files];

    files.forEach((row) => {
      if (row.resLblId == currentFile.resLblId) {
        row.selected = !row.selected;
        if (row.selected) {
          this.selectedFiles = [...this.selectedFiles, currentFile];
        } else {
          this.selectedFiles = this.selectedFiles.filter(
            (file) => file.resLblId !== currentFile.resLblId
          );
        }
      }
    });

    this.files = files;
  }

  closeDialog() {
    this.reset();
    storeInstance.dispatch("setState", {
      property: "file_picker_dialog_open",
      value: null,
    });
  }

  handleCancel() {
    this.selectedFiles.forEach((file) => delete file.selected);
    if (this.cancelCallback) {
      this.cancelCallback();
    }
    this.closeDialog();
  }

  handleOpen() {
    if (this.callback) {
      this.callback(
        this.selectedFiles.map((file) => {
          delete file.selected;
          return file;
        })
      );
    }
    this.closeDialog();
  }

  render() {
    return html` <style>
        .data-container {
          border: 1px solid var(--primary-100);
          overflow-y: auto;
        }

        .wex-folders {
          flex: 4;
        }

        .file-table {
          flex: 8;
        }

        .main {
          display: flex;
          height: 20rem;
          margin: var(--spacing-md) 0;
          width: 60rem;
        }

        .dialog-container {
          padding: var(--spacing-lg);
        }

        .footer {
          display: flex;
          justify-content: flex-end;
          gap: var(--spacing-sm);
        }

        wex-table {
          width: 100%;
        }
      </style>
      <ui5-dialog header-text="Choose files">
        <div class="dialog-container">
          <div class="header">${this.projectSelectorTemplate()}</div>
          <div class="main">
            <div class="data-container wex-folders">
              <bds-tree
                .config=${this.treeConfig}
                .root=${this.folders}
                @treeClick="${this.handleFolderSelected}"
              ></bds-tree>
            </div>
            <div class="data-container file-table">
              <wex-table
                .columns="${this.columns}"
                .rows="${this.files}"
                @click=${this.handleClickRow}
                .enableSelect="${false}"
              ></wex-table>
            </div>
          </div>
          <div class="footer">
            <ui5-button @click=${this.handleOpen}>Open</ui5-button>
            <ui5-button @click=${this.handleCancel}>Cancel</ui5-button>
          </div>
        </div>
      </ui5-dialog>`;
  }
}

customElements.define("file-picker", FilePicker);
