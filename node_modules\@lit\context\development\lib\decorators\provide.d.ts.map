{"version": 3, "file": "provide.d.ts", "sourceRoot": "", "sources": ["../../../src/lib/decorators/provide.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,eAAe,EAAC,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAU7C;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAgB,OAAO,CAAC,SAAS,EAAE,EACjC,OAAO,EAAE,OAAO,GACjB,EAAE;IACD,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;CACtC,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAuE9B;AAED;;;;GAIG;AACH,KAAK,SAAS,CAAC,CAAC,IAAI;KACjB,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACrB,CAAC;AAEF,KAAK,gBAAgB,CAAC,WAAW,IAAI;IAEnC,CACE,CAAC,SAAS,WAAW,EACrB,KAAK,SAAS,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,EAE5D,iBAAiB,EAAE,KAAK,EACxB,IAAI,CAAC,EAAE,CAAC,GACP,yBAAyB,CAAC,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;IAGpD,CACE,CAAC,SAAS,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,EACxD,CAAC,SAAS,WAAW,EAErB,KAAK,EAAE,4BAA4B,CAAC,CAAC,EAAE,CAAC,CAAC,EACzC,OAAO,EAAE,6BAA6B,CAAC,CAAC,EAAE,CAAC,CAAC,GAC3C,IAAI,CAAC;CACT,CAAC;AAIF,KAAK,eAAe,GAAG,IAAI,GAAG,GAAG,CAAC;AAElC,KAAK,yBAAyB,CAAC,GAAG,EAAE,GAAG,SAAS,WAAW,EAAE,WAAW,IAEtE,GAAG,SAAS,MAAM,CAAC,GAAG,EAAE,MAAM,aAAa,CAAC,GAGxC;IAAC,aAAa;CAAC,SAAS,CAAC,WAAW,CAAC,GACnC,eAAe,GACf;IACE,OAAO,EAAE,2CAA2C,CAAC;IACrD,OAAO,EAAE,WAAW,CAAC;IACrB,QAAQ,EAAE,aAAa,CAAC;CACzB,GAEH,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,SAAS,CAAC,CAAC,GAG/C;IAAC,SAAS,GAAG,SAAS;CAAC,SAAS,CAAC,WAAW,CAAC,GAC3C,eAAe,GACf;IACE,OAAO,EAAE,2CAA2C,CAAC;IACrD,OAAO,EAAE,WAAW,CAAC;IACrB,SAAS,EAAE,SAAS,GAAG,SAAS,CAAC;CAClC,GAKH,eAAe,CAAC"}