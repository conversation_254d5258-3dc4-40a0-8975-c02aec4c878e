import { registerIcon } from "@ui5/webcomponents-base/dist/asset-registries/Icons.js";

const name = "per-diem";
const pathData = "M390 64q38 0 64 26t26 64v12q0 11-7.5 18.5T454 192t-18-7.5-7-18.5v-12q0-17-11-28t-28-11h-6v19q0 11-7.5 18.5T358 160t-18-7.5-7-18.5v-19H179v19q0 11-7 18.5t-18 7.5-18.5-7.5T128 134v-19h-6q-17 0-28 11t-11 28v51h179q11 0 18.5 7t7.5 18-7.5 18.5T262 256H83v166q0 17 11 28t28 11h140q11 0 18.5 7t7.5 18-7.5 18.5T262 512H122q-38 0-64-26t-26-64V154q0-38 26-64t64-26h6V26q0-11 7.5-18.5T154 0t18 7.5 7 18.5v38h154V26q0-11 7-18.5T358 0t18.5 7.5T384 26v38h6zm1 239q-13 0-13 16 0 7 4 10.5t9 3.5h25q26 0 45 19t19 45q0 23-14.5 40T429 459v27q0 11-7.5 18.5T403 512t-18-7.5-7-18.5v-25h-26q-11 0-18.5-7.5T326 435t7.5-18 18.5-7h64q5 0 9-4t4-9-4-9-9-4h-26q-26 0-45-19.5T326 319q0-23 15-42t37-24v-23q0-11 7-18t18-7 18.5 7 7.5 18v22h25q11 0 18.5 7.5T480 278t-7.5 18-18.5 7h-63z";
const ltr = true;
const accData = null;
const collection = "SAP-icons-v5";
const packageName = "@ui5/webcomponents-icons";

registerIcon(name, { pathData, ltr, collection, packageName });

export default "SAP-icons-v5/per-diem";
export { pathData, ltr, accData };