import { registerIcon } from "@ui5/webcomponents-base/dist/asset-registries/Icons.js";

const name = "pixelate";
const pathData = "M390 32q38 0 64 26t26 64v268q0 38-26 64t-64 26H122q-38 0-64-26t-26-64V122q0-38 26-64t64-26h268zM160 192q0-14 9-23t23-9H83v5q13 10 13 27 0 18-13 26v75q13 10 13 27 0 18-13 26v44q0 17 11 28t28 11h44q8-13 26-13 17 0 27 13h75q8-13 26-13 17 0 27 13h43q17 0 28-11t11-28v-44q-13-8-13-26 0-17 13-27v-75q-13-8-13-26 0-17 13-27v-5H320q14 0 23 9t9 23-9 23-23 9-23-9-9-23 9-23 23-9H192q14 0 23 9t9 23-9 23-23 9-23-9-9-23zm224 32q14 0 23 9t9 23-9 23-23 9-23-9-9-23 9-23 23-9zm-128 64q-14 0-23-9t-9-23 9-23 23-9 23 9 9 23-9 23-23 9zm-128 0q-14 0-23-9t-9-23 9-23 23-9 23 9 9 23-9 23-23 9zm64 0q14 0 23 9t9 23-9 23-23 9-23-9-9-23 9-23 23-9zm128 0q14 0 23 9t9 23-9 23-23 9-23-9-9-23 9-23 23-9zm64 64q14 0 23 9t9 23-9 23-23 9-23-9-9-23 9-23 23-9zm-128 0q14 0 23 9t9 23-9 23-23 9-23-9-9-23 9-23 23-9zm-128 0q14 0 23 9t9 23-9 23-23 9-23-9-9-23 9-23 23-9z";
const ltr = false;
const accData = null;
const collection = "SAP-icons-v5";
const packageName = "@ui5/webcomponents-icons";

registerIcon(name, { pathData, ltr, collection, packageName });

export default "SAP-icons-v5/pixelate";
export { pathData, ltr, accData };