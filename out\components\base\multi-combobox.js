var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit-element";
import { customElement, property, state } from "lit/decorators.js";
import "@ui5/webcomponents/dist/MultiComboBox.js";
import "@ui5/webcomponents/dist/MultiComboBoxItem.js";
import "@ui5/webcomponents/dist/MultiComboBoxItemGroup.js";
let WexMultiComboBox = class WexMultiComboBox extends LitElement {
    constructor() {
        super(...arguments);
        this.mcbData = null;
        this.mcbItems = [];
        this.initialSelectedItems = null;
        this.dataGroup = null;
        this.placeholder = "Select...";
        this._selectedMcbItems = [];
        // additional-text="${item.additionalText}"
    }
    connectedCallback() {
        super.connectedCallback();
        this._init();
    }
    disconnectedCallback() {
        super.disconnectedCallback();
    }
    updated(changedProperties) {
        if (changedProperties.has("mcbData") && this.mcbData && this.dataGroup) {
            const { mcbItems, placeholder } = this.mcbData[this.dataGroup];
            this.mcbItems = mcbItems;
            this.placeholder = placeholder;
        }
    }
    _init() {
        if (this.initialSelectedItems) {
            this._selectedMcbItems = Object.values(this.initialSelectedItems).flat();
        }
    }
    _handleSelectionChange(e) {
        const comboBox = e.target;
        const selectedItems = comboBox.items
            .filter((item) => item.selected)
            .map((item) => item.text);
        console.log("_handleSelectionChange: selectedItems", selectedItems);
        this.dispatchEvent(new CustomEvent("selection-change", {
            detail: { selectedItems, comboBoxGroup: this.dataGroup || "" },
            bubbles: true,
            composed: true,
        }));
        this._selectedMcbItems = selectedItems;
        console.log("_handleSelectionChange: this._selectedMcbItems", this._selectedMcbItems);
    }
    _renderItems() {
        return this.mcbItems?.map((item) => {
            return html `
        <ui5-mcb-item
          text="${item}"
          data-group="${this.dataGroup || ""}"
          ?selected=${this._selectedMcbItems.includes(item)}
        >
        </ui5-mcb-item>
      `;
        });
    }
    _renderGroupedItems() {
        if (!this.mcbData)
            return null;
        return Object.entries(this.mcbData).map(([groupName, group]) => html `
        <ui5-mcb-group-item text="${group.placeholder}"></ui5-mcb-group-item>
        ${group.mcbItems.map((item) => html `
            <ui5-mcb-item
              text="${item}"
              data-group="${groupName}"
              ?selected=${this._selectedMcbItems.includes(item)}
            ></ui5-mcb-item>
          `)}
      `);
    }
    render() {
        return html `
      <ui5-multi-combobox
        placeholder=${this.placeholder}
        data-group=${this.dataGroup || ""}
        @selection-change=${this._handleSelectionChange}
      >
        ${this.dataGroup ? this._renderItems() : this._renderGroupedItems()}
      </ui5-multi-combobox>
    `;
    }
};
WexMultiComboBox.styles = css `
    ui5-multi-combobox {
      width: 100%;
    }
  `;
__decorate([
    property({ type: Object })
], WexMultiComboBox.prototype, "mcbData", void 0);
__decorate([
    property({ type: Array })
], WexMultiComboBox.prototype, "mcbItems", void 0);
__decorate([
    property({ type: Object })
], WexMultiComboBox.prototype, "initialSelectedItems", void 0);
__decorate([
    property({ type: String })
], WexMultiComboBox.prototype, "dataGroup", void 0);
__decorate([
    property({ type: String })
], WexMultiComboBox.prototype, "placeholder", void 0);
__decorate([
    state()
], WexMultiComboBox.prototype, "_selectedMcbItems", void 0);
WexMultiComboBox = __decorate([
    customElement("wex-multi-combobox")
], WexMultiComboBox);
export { WexMultiComboBox };
//# sourceMappingURL=multi-combobox.js.map