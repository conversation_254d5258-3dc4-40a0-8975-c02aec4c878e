{"version": 3, "file": "multi-combobox.js", "sourceRoot": "", "sources": ["../../../src/components/base/multi-combobox.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAkB,MAAM,aAAa,CAAC;AACpE,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAEnE,OAAO,0CAA0C,CAAC;AAClD,OAAO,8CAA8C,CAAC;AACtD,OAAO,mDAAmD,CAAC;AAwBpD,IAAM,gBAAgB,GAAtB,MAAM,gBAAiB,SAAQ,UAAU;IAAzC;;QACuB,YAAO,GAAmB,IAAI,CAAC;QAChC,aAAQ,GAAa,EAAE,CAAC;QACvB,yBAAoB,GAAwB,IAAI,CAAC;QACjD,cAAS,GAAkB,IAAI,CAAC;QAChC,gBAAW,GAAW,WAAW,CAAC;QAE7C,sBAAiB,GAAa,EAAE,CAAC;QAkGlD,2CAA2C;IAC7C,CAAC;IA3FC,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,oBAAoB,EAAE,CAAC;IAC/B,CAAC;IAED,OAAO,CAAC,iBAAiC;QACvC,IAAI,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACvE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QACjC,CAAC;IACH,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,EAAE,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,sBAAsB,CAAC,CAAc;QACnC,MAAM,QAAQ,GAAG,CAAC,CAAC,MAElB,CAAC;QACF,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK;aACjC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC/B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE5B,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,aAAa,CAAC,CAAC;QAEpE,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAwB,kBAAkB,EAAE;YACzD,MAAM,EAAE,EAAE,aAAa,EAAE,aAAa,EAAE,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE;YAC9D,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC,CACH,CAAC;QAEF,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC;QACvC,OAAO,CAAC,GAAG,CACT,gDAAgD,EAChD,IAAI,CAAC,iBAAiB,CACvB,CAAC;IACJ,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACjC,OAAO,IAAI,CAAA;;kBAEC,IAAI;wBACE,IAAI,CAAC,SAAS,IAAI,EAAE;sBACtB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC;;;OAGpD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAC/B,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CACrC,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,CAAA;oCACE,KAAK,CAAC,WAAW;UAC3C,KAAK,CAAC,QAAQ,CAAC,GAAG,CAClB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAA;;sBAEF,IAAI;4BACE,SAAS;0BACX,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC;;WAEpD,CACF;OACF,CACF,CAAC;IACJ,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAA;;sBAEO,IAAI,CAAC,WAAW;qBACjB,IAAI,CAAC,SAAS,IAAI,EAAE;4BACb,IAAI,CAAC,sBAAsB;;UAE7C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;;KAEtE,CAAC;IACJ,CAAC;;AA/FM,uBAAM,GAAG,GAAG,CAAA;;;;GAIlB,AAJY,CAIX;AAZ0B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;iDAAgC;AAChC;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;kDAAyB;AACvB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8DAAkD;AACjD;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;mDAAiC;AAChC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;qDAAmC;AAE7C;IAAhB,KAAK,EAAE;2DAA0C;AAPvC,gBAAgB;IAD5B,aAAa,CAAC,oBAAoB,CAAC;GACvB,gBAAgB,CA0G5B", "sourcesContent": ["import { LitElement, html, css, PropertyValues } from \"lit-element\";\r\nimport { customElement, property, state } from \"lit/decorators.js\";\r\n\r\nimport \"@ui5/webcomponents/dist/MultiComboBox.js\";\r\nimport \"@ui5/webcomponents/dist/MultiComboBoxItem.js\";\r\nimport \"@ui5/webcomponents/dist/MultiComboBoxItemGroup.js\";\r\n\r\n/**\r\n * MultiComboBox component using UI5 Web Components.\r\n *\r\n * comboBoxGroup is either the group target, or a single MCB\r\n *\r\n */\r\n\r\ninterface McbData {\r\n  [mcbTargetGroup: string]: McbGroupData;\r\n}\r\n\r\ninterface McbGroupData {\r\n  placeholder: string;\r\n  mcbItems: string[];\r\n}\r\n\r\ninterface SelectionChangeDetail {\r\n  selectedItems: string[];\r\n  comboBoxGroup: string;\r\n}\r\n\r\n@customElement(\"wex-multi-combobox\")\r\nexport class WexMultiComboBox extends LitElement {\r\n  @property({ type: Object }) mcbData: McbData | null = null;\r\n  @property({ type: Array }) mcbItems: string[] = [];\r\n  @property({ type: Object }) initialSelectedItems: McbGroupData | null = null;\r\n  @property({ type: String }) dataGroup: string | null = null;\r\n  @property({ type: String }) placeholder: string = \"Select...\";\r\n\r\n  @state() private _selectedMcbItems: string[] = [];\r\n\r\n  static styles = css`\r\n    ui5-multi-combobox {\r\n      width: 100%;\r\n    }\r\n  `;\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this._init();\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n  }\r\n\r\n  updated(changedProperties: PropertyValues) {\r\n    if (changedProperties.has(\"mcbData\") && this.mcbData && this.dataGroup) {\r\n      const { mcbItems, placeholder } = this.mcbData[this.dataGroup];\r\n      this.mcbItems = mcbItems;\r\n      this.placeholder = placeholder;\r\n    }\r\n  }\r\n\r\n  _init() {\r\n    if (this.initialSelectedItems) {\r\n      this._selectedMcbItems = Object.values(this.initialSelectedItems).flat();\r\n    }\r\n  }\r\n\r\n  _handleSelectionChange(e: CustomEvent) {\r\n    const comboBox = e.target as HTMLElement & {\r\n      items: { selected: boolean; text: string }[];\r\n    };\r\n    const selectedItems = comboBox.items\r\n      .filter((item) => item.selected)\r\n      .map((item) => item.text);\r\n\r\n    console.log(\"_handleSelectionChange: selectedItems\", selectedItems);\r\n\r\n    this.dispatchEvent(\r\n      new CustomEvent<SelectionChangeDetail>(\"selection-change\", {\r\n        detail: { selectedItems, comboBoxGroup: this.dataGroup || \"\" },\r\n        bubbles: true,\r\n        composed: true,\r\n      })\r\n    );\r\n\r\n    this._selectedMcbItems = selectedItems;\r\n    console.log(\r\n      \"_handleSelectionChange: this._selectedMcbItems\",\r\n      this._selectedMcbItems\r\n    );\r\n  }\r\n\r\n  _renderItems() {\r\n    return this.mcbItems?.map((item) => {\r\n      return html`\r\n        <ui5-mcb-item\r\n          text=\"${item}\"\r\n          data-group=\"${this.dataGroup || \"\"}\"\r\n          ?selected=${this._selectedMcbItems.includes(item)}\r\n        >\r\n        </ui5-mcb-item>\r\n      `;\r\n    });\r\n  }\r\n\r\n  _renderGroupedItems() {\r\n    if (!this.mcbData) return null;\r\n    return Object.entries(this.mcbData).map(\r\n      ([groupName, group]) => html`\r\n        <ui5-mcb-group-item text=\"${group.placeholder}\"></ui5-mcb-group-item>\r\n        ${group.mcbItems.map(\r\n          (item) => html`\r\n            <ui5-mcb-item\r\n              text=\"${item}\"\r\n              data-group=\"${groupName}\"\r\n              ?selected=${this._selectedMcbItems.includes(item)}\r\n            ></ui5-mcb-item>\r\n          `\r\n        )}\r\n      `\r\n    );\r\n  }\r\n\r\n  render() {\r\n    return html`\r\n      <ui5-multi-combobox\r\n        placeholder=${this.placeholder}\r\n        data-group=${this.dataGroup || \"\"}\r\n        @selection-change=${this._handleSelectionChange}\r\n      >\r\n        ${this.dataGroup ? this._renderItems() : this._renderGroupedItems()}\r\n      </ui5-multi-combobox>\r\n    `;\r\n  }\r\n  // additional-text=\"${item.additionalText}\"\r\n}\r\n"]}