import { LitElement, html } from "lit";
import { storeInstance as Store } from "store/index.js";
import { Router } from "@vaadin/router/dist/vaadin-router.js";
import * as util from "lib/util.ts";
import { httpError } from "store/httpError.js";
import pubsub from "pubsub-js";

// import "@ui5/webcomponents-icons/dist/AllIcons.js";

import "./wex-header.js";
import "./wex-editors.js";
import "./wex-navigators.js";

import "./wex-layout-loggedout.js";
import "../pages/workflow/tasks.js";
import "./wex-layout-search.js";
import "./wex-layout-browse.ts";
import "./wex-layout-preview.js";
import "./wex-layout-edit.js";
import "./pages/reports";
import "../pages/reports/reports-locks";
import "../pages/reports/reports-rooms";
import "../pages/publish/publish-defs";
import "../pages/publish/publish-jobs";
import "../pages/publish/publish-adhoc-jobs";
import "../pages/publish/publish-pubs";
// import "../pages/publish/publish-run-defs";
// import "../pages/publish/publish-run-adhoc";

// import "./wex-dialog-file-properties.js";
import "./dialog/file-properties.js";
// import "./wex-dialog-asof.js";
import "./dialog/asof.js";
// import "./wex-dialog-project-selector.js";
import "./dialog/project-selector.js";
// import "./wex-dialog-select-file.js";
import "./dialog/select-file.ts";
// import "./wex-dialog-import.js";
import "./dialog/import.js";
// import "./wex-dialog-extended-folder-create-file.js";
import "./dialog/extended-folder-create-file.ts";

// converted but untested
import "./wex-dialog-dirtyeditors.js";
// import "./dialog/dirtyeditors.js";
import "./wex-dialog-close-reachable.js";
// import "./dialog/close-reachable.js";
import "./wex-dialog-ditamap-lock-fail.js";
// import "./dialog/ditamap-lock-fail.js";
import "./wex-dialog-task-history-item-detail.js";
// import "./dialog/task-history-item-detail.js";
import "./wex-dialog-task-properties.js";
// import "./dialog/task-properties.js";

// WIP - TODO EJS - work in progress below
import "./wex-dialog-create.js";
// import "./dialog/create.js";

import "./wex-dialog-folder-import.js";
import "./wex-dialog-ditamapnav-create-topic.js";
import "./wex-dialog-project-selector.ts";
import "./wex-dialog-task-history-item-detail.js";
import "./wex-dialog-file-properties.js";
import "./wex-dialog-task-properties.js";
import "./wex-dialog-asof.js";
import "./wex-dialog-login.js";
import "./wex-dialog-error.js";
import "./wex-dialog-ditabase-nolock.js";
import "./wex-dialog-folder-create-file.js";
import "./wex-dialog-search-filters.js";
import "./wex-dialog-select-folder.js";
import "./wex-dialog-rootmap-selector.js";
import "./wex-dialog-profile-selector.js";
import "./wex-dialog-finish-form.js";
import "./wex-dialog-savecomment.js";
import "./wex-dialog-save-or-lose.js";
import "./wex-dialog-generic-message.js";

import "./wex-toast.ts";

// import "@ui5/webcomponents/dist/assets.js";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "@ui5/webcomponents/dist/Label.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/Icon.js";

import "./dialog/browse-create-folder";
import "./dialog/browse-rename-folder";
import "./dialog/browse-delete-folder";
import "./dialog/browse-delete-file";
import "./dialog/browse-rename-file";
import "./dialog/editor-create-image";
import "./dialog/select-editor-mode";
import "./dialog/message";
import "./dialog/publish-defs-create";
import "./dialog/publish-run-confirm";
import "./dialog/export-files.js";
import "./dialog/browse-publish.js";
import "./common/dialog-button.js"; // import should be in individual file?
import "./dialog/import-files.js";
import "./dialog/find-links.js";
import "./dialog/process.js";
import "../components/common/file-picker.js";

// popovers
import "./popover/output-job-info.js";

import "./dialog/wex-select";
import "./wex-layout-edit-placeholder.js";
import "../pages/workflow/processes.js";

import "./wex-csh-nav";
import "./wex-csh";
import { getAvailableLanguages, redirectToSSOLogin } from "../lib/util.ts";
import { until } from "lit/directives/until.js";

class WexApp extends LitElement {
  constructor() {
    super();
  }

  connectedCallback() {
    super.connectedCallback();
    this._loadConfig("company_config.json");
    this._loadConfig("user_config.json");
    this.subscription = Store.subscribe((state) => {
      this.stateChange(state);
    });
    this.state = Store.state;
    this.string = this.state.strings;
    Store.dispatch("setLanguage", this.getPathLang());
    this.getAuthInfoPromise = this.getAuthInfo();
    this.wexUser = this.state.wex_user;
  }

  disconnectedCallback() {
    Store.unsubscribe(this.subscription);
  }

  _loadConfig(configFile) {
    fetch("/wex/config/" + configFile)
      .then(
        function (response) {
          if (!response.ok) {
            throw Error(); //to bail out of promise chain
          }
          return response;
        }.bind(this)
      )
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        Object.keys(data).forEach(function (key) {
          Store.dispatch("setState", {
            property: key,
            value: data[key],
          });
        });
      })
      .catch(function (error) {})
      .finally(function () {});
  }

  async getAuthInfo() {
    pubsub.publish("requestout");
    try {
      const response = await fetch("/lwa/jrest/Logon");
      if (!response.ok) {
        redirectToSSOLogin();
        throw Error();
      }
      response.json().then((userData) => {
        if (userData.userName === undefined || userData.userName == "") {
          redirectToSSOLogin();
        } else {
          Store.dispatch("legacyHandleAuthData", userData);
          this.postLoginActions();
        }
        return userData;
      });
    } catch (e) {
      console.log(e);
    } finally {
      pubsub.publish("requestin");
    }
  }

  postLoginActions() {
    Store.dispatch("getEmptyRootmapResLblId");
    Store.dispatch("getGlobalProperties");
    Store.dispatch("getConfiguredLangs");
    Store.dispatch("getBranches");
    Store.dispatch("getProjects");
    Store.dispatch("getCollectionTree");
  }

  initDefaults() {
    Store.dispatch("setState", {
      property: "browse_lang_filter",
      value: this.state.default_lang_object,
    });
    Store.dispatch("setState", {
      property: "browse_branch_filter",
      value: this.state.default_branch_object,
    });
    Store.dispatch("setState", {
      property: "browse_asof_filter",
      value: this.state.default_asof,
    });
    Store.dispatch("setState", {
      property: "search_lang_filter",
      value: this.state.default_lang_object,
    });
    Store.dispatch("setState", {
      property: "search_branch_filter",
      value: this.state.default_branch_object,
    });
    Store.dispatch("setState", {
      property: "search_asof_filter",
      value: this.state.default_asof,
    });
    this.flat = [];
    this._treeToArray(this.state.global_folders, this);
    Store.dispatch("setState", {
      property: "flat_folders",
      value: this.flat,
    });
  }

  _treeToArray(node, c) {
    c.flat.push({ id: node.id, name: node.name });
    if (node.children) {
      for (var i = 0; i < node.children.length; i++) {
        c._treeToArray(node.children[i], c);
      }
    }
  }

  stateChange(state) {
    this.state = state;
    this.string = this.state.strings;
    this.wexUser = this.state.wex_user;

    if (this.state.global_langs && !this.state.default_lang_object) {
      var defLangObj = this.state.global_langs.filter(
        function (item) {
          return item.langCode === this.state.default_lang_code;
        }.bind(this)
      );
      if (defLangObj[0]) {
        Store.dispatch("setState", {
          property: "default_lang_object",
          value: defLangObj[0],
        });
      }
    }

    if (this.state.global_branches && !this.state.default_branch_object) {
      var defBranchObj = this.state.global_branches.filter(
        function (item) {
          return item.name === this.state.default_branch_name;
        }.bind(this)
      );
      if (defBranchObj[0]) {
        Store.dispatch("setState", {
          property: "default_branch_object",
          value: defBranchObj[0],
        });
      }
    }

    if (
      this.state.default_lang_object &&
      this.state.default_branch_object &&
      this.state.global_folders &&
      this.state.global_projects
    ) {
      if (!this.defaultInit) {
        this.defaultInit = true;
        this.initDefaults();
        this.initRouter();
      }
    }

    if (!this.dialogsCreated) {
      if (
        this.state.browse_lang_filter &&
        this.state.browse_branch_filter &&
        this.state.search_lang_filter &&
        this.state.search_branch_filter
      ) {
        this.initDialogs();
      }
    }
  }

  getPathLang() {
    var langCode = this.state.default_lang_code;
    if (window.location.pathname.toLowerCase() == "/wex/") {
      window.location.href = "/wex/" + langCode;
    } else {
      var path = window.location.pathname.split("/");
      if (path.length >= 2) {
        langCode = window.location.pathname.split("/")[2];
      }
    }
    return langCode;
  }

  local(inVal) {
    return localize(inVal, this.state.langCode);
  }

  initDialogs() {
    this.dialogsContainer = this.shadowRoot.querySelector("#dialogs");
    if (this.dialogsContainer) {
      const dialogs = [
        "wex-popover-output-job-info",
        "wex-dialog-project-selector",
        "wex-dialog-task-history-item-detail",
        "wex-dialog-file-properties",
        "wex-dialog-task-properties",
        "wex-dialog-select-file",
        "wex-dialog-import",
        "wex-dialog-create",
        "wex-dialog-ditamapnav-create-topic",
        "wex-dialog-asof",
        "wex-dialog-folder-import",
        "wex-dialog-folder-create-file",
        "wex-dialog-search-filters",
        "wex-dialog-select-folder",
        "wex-dialog-rootmap-selector",
        "wex-dialog-profile-selector",
        "wex-dialog-finish-form",
        "wex-dialog-dirtyeditors",
        "wex-dialog-savecomment",
        "wex-dialog-error",
        "wex-dialog-save-or-lose",
        "wex-dialog-close-reachable",
        "wex-dialog-ditabase-nolock",
        "wex-dialog-generic-message",
        "wex-dialog-ditamap-lock-fail",
        "wex-dialog-extended-folder-create-file",
        "wex-dialog-browse-create-folder",
        "wex-dialog-browse-rename-folder",
        "wex-dialog-browse-delete-folder",
        "wex-dialog-browse-rename-file",
        "wex-dialog-browse-delete-file",
        "wex-dialog-editor-create-image",
        "wex-dialog-select-editor-mode",
        "wex-dialog-select",
        "wex-dialog-message",
        "wex-dialog-publish-defs-create",
        "wex-dialog-publish-run-confirm",
        "wex-dialog-browse-publish",
        "wex-dialog-export-files",
        "wex-dialog-import-files",
        "wex-dialog-find-links",
        "wex-dialog-process",
        "file-picker",
      ];
      this._dialogs = this.shadowRoot.querySelector("#dialogs");
      if (this._dialogs) {
        dialogs.forEach((dialog) =>
          this._dialogs.appendChild(document.createElement(dialog))
        );
      }
      this.dialogsCreated = true;
    }
  }

  // Validates that there is a language table for url parameter other wise redirect to english
  async validateLangcode(context, commands) {
    const langcode = context.params.langcode;
    const availableLanguages = await getAvailableLanguages();
    if (availableLanguages.includes(langcode)) {
      return true;
    }
    window.location.href = "/wex/en-US/tasks";
  }

  initRouter() {
    if (!this.router) {
      const outlet = this.shadowRoot.querySelector("#outlet"); // previously was "DIV"
      this.router = new Router(outlet);
      this.router.setRoutes([
        {
          path: "/wex/:langcode",
          // redirect: "/wex/:langcode/tasks",
          action: async (context, commands) =>
            this.validateLangcode(context, commands),
          children: [
            // redirect still needs the entire path; does not respect child status
            { path: "/", redirect: "/wex/:langcode/workflow/tasks" },
            { path: "/loggedout", component: "wex-layout-loggedout" },
            { path: "/home", redirect: "/tasks" },
            { path: "/workflow/tasks", component: "wex-tasks" },
            { path: "/workflow/tasks/:taskId", component: "wex-tasks" },
            { path: "/workflow/processes", component: "wex-processes" },
            {
              path: "/workflow/processes/:processInstId",
              component: "wex-processes",
            },
            { path: "/browse", component: "wex-layout-browse" },
            { path: "/browse/:collectionId", component: "wex-layout-browse" },
            { path: "/search", component: "wex-layout-search" },
            { path: "/search/:searchTerm", component: "wex-layout-search" },

            { path: "/preview", component: "wex-layout-preview" },
            { path: "/edit", component: "wex-layout-edit-placeholder" },

            { path: "/reports", redirect: "/wex/:langcode/reports/locks" },
            // { path: "/reports", component: "wex-page-reports" },
            { path: "/reports/locks", component: "wex-page-reports-locks" },
            { path: "/reports/rooms", component: "wex-page-reports-rooms" },
            {
              path: "/publish",
              redirect: "/wex/:langcode/publish/publications",
            },
            {
              path: "/publish/publications",
              component: "wex-page-publish-pubs",
            },
            {
              path: "/publish/definitions",
              component: "wex-page-publish-defs",
            },
            // {
            //   path: "/publish/run",
            //   redirect: "/wex/:langcode/publish/run/definitions",
            // },
            // {
            //   path: "/publish/run/definitions",
            //   component: "wex-page-publish-run-defs",
            // },
            // {
            //   path: "/publish/run/adhoc",
            //   component: "wex-page-publish-run-adhoc",
            // },
            { path: "/publish/jobs", component: "wex-page-publish-jobs" },
            {
              path: "/publish/adhoc-jobs",
              component: "wex-page-publish-adhoc-jobs",
            },
          ],
        },
      ]);
    }
  }

  /* Templates */
  appTemplate() {
    // changes from 55 merge:
    // added #container
    // added flex settings to #outlet
    // added styling to children of #outlet
    // version 1.31.2.3

    // wrapping the whole app in div#container
    return html`
      <style>
        #container {
          /* width: 100vw; */
          height: 100vh;
          display: flex;
          flex-direction: column;
        }
        #outlet {
          display: flex;
          flex-direction: column;
          flex-grow: 1;
          background-color: var(--clr-bg);
        }
        #outlet > * {
          flex-grow: 1;
          display: flex;
          flex-direction: column;
        }
      </style>

      <div id="container">
        <wex-header id="appheader"></wex-header>
        <div id="outlet"></div>
        <span id="dialogs"></span>
        <wex-toast></wex-toast>
        <wex-layout-edit></wex-layout-edit>
      </div>
    `;
  }

  loadingTemplate() {
    return html`<div id="loading" aria-live="polite" role="status">...</div>`;
  }

  render() {
    return html` ${until(
      this.getAuthInfoPromise.then(() => this.appTemplate()),
      this.loadingTemplate()
    )}`;
  }
}

customElements.define("wex-app", WexApp);
