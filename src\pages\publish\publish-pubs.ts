import { LitElement, html, css } from "lit";

import "../../components/header/publish";

class WexPagePublishPubs extends LitElement {
  static get properties() {
    return {};
  }

  static get styles() {
    return css``;
  }

  _renderMainPane() {
    const template = html`<div>PUBLICATION EDITOR</div>`;
    return template;
  }

  render() {
    const template = html`
      <div id="publish-pubs-container">
        <wex-publish-header></wex-publish-header>
        <wex-main-pane> ${this._renderMainPane()} </wex-main-pane>
      </div>
    `;
    return template;
  }
}

customElements.define("wex-page-publish-pubs", WexPagePublishPubs);
