{"version": 3, "file": "publish-adhoc-jobs.js", "sourceRoot": "", "sources": ["../../../src/pages/publish/publish-adhoc-jobs.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC5D,aAAa;AACb,OAAO,KAAK,MAAM,MAAM,YAAY,CAAC;AAErC,OAAO,iCAAiC,CAAC;AACzC,OAAO,+CAA+C,CAAC;AAGhD,IAAM,uBAAuB,GAA7B,MAAM,uBAAwB,SAAQ,UAAU;IAAhD;;QACsB,eAAU,GAAU,EAAE,CAAC;QAelD,uBAAkB,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,mBAAmB,EAAE,CAAC;QACvD,CAAC,CAAC;IAwBJ,CAAC;IAvCC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;KAIT,CAAC;IACJ,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAMD,eAAe;QACb,MAAM,QAAQ,GAAG,IAAI,CAAA;oBACL,IAAI,CAAC,UAAU;;iCAEF,CAAC;QAC9B,OAAO,QAAQ,CAAC;QAChB,OAAO,IAAI,CAAA;;eAEA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;;KAEzC,CAAC;IACJ,CAAC;IAED,MAAM;QACJ,MAAM,QAAQ,GAAG,IAAI,CAAA;;;0BAGC,IAAI,CAAC,eAAe,EAAE;;KAE3C,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAzC4B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;2DAAwB;AADvC,uBAAuB;IADnC,aAAa,CAAC,6BAA6B,CAAC;GAChC,uBAAuB,CA0CnC", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport * as wexlib from \"lib/wexlib\";\r\n\r\nimport \"../../components/header/publish\";\r\nimport \"../../components/publish/publish-outputs-pane\";\r\n\r\n@customElement(\"wex-page-publish-adhoc-jobs\")\r\nexport class WexPagePublishAdhocJobs extends LitElement {\r\n  @property({ type: Array }) outputJobs: any[] = [];\r\n\r\n  static get styles() {\r\n    return css`\r\n      wex-publish-outputs-pane {\r\n        width: 50%;\r\n      }\r\n    `;\r\n  }\r\n\r\n  connectedCallback(): void {\r\n    super.connectedCallback();\r\n    this._refreshOutputJobs();\r\n  }\r\n\r\n  _refreshOutputJobs = async () => {\r\n    this.outputJobs = await wexlib.getPubDefOutputJobs();\r\n  };\r\n\r\n  _renderMainPane() {\r\n    const template = html` <wex-publish-outputs-pane\r\n      .outputJobs=${this.outputJobs}\r\n      class=\"right\"\r\n    ></wex-publish-outputs-pane>`;\r\n    return template;\r\n    return html`\r\n      <div>\r\n        <pre>${JSON.stringify(this.outputJobs)}</pre>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  render() {\r\n    const template = html`\r\n      <div id=\"publish-adhoc-jobs-container\">\r\n        <wex-publish-header></wex-publish-header>\r\n        <wex-main-pane> ${this._renderMainPane()} </wex-main-pane>\r\n      </div>\r\n    `;\r\n    return template;\r\n  }\r\n}\r\n"]}