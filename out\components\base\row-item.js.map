{"version": 3, "file": "row-item.js", "sourceRoot": "", "sources": ["../../../src/components/base/row-item.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAkB,MAAM,KAAK,CAAC;AAC5D,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAGrD,IAAM,UAAU,GAAhB,MAAM,UAAW,SAAQ,UAAU;IA4BxC,OAAO,CAAC,YAA4B;QAClC,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;QACxC,CAAC;IACH,CAAC;IAED,MAAM;QACJ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC;QACrC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC;QAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC;QAChD,OAAO,IAAI,CAAA;;;6BAGc,MAAM,aAAa,MAAM,sBAAsB,cAAc,kBAAkB,UAAU;;;;KAIjH,CAAC;IACJ,CAAC;;AAxCM,iBAAM,GAAG,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;GAoBlB,AApBY,CAoBX;AAzB0B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;0CAAiB;AAChB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;kDAAyB;AACxB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8CAAqB;AACpB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;0CAAiB;AAJjC,UAAU;IADtB,aAAa,CAAC,cAAc,CAAC;GACjB,UAAU,CAmDtB", "sourcesContent": ["import { LitElement, html, css, PropertyValues } from \"lit\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\n\r\n@customElement(\"wex-row-item\")\r\nexport class WexRowItem extends LitElement {\r\n  @property({ type: String }) border?: string;\r\n  @property({ type: String }) justifyContent?: string;\r\n  @property({ type: String }) alignItems?: string;\r\n  @property({ type: String }) height?: string;\r\n\r\n  static styles = css`\r\n    :host {\r\n      display: flex;\r\n      flex: 1;\r\n      width: 100%;\r\n      min-height: 0;\r\n    }\r\n\r\n    .wrapper {\r\n      display: flex;\r\n      flex-direction: row;\r\n      overflow-x: auto;\r\n      gap: 1rem;\r\n      flex: 1;\r\n      min-height: 40px;\r\n      width: 100%;\r\n    }\r\n    .wrapper > *:not(:last-child) {\r\n      margin-right: 0.5rem;\r\n    }\r\n  `;\r\n\r\n  updated(changedProps: PropertyValues) {\r\n    if (changedProps.has(\"border\")) {\r\n      this.style.border = this.border || \"\";\r\n    }\r\n  }\r\n\r\n  render() {\r\n    const height = this.height ?? \"auto\";\r\n    const justifyContent = this.justifyContent ?? \"space-evenly\";\r\n    const alignItems = this.alignItems ?? \"stretch\";\r\n    return html`\r\n      <div\r\n        class=\"wrapper\"\r\n        style=\"max-height: ${height}; height: ${height}; justify-content: ${justifyContent}; align-items: ${alignItems};\"\r\n      >\r\n        <slot></slot>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  // protected createRenderRoot(): HTMLElement | DocumentFragment {\r\n  //   return this;\r\n  // }\r\n}\r\n"]}