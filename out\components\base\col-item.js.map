{"version": 3, "file": "col-item.js", "sourceRoot": "", "sources": ["../../../src/components/base/col-item.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAGrD,IAAM,UAAU,GAAhB,MAAM,UAAW,SAAQ,UAAU;IAwBxC,MAAM;QACJ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC;QACnC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC;QAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC;QAChD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC;QAC9B,OAAO,IAAI,CAAA;;;wBAGS,KAAK,sBAAsB,cAAc,kBAAkB,UAAU,WAAW,IAAI;;;;KAIvG,CAAC;IACJ,CAAC;;AA/BM,iBAAM,GAAG,GAAG,CAAA;;;;;;;;;;;;;;;;GAgBlB,AAhBY,CAgBX;AArB0B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8CAAqB;AACpB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;kDAAyB;AACxB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wCAAe;AACd;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;yCAAgB;AAJhC,UAAU;IADtB,aAAa,CAAC,cAAc,CAAC;GACjB,UAAU,CA0CtB", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\n\r\n@customElement(\"wex-col-item\")\r\nexport class WexColItem extends LitElement {\r\n  @property({ type: String }) alignItems?: string;\r\n  @property({ type: String }) justifyContent?: string;\r\n  @property({ type: String }) flex?: string;\r\n  @property({ type: String }) width?: string;\r\n\r\n  static styles = css`\r\n    :host {\r\n      display: flex;\r\n      flex: 1;\r\n      height: 100%;\r\n    }\r\n\r\n    .wrapper {\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      min-height: 40px;\r\n    }\r\n    .wrapper > *:not(:last-child) {\r\n      margin-bottom: 0.5rem;\r\n    }\r\n  `;\r\n\r\n  render() {\r\n    const width = this.width ?? \"auto\";\r\n    const justifyContent = this.justifyContent ?? \"space-evenly\";\r\n    const alignItems = this.alignItems ?? \"stretch\";\r\n    const flex = this.flex ?? \"1\";\r\n    return html`\r\n      <div\r\n        class=\"wrapper\"\r\n        style=\"width: ${width}; justify-content: ${justifyContent}; align-items: ${alignItems}; flex: ${flex};\"\r\n      >\r\n        <slot></slot>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  // protected createRenderRoot(): HTMLElement | DocumentFragment {\r\n  //   return this;\r\n  // }\r\n}\r\n"]}