export declare class DitamapNode {
    /** Unique identifier for the node */
    id: string | null;
    /** Type of DITA element (topicref, mapref, etc.) */
    type: string | null;
    /** Reference to the target resource */
    href?: string | null;
    /** Display title for the node */
    title: string | null;
    /** Name of the containing map */
    containingMap: string | null;
    /** Path hierarchy to this node */
    mapPath: string[];
    /** Children of this node */
    children: DitamapNode[];
    /** Reference to the actual DOM element in XML tree */
    xmlElement: HTMLElement;
    /** Reference to element in parent map (for nested maps) */
    embeddedElement?: HTMLElement | null;
    constructor();
    /** Tag name at the root of the XML element */
    get tagName(): string;
}
export interface DitamapNodeStructure {
    type: string;
    title: string;
    mapName?: string;
    mapPath: string[];
    href: string;
    tagName: string;
    children: DitamapNodeStructure[];
}
//# sourceMappingURL=ditamap-node.d.ts.map