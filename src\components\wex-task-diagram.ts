import { LitElement, html } from "lit";
import { customElement, property } from "lit/decorators.js";
import { Task } from "@bds/types";

/**
 * Displays workflow diagram for selected task
 */
@customElement("wex-task-diagram")
export class WexTaskDiagram extends LitElement {
  @property({ type: Object, attribute: false })
  selectedTask: Task | null = null;

  /* Templates */
  render() {
    return html`
      <img
        id="diagram"
        src="/lwa/jrest/GetTaskDiagram?processInstId=${this.selectedTask
          ?.processInstId}&amp;cachebuster=${Date.now()}"
        style="display: block;margin-left: auto; margin-right: auto;"
      />
    `;
  }
}
