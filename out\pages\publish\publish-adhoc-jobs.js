var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property } from "lit/decorators.js";
// @ts-ignore
import * as wexlib from "lib/wexlib";
import "../../components/header/publish";
import "../../components/publish/publish-outputs-pane";
let WexPagePublishAdhocJobs = class WexPagePublishAdhocJobs extends LitElement {
    constructor() {
        super(...arguments);
        this.outputJobs = [];
        this._refreshOutputJobs = async () => {
            this.outputJobs = await wexlib.getPubDefOutputJobs();
        };
    }
    static get styles() {
        return css `
      wex-publish-outputs-pane {
        width: 50%;
      }
    `;
    }
    connectedCallback() {
        super.connectedCallback();
        this._refreshOutputJobs();
    }
    _renderMainPane() {
        const template = html ` <wex-publish-outputs-pane
      .outputJobs=${this.outputJobs}
      class="right"
    ></wex-publish-outputs-pane>`;
        return template;
        return html `
      <div>
        <pre>${JSON.stringify(this.outputJobs)}</pre>
      </div>
    `;
    }
    render() {
        const template = html `
      <div id="publish-adhoc-jobs-container">
        <wex-publish-header></wex-publish-header>
        <wex-main-pane> ${this._renderMainPane()} </wex-main-pane>
      </div>
    `;
        return template;
    }
};
__decorate([
    property({ type: Array })
], WexPagePublishAdhocJobs.prototype, "outputJobs", void 0);
WexPagePublishAdhocJobs = __decorate([
    customElement("wex-page-publish-adhoc-jobs")
], WexPagePublishAdhocJobs);
export { WexPagePublishAdhocJobs };
//# sourceMappingURL=publish-adhoc-jobs.js.map