import { LitElement, html, css } from "lit";
import { storeInstance } from "store/index.js";
import * as util from "lib/util.ts";
import * as wexlib from "lib/wexlib.js";

import "../../components/base/col-item";
import "../../components/common/wizard-dialog.js";
import "../../components/common/list-picker.js";

import "@ui5/webcomponents/dist/Label.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/Select.js";
import "@ui5/webcomponents/dist/Option.js";

class WexDialogPublishDefsCreate extends LitElement {
  static get properties() {
    return {
      dialogOpenData: { type: Object },
      _state: { type: Object },
      _string: { type: Object },

      _name: { type: String },
      _desc: { type: String },
      _errors: { type: Object },
      _projects: { type: Array },
      _selectedProjectId: { type: Number },

      stage: { type: String },
    };
  }

  static get styles() {
    return css`
      wex-row-item {
        color: var(--font-color);
        margin-bottom: 1rem;
      }

      wex-col-item {
        margin-bottom: 1rem;
      }

      ui5-input {
        width: 100%;
      }

      ui5-select {
        width: 100%;
      }

      .error-msg {
        font-style: italic;
        color: var(--clr-negative);
      }
    `;
  }

  constructor() {
    super();
    this.dialogOpenData = null;

    this._state = {};
    this._string = {};
    this._subscription = null;

    // this.mode = "Add";
    this._mode = "";
    // this.modes = ["create", "edit"];
    this.initialStage = "basic";
    this.stage = "";
    // this.stage = this.initialStage;

    // this.stageComplete = false;

    this.stages = {
      basic: {
        name: "basic",
        title: "Definition Name",
        confirmLabel: "Next",
        confirmAction: () => this._validate(),
        cancelAction: () => this._init(),
      },
      content: {
        name: "content",
        title: "Publication Content",
        confirmLabel: "Next",
        // confirmAction: () => this._confirmPubContent(this._pubContent),
        // confirmAction: () => this._setPubLangs(),
        confirmAction: () => this._validate(),
        cancelAction: () => this._init(),
      },
      languages: {
        name: "languages",
        title: "Languages",
        confirmLabel: "Next",
        confirmAction: () => this._validate(),
        cancelAction: () => this._init(),
      },
      outputs: {
        name: "outputs",
        title: "Output Formats",
        confirmLabel: "Save",
        confirmAction: () => this._confirm(),
        cancelAction: () => this._init(),
      },
    };

    // private properties
    this._name = "";
    this._desc = "";
    this._errors = {
      name: null,
      desc: null,
      project: null,
      selected: null,
    };

    this._projects = [];
    this._selectedProjectId = -1;

    this._pubContent = [];

    this._pubLangs = [];
    this._pubOutputs = [];

    this._selectedPubContent = [];
    this._selectedPubLangs = [];
    this._selectedPubOutputs = [];

    this._pubDefs = [];
    /**
     *
     * pass  all pubdefs, or grab from state?
     * seems like I can get from state since it's non-specific
     * then parse all the names into an array, and
     * use that to validate names
     */
  }

  connectedCallback() {
    super.connectedCallback();
    this._state = storeInstance.state;
    this._string = this._state[this._state.langCode];
    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  updated(changedProperties) {
    const dialog = this.shadowRoot.querySelector("ui5-dialog");
    if (
      dialog &&
      changedProperties.has("dialogOpenData") &&
      this.dialogOpenData
    ) {
      // console.log("dialogOpenData changed");
      this._init();
    }

    if (
      changedProperties.has("_selectedProjectId") &&
      this._selectedProjectId >= 0
    ) {
      this._setMode();
      this._setPubContent();
      this._setPubLangs();
      this._setPubOutputs();
    }
  }

  async _confirmPubContent() {
    return await wexlib.setPubDefPubContents(this._selectedPubContent);
  }

  _setMode() {
    if (this.dialogOpenData && this.dialogOpenData.mode) {
      this._mode = this.dialogOpenData.mode;
    }
  }

  async _confirmOutputs() {
    this._init();
  }

  stateChange(state) {
    this._state = state;
    this._string = state[state.langCode];
    this.dialogOpenData = state.publish_defs_create_dialog_open;
    if (!!this.dialogOpenData) {
      this._setBasicInfo();
      // this._setPubContent();
      // this._setPubLangs();
      // this._setPubOutputs();
    }
  }

  async _setBasicInfo() {
    const pubDef = this.dialogOpenData.pubDef;
    this._errors = {};

    const pubDefs = await wexlib.getAllPubDefs();
    if (pubDefs.length) {
      storeInstance.dispatch("setPubDefs", {
        pubDefs,
      });
    }

    this._pubDefNames = pubDefs.map((pubdef) => pubdef.name);
    // console.log("pubdefNames", this._pubDefNames);

    if (pubDef) {
      this._name = pubDef.name;
      this._desc = pubDef.desc;
      this._selectedProjectId = pubDef.projectId;
    } else {
      this._name = "";
      this._desc = "";
      this._selectedProjectId = -1;
    }
  }

  async _setPubContent() {
    const pubDef = this.dialogOpenData.pubDef;

    const pubContent = await wexlib.getProjPubContents(this._selectedProjectId);
    this._pubContent = pubContent;

    // console.log("set pubContent", this._pubContent);

    if (pubDef) {
      // console.log("pubdef!!!");
      this._selectedPubContent = pubDef.lstPubContentDtos;
      this._pubContent = pubContent.filter(
        (content) =>
          !pubDef.lstPubContentDtos.some(
            (dto) => dto.pubContentId === content.pubContentId
          )
      );
    }
  }

  async _setPubLangs() {
    const pubDef = this.dialogOpenData.pubDef;
    const pubLangs = await wexlib.getProjLanguages(this._selectedProjectId);
    const selectedPubLangs = pubDef?.lstLanguages;
    // console.log("setting langs", pubLangs, selectedPubLangs);

    // set name for picker
    pubLangs.forEach((lang) => {
      lang.name = lang.description;
    });
    if (selectedPubLangs) {
      selectedPubLangs.forEach((lang) => {
        lang.name = lang.description;
      });
    }
    this._pubLangs = pubLangs;

    if (pubDef) {
      this._selectedPubLangs = selectedPubLangs;
      this._pubLangs = pubLangs.filter(
        (lang) => !selectedPubLangs.some((dto) => dto.langId === lang.langId)
      );
    }
  }

  async _setPubOutputs() {
    const pubDef = this.dialogOpenData.pubDef;
    const pubOutputs = await wexlib.getPubEngineOutputs();
    const selectedPubOutputs = pubDef?.lstSelectedOutputFormats.map(
      (engine) => {
        return engine.lstPublishOutputs[0];
      }
    );

    this._pubOutputs = pubOutputs;

    if (pubDef) {
      this._selectedPubOutputs = selectedPubOutputs;
      this._pubOutputs = pubOutputs.filter(
        (output) =>
          !selectedPubOutputs.some(
            (dto) => dto.outputTypeId === output.outputTypeId
          )
      );
    }
  }

  async _init() {
    this.dialogOpenData = this._state.publish_defs_create_dialog_open;
    this.stage = this.initialStage;
    this._selectedProjectId = -1;
    this._selectedPubContent = [];
    this._selectedPubLangs = [];
    this._selectedPubOutputs = [];
    this._name = "";
    this._desc = "";
  }

  async _handleProjectSelect(type = null, e) {
    // console.log("project selection changed");
    try {
      if (!type) return;

      if (type == "project") {
        const id = e.detail.selectedOption.value;
        this._selectedProjectId = id;
        this._errors.project = null;
      }

      // console.log("selected project:", this._selectedProjectId);
    } catch {}
  }

  _handleChange(type = null, e) {
    if (!type) return;
    const value = e.currentTarget.value;
    this[`_${type}`] = value;
    if (!value.length) {
      this._errors[`${type}`] = this._string["_error_empty_value"];
    } else if (!this._isValidString(value)) {
      this._errors[`${type}`] = this._string["_error_invalid_value"];
    } else {
      this._errors[`${type}`] = null;
    }
    this.requestUpdate();
  }

  _isValidString(str) {
    if (!str) return false;

    const rg1 = /^[^\\/:\*\?"<>+%@#&,=~'\|]+$/; // forbidden characters \ / : * ? " < > |
    const rg2 = /^[^\.]/; // cannot start with dot (.)
    const rg3 = /^(nul|prn|con|lpt[0-9]|com[0-9])(\.|$)/i; // forbidden folder names
    return rg1.test(str) && rg2.test(str) && !rg3.test(str);
  }

  _validate = async () => {
    switch (this.stage) {
      case "basic":
        if (!this._name || !this._name.length) {
          this._errors.name = this._string["_error_empty_value"];
        }

        if (
          this.dialogOpenData.mode === "Create" &&
          this._pubDefNames.includes(this._name)
        ) {
          this._errors.name =
            "A publish definition with this name already exists.";
        }

        if (!this._desc || !this._desc.length) {
          this._errors.desc = this._string["_error_empty_value"];
        }
        if (!this._selectedProjectId || this._selectedProjectId == -1) {
          this._errors.project = "A project must be selected.";
        }

        if (
          !!this._errors.name ||
          !!this._errors.desc ||
          !!this._errors.project
        ) {
          this.requestUpdate();
          return false;
        }

        this._setPubContent();
        return true;
      case "content":
        if (!this._selectedPubContent.length) {
          this._errors.selected = "The selected column cannot be empty.";
        } else {
          this._errors = {};
        }

        if (!!this._errors.selected) {
          this.requestUpdate();
          return false;
        }
        return true;
      case "languages":
        if (!this._selectedPubLangs.length) {
          this._errors.selected = "The selected column cannot be empty.";
        } else {
          this._errors = {};
        }

        if (!!this._errors.selected) {
          this.requestUpdate();
          return false;
        }
        return true;
      case "outputs":
        if (!this._selectedPubOutputs.length) {
          this._errors.selected = "The Selected column cannot be empty.";
        } else {
          this._errors = {};
        }

        if (!!this._errors.selected) {
          this.requestUpdate();
          // console.log("validate is false");
          return false;
        }
        // console.log("validate is true");
        return true;
      default:
        this._errors = {};
        return true;
    }
  };

  _confirm = async () => {
    const isValid = await this._validate();
    if (!isValid) {
      // console.log("should close?", isValid);
      return false;
    }
    const body = {
      name: this._name,
      desc: this._desc,
      projectId: this._selectedProjectId,
      pubContent: this._selectedPubContent,
      pubLangs: this._selectedPubLangs,
      pubOutputs: this._selectedPubOutputs,
    };

    if (this._mode === "Edit") {
      body.pubDefId = this.dialogOpenData.pubDef.pubDefId;
      await wexlib.editPubDef(body);
      util.notifyUser(
        "positive",
        this._string["_msg_publish_defs_create_pubdef_success"]
      );
    } else {
      await wexlib.createPubDef(body);
      util.notifyUser(
        "positive",
        this._string["_msg_publish_defs_create_pubdef_success"]
      );
    }
    // this.dispatchEvent(
    //   new CustomEvent("update-def-list", {
    //     bubbles: true,
    //     composed: true,
    //   })
    // );
    this._init(); // reset the dialog

    // refresh the API data
    const pubDefs = await wexlib.getAllPubDefs();
    if (pubDefs.length) {
      storeInstance.dispatch("setPubDefs", {
        pubDefs,
      });
    }

    return true; // true closes the dialog
  };

  _handlePickListUpdated = (e, stage) => {
    // console.log("handle pick list", e.detail);
    switch (stage) {
      case "content":
        this._selectedPubContent = e.detail.selected;
        this._pubContent = e.detail.unselected;
        break;
      case "languages":
        this._selectedPubLangs = e.detail.selected;
        this._pubLangs = e.detail.unselected;
        break;
      case "outputs":
        this._selectedPubOutputs = e.detail.selected;
        this._pubOutputs = e.detail.unselected;
        break;
      default:
        console.error("error in _handlePickListUpdated");
    }
  };

  _renderBasicInfo() {
    const template = html`
      <wex-col-item>
        <wex-col-item>
          <ui5-label required>${this._string["_name"]}</ui5-label>
          <ui5-input
            value="${this._name}"
            @change="${this._handleChange.bind(this, "name")}"
          >
          </ui5-input>
          ${!!this._errors.name
            ? html` <span class="error-msg">${this._errors.name}</span> `
            : html``}
        </wex-col-item>

        <wex-col-item>
          <ui5-label required>${this._string["_description"]}</ui5-label>
          <ui5-input
            value="${this._desc}"
            @change="${this._handleChange.bind(this, "desc")}"
          >
          </ui5-input>
          ${!!this._errors.desc
            ? html` <span class="error-msg">${this._errors.desc}</span> `
            : html``}
        </wex-col-item>

        <wex-col-item>
          <ui5-label required>${this._string["_project"]}</ui5-label>

          <ui5-select
            @change="${this._handleProjectSelect.bind(this, "project")}"
          >
            <ui5-option value="-1"> No project selected </ui5-option>
            ${this.dialogOpenData
              ? this.dialogOpenData.projects.map(
                  (project) => html`
                    <ui5-option
                      ?selected=${this._selectedProjectId == project.projectId}
                      value="${project.projectId}"
                    >
                      ${project.name}
                    </ui5-option>
                  `
                )
              : null}
          </ui5-select>
          ${!!this._errors.project
            ? html` <span class="error-msg">${this._errors.project}</span> `
            : html``}
        </wex-col-item>
      </wex-col-item>
    `;
    return template;
  }

  _renderPicker(stage, selected, unselected) {
    // console.log("_renderPicker", stage, selected, unselected);
    return html`
      <wex-col-item>
        <wex-list-picker
          id="pubdef-${stage}"
          .selected=${selected}
          .unselected=${unselected}
          @pick-list-updated=${(e) => this._handlePickListUpdated(e, stage)}
        ></wex-list-picker>

        ${!!this._errors.selected
          ? html` <span class="error-msg">${this._errors.selected}</span> `
          : html``}
      </wex-col-item>
    `;
  }

  _renderBody() {
    // console.log(
    //   "render wizard body",
    //   this.stage,
    //   this._selectedPubContent,
    //   this._pubContent
    // );
    switch (this.stage) {
      case "basic":
        return this._renderBasicInfo();
      case "content":
        // console.log(
        //   "case content",
        //   this.stage,
        //   this._selectedPubContent,
        //   this._pubContent
        // );
        return this._renderPicker(
          this.stage,
          this._selectedPubContent,
          this._pubContent
        );
      case "languages":
        return this._renderPicker(
          this.stage,
          this._selectedPubLangs,
          this._pubLangs
        );
      case "outputs":
        return this._renderPicker(
          this.stage,
          this._selectedPubOutputs,
          this._pubOutputs
        );
      default:
        return html` <div>Error, ${this.stage}</div> `;
    }
  }

  render() {
    const template = html`
      <wex-wizard-dialog
        .open=${!!this.dialogOpenData}
        headerText="Publish Definition"
        mode=${this.dialogOpenData?.mode}
        width="50%"
        height="70vh"
        .stages=${this.stages}
        initialStage="basic"
        dialogOpenStateProperty="publish_defs_create_dialog_open"
        @set-wizard-stage=${(e) => (this.stage = e.detail)}
      >
        ${this._renderBody()}
      </wex-wizard-dialog>
    `;
    return template;
  }

  renderError() {
    this._errors.length
      ? html`
          <div class="error-msgs">
            ${this._errors.map((error) => html`<span>${error}</span>`)}
          </div>
        `
      : html``;
  }
}

customElements.define(
  "wex-dialog-publish-defs-create",
  WexDialogPublishDefsCreate
);
