{"version": 3, "file": "wex-layout-browse.js", "sourceRoot": "", "sources": ["../../src/components/wex-layout-browse.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAuB,MAAM,KAAK,CAAC;AACtD,OAAO,EAAE,IAAI,EAAU,MAAM,aAAa,CAAC;AAC3C,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,sCAAsC,CAAC;AAC9D,OAAO,MAAM,MAAM,WAAW,CAAC;AAC/B,OAAO,KAAK,IAAI,MAAM,aAAa,CAAC;AACpC,OAAO,UAAU,CAAC;AAElB,0BAA0B;AAC1B,uCAAuC;AACvC,OAAO,oBAAoB,CAAC;AAC5B,OAAO,oDAAoD,CAAC;AAC5D,OAAO,oDAAoD,CAAC;AAC5D,OAAO,iCAAiC,CAAC;AACzC,OAAO,mCAAmC,CAAC;AAC3C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,oCAAoC,CAAC;AAC5C,8CAA8C;AAC9C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,iCAAiC,CAAC;AACzC,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAKlD,IAAM,eAAe,GAArB,MAAM,eAAgB,SAAQ,UAAU;IAmB7C;QACE,KAAK,EAAE,CAAC;QAnBkB,SAAI,GAA+B,IAAI,CAAC;QAExC,WAAM,GAA2B,EAAE,CAAC;QAEpC,eAAU,GAAsB,IAAI,CAAC;QAErC,UAAK,GAAmB,IAAI,CAAC;QAC7B,YAAO,GAAmB,IAAI,CAAC;QAC9B,sBAAiB,GAAY,KAAK,CAAC;QAChE,2EAA2E;QAC/C,eAAU,GAAW,KAAK,CAAC;QAC3B,oBAAe,GAAW,KAAK,CAAC;QAChC,oBAAe,GAAW,KAAK,CAAC;QAE5D,gEAAgE;QAChE,aAAQ,GAAQ,IAAI,CAAC;QAKnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED,2EAA2E;IAC3E,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,QAAQ,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACzC,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC;QAC9D,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,SAAS;QAClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QACvE,IAAI,CAAC,UAAU,GAAG;YAChB,aAAa,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY;YAClD,SAAS,EAAE,MAAM;YACjB,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,UAAU;YACrB,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC;SAC1D,CAAC;QAEF,MAAM,QAAQ,GACZ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;QACtE,oDAAoD;QACpD,oCAAoC;QACpC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,YAAY,CAAC,CAAC;YACzE,IAAI,GAAG,IAAI,CAAC;gBAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;QACtD,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,CACnB,kBAAkB,EAClB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAClC,CAAC;QACF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBACtC,IAAI,CAAC,kBAAkB,CACrB,QAAQ,EACR,OAAO;oBACL,IAAI,CAAC,KAAK,CAAC,QAAQ;oBACnB,UAAU;oBACV,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,EACnC,IAAI,CACL,CAAC;gBACF,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,uBAAuB;oBACjC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY;iBACzC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QACD,aAAa,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IAC9C,CAAC;IAED,oBAAoB;QAClB,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,WAAW,CAAC,KAAU;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAE9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QAC5C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC;QAE9D,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;YACrC,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;YAC9C,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,uBAAuB;gBACjC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;YACvC,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,wBAAwB;gBAClC,KAAK,EAAE,IAAI,CAAC,UAAU;aACvB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC;gBACjE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC1D,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,YAAY,CAAC;YACpE,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC;oBAC9C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;oBACrD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;gBACvD,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;oBAC7B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;QAC9D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC;QACxE,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,4BAA4B;YAC5B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;QACzC,CAAC;IACH,CAAC;IAED,YAAY;QACV,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;QAC/D,IAAI,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;YACxC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,uBAAuB,CAAC,CAAM,EAAE,IAAS;QACvC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACrB,QAAQ,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAC9B,KAAK,QAAQ;gBACX,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,2BAA2B;oBACrC,KAAK,EAAE,IAAI,CAAC,IAAI;iBACjB,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,iBAAiB;gBACpB,IAAI,cAAc,GAAG,KAAK,CAAC;gBAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,IAAI,YAAY,EAAE,CAAC;oBACrD,cAAc,GAAG,IAAI,CAAC;gBACxB,CAAC;gBACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,yCAAyC;oBACnD,KAAK,EAAE;wBACL,MAAM,EAAE,QAAQ;wBAChB,MAAM,EAAE,IAAI,CAAC,IAAI;wBACjB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;wBACvC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;wBACnC,aAAa,EAAE,cAAc;qBAC9B;iBACF,CAAC,CAAC;gBACH,2JAA2J;gBAC3J,MAAM;YACR,KAAK,oBAAoB;gBACvB,IAAI,cAAc,GAAG,KAAK,CAAC;gBAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,IAAI,YAAY,EAAE,CAAC;oBACrD,cAAc,GAAG,IAAI,CAAC;gBACxB,CAAC;gBACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,yCAAyC;oBACnD,KAAK,EAAE;wBACL,MAAM,EAAE,QAAQ;wBAChB,MAAM,EAAE,IAAI,CAAC,IAAI;wBACjB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB;wBACvC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;wBACnC,aAAa,EAAE,cAAc;qBAC9B;iBACF,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,eAAe;gBAClB,MAAM,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAC3D,IAAI,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;gBACjE,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;gBAC3B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,gBAAgB;oBAC1B,KAAK,EAAE,OAAO;iBACf,CAAC,CAAC;gBACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,sBAAsB;oBAChC,KAAK,EAAE,IAAI,CAAC,IAAI;iBACjB,CAAC,CAAC;gBACH,MAAM,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;gBACrD,MAAM;YACR,KAAK,eAAe;gBAClB,MAAM,CAAC,OAAO,CAAC,kCAAkC,EAAE;oBACjD,MAAM,EAAE,IAAI,CAAC,IAAI;iBAClB,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,eAAe;gBAClB,MAAM,CAAC,OAAO,CAAC,kCAAkC,EAAE;oBACjD,MAAM,EAAE,IAAI,CAAC,IAAI;iBAClB,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,YAAY;gBACf,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,0BAA0B;oBACpC,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,eAAe;gBAClB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;gBACrC,uCAAuC;gBACvC,0CAA0C;gBAC1C,sBAAsB;gBACtB,MAAM;gBACN,MAAM;YACR,KAAK,eAAe;gBAClB,MAAM,CAAC,OAAO,CAAC,kCAAkC,EAAE;oBACjD,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;oBACtB,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;oBAC1B,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;iBACnD,CAAC,CAAC;gBACH,MAAM;QACV,CAAC;IACH,CAAC;IAED,iBAAiB,CAAC,CAAM;QACtB,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;QAC5C,IAAI,YAAY,KAAK,QAAQ;YAAE,OAAO;QACtC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,EAAE;YACzC,YAAY,EAAE,YAAY;YAC1B,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,IAAS,EAAE,MAAW;QAChC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO;QAEhC,6CAA6C;QAC7C,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAEvD,qBAAqB;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CACnD,IAAI,EACJ,MAAM,CAAC,MAAM,CAAC,EACd,EAAE,CACH,CAAC;QACF,IAAI,CAAC,MAAM;YAAE,OAAO;QACpB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;QACxB,IAAI,CAAC,kBAAkB,CACrB,QAAQ,EACR,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,GAAG,IAAI,CAAC,EAAE,EACpD,IAAI,CACL,CAAC;QACF,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,wBAAwB;YAClC,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE;SAChD,CAAC,CAAC;QACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB;QAClB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC;QACjD,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAED,oBAAoB,CAAC,OAAY,EAAE,IAAS;QAC1C,OAAO,IAAI,CAAA;;gBAEC,IAAI,CAAC,WAAW;wBACR,CAAC,CAAM,EAAE,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC;;QAEjE,OAAO;2BACY,CAAC;IAC1B,CAAC;IAED,MAAM;QACJ,8CAA8C;QAC9C,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,OAAO,IAAI,CAAA,uBAAuB,CAAC;QACrC,CAAC;QAED,OAAO,IAAI,CAAA;;;;;;;;;;;;;;;mBAeI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,wBAAwB;;;;;mBAKvD,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAqDzC,IAAI,CAAC,KAAK,CAAC,mBAAmB;aACrD,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBA+EJ,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC;qBACrC,IAAI,CAAC,oBAAoB;YAChC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI;YAChC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC;;cAEtC,IAAI,CAAC,oBAAoB;YACzB,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI;YAChC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC;;;yBAG3B,CAAC,IAAI,CAAC,oBAAoB;;sBAE7B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;qBAE9B,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC;;;;uBAIpC,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC;;;;;;;;;;;sBAWvC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;yBAO3B,IAAI,CAAC,iBAAiB;;;;;;uBAMxB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEvC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAC3B,CAAC,IAAS,EAAE,EAAE,CACZ,IAAI,CAAA;+BACW,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,IAAI;2BACzC,IAAI;qBACV,IAAI,CAAC,WAAW;kBACnB,CACL;;;;;;;uBAOU,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;;cAEzC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,CAC9B,CAAC,IAAS,EAAE,EAAE,CACZ,IAAI,CAAA;+BACW,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,IAAI;2BAC3C,IAAI;qBACV,IAAI,CAAC,IAAI;kBACZ,CACL;;;;;;;uBAOU,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;;cAE3C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAC9B,CAAC,IAAS,EAAE,EAAE,CACZ,IAAI,CAAA;+BACW,IAAI,CAAC,KAAK,CAAC,sBAAsB,IAAI,IAAI,CAAC,KAAK;2BACnD,IAAI,CAAC,KAAK;qBAChB,IAAI,CAAC,IAAI;kBACZ,CACL;;;;qBAIQ,IAAI,CAAC,eAAe;;sBAEnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;qBAC5B,IAAI,CAAC,eAAe;;oBAErB,IAAI,CAAC,eAAe;;;;;;;;;sBASlB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;eACpC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;;;;sBAIrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;;eAG/B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;;;;;;sBAOhB,IAAI,CAAC,UAAU;oBACjB,IAAI,CAAC,UAAU;0BACT,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;;;;wBAIjC,IAAI,CAAC,UAAU;4BACX,IAAI;+BACD,IAAI,CAAC,KAAK,CAAC,sBAAsB;4BACpC,IAAI,CAAC,WAAW;+BACb,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;KAOzD,CAAC;IACJ,CAAC;IAED,WAAW,CAAC,EAAO;QACjB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,kBAAkB;YAC5B,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,QAAQ;gBAClD,UAAU,EAAE,oBAAoB;aACjC;SACF,CAAC,CAAC;IACL,CAAC;IACD,aAAa,CAAC,EAAO;QACnB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,oBAAoB;YAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;SAC/B,CAAC,CAAC;QACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,oBAAoB;YAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;SACtC,CAAC,CAAC;QACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB;SACxC,CAAC,CAAC;QACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,wBAAwB;YAClC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,wBAAwB;YAClC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QACH,4BAA4B;QAC5B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IACD,QAAQ,CAAC,EAAO;QACd,4BAA4B;QAC5B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IAED,eAAe,CAAC,CAAM;QACpB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACf,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YAC7D,IAAI,CAAC,kBAAkB,CACrB,QAAQ,EACR,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAC7D,IAAI,CACL,CAAC;YACF,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;YACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,wBAAwB;gBAClC,KAAK,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;aAClE,CAAC,CAAC;YACH,4BAA4B;YAC5B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,cAAc,CAAC,CAAM;QACnB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,IACE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM;gBACnC,IAAI,CAAC,oBAAoB,CAAC,UAAU,EACpC,CAAC;gBACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,wBAAwB;oBAClC,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,oBAAoB;YAC9B,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI;SACpC,CAAC,CAAC;QACH,4BAA4B;QAC5B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IACD,gBAAgB,CAAC,CAAM;QACrB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,IACE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ;gBACrC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAClC,CAAC;gBACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,wBAAwB;oBAClC,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI;SACpC,CAAC,CAAC;QACH,4BAA4B;QAC5B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IACD,kBAAkB,CAAC,CAAM;QACvB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,wBAAwB;YAClC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK;SACrC,CAAC,CAAC;QACH,4BAA4B;QAC5B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB;QAClB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,qBAAqB;YAC/B,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;SAC5D,CAAC,CAAC;IACL,CAAC;IAED,sBAAsB,CAAC,GAAY;QACjC,IAAI,GAAG,EAAE,CAAC;YACR,IAAI,QAAQ,CAAC;YACb,IAAI,UAAU,CAAC;YACf,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,IAAS;gBACzD,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG,EAAE,UAAU,CAAC;YACzC,CAAC,CAAC,CAAC;YACH,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,IAAS;gBAC9D,OAAO,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC;YACxC,CAAC,CAAC,CAAC;YACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,oBAAoB;gBAC9B,KAAK,EAAE,QAAQ;aAChB,CAAC,CAAC;YACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,sBAAsB;gBAChC,KAAK,EAAE,UAAU;aAClB,CAAC,CAAC;YACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,wBAAwB;gBAClC,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,uDAAuD;YACvD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACjC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAC9B,IAAI,CAAC,UAAU,EACf,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CACpC,CAAC;gBACF,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CACnD,IAAI,CAAC,UAAU,EACf,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,EACnC,EAAE,CACH,CAAC;gBACF,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;oBACxB,IAAI,CAAC,kBAAkB,CACrB,QAAQ,EACR,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,GAAG,IAAI,CAAC,EAAE,EACpD,IAAI,CACL,CAAC;oBACF,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACjC,QAAQ,EAAE,wBAAwB;wBAClC,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE;qBAChD,CAAC,CAAC;oBACH,4BAA4B;oBAC5B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACjC,QAAQ,EAAE,sBAAsB;wBAChC,KAAK,EAAE,IAAI;qBACZ,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,wBAAwB;gBAClC,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,aAAa,CAAC,EAAO;QACnB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,IAAI,CAAC,WAAW,CACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAClD,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AApxB6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;6CAAyC;AACxC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8CAAY;AACX;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;+CAAqC;AACpC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;qDAAmB;AAClB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;mDAAsC;AACrC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;oDAAkB;AACjB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8CAA8B;AAC7B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;gDAAgC;AAC9B;IAA5B,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;0DAAoC;AAEpC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;mDAA4B;AAC3B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wDAAiC;AAChC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wDAAiC;AAbjD,eAAe;IAD3B,aAAa,CAAC,mBAAmB,CAAC;GACtB,eAAe,CAqxB3B", "sourcesContent": ["import { LitElement, PropertyValues, css } from \"lit\";\r\nimport { html, render } from \"lit/html.js\";\r\nimport { storeInstance } from \"store/index.js\";\r\nimport { Router } from \"@vaadin/router/dist/vaadin-router.js\";\r\nimport PubSub from \"pubsub-js\";\r\nimport * as util from \"lib/util.ts\";\r\nimport \"bds-tree\";\r\n\r\n// import \"./wex-folders\";\r\n// import \"./wex-browse-folders-panel\";\r\nimport \"./wex-browse-files\";\r\nimport \"@vaadin/vaadin-split-layout/vaadin-split-layout.js\";\r\nimport \"@vaadin/vaadin-context-menu/vaadin-context-menu.js\";\r\nimport \"@polymer/iron-icon/iron-icon.js\";\r\nimport \"@polymer/iron-icons/iron-icons.js\";\r\nimport \"@vaadin/vaadin-icons/vaadin-icons.js\";\r\nimport \"@ui5/webcomponents/dist/DatePicker\";\r\n// import \"@ui5/webcomponents/dist/assets.js\";\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\nimport \"@ui5/webcomponents/dist/Dialog.js\";\r\nimport \"@ui5/webcomponents/dist/Label.js\";\r\nimport \"@ui5/webcomponents/dist/Input.js\";\r\nimport \"@ui5/webcomponents/dist/Icon.js\";\r\nimport \"@ui5/webcomponents/dist/Select.js\";\r\nimport \"@ui5/webcomponents/dist/Option.js\";\r\nimport { customElement, property } from \"lit/decorators\";\r\nimport { BDSTree } from \"bds-tree\";\r\nimport { Collection, Project } from \"@bds/types\";\r\n\r\n@customElement(\"wex-layout-browse\")\r\nexport class WexLayoutBrowse extends LitElement {\r\n  @property({ type: Object }) tree: BDSTree<Collection> | null = null;\r\n  @property({ type: Object }) state: any;\r\n  @property({ type: Object }) string: Record<string, string> = {};\r\n  @property({ type: Object }) subscription: any;\r\n  @property({ type: Object }) folderData: Collection | null = null;\r\n  @property({ type: Object }) contextMenu: any;\r\n  @property({ type: Object }) files: Element | null = null;\r\n  @property({ type: Object }) folders: Element | null = null;\r\n  @property({ type: Boolean }) _showMobileFilter: boolean = false;\r\n  // @property({ type: Object }) browseProjectContext: Project | null = null;\r\n  @property({ type: String }) formerAsof: string = \"Now\";\r\n  @property({ type: String }) asOfNameDisplay: string = \"Now\";\r\n  @property({ type: String }) asOfDateDisplay: string = \"Now\";\r\n\r\n  // Router location property - set automatically by Vaadin Router\r\n  location: any = null;\r\n  treeConfig: any;\r\n\r\n  constructor() {\r\n    super();\r\n    this.state = null;\r\n    this.subscription = null;\r\n  }\r\n\r\n  // Getter to handle router location - Vaadin Router sets this automatically\r\n  get routerLocation() {\r\n    return this.location || { params: {} };\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this.state = storeInstance.state;\r\n    this.subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    this.browseProjectContext = this.state.browse_project_context;\r\n    this.formerAsof = \"Now\"; //testing\r\n    this.string = this.state[this.state.langCode];\r\n    this.folderData = this.state.global_folders;\r\n    this.contextMenu = structuredClone(this.state.menus.folder_menu) ?? [];\r\n    this.treeConfig = {\r\n      initialNodeId: this.location?.params?.collectionId,\r\n      labelPath: \"name\",\r\n      idPath: \"id\",\r\n      childPath: \"children\",\r\n      contextMenuRenderer: this._contextMenuRenderer.bind(this),\r\n    };\r\n\r\n    const canPaste =\r\n      !!this.state.browse_file_to_copy || !!this.state.browse_file_to_cut;\r\n    // TEMP -- disable paste if nothing is copied or cut\r\n    // need to find a different solution\r\n    if (!canPaste) {\r\n      let idx = this.contextMenu.findIndex((x: any) => x.name == \"paste_file\");\r\n      if (idx >= 0) this.contextMenu[idx].disabled = true;\r\n    }\r\n    this.contextMenu.forEach((item: any) => {\r\n      item.text = this.string[item.label];\r\n    });\r\n    this.addEventListener(\r\n      \"folderdropfolder\",\r\n      this._folderDropFolder.bind(this)\r\n    );\r\n    if (this.location) {\r\n      if (this.location.params.collectionId) {\r\n        util.updatePageLocation(\r\n          \"browse\",\r\n          \"/wex/\" +\r\n            this.state.langCode +\r\n            \"/browse/\" +\r\n            this.location.params.collectionId,\r\n          true\r\n        );\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"selected_collectionId\",\r\n          value: this.location.params.collectionId,\r\n        });\r\n      }\r\n    }\r\n    storeInstance.dispatch(\"getCollectionTree\");\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  stateChange(state: any) {\r\n    this.state = state;\r\n    this.string = this.state[this.state.langCode];\r\n\r\n    this.folderData = this.state.global_folders;\r\n    this.browseProjectContext = this.state.browse_project_context;\r\n\r\n    if (this.state.selected_collectionId) {\r\n      var collId = this.state.selected_collectionId;\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"selected_collectionId\",\r\n        value: null,\r\n      });\r\n      this._folderById(this.folderData, collId);\r\n    }\r\n\r\n    if (!this.state.browse_selected_folder) {\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_selected_folder\",\r\n        value: this.folderData,\r\n      });\r\n    }\r\n\r\n    if (this.state.browse_asof_filter) {\r\n      if (this.state.browse_asof_filter.hasOwnProperty(\"gmtLabelDate\")) {\r\n        this.asOfNameDisplay = this.state.browse_asof_filter.name;\r\n        this.asOfDateDisplay = this.state.browse_asof_filter.gmtLabelDate;\r\n      } else {\r\n        if (Date.parse(this.state.browse_asof_filter)) {\r\n          this.asOfNameDisplay = this.state.browse_asof_filter;\r\n          this.asOfDateDisplay = this.state.browse_asof_filter;\r\n        } else {\r\n          this.asOfNameDisplay = \"Now\";\r\n          this.asOfDateDisplay = \"Now\";\r\n        }\r\n      }\r\n    }\r\n\r\n    this.files = this.shadowRoot?.querySelector(\"#files\") || null;\r\n    this.folders = this.shadowRoot?.querySelector(\"#browsefolders\") || null;\r\n    if (this.formerAsof != this.asOfNameDisplay) {\r\n      // this.files.refreshData();\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_refresh_files\",\r\n        value: true,\r\n      });\r\n      this.formerAsof = this.asOfNameDisplay;\r\n    }\r\n  }\r\n\r\n  firstUpdated(): void {\r\n    this.tree = this.shadowRoot?.querySelector(\"bds-tree\") || null;\r\n    if (this.location?.params?.collectionId) {\r\n      this._folderById(this.folderData, this.location.params.collectionId);\r\n    }\r\n  }\r\n\r\n  _folderContextMenuClick(e: any, data: any) {\r\n    console.log(e, data);\r\n    switch (e.detail?.value?.name) {\r\n      case \"import\":\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"folder_import_dialog_open\",\r\n          value: data.node,\r\n        });\r\n        break;\r\n      case \"newfromtemplate\":\r\n        var tmpLockFolders = false;\r\n        if (this.state.wex_user.effectiveCap == \"contribute\") {\r\n          tmpLockFolders = true;\r\n        }\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"extended_folder_create_file_dialog_open\",\r\n          value: {\r\n            target: \"folder\",\r\n            folder: data.node,\r\n            branch: this.state.browse_branch_filter,\r\n            lang: this.state.browse_lang_filter,\r\n            lockedFolders: tmpLockFolders,\r\n          },\r\n        });\r\n        //storeInstance.dispatch(\"setState\", {property: \"folder_create_file_dialog_open\", value:{target: \"folder\", payload: this.state.browse_selected_folder } } )\r\n        break;\r\n      case \"extnewfromtemplate\":\r\n        var tmpLockFolders = false;\r\n        if (this.state.wex_user.effectiveCap == \"contribute\") {\r\n          tmpLockFolders = true;\r\n        }\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"extended_folder_create_file_dialog_open\",\r\n          value: {\r\n            target: \"folder\",\r\n            folder: data.node,\r\n            branch: this.state.browse_branch_filter,\r\n            lang: this.state.browse_lang_filter,\r\n            lockedFolders: tmpLockFolders,\r\n          },\r\n        });\r\n        break;\r\n      case \"search_folder\":\r\n        const filters = structuredClone(this.state.search_filters);\r\n        let folderFilter = filters.find((x: any) => x.name === \"folder\");\r\n        folderFilter.active = true;\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"search_filters\",\r\n          value: filters,\r\n        });\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"search_folder_filter\",\r\n          value: data.node,\r\n        });\r\n        Router.go(\"/wex/\" + this.state.langCode + \"/search\");\r\n        break;\r\n      case \"create_folder\":\r\n        PubSub.publish(\"dialog-browse-create-folder-open\", {\r\n          folder: data.node,\r\n        });\r\n        break;\r\n      case \"rename_folder\":\r\n        PubSub.publish(\"dialog-browse-rename-folder-open\", {\r\n          folder: data.node,\r\n        });\r\n        break;\r\n      case \"import_zip\":\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"import_files_dialog_open\",\r\n          value: true,\r\n        });\r\n        break;\r\n      case \"export_folder\":\r\n        console.log(\"Export folder clicked\");\r\n        // storeInstance.dispatch(\"setState\", {\r\n        //   property: \"export_files_dialog_open\",\r\n        //   value: data.node,\r\n        // });\r\n        break;\r\n      case \"delete_folder\":\r\n        PubSub.publish(\"dialog-browse-delete-folder-open\", {\r\n          folderId: data.node.id,\r\n          folderName: data.node.name,\r\n          branchId: this.state.browse_branch_filter.branchId,\r\n        });\r\n        break;\r\n    }\r\n  }\r\n\r\n  _folderDropFolder(e: any) {\r\n    const { collectionId, parentId } = e.detail;\r\n    if (collectionId === parentId) return;\r\n    storeInstance.dispatch(\"browseMoveFolder\", {\r\n      collectionId: collectionId,\r\n      parentId: parentId,\r\n    });\r\n  }\r\n\r\n  _folderById(root: any, collId: any) {\r\n    if (!this.tree || !root) return;\r\n\r\n    // First set the node to expand the tree path\r\n    this.tree.treeController.setNode(root, String(collId));\r\n\r\n    // Then find the node\r\n    const result = this.tree.treeController.findNodeByKey(\r\n      root,\r\n      String(collId),\r\n      []\r\n    );\r\n    if (!result) return;\r\n    const { node } = result;\r\n    util.updatePageLocation(\r\n      \"browse\",\r\n      \"/wex/\" + this.state.langCode + \"/browse/\" + node.id,\r\n      true\r\n    );\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_selected_folder\",\r\n      value: { ...node, path: node.collectionPathId },\r\n    });\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_refresh_files\",\r\n      value: true,\r\n    });\r\n  }\r\n\r\n  _selectedProjectName() {\r\n    if (this.browseProjectContext) {\r\n      return this.browseProjectContext.name;\r\n    } else {\r\n      return \"\";\r\n    }\r\n  }\r\n\r\n  _toggleFilters() {\r\n    this._showMobileFilter = !this._showMobileFilter;\r\n    storeInstance.dispatch(\"touch\");\r\n  }\r\n\r\n  _contextMenuRenderer(content: any, data: any) {\r\n    return html`<vaadin-context-menu\r\n      id=\"contextmenu\"\r\n      .items=\"${this.contextMenu}\"\r\n      @item-selected=\"${(e: any) => this._folderContextMenuClick(e, data)}\"\r\n    >\r\n      ${content}\r\n    </vaadin-context-menu>`;\r\n  }\r\n\r\n  render() {\r\n    // Defensive check to prevent rendering issues\r\n    if (!this.state || !this.string) {\r\n      return html`<div>Loading...</div>`;\r\n    }\r\n\r\n    return html`\r\n      <style>\r\n        #browseheader {\r\n          border-top: 1px solid;\r\n          border-bottom: 1px solid;\r\n          border-color: var(--primary-100);\r\n          padding: var(--spacing-md) var(--spacing-sm);\r\n          display: flex;\r\n          justify-content: space-between;\r\n          gap: var(--spacing-xxs);\r\n          align-items: center;\r\n        }\r\n\r\n        #folderpane {\r\n          height: calc(100vh - 7rem);\r\n          width: ${this.state.global_screen_specs.browse_leftSideWidthPerc}%;\r\n          overflow: auto;\r\n        }\r\n        #filespane {\r\n          height: calc(100vh - 7rem);\r\n          width: ${this.state.global_screen_specs.browse_rightSideWidthPerc}%;\r\n          overflow: auto;\r\n        }\r\n        #layout {\r\n          height: 100%;\r\n        }\r\n\r\n        .btn {\r\n          display: flex;\r\n          align-items: center;\r\n          background-color: #fbfbfb;\r\n          border: 1px solid white;\r\n          border-radius: 2px;\r\n          justify-content: space-between;\r\n          overflow: hidden;\r\n        }\r\n        .btn:hover {\r\n          border: 1px solid var(--clr-primary);\r\n        }\r\n        .btn > * {\r\n          color: inherit;\r\n        }\r\n        .btn > span {\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n        }\r\n        .project-filter-btn {\r\n          border: 1px solid; */\r\n        }\r\n        .action-el {\r\n          cursor: pointer;\r\n        }\r\n        #headerleft {\r\n          grid-column: 1 / 2;\r\n          justify-self: start;\r\n          display: flex;\r\n          align-items: center;\r\n        }\r\n        #headerfiltericon {\r\n          display: flex;\r\n          justify-content: center;\r\n        }\r\n        #headerright {\r\n          grid-column: 3 / 4;\r\n          justify-self: end;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: var(--spacing-xs)\r\n        }\r\n        @media screen and (min-width: 0px) {\r\n          /* #browseheader {\r\n            display: grid;\r\n            grid-template-columns: minmax(0, 1fr) min-content minmax(0, 1fr);\r\n            grid-template-rows: ${this.state.global_screen_specs\r\n          .subheaderHeight}px 1fr;\r\n            align-items: center;\r\n          } */\r\n          .mobile {\r\n            display: block;\r\n          }\r\n          #headerfilters {\r\n            grid-column: 2 / 3;\r\n            grid-row: 2 / 3;\r\n            justify-self: center;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            padding-bottom: 8px;\r\n          }\r\n          #headerfilters[data-mobile=\"true\"] {\r\n            display: flex;\r\n          }\r\n          #headerfilters[data-mobile=\"false\"] {\r\n            display: none;\r\n          }\r\n          .filter-btn {\r\n            width: fit-content;\r\n          }\r\n          .filterinputs {\r\n            --_ui5_input_width: 8rem;\r\n          }\r\n        }\r\n        @media screen and (min-width: 780px) {\r\n          #headerfilters {\r\n            grid-column: 1 / 4;\r\n            grid-row: 2 / 3;\r\n            justify-self: center;\r\n            flex-direction: row;\r\n            align-items: center;\r\n            padding-bottom: 8px;\r\n          }\r\n          er-btn {\r\n            width: calc(7rem - 2 * var(--padding));\r\n          }\r\n        }\r\n        @media screen and (min-width: 1280px) {\r\n          .desktop {\r\n            display: block;\r\n          }\r\n          .mobile {\r\n            display: none;\r\n          }\r\n          #browseheader {\r\n            display: flex;\r\n            flex-direction: row;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n          }\r\n          #headermiddle {\r\n            grid-column: 2 / 3;\r\n            justify-self: center;\r\n          }\r\n          #headerfilters {\r\n            grid-column: 2 / 3;\r\n            justify-self: center;\r\n            flex-direction: row;\r\n            align-items: center;\r\n            padding-bottom: 0px;\r\n          }\r\n          #headerfilters[data-mobile=\"true\"],\r\n          #headerfilters[data-mobile=\"false\"] {\r\n            display: flex;\r\n          }\r\n\r\n          iron-icon {\r\n            width: 1rem;\r\n          }\r\n        }\r\n      </style>\r\n\r\n      <div id=\"browseheader\">\r\n        <div id=\"headerleft\">\r\n          <ui5-button\r\n            class=\"btn dialog-btn project-filter-btn action-el\"\r\n            @click=\"${this._projectSelectDialog.bind(this)}\"\r\n            title=\"${this.browseProjectContext\r\n              ? this.browseProjectContext.name\r\n              : this.string[\"_select_project_label\"]}\"\r\n          >\r\n            ${this.browseProjectContext\r\n              ? this.browseProjectContext.name\r\n              : this.string[\"_select_project_label\"]}\r\n          </ui5-button>\r\n          <ui5-button\r\n            ?disabled=\"${!this.browseProjectContext}\"\r\n            id=\"jump-proj-fldr-btn\"\r\n            @click=\"${this._jumpToFolder.bind(this)}\"\r\n            class=\"ui5button ui5-content-density-compact\"\r\n            title=\"${this.string[\"_jump_to_project_folder\"]}\"\r\n          >\r\n            <iron-icon\r\n              icon=\"vaadin:arrow-circle-right-o\"\r\n              title=\"${this.string[\"_jump_to_project_folder\"]}\"\r\n            >\r\n            </iron-icon>\r\n          </ui5-button>\r\n        </div>\r\n\r\n        <div id=\"headerfiltericon\" class=\"mobile\">\r\n          <iron-icon\r\n            id=\"headermiddle\"\r\n            class=\"mobile action-el\"\r\n            icon=\"vaadin:sliders\"\r\n            @click=\"${this._toggleFilters.bind(this)}\"\r\n          >\r\n          </iron-icon>\r\n        </div>\r\n        <div\r\n          id=\"headerfilters\"\r\n          class=\"desktop\"\r\n          data-mobile=\"${this._showMobileFilter}\"\r\n        >\r\n          <ui5-select\r\n            class=\"filterinputs\"\r\n            id=\"langselector\"\r\n            class=\"ui5-content-density-compact\"\r\n            @change=\"${this._setLangFilter.bind(this)}\"\r\n          >\r\n            ${this.state.global_langs.map(\r\n              (item: any) =>\r\n                html`<ui5-option\r\n                  ?selected=\"${this.state.browse_lang_filter == item}\"\r\n                  .item=\"${item}\"\r\n                  >${item.description}</ui5-option\r\n                >`\r\n            )}\r\n          </ui5-select>\r\n\r\n          <ui5-select\r\n            class=\"filterinputs\"\r\n            id=\"branchselector\"\r\n            class=\"ui5-content-density-compact\"\r\n            @change=\"${this._setBranchFilter.bind(this)}\"\r\n          >\r\n            ${this.state.global_branches.map(\r\n              (item: any) =>\r\n                html`<ui5-option\r\n                  ?selected=\"${this.state.browse_branch_filter == item}\"\r\n                  .item=\"${item}\"\r\n                  >${item.name}</ui5-option\r\n                >`\r\n            )}\r\n          </ui5-select>\r\n\r\n          <ui5-select\r\n            class=\"filterinputs\"\r\n            id=\"mimetypeselector\"\r\n            class=\"ui5-content-density-compact\"\r\n            @change=\"${this._setMimetypeFilter.bind(this)}\"\r\n          >\r\n            ${this.state.menus.mimetypes.map(\r\n              (item: any) =>\r\n                html`<ui5-option\r\n                  ?selected=\"${this.state.browse_mimetype_filter == item.value}\"\r\n                  value=\"${item.value}\"\r\n                  >${item.name}</ui5-option\r\n                >`\r\n            )}\r\n          </ui5-select>\r\n\r\n          <ui5-button\r\n            value=\"${this.asOfDateDisplay}\"\r\n            class=\"btn dialog-btn filter-btn action-el\"\r\n            @click=\"${this._asOfDialog.bind(this)}\"\r\n            title=\"${this.asOfDateDisplay}\"\r\n          >\r\n            <span>${this.asOfNameDisplay}</span>\r\n            <iron-icon icon=\"icons:date-range\" title=\"Select date range\">\r\n            </iron-icon>\r\n          </ui5-button>\r\n        </div>\r\n\r\n        <div id=\"headerright\">\r\n          <ui5-button\r\n            class=\"ui5button ui5-content-density-compact\"\r\n            @click=\"${this._clearFilters.bind(this)}\"\r\n            >${this.string[\"_clearfilters\"]}\r\n          </ui5-button>\r\n          <ui5-button\r\n            id=\"refreshbtn\"\r\n            @click=\"${this._refresh.bind(this)}\"\r\n            class=\"ui5-content-density-compact\"\r\n            design=\"Emphasized\"\r\n            >${this.string[\"_refresh\"]}</ui5-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <vaadin-split-layout id=\"layout\">\r\n        <div id=\"folderpane\">\r\n          <bds-tree\r\n            .config=${this.treeConfig}\r\n            .root=${this.folderData}\r\n            @treeClick=\"${this._folderSelected.bind(this)}\"\r\n          ></bds-tree>\r\n          <!-- <wex-folders\r\n            id=\"browsefolders\"\r\n            .folders=\"${this.folderData}\"\r\n            .isDraggable=\"${true}\"\r\n            .selectedfolder=\"${this.state.browse_selected_folder}\"\r\n            .contextmenu=\"${this.contextMenu}\"\r\n            @folderselected=\"${this._folderSelected.bind(this)}\"\r\n          ></wex-folders> -->\r\n        </div>\r\n        <div id=\"filespane\">\r\n          <wex-browse-files id=\"files\"></wex-browse-files>\r\n        </div>\r\n      </vaadin-split-layout>\r\n    `;\r\n  }\r\n\r\n  _asOfDialog(_e: any) {\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"asof_dialog_open\",\r\n      value: {\r\n        branchId: this.state.browse_branch_filter.branchId,\r\n        statefield: \"browse_asof_filter\",\r\n      },\r\n    });\r\n  }\r\n  _clearFilters(_e: any) {\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_asof_filter\",\r\n      value: this.state.default_asof,\r\n    });\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_lang_filter\",\r\n      value: this.state.default_lang_object,\r\n    });\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_branch_filter\",\r\n      value: this.state.default_branch_object,\r\n    });\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_mimetype_filter\",\r\n      value: null,\r\n    });\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_project_context\",\r\n      value: null,\r\n    });\r\n    // this.files.refreshData();\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_refresh_files\",\r\n      value: true,\r\n    });\r\n  }\r\n  _refresh(_e: any) {\r\n    // this.files.refreshData();\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_refresh_files\",\r\n      value: true,\r\n    });\r\n  }\r\n\r\n  _folderSelected(e: any) {\r\n    console.log(e);\r\n    if (this.state.browse_selected_folder.id != e.detail.node.id) {\r\n      util.updatePageLocation(\r\n        \"browse\",\r\n        \"/wex/\" + this.state.langCode + \"/browse/\" + e.detail.node.id,\r\n        true\r\n      );\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_selected_file\",\r\n        value: null,\r\n      });\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_selected_folder\",\r\n        value: { ...e.detail.node, path: e.detail.node.collectionPathId },\r\n      });\r\n      // this.files.refreshData();\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_refresh_files\",\r\n        value: true,\r\n      });\r\n    }\r\n  }\r\n\r\n  _setLangFilter(e: any) {\r\n    if (this.browseProjectContext) {\r\n      if (\r\n        e.detail.selectedOption.item.langId !=\r\n        this.browseProjectContext.languageId\r\n      ) {\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"browse_project_context\",\r\n          value: null,\r\n        });\r\n      }\r\n    }\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_lang_filter\",\r\n      value: e.detail.selectedOption.item,\r\n    });\r\n    // this.files.refreshData();\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_refresh_files\",\r\n      value: true,\r\n    });\r\n  }\r\n  _setBranchFilter(e: any) {\r\n    if (this.browseProjectContext) {\r\n      if (\r\n        e.detail.selectedOption.item.branchId !=\r\n        this.browseProjectContext.branchId\r\n      ) {\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"browse_project_context\",\r\n          value: null,\r\n        });\r\n      }\r\n    }\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_branch_filter\",\r\n      value: e.detail.selectedOption.item,\r\n    });\r\n    // this.files.refreshData();\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_refresh_files\",\r\n      value: true,\r\n    });\r\n  }\r\n  _setMimetypeFilter(e: any) {\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_mimetype_filter\",\r\n      value: e.detail.selectedOption.value,\r\n    });\r\n    // this.files.refreshData();\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"browse_refresh_files\",\r\n      value: true,\r\n    });\r\n  }\r\n\r\n  _projectSelectDialog() {\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"project_dialog_open\",\r\n      value: { callback: this._selectProjectCallback.bind(this) },\r\n    });\r\n  }\r\n\r\n  _selectProjectCallback(prj: Project) {\r\n    if (prj) {\r\n      var langItem;\r\n      var branchItem;\r\n      langItem = this.state.global_langs.find(function (item: any) {\r\n        return item.langId === prj?.languageId;\r\n      });\r\n      branchItem = this.state.global_branches.find(function (item: any) {\r\n        return item.branchId === prj.branchId;\r\n      });\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_lang_filter\",\r\n        value: langItem,\r\n      });\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_branch_filter\",\r\n        value: branchItem,\r\n      });\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_project_context\",\r\n        value: prj,\r\n      });\r\n\r\n      // Set the tree node and navigate to the project folder\r\n      if (this.tree && this.folderData) {\r\n        this.tree.treeController.setNode(\r\n          this.folderData,\r\n          String(prj.projectHomeCollectionId)\r\n        );\r\n        const result = this.tree.treeController.findNodeByKey(\r\n          this.folderData,\r\n          String(prj.projectHomeCollectionId),\r\n          []\r\n        );\r\n        if (result) {\r\n          const { node } = result;\r\n          util.updatePageLocation(\r\n            \"browse\",\r\n            \"/wex/\" + this.state.langCode + \"/browse/\" + node.id,\r\n            true\r\n          );\r\n          storeInstance.dispatch(\"setState\", {\r\n            property: \"browse_selected_folder\",\r\n            value: { ...node, path: node.collectionPathId },\r\n          });\r\n          // this.files.refreshData();\r\n          storeInstance.dispatch(\"setState\", {\r\n            property: \"browse_refresh_files\",\r\n            value: true,\r\n          });\r\n        }\r\n      }\r\n    } else {\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"browse_project_context\",\r\n        value: null,\r\n      });\r\n    }\r\n  }\r\n\r\n  _jumpToFolder(_e: any) {\r\n    if (this.browseProjectContext) {\r\n      this._folderById(\r\n        this.folderData,\r\n        this.browseProjectContext.projectHomeCollectionId\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}