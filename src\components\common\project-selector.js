import { LitElement, html, css } from "lit-element";
import { storeInstance } from "store";

// import "../base/icon";

class WexProjectSelector extends LitElement {
  static get properties() {
    return {
      _projectName: { type: String },
      jumpToFolderAction: { type: Function },
    };
  }

  static get styles() {
    return css`
      .projectbtn-container {
        display: flex;
        flex-direction: row;
        gap: 0rem;
      }
    `;
  }

  constructor() {
    super();
    this.state = {};
    this.string = {};
    this._subscription = null;

    this._projectName = "";
    this.selectedProject = null;
    // this._projectNameClass = "";
  }

  connectedCallback() {
    super.connectedCallback();
    this.state = storeInstance.state;
    this.string = this.state[this.state.langCode];
    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  _init() {
    this._projectName = this.string["_select_project_label"];
    this.selectedProject = this.state.task_filter_project;
  }

  stateChange(state) {
    this.state = state;
    this.string = this.state[this.state.langCode];
    if (state.task_filter_project) {
      this._projectName = state.task_filter_project.name;
      this.selectedProject = state.task_filter_project;
      //   projectNameClass = "valuestyle";
    }
  }

  _jumpToFolderAction() {
    console.log("jumpToFolderAction", this.selectedProject);
    if (this.jumpToFolderAction) {
      this.jumpToFolderAction.bind(this);
    }
  }

  render() {
    const template = html`
      <div class="projectbtn-container">
        <ui5-button @click="${this._openProjectSelectDialog}">
          ${this._projectName}
          <iron-icon icon="filter-list"></iron-icon>
        </ui5-button>
        <ui5-button
          ?disabled="${!this.selectedProject}"
          id="jump-proj-fldr-btn"
          @click="${this._jumpToFolderAction.bind(this)}"
          title="${this.string["_jump_to_project_folder"]}"
        >
          <iron-icon
            icon="vaadin:arrow-circle-right-o"
            title="${this.string["_jump_to_project_folder"]}"
          ></iron-icon>
        </ui5-button>
      </div>
    `;
    return template;
    // return html`<div>TEST</div>`;
  }

  _openProjectSelectDialog = () => {
    storeInstance.dispatch("setState", {
      property: "project_dialog_open",
      value: "task_filter_project",
    });
  };
}

customElements.define("wex-project-selector", WexProjectSelector);
