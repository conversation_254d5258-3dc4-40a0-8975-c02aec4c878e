import { LitElement, html, css } from "lit";
import { storeInstance } from "store/index.js";
import * as wexlib from "lib/wexlib.js";

import "@ui5/webcomponents/dist/MultiComboBox.js";
import "@ui5/webcomponents/dist/MultiComboBoxItem.js";
import "@ui5/webcomponents/dist/MultiComboBoxItemGroup.js";
import "@ui5/webcomponents/dist/Popover.js";

import "../../components/header/publish";
import "../../components/wex-table";
import "../../components/base/col-item";
import "../../components/base/row-item";
import "../../components/base/multi-combobox";
import "../../components/publish/publish-outputs-pane";
import _get from "lodash.get";

class WexPagePublishJobs extends LitElement {
  static get properties() {
    return {
      _defJobs: { type: Array }, // array of all def jobs, includes ad hoc jobs
      _outputJobs: { type: Array }, // array derived from selected def job, displays all ad hoc jobs when an adhoc def job is selected
      _activeFilters: { type: Object }, // object containing filters to be applied to dejobs table
      _barMode: { type: String },
    };
  }

  static get styles() {
    return css`
      #publish-jobs-container {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        max-height: 100%;
      }

      .mainpane {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-evenly;
        justify-content: center;
        padding: 0rem 1.25rem 0 1.25rem;
        color: var(--font-color);
        height: 100%;
        max-height: 100%;
      }
      .mainpane > *:not(:last-child) {
        margin-bottom: 1rem;
        border: 1px solid #f00;
      }
      .split-layout {
        width: 100%;
      }
      .right {
        padding-left: 2rem;
      }
      .left {
        padding-right: 2rem;
      }
    `;
  }

  constructor() {
    super();
    this._state = {};
    this._string = [];
    this._subscription = null;

    // private properties
    this._cachedFilterOptions = null;

    // internal properties
    this._defJobs = [];
    this._outputJobs = [];
    this._filterOptions = {};
    this._activeFilters = {};
    this._defJobsColumns = [];
    this._outputJobsColumns = [];
    // this._showFilterBar = true;
    this._barMode = "filter";
    // this._outputJobsFilters = {
    // };
    // this._debounceTimer = {
    //   defJobs: null,
    //   outputJobs: null,
    // };
    // this.cellRenderers = {
    //   pctCompleteRenderer: (row) => {
    //     const pct = _get(row, "pctComplete");
    //     return html`
    //       <span>${pct}</span>
    //       ${pct === "100%"
    //         ? html`<iron-icon
    //             icon="icons:file-download"
    //             title="${this._string["_download"]}"
    //           ></iron-icon>`
    //         : null}
    //     `;
    //   },
    // };
  }

  connectedCallback() {
    super.connectedCallback();
    const state = storeInstance.state;
    this._state = state;
    this._string = state[state.langCode];
    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init(state);
  }

  disconnectedCallback() {
    if (this._subscription) {
      storeInstance.unsubscribe(this._subscription);
      this._subscription = null;
    }
    super.disconnectedCallback();
  }

  updated(changedProperties) {
    if (changedProperties.has("_defJobs")) {
      const selectedTest = this._defJobs.filter((job) => job.selected).length;
      // console.log("selected?", selectedTest);
      if (selectedTest) {
        this._barMode = "action";
      } else {
        this._barMode = "filter";
      }
    }
  }

  stateChange(state) {
    this._state = state;
    this._string = state[state.langCode];
  }

  async _init(state) {
    if (!this._defJobs.length) this._defJobs = await wexlib.getPubDefJobs();
    this._defJobsColumns = state.columns.publishJobsDefJobs;
    this._outputJobsColumns = state.columns.publishJobsOutputJobs;

    this._generateDefJobFilterOptions();
  }

  async _refreshDefJobs() {
    const defJobs = await wexlib.getPubDefJobs(); // get all def jobs
    this._defJobs = defJobs;
    this._outputJobs = [];
  }

  async _refreshOutputJobs() {
    const selectedDefJob = this._defJobs.find((job) => job.selected);
    if (this._defJobs.filter((job) => job.selected).length === 1) {
      this._outputJobs = await wexlib.getPubDefOutputJobs(
        selectedDefJob.pubJobId
      );
    } else {
      this._outputJobs = [];
    }
  }

  // returns an object of data to populate filter combo boxes
  _generateDefJobFilterOptions() {
    if (this._cachedFilterOptions) return this._cachedFilterOptions;

    const filterOptions = {};
    const keysToInclude = [
      { key: "creatorUserName", label: "Publisher" },
      { key: "pubdefName", label: "Definition Name" },
    ];

    keysToInclude.forEach(({ key, label }) => {
      const values = [...new Set(this._defJobs.map((defjob) => defjob[key]))];
      filterOptions[key] = {
        placeholder: label, // specific for wex-mcb
        mcbItems: values, // for wex-mcb
      };
    });

    this._filterOptions = filterOptions;
  }

  _handleSort(type, e) {
    const data = e.detail;
    if (!type || !data) return;

    const { field, value } = data;
    if (type == "defjobs") {
      // shallow clone sufficient
      const columns = [...this._defJobsColumns];
      columns.forEach((x) =>
        x.field == field ? (x.sort = value) : (x.sort = null)
      );
      // shallow clone sufficient
      const rows = [...this._defJobs];
      rows.sort((a, b) => {
        const c = `${a[field]}`.toUpperCase();
        const d = `${b[field]}`.toUpperCase();
        if (value == "asc") {
          return c.localeCompare(d, undefined, { sensitivity: "base" });
        } else if (value == "desc") {
          return d.localeCompare(c, undefined, { sensitivity: "base" });
        }
      });
      this._defJobs = rows;
      this._defJobsColumns = columns;
    } else if (type == "outputjobs") {
      //
    }
  }

  _clearSelectedDefJobs() {
    const rows = this._defJobs.map((job) => {
      job.selected = false; // reset selected state
      return job; // shallow clone sufficient
    });
    this._defJobs = rows;
    this._outputJobs = [];
    // this._refreshDefJobs();
  }

  _clearSelectedOutputJobs() {
    this._outputJobs = [];
  }

  async _handleClickDefJobRow(e) {
    try {
      const data = e.detail;
      if (!data) return;

      const obj = { ...data.value };

      // shallow clone sufficient
      const rows = [...this._defJobs];

      if (e.detail.ctrlKey) {
        rows.forEach((row) => {
          // console.log("selected", row.selected);
          row.selected = row.selected;
          if (row.pubJobId == obj.pubJobId) row.selected = !row.selected;
        });
        this._defJobs = rows;
        this._refreshOutputJobs();
        return;
      }

      rows.forEach((row) => {
        row.selected = row.pubJobId == obj.pubJobId;
      });

      this._defJobs = rows;
      this._refreshOutputJobs();
    } catch {}
  }

  async _handleDeleteClick(item) {
    const res = await fetch("/lwa/jrest/RemovePublishJob", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        csvPubJobIds: item.pubJobId,
      }),
    });
    const data = await res.json();
    this._refreshDefJobs();
  }

  _handleSelectAllRows() {
    const rows = this._defJobs.map((job) => ({ ...job, selected: true }));
    this._defJobs = rows;
    this._refreshOutputJobs();
  }

  // Handler to update _activeFilters
  _handleFilterChange = (e) => {
    // grab the selected items and combobox group from the event detail
    const { selectedItems, comboBoxGroup } = e.detail;

    // console.log("_handleFilterChange: selectedItems", selectedItems);

    // set active filters to existing active filters
    // maybe I don't want to set this to active filters?
    let activeFilters = {};

    // console.log("comboboxGroup?", comboBoxGroup);

    // this works for separate comboboxes
    if (comboBoxGroup) {
      // console.log("comboBoxGroup");
      activeFilters = { ...this._activeFilters };
      activeFilters[comboBoxGroup] = selectedItems.map((item) => item); // was item.text
    } else {
      // console.log("!comboBoxGroup");
      // this is for grouped comboboxes, not in current spec
      // activeFilters = selectedItems.reduce((acc, item) => {
      //   const group = item.dataset.group;
      //   const text = item.text;
      //   if (!acc[group]) acc[group] = [];
      //   acc[group].push(text);
      //   return acc;
      // }, {});
    }

    // const activeFilters = {};
    // console.log("activeFilters", activeFilters);

    // reset filters when they are cleared... how do I know that they are cleared here though?
    // if (comboBoxGroup) {
    //   activeFilters[comboBoxGroup] = [];
    // }

    // selectedItems.forEach((item) => {
    //   if (!item || !item.dataset) return;
    //   console.log("item", item);

    //   const group = item.dataset.group;
    //   const text = item.text;

    //   if (!group || !text) return;
    //   activeFilters[group] = activeFilters[group] || [];

    //   if (!activeFilters[group]) activeFilters[group] = [];
    //   if (!activeFilters[group].includes(text)) activeFilters[group].push(text);
    // });

    // console.log("selectedItems", selectedItems);

    // const activeFilters = selectedItems.map((item) => item.text);

    this._activeFilters = activeFilters;
    // console.log(
    //   "_handleFilterChange: this._activeFilters",
    //   this._activeFilters
    // );
  };

  async _deleteDefJobs() {
    const selectedDefJobs = this._defJobs.filter((job) => job.selected);
    const csvPubJobIds = selectedDefJobs.map((job) => job.pubJobId).join(",");
    const res = await fetch("/lwa/jrest/RemovePublishJob", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        csvPubJobIds,
      }),
    });
    const data = await res.json();
    this._refreshDefJobs();
  }

  _renderDefJobActionBar() {
    return html`
      <wex-row-item
        alignItems="center"
        justifyContent="flex-start"
        height="42px"
      >
        <iron-icon
          icon="icons:clear"
          @click="${this._clearSelectedDefJobs}"
        ></iron-icon>

        <ui5-button @click="${this._handleSelectAllRows}"
          >Select all</ui5-button
        >
      </wex-row-item>
      <wex-row-item justifyContent="flex-end" alignItems="center">
        <ui5-button @click="${this._deleteDefJobs.bind(this)}" design="Negative"
          >Delete</ui5-button
        >
      </wex-row-item>
    `;
  }

  _renderDefJobFilterBar() {
    // console.log("**this._filterOptions => .mcbData", this._filterOptions);
    return html`<wex-col-item>
        <wex-multi-combobox
          .mcbData="${this._filterOptions}"
          @selection-change="${this._handleFilterChange}"
          dataGroup="pubdefName"
        >
        </wex-multi-combobox>
      </wex-col-item>
      <wex-col-item>
        <wex-multi-combobox
          .mcbData="${this._filterOptions}"
          @selection-change="${this._handleFilterChange}"
          dataGroup="creatorUserName"
        >
        </wex-multi-combobox>
      </wex-col-item>`;
  }

  _renderDefJobsTable() {
    return html` <wex-table
      .activeFilters=${this._activeFilters}
      defaultHeight="70vh"
      .columns=${this._defJobsColumns}
      .rows=${this._defJobs}
      @sort=${this._handleSort.bind(this, "defjobs")}
      .enableSelect=${false}
      @click=${this._handleClickDefJobRow.bind(this)}
      @selectall=${this._handleSelectAllRows.bind(this)}
      .enableMenu=${false}
    ></wex-table>`;
  }

  _renderDefJobsListPane() {
    return html`
      <wex-col-item class="left">
        <h3>${this._string["_def_jobs"]}</h3>

        <wex-row-item alignItems="center">
          ${this._barMode === "action"
            ? this._renderDefJobActionBar()
            : this._renderDefJobFilterBar()}
        </wex-row-item>

        ${this._renderDefJobsTable()}
      </wex-col-item>
    `;
  }

  _renderOutputsPane() {
    const template = html` <wex-publish-outputs-pane
      .outputJobs=${this._outputJobs}
      class="right"
    ></wex-publish-outputs-pane>`;
    return template;
  }

  _renderMainPane() {
    // <vaadin-split-layout class="split-layout">
    return html`
      <section class="mainpane">
        <wex-row-item>
          ${this._renderDefJobsListPane()} ${this._renderOutputsPane()}
        </wex-row-item>
      </section>
    `;
    // </vaadin-split-layout>
  }

  render() {
    return html`
      <div id="publish-jobs-container">
        <wex-publish-header></wex-publish-header>
        ${this._renderMainPane()}
      </div>
    `;
  }
}

customElements.define("wex-page-publish-jobs", WexPagePublishJobs);
