{"version": 3, "file": "context-root.js", "sources": ["../src/lib/context-root.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {Context} from './create-context.js';\nimport {ContextCallback, ContextRequestEvent} from './context-request-event.js';\nimport {ContextProviderEvent} from './controllers/context-provider.js';\n\n/**\n * A ContextRoot can be used to gather unsatisfied context requests and\n * re-dispatch them when new providers which satisfy matching context keys are\n * available.\n *\n * This allows providers to be added to a DOM tree, or upgraded, after the\n * consumers.\n */\nexport class ContextRoot {\n  private pendingContextRequests = new Map<\n    Context<unknown, unknown>,\n    {\n      // The WeakMap lets us detect if we're seen an element/callback pair yet,\n      // without needing to iterate the `requests` array\n      callbacks: WeakMap<HTMLElement, WeakSet<ContextCallback<unknown>>>;\n\n      // Requests lets us iterate over every element/callback that we need to\n      // replay context events for\n      // Both the element and callback must be stored in WeakRefs because the\n      // callback most likely has a strong ref to the element.\n      requests: Array<{\n        elementRef: WeakRef<HTMLElement>;\n        callbackRef: WeakRef<ContextCallback<unknown>>;\n      }>;\n    }\n  >();\n\n  /**\n   * Attach the ContextRoot to a given element to intercept `context-request` and\n   * `context-provider` events.\n   *\n   * @param element an element to add event listeners to\n   */\n  attach(element: HTMLElement): void {\n    element.addEventListener('context-request', this.onContextRequest);\n    element.addEventListener('context-provider', this.onContextProvider);\n  }\n\n  /**\n   * Removes the ContextRoot event listeners from a given element.\n   *\n   * @param element an element from which to remove event listeners\n   */\n  detach(element: HTMLElement): void {\n    element.removeEventListener('context-request', this.onContextRequest);\n    element.removeEventListener('context-provider', this.onContextProvider);\n  }\n\n  private onContextProvider = (\n    event: ContextProviderEvent<Context<unknown, unknown>>\n  ) => {\n    const pendingRequestData = this.pendingContextRequests.get(event.context);\n    if (pendingRequestData === undefined) {\n      // No pending requests for this context at this time\n      return;\n    }\n\n    // Clear our list. Any still unsatisfied requests will re-add themselves\n    // when we dispatch the events below.\n    this.pendingContextRequests.delete(event.context);\n\n    // Loop over all pending requests and re-dispatch them from their source\n    const {requests} = pendingRequestData;\n    for (const {elementRef, callbackRef} of requests) {\n      const element = elementRef.deref();\n      const callback = callbackRef.deref();\n\n      if (element === undefined || callback === undefined) {\n        // The element was GC'ed. Do nothing.\n      } else {\n        // Re-dispatch if we still have the element and callback\n        element.dispatchEvent(\n          new ContextRequestEvent(event.context, element, callback, true)\n        );\n      }\n    }\n  };\n\n  private onContextRequest = (\n    event: ContextRequestEvent<Context<unknown, unknown>>\n  ) => {\n    // Events that are not subscribing should not be buffered\n    if (event.subscribe !== true) {\n      return;\n    }\n\n    // Note, it's important to use the initial target\n    // since that's the requesting element and the event may be re-targeted\n    // to an outer host element.\n    const element = (event.contextTarget ??\n      event.composedPath()[0]) as HTMLElement;\n    const callback = event.callback;\n\n    let pendingContextRequests = this.pendingContextRequests.get(event.context);\n    if (pendingContextRequests === undefined) {\n      this.pendingContextRequests.set(\n        event.context,\n        (pendingContextRequests = {\n          callbacks: new WeakMap(),\n          requests: [],\n        })\n      );\n    }\n\n    let callbacks = pendingContextRequests.callbacks.get(element);\n    if (callbacks === undefined) {\n      pendingContextRequests.callbacks.set(\n        element,\n        (callbacks = new WeakSet())\n      );\n    }\n\n    if (callbacks.has(callback)) {\n      // We're already tracking this element/callback pair\n      return;\n    }\n\n    callbacks.add(callback);\n    pendingContextRequests.requests.push({\n      elementRef: new WeakRef(element),\n      callbackRef: new WeakRef(callback),\n    });\n  };\n}\n"], "names": ["ContextRoot", "constructor", "this", "pendingContextRequests", "Map", "onContextProvider", "event", "pendingRequestData", "get", "context", "undefined", "delete", "requests", "elementRef", "callback<PERSON><PERSON>", "element", "deref", "callback", "dispatchEvent", "ContextRequestEvent", "onContextRequest", "subscribe", "contextTarget", "<PERSON><PERSON><PERSON>", "set", "callbacks", "WeakMap", "WeakSet", "has", "add", "push", "WeakRef", "attach", "addEventListener", "detach", "removeEventListener"], "mappings": ";;;;;SAkBaA,EAAb,WAAAC,GACUC,KAAAC,uBAAyB,IAAIC,IAuC7BF,KAAAG,kBACNC,IAEA,MAAMC,EAAqBL,KAAKC,uBAAuBK,IAAIF,EAAMG,SACjE,QAA2BC,IAAvBH,EAEF,OAKFL,KAAKC,uBAAuBQ,OAAOL,EAAMG,SAGzC,MAAMG,SAACA,GAAYL,EACnB,IAAK,MAAMM,WAACA,EAAUC,YAAEA,KAAgBF,EAAU,CAChD,MAAMG,EAAUF,EAAWG,QACrBC,EAAWH,EAAYE,aAEbN,IAAZK,QAAsCL,IAAbO,GAI3BF,EAAQG,cACN,IAAIC,EAAoBb,EAAMG,QAASM,EAASE,GAAU,GAG/D,GAGKf,KAAAkB,iBACNd,IAGA,IAAwB,IAApBA,EAAMe,UACR,OAMF,MAAMN,EAAWT,EAAMgB,eACrBhB,EAAMiB,eAAe,GACjBN,EAAWX,EAAMW,SAEvB,IAAId,EAAyBD,KAAKC,uBAAuBK,IAAIF,EAAMG,cACpCC,IAA3BP,GACFD,KAAKC,uBAAuBqB,IAC1BlB,EAAMG,QACLN,EAAyB,CACxBsB,UAAW,IAAIC,QACfd,SAAU,KAKhB,IAAIa,EAAYtB,EAAuBsB,UAAUjB,IAAIO,QACnCL,IAAde,GACFtB,EAAuBsB,UAAUD,IAC/BT,EACCU,EAAY,IAAIE,SAIjBF,EAAUG,IAAIX,KAKlBQ,EAAUI,IAAIZ,GACdd,EAAuBS,SAASkB,KAAK,CACnCjB,WAAY,IAAIkB,QAAQhB,GACxBD,YAAa,IAAIiB,QAAQd,KACzB,CAEL,CA1FC,MAAAe,CAAOjB,GACLA,EAAQkB,iBAAiB,kBAAmB/B,KAAKkB,kBACjDL,EAAQkB,iBAAiB,mBAAoB/B,KAAKG,kBACnD,CAOD,MAAA6B,CAAOnB,GACLA,EAAQoB,oBAAoB,kBAAmBjC,KAAKkB,kBACpDL,EAAQoB,oBAAoB,mBAAoBjC,KAAKG,kBACtD"}