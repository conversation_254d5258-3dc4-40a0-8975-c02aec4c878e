export class DitamapNode {
  /** Unique identifier for the node */
  id: string | null;

  /** Type of DITA element (topicref, mapref, etc.) */
  type: string | null;

  /** Reference to the target resource */
  href?: string | null;

  /** Display title for the node */
  title: string | null;

  /** Name of the containing map */
  mapName: string | null;

  /** Path hierarchy to this node */
  mapPath: string[];

  /** Children of this node */
  children: DitamapNode[];

  /** Reference to the actual DOM element in XML tree */
  domElement!: HTMLElement;

  /** Reference to element in parent map (for nested maps) */
  embeddedElement?: HTMLElement | null;

  constructor() {
    this.id = null;
    this.type = "root";
    this.href = null;
    this.title = null;
    this.mapName = null;
    this.mapPath = [];
    this.embeddedElement = null;
    this.children = [];
  }

  /** Tag name at the root of the XML element */
  get tagName() {
    return this.domElement.tagName;
  }


}

export interface DitamapNodeStructure {
  type: string;
  title: string;
  mapName?: string;
  mapPath: string[];
  href: string;
  tagName: string;
  children: DitamapNodeStructure[];
}
