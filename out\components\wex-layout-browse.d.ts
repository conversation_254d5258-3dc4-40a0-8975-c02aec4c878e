import { LitElement } from "lit";
import "bds-tree";
import "./wex-browse-files";
import "@vaadin/vaadin-split-layout/vaadin-split-layout.js";
import "@vaadin/vaadin-context-menu/vaadin-context-menu.js";
import "@polymer/iron-icon/iron-icon.js";
import "@polymer/iron-icons/iron-icons.js";
import "@vaadin/vaadin-icons/vaadin-icons.js";
import "@ui5/webcomponents/dist/DatePicker";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "@ui5/webcomponents/dist/Label.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/Icon.js";
import "@ui5/webcomponents/dist/Select.js";
import "@ui5/webcomponents/dist/Option.js";
import { BDSTree } from "bds-tree";
import { Collection, Project } from "@bds/types";
export declare class WexLayoutBrowse extends LitElement {
    tree: BDSTree<Collection> | null;
    state: any;
    string: Record<string, string>;
    subscription: any;
    folderData: Collection | null;
    contextMenu: any;
    files: Element | null;
    folders: Element | null;
    _showMobileFilter: boolean;
    formerAsof: string;
    asOfNameDisplay: string;
    asOfDateDisplay: string;
    location: any;
    treeConfig: any;
    constructor();
    get routerLocation(): any;
    connectedCallback(): void;
    disconnectedCallback(): void;
    stateChange(state: any): void;
    firstUpdated(): void;
    _folderContextMenuClick(e: any, data: any): void;
    _folderDropFolder(e: any): void;
    _folderById(root: any, collId: any): void;
    _selectedProjectName(): any;
    _toggleFilters(): void;
    _contextMenuRenderer(content: any, data: any): import("lit").TemplateResult<1>;
    render(): import("lit").TemplateResult<1>;
    _asOfDialog(_e: any): void;
    _clearFilters(_e: any): void;
    _refresh(_e: any): void;
    _folderSelected(e: any): void;
    _setLangFilter(e: any): void;
    _setBranchFilter(e: any): void;
    _setMimetypeFilter(e: any): void;
    _projectSelectDialog(): void;
    _selectProjectCallback(prj: Project): void;
    _jumpToFolder(_e: any): void;
}
//# sourceMappingURL=wex-layout-browse.d.ts.map