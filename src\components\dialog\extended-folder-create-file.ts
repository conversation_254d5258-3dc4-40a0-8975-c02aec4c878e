import * as util from "lib/util.js";
import { LitElement, html, css } from "lit-element";
import { storeInstance } from "store/index.js";
import "@vaadin/vaadin-split-layout/vaadin-split-layout.js";

import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/Label.js";
// import "@ui5/webcomponents/dist/Icon.js";
import "@ui5/webcomponents-icons/dist/error.js";
// import "@ui5/webcomponents/dist/Dialog.js";
// import "@ui5/webcomponents/dist/FileUploader.js";
import "../common/dialog.js";
import "../common/project-selector.js";
import "bds-tree";
import { property, customElement } from "lit/decorators.js";
import { Collection } from "@bds/types";
/*
https://localhost:8443/lwa/jrest/GetFileTemplates?branchId=1
*/

@customElement("wex-dialog-extended-folder-create-file")
class WexDialogExtendedFolderCreateFile extends LitElement {
  @property({ type: Object }) dialogOpenData: Record<string, any> = {};
  @property({ type: Array }) templateList: any[] = [];
  @property({type: Object}) selectedTemplate: any = null;
  @property({type: Object}) selectedFolder: Collection | null = null;

  constructor() {
    super();
    this.state = {};
    this.string = {};
    this._subscription = null;
    this.dialogOpenData = null;

    this.foldersData = {};
    this.selectedTemplate = null;

    this.wexGlobalCapability = null;
  }

  connectedCallback() {
    super.connectedCallback();
    const state = storeInstance.state;
    this.state = state;
    this.string = state[state.langCode];

    this._subscription = storeInstance.subscribe((state) => {
      /*WexDialogFolderCreateFile*/
      this.stateChange(state);
    });
    this.foldersData = state.global_folders;
    this.files = [];
    this.open = false;
    this.fileName = "";
    this.fileNameExistsInCollection = null;
    this.dialogHeight = 0;
    this.displayBranch = "";
    this.displayLang = "";
    this.treeConfig = {
      initialNodeId: "2",
      labelPath: "name",
      idPath: "id",
      childPath: "children",
    };
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  firstUpdated() {
    this.filenameinput = this.shadowRoot.querySelector("#filename");
    // this.refreshData();
  }

  stateChange(state) {
    this.state = state;
    this.string = state[state.langCode];

    this.wexGlobalCapability = state.wex_user.globalCapability;

    this.dialogOpenData = state.extended_folder_create_file_dialog_open;

    if (this.dialogOpenData) {
      // this.refreshData();
      this.templateList = state.task_file_template_list;

      this.lockedFolders = this.dialogOpenData.lockedFolders;
      this.branchId = this.dialogOpenData.branch.branchId;
      this.langId = this.dialogOpenData.lang.langId;

      if (this.dialogOpenData.folder && !this.payloadprocessed) {
        this.payloadprocessed = true;
        storeInstance.dispatch("setState", {
          property: "folder_createfile_folder",
          value: this.dialogOpenData.folder,
        });
      }

      this.displayBranch = this.dialogOpenData.branch.name;
      this.displayLang = this.dialogOpenData.lang.description;

      if (this.dialogOpenData.target == "package") {
        util.log(this.dialogOpenData.target);
      }

      this.dialogHeight = state.global_screen_specs.viewportHeight * 0.7;

      if (!this.open) {
        this.uploadran = false;
        // this.dialog.show();
        // this.dialog.open = true;

        this.open = true;
        this.selectedTemplate = null;
        this.refreshData();
      }

      this.selectedFolder = state.folder_createfile_folder;
      this.selectedTemplate = state.folder_createfile_template;
      this.selectedFilename = state.folder_createfile_filename;

      // this.myRender();
    } else {
      if (this.open) {
        this.selectedFolder = null;
        this.selectedTemplate = null;
        this.selectedFilename = "";
        // this.dialog.close();
        // this.dialog.open = false;
        this.open = false;
        storeInstance.dispatch("setState", {
          property: "folder_createfile_folder",
          value: null,
        });
        storeInstance.dispatch("setState", {
          property: "folder_createfile_template",
          value: null,
        });
        storeInstance.dispatch("setState", {
          property: "folder_createfile_filename",
          value: null,
        });
        storeInstance.dispatch("setState", {
          property: "folder_createfile_name_exists_in_collection",
          value: null,
        });
      }
    }
  }

  refreshData() {
    storeInstance.dispatch("getTemplateList", {
      branchId: this.branchId,
      langId: this.langId,
    });
  }

  _scrollToSelected(e) {
    this.e = e;
    setTimeout(
      function () {
        this.selectedY = e.detail.getBoundingClientRect().y;
        if (!this.folders) {
          this.folders = this.shadowRoot.querySelector("#folders");
        }
        var folderContainerRect =
          this.folders.parentElement.getBoundingClientRect();
        var folderViewPortTop =
          folderContainerRect.top + this.folders.parentElement.scrollTop;
        var folderViewPortBottom =
          folderContainerRect.bottom + this.folders.parentElement.scrollTop;
        if (
          this.selectedY < folderContainerRect.top ||
          this.selectedY > folderContainerRect.bottom
        ) {
          this.e.detail.scrollIntoView({
            behavior: "auto",
            block: "center",
            inline: "nearest",
          });
        }
      }.bind(this),
      300
    );

    /*
            if(!this.state.project_dialog_open){
                var selectedY = e.detail.getBoundingClientRect().y;
                if(!this.folders){
                    this.folders =  this.shadowRoot.querySelector("#folders");
                    }
                var folderContainerRect = this.folders.parentElement.getBoundingClientRect();
                
                if(this.folders)
                    
                        if(selectedY <  this.folders.parentElement.scrollTop){
                            this.folders.parentElement.scrollBy( {top: (selectedY- folderContainerRect.y)-100 ,left: 0,behavior: 'instant'}  );
                            }
                        if(selectedY >  this.folders.parentElement.scrollTop + folderContainerRect.height ){
                            this.folders.parentElement.scrollBy({top: (selectedY- folderContainerRect.y)+100 ,left: 0,behavior: 'instant'});
                            }
                    }
                    */
  }

  _closeDialog() {
    this.payloadprocessed = false;
    this.filenameinput.value = "";
    this.sectionHeight = undefined;
    storeInstance.dispatch("setState", {
      property: "extended_folder_create_file_dialog_open",
      value: null,
    });
    storeInstance.dispatch("setState", {
      property: "createfile_project_context",
      value: null,
    });
  }

  render() {
    const template = html`
      <wex-dialog
        width="80%"
        height="90vh"
        dialogOpenStateProperty="extended_folder_create_file_dialog_open"
        .open=${!!this.dialogOpenData}
        confirmLabel="${this.string["_createfile"]}"
        headerText="${this.string["_createfile"]}"
      >
        ${this._renderBody()}
      </wex-dialog>
    `;
    return template;
    // <div slot="footer" class="dialog-footer">
    //   <div style="flex: 1;"></div>
    //   <ui5-button
    //     class="dialogbtns"
    //     id="create-file-btn"
    //     ?disabled="${this._saveButtonDisabled()}"
    //     design="Emphasized"
    //     @click="${this._checkFileName.bind(this)}"
    //     >${this.string["_createfile"]}</ui5-button
    //   >
    //   <ui5-button
    //     class="dialogbtns"
    //     id="closeDialogButton"
    //     design="Emphasized"
    //     @click="${this._closeDialog.bind(this)}"
    //     >${this.string["_close"]}</ui5-button
    //   >
  }

  _renderLeftSide() {
    const template = html`
      <div id="left">
        <div class="lablerow">
          <ui5-label>${this.string["_selecttemplate"]}</ui5-label><br />
        </div>
        <div id="template-container">${this._templateListTemplate()}</div>
      </div>
    `;
    return template;
  }

  // <ui5-button
  //   id="jump-proj-fldr-btn"
  //   @click="${this._projectSelectDialog.bind(this)}"
  //   class="prj ui5button ui5-content-density-compact"
  //   title="${this.string["_jump_to_project_folder"]}"
  // >
  //   ${this.string["_jump_to_project_folder"]}
  //   <iron-icon
  //     icon="vaadin:arrow-circle-down-o"
  //     title="${this.string["_jump_to_a_project_folder"]}"
  //   ></iron-icon>
  // </ui5-button>

  _renderRightSide() {
    const template = html` <div id="right">
      <div class="lablerow">
        <ui5-label>${this.string["_selectfolder"]}</ui5-label>

        <wex-project-selector
          .jumpToFolderAction=${this._selectProjectCallback.bind(this)}
        ></wex-project-selector>
      </div>
      <div id="folder-container">
        <bds-tree
          .config=${this.treeConfig}
          .root=${this.foldersData}
          @treeClick="${this._folderSelected.bind(this)}"
        ></bds-tree>
        <!-- <wex-folders
          id="folders"
          name="selectfolder"
          .folders="${this.foldersData}"
          .selectedfolder="${this.selectedFolder}"
          @folderselected="${this._folderSelected.bind(this)}"
        ></wex-folders> -->
      </div>
    </div>`;
    return template;
  }

  _renderBottom() {
    return html`
      <div id="filenamerow2">
        <div>
          <ui5-label>${this.string["_enterfilename"]}</ui5-label><br />
          <ui5-input
            id="filename"
            placeholder="${this.string["_filename"]}"
            .value="${this.fileName}"
          ></ui5-input>
        </div>
        <div id="branchlang">
          ${this.string["_branch"]}: ${this.displayBranch}<br />
          ${this.string["_language"]}: ${this.displayLang}
        </div>
      </div>
    `;
  }

  _renderBody() {
    const template = html`
      <vaadin-split-layout id="layout">
        ${this._renderLeftSide()} ${this._renderRightSide()}
      </vaadin-split-layout>
      ${this._renderBottom()}
    `;
    return template;
  }

  /*
        <ui5-button @click="${this._projectSelectDialog.bind(this)}" class='prj ui5button ui5-content-density-compact' title="${this.string['_filter_by_project']}">&#8230;</ui5-button>
                                        <ui5-button ?disabled='${!this.state.createfile_project_context}'  id="jump-proj-fldr-btn" @click="${this._jumpToFolder.bind(this)}" class='prj ui5button ui5-content-density-compact' title="${this.string['_jump_to_project_folder']}">
                                            <iron-icon icon="vaadin:arrow-circle-right-o" title="${this.string['_jump_to_project_folder']}"></iron-icon>
                                        </ui5-button> 

                                    <ui5-input class='projectinput ui5-content-density-compact' readonly value="${this.state.createfile_project_context ? this.state.createfile_project_context.name : this.string["_jump_to_project_folder"]}" @click="${this._projectSelectDialog.bind(this)}"></ui5-input>
                                        
        */

  _templateListTemplate() {
    if (this.templateList && this.templateList.length) {
      return html`<ui5-list
        id="templateselector"
        class="full-width  ui5-content-density-compact"
        infinite-scroll
      >
        ${this.templateList.map(
          (item) =>
            html`<ui5-li
              .item="${item}"
              value="${item.resId}"
              ?selected="${item == this.selectedTemplate}"
              class="${item == this.selectedTemplate ? "selectedtemplate" : ""}"
              @click="${this._selectTemplate.bind(this)}"
              >${item.fileName}</ui5-li
            >`
        )}
      </ui5-list>`;
    } else {
      return html`<div id="notemplates">
        ${this.string["_noTemplatesFoundFor"]} <br />
        ${this.string["_branch"]}: ${this.displayBranch}<br />
        ${this.string["_language"]}: ${this.displayLang}
        <div>
          <ui5-list
            id="templateselector"
            class="full-width  ui5-content-density-compact"
            infinite-scroll
          >
          </ui5-list>
        </div>
      </div>`;
    }
  }

  _projectSelectDialog() {
    console.log("jump to project");
    // storeInstance.dispatch("setState", {
    //   property: "project_dialog_open",
    //   value: { callback: this._selectProjectCallback.bind(this) },
    // });
  }

  _selectProjectCallback(prj) {
    // prj.projectHomeCollectionId
    // this.sectionHeight = undefined;
    // this.firstOpen = 2;
    storeInstance.dispatch("setState", {
      property: "createfile_project_context",
      value: prj,
    });
    var collectionId = prj.projectHomeCollectionId;
    if (this.wexGlobalCapability == "Contribute") {
      collectionId = prj.projectContributeCollectionId;
    }
    storeInstance.dispatch("setState", {
      property: "folder_createfile_folder",
      value: { id: collectionId },
    });
  }

  _jumpToFolder() {}

  _folderSelected(e) {
    if (!this.lockedFolders) {
      storeInstance.dispatch("setState", {
        property: "folder_createfile_folder",
        value: e.detail.node,
      });
    }
  }

  _saveButtonDisabled() {
    //if template selected and folder selected and filename ok then false else true
    var retval = false;
    return retval;
  }

  _selectTemplate(e) {
    storeInstance.dispatch("setState", {
      property: "folder_createfile_template",
      value: e.currentTarget.item,
    });
  }

  _validFileName(name) {
    var retval = false;
    if (name) {
      var rg1 = /^[^\\/:\*\?"<>+%@#&,=~'\|]+$/; // forbidden characters \ / : * ? " < > |
      var rg2 = /^\./; // cannot start with dot (.)
      var rg3 = /^(nul|prn|con|lpt[0-9]|com[0-9])(\.|$)/i; // forbidden file names
      retval = rg1.test(name) && !rg2.test(name) && !rg3.test(name);
    }
    return retval;
  }

  _checkFileName(e) {
    this.selectedFilename = this.filenameinput.value;
    this.aMsg = [];
    if (!this.selectedFolder) {
      this.aMsg.push(this.string["_folderrequired"]);
    }
    if (!this.selectedTemplate) {
      this.aMsg.push(this.string["_templaterequired"]);
    }
    if (!this.selectedFilename) {
      this.aMsg.push(this.string["_filerequired"]);
    }

    if (this.selectedFilename && this.selectedTemplate) {
      var aTemplateType = this.selectedTemplate.fileName.split(".");
      var templateExt = aTemplateType[aTemplateType.length - 1];
      var tmpName = this.selectedFilename;
      var aTmp = this.selectedFilename.split(".");
      var tmpExt = aTmp[aTmp.length - 1];
      if (tmpExt.toLowerCase() != "xml" && tmpExt.toLowerCase() != "ditamap") {
        this.selectedFilename = this.selectedFilename + "." + templateExt;
      }

      this.validName = this._validFileName(tmpName);
      if (!this.validName) {
        this.aMsg.push(this.string["_invalidfilename"]);
        //open message dialog
        //storeInstance.dispatch("setState", {property:  "folder_createfile_dialog_message" ,  value: {type:'Negative', message: this.string["_invalidfilename"] + ": " + this.filenameinput.value} });
      }
    }
    if (this.validName) {
      PubSub.publish("requestout");
      fetch(
        "/lwa/jrest/DoesResourceExistInColl?branchId=" +
          this.branchId +
          "&langId=" +
          this.langId +
          "&intCollectionId=" +
          this.selectedFolder.id +
          "&resName=" +
          encodeURIComponent(this.selectedFilename)
      )
        .then(
          function (response) {
            if (!response.ok) {
              throw Error(); //to bail out of promise chain
            }
            return response;
          }.bind(this)
        )
        .then((response) => {
          return response.json();
        })
        .then((data) => {
          util.log(data);
          if (data.exists != "false") {
            this.aMsg.push(this.string["_filenamealreadyexists"]);
          }
          this._nextSteps();
        })
        .catch(function (error) {})
        .finally(function () {
          PubSub.publish("requestin");
        });
    } else {
      this._nextSteps();
    }
  }

  _nextSteps() {
    if (this.aMsg.length > 0) {
      var message = {};
      message.error = {};
      message.error.summary = "Error";
      message.error.shortDesc = "";
      for (var i = 0; i < this.aMsg.length; i++) {
        message.error.shortDesc =
          message.error.shortDesc + "<li>" + this.aMsg[i] + "</li>";
      }
      message.error.shortDesc = "<ul>" + message.error.shortDesc + "</ul>";
      storeInstance.dispatch("setState", {
        property: "error_dialog_open",
        value: message,
      });
    } else {
      // const obj = this.dialogOpenData;
      switch (this.dialogOpenData.target) {
        case "package":
          storeInstance.dispatch("taskCreateFile", {
            templateId: this.selectedTemplate.resId,
            collectionId: this.selectedFolder.id,
            filename: this.selectedFilename,
            branchid: this.dialogOpenData.branch.branchId,
            langid: this.dialogOpenData.lang.langId,
            task: this.dialogOpenData.task.querySelector,
            callback: this.dialogOpenData.callback,
          });
          break;
        case "folder":
          storeInstance.dispatch("folderCreateFile", {
            templateResId: this.selectedTemplate.resId,
            folderId: this.selectedFolder.id,
            name: this.selectedFilename,
            branchid: this.dialogOpenData.branch.branchId,
            langid: this.dialogOpenData.lang.langId,
            callback: this.dialogOpenData.callback,
          });
          break;
        case "ditamap":
          storeInstance.dispatch("navigatorCreateFile", {
            templateResId: this.selectedTemplate.resId,
            folderId: this.selectedFolder.id,
            name: this.selectedFilename,
            branchid: this.dialogOpenData.branch.branchId,
            langid: this.dialogOpenData.lang.langId,
            callback: this.dialogOpenData.callback,
          });
          break;
      }

      this._closeDialog();
    }

    /*
        if(tmpName){
                storeInstance.dispatch("setState", {property:  "folder_createfile_name" ,  value:  tmpName });
                storeInstance.dispatch("folderCheckCreateFileName", {name: tmpName, langId: this.state.browse_lang_filter.langId, branchId: this.state.browse_branch_filter.branchId, folderId: this.state.browse_selected_folder.id});    
            } else {
                storeInstance.commit("setState", {property:  "folder_createfile_name" ,  value:  null });
        }
        */
  }
  static get styles() {
    return css`
      #main {
        width: 100%;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        height: 100%;
      }
      #layout {
        height: 100%;
        display: flex;
        flex: 1 1 auto;
        overflow: hidden;
        border-bottom: 1px solid lightgrey;
      }

      #left,
      #right {
        width: 50%;
        overflow: hidden;
        height: 100%;
      }

      .lablerow {
        padding-left: 0.5rem;
        line-height: 30px;
        justify-content: space-between;
        display: flex;
        flex-direction: row;
      }

      #template-container {
        height: 100%;
        overflow: auto;
      }

      #folder-container {
        height: 100%;
        overflow: auto;
      }

      #filenamerow2 {
        box-sizing: border-box;
        padding-left: 4rem;
        padding-top: 1rem;
        background-color: #f8f8f8;
        display: flex;
        flex-direction: row;
        justify-content: center;
      }

      #branchlang {
        margin: 1rem 0 0 1rem;
      }

      ui5-label {
        text-align: left;
      }

      .selectedtemplate {
        background-color: var(--row-selected-background);
      }

      #notemplates {
        text-align: center;
      }
    `;
  }
}
