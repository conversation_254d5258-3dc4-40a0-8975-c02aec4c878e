<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE bookmap PUBLIC "-//OASIS//DTD DITA BookMap//EN" "/SysSchema/dita/dtd/bookmap/dtd/bookmap.dtd">
<bookmap xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/"
	id="xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0" xml:lang="en-US"
	class="- map/map bookmap/bookmap ">
	<booktitle class="- topic/title bookmap/booktitle ">
		<mainbooktitle class="- topic/ph bookmap/mainbooktitle ">
			<?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>
	</booktitle>

	<bookmeta class="- map/topicmeta bookmap/bookmeta ">
		<bookid class="- topic/data bookmap/bookid ">
			<bookpartno class="- topic/data bookmap/bookpartno ">
				<?xm-replace_text Book Part Number?>
			</bookpartno>
			<isbn class="- topic/data bookmap/isbn ">
				<?xm-replace_text ISBN Number?>
			</isbn>
		</bookid>
		<bookrights class="- topic/data bookmap/bookrights ">
			<copyrfirst class="- topic/data bookmap/copyrfirst ">
				<year class="- topic/ph bookmap/year ">
					<?xm-replace_text First Year?>
				</year>
			</copyrfirst>
			<copyrlast class="- topic/data bookmap/copyrlast ">
				<year class="- topic/ph bookmap/year ">
					<?xm-replace_text Last Year?>
				</year>
			</copyrlast>
			<bookowner class="- topic/data bookmap/bookowner ">
				<person class="- topic/data bookmap/person ">
					<?xm-replace_text Copyright owner?>
				</person>
			</bookowner>
		</bookrights>
	</bookmeta>
	<frontmatter class="- map/topicref bookmap/frontmatter ">
		<booklists class="- map/topicref bookmap/booklists ">
			<toc class="- map/topicref bookmap/toc " />
		</booklists>
		<keydef keys="productSpec" href="/Content/phone_1_spec_xi1622_1_1.xml"
			class="+ map/topicref mapgroup-d/keydef " />
		<keydef href="/Content/phone1_small_xi1596_1_1.jpg" format="png" keys="phoneImage"
			class="+ map/topicref mapgroup-d/keydef " />
	</frontmatter>
	<part href="/Content/submap_phones_xi1609_1_1.ditamap" format="ditamap"
		class="- map/topicref bookmap/part " />
	<backmatter class="- map/topicref bookmap/backmatter ">
		<booklists class="- map/topicref bookmap/booklists ">
			<indexlist class="- map/topicref bookmap/indexlist " />
		</booklists>
	</backmatter>
</bookmap>