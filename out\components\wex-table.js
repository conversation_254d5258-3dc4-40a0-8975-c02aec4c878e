var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
import { getLocalTime } from "lib/util";
import "@ui5/webcomponents-compat/dist/Table.js";
import "@ui5/webcomponents-compat/dist/TableColumn.js";
import "@ui5/webcomponents-compat/dist/TableRow.js";
import "@ui5/webcomponents-compat/dist/TableCell.js";
import _get from "lodash.get";
let WexTable = class WexTable extends LitElement {
    constructor() {
        super(...arguments);
        this.columns = [];
        this.rows = [];
        this.enableSelect = false;
        this.enableMenu = false;
        this.menuItems = [];
        this.defaultHeight = "";
        // private state: object = {};
        this.string = [];
        this._subscription = null;
    }
    // private contextMenu: HTMLElement | null = null;
    static get styles() {
        return css `
      #container {
        display: flex;
        flex-direction: column;
        min-width: 100%;
        height: 100%;
        background: #fff;
        overflow-y: auto;

        position: relative;
      }
      button {
        margin: 0;
        padding: 0;
        border: 0;
        background: transparent;
        overflow: hidden;
        cursor: pointer;
      }
      .column {
        display: flex;
        flex-direction: row;
        align-items: center;
        min-width: 40px;
      }
      .column > *:not(:last-child) {
        margin-right: 0.5rem;
      }
      .sort-container {
        display: flex;
        flex-direction: column;
      }
      .sort-container > *:not(:last-child) {
        margin-bottom: 0.25rem;
      }
      .sort-container > button {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 0.75rem;
        color: var(--clr-gray-light);
      }
      .sort-container > button:not(:disabled):hover {
        color: var(--clr-primary-ultra-light);
      }
      .sort-container > button[data-value="asc"] {
      }
      .sort-container > button[data-value="desc"] {
      }
      .sort-container > button:disabled {
        cursor: default;
        color: var(--clr-black);
      }
      .sort-icon {
        width: 1rem;
      }
      iron-icon.red {
        color: red !important;
      }
      .row[data-selected="true"] {
        --sapList_Background: var(--row-selected-background);
      }

      .action-icon {
        width: 1rem;
        opacity: 0.7;
        cursor: pointer;
      }
      .action-icon:hover {
        opacity: 1;
      }

      .file-icon[data-status="_rogue_lock"] {
        color: purple;
      }
      .file-icon[data-status="_locked_by_other"] {
        color: red;
      }
      .file-icon[data-status="_room_locked_by_user"],
      .file-icon[data-status="_sesh_locked_by_user"] {
        color: green;
      }
      .file-icon[data-status="_xdocs_locked_by_user"] {
        color: orange;
      }
      .file-icon[data-status="_locked_concurrent"] {
        color: blue;
      }

      .flex-spacer {
        flex-grow: 1;
      }

      .popup-container {
        z-index: 100;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;

        box-sizing: border-box;

        background-color: rgba(0, 0, 0, 0.3);
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .popup {
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        box-sizing: border-box;

        padding: 3rem;
        background-color: #fff;
        width: 80%;
        height: 80%;
      }

      .close-container {
        display: flex;
        justify-content: right;
        position: absolute;
        top: 9%;
        right: 9%;
      }

      .close-container button {
        background-color: #fff;
        border-radius: 100%;
        user-select: none;
      }

      .popup-container[hidden] {
        display: none;
      }
    `;
    }
    connectedCallback() {
        super.connectedCallback();
        const state = storeInstance.state;
        // this.state = state;
        this.string = state[state.langCode];
        this._subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this._subscription);
    }
    firstUpdated() {
        // this.contextMenu = this.shadowRoot?.querySelector("#context-menu") ?? null;
    }
    updated() {
        console.log("updated");
        console.log(this.activeFilters);
    }
    stateChange(state) {
        // this.state = state;
        this.string = state[state.langCode];
    }
    _isAllSelected() {
        const res = this.rows.find((row) => !row.selected);
        return this.rows.length && !res;
    }
    _handleSort(e) {
        const target = e.currentTarget;
        const field = target.getAttribute("data-field");
        const value = target.getAttribute("data-value");
        if (!field || !value)
            return;
        this.dispatchEvent(new CustomEvent("sort", {
            detail: { field: field, value: value },
        }));
    }
    _handleSelect(item, e) {
        e.preventDefault();
        e.stopPropagation();
        if (!this.enableSelect)
            return;
        if (!item)
            return;
        this.dispatchEvent(new CustomEvent("select", {
            detail: { value: item },
        }));
    }
    _handleSelectAll(e) {
        e.preventDefault();
        e.stopPropagation();
        if (!this.enableSelect)
            return;
        const bool = this._isAllSelected();
        this.dispatchEvent(new CustomEvent("selectall", {
            detail: { value: !bool },
        }));
    }
    _handleClick(item, e) {
        e.preventDefault();
        e.stopPropagation();
        if (this.enableSelect)
            return;
        if (!item)
            return;
        console.log("row?", item);
        const icon = e.target.icon;
        this.dispatchEvent(new CustomEvent("click", {
            detail: {
                value: item,
                icon,
                shiftKey: e.shiftKey,
                ctrlKey: e.ctrlKey,
            },
        }));
    }
    _handleRightClick(item, e) {
        e.preventDefault();
        this.dispatchEvent(new CustomEvent("rightclick", {
            detail: { value: item },
        }));
    }
    _handleClickContextMenu(e) {
        const data = e.detail;
        if (!data)
            return;
        const { value } = data;
        const { name: action } = value;
        this.dispatchEvent(new CustomEvent("menuclick", {
            detail: { action: action },
        }));
    }
    _handleAction(item, e) {
        e.stopPropagation();
        const target = e.currentTarget;
        const action = target.getAttribute("data-action");
        if (!action)
            return;
        this.dispatchEvent(new CustomEvent("actionclick", {
            detail: {
                action: action,
                value: item,
            },
        }));
    }
    // _handleInfoClick(item, e) {
    //   e.preventDefault();
    //   e.stopPropagation();
    //   this.showPopup = true;
    //   this.selectedRow = item;
    // }
    // _handleOutsideClick(event) {
    //   if (!this.showPopup) return;
    //   const popupContainer = this.shadowRoot.querySelector("#popup");
    //   if (popupContainer && !popupContainer.contains(event.target)) {
    //     this._handleClosePopup();
    //   }
    // }
    // _handleClosePopup() {
    //   this.showPopup = false;
    //   // document.removeEventListener("mousedown", this._handleOutsideClick.bind(this));
    // }
    /**
     * This incorporates filtering logic directly into wex table. If there are no filterValues passed
     * to the table, it will just return all rows.
     *
     * The structure of the filterValues object should be
     * {
     *  key: [...filterStrings]
     * }
     * @returns filtered rows based on filterValues property input
     */
    _filterRows() {
        return this.rows.filter((row) => {
            let shouldInclude = true;
            if (this.activeFilters) {
                Object.keys(this.activeFilters).forEach((key) => {
                    if (this.activeFilters[key].length) {
                        shouldInclude =
                            shouldInclude && this.activeFilters[key].includes(row[key]);
                    }
                });
            }
            if (!shouldInclude) {
                console.log("filtered a row;", row);
            }
            return shouldInclude;
        });
    }
    /*
     * * added dummy column and cell to handle unexpected padding-left on first column
     */
    render() {
        const isAllSelected = this._isAllSelected();
        const isSelectable = this.enableSelect;
        const containerStyle = this.defaultHeight
            ? `height: ${this.defaultHeight};`
            : "";
        const cellRenderers = this.cellRenderers; // || this._cellRenderers; // removed built-in renderers
        const template = html `
      <div id="container" style="${containerStyle}">
        <vaadin-context-menu
          id="context-menu"
          .items=${this.menuItems}
          selector=".has-menu"
          @item-selected="${this._handleClickContextMenu.bind(this)}"
        >
          <ui5-table
            no-data-text=${this.string["_nofiles"]}
            ?show-no-data="${this.rows.length}"
            sticky-column-header
          >
            <ui5-table-column slot="columns" id="dummy-column">
            </ui5-table-column>
            ${isSelectable
            ? html `
                  <ui5-table-column slot="columns">
                    <div class="column check-column">
                      <button @click="${this._handleSelectAll.bind(this)}">
                        <iron-icon
                          icon="${isAllSelected
                ? "icons:check-box"
                : "icons:check-box-outline-blank"}"
                          class="action-icon check-icon"
                        ></iron-icon>
                      </button>
                    </div>
                  </ui5-table-column>
                `
            : null}
            ${this.columns.map((col) => html `
                <ui5-table-column slot="columns">
                  <div class="column">
                    <span>${this.string[col.label]}</span>
                    ${col.isSortable
            ? html `
                          <div class="sort-container">
                            <button
                              ?disabled="${col.sort == "asc"}"
                              data-field="${col.field}"
                              data-value="asc"
                              @click="${this._handleSort.bind(this)}"
                            >
                              <iron-icon
                                class="sort-icon"
                                icon="vaadin:caret-up"
                              ></iron-icon>
                            </button>
                            <button
                              ?disabled="${col.sort == "desc"}"
                              data-field="${col.field}"
                              data-value="desc"
                              @click="${this._handleSort.bind(this)}"
                            >
                              <iron-icon
                                class="sort-icon"
                                icon="vaadin:caret-down"
                              ></iron-icon>
                            </button>
                          </div>
                        `
            : html ``}
                  </div>
                </ui5-table-column>
              `)}
            ${this._filterRows().map((row) => html `
                <ui5-table-row
                  class="row has-menu"
                  data-selected="${row.selected}"
                  @click="${this._handleClick.bind(this, row)}"
                  @contextmenu="${this._handleRightClick.bind(this, row)}"
                >
                  <ui5-table-cell id="dummy-cell"> </ui5-table-cell>
                  ${isSelectable
            ? html `
                        <ui5-table-cell>
                          <button
                            data-action="select"
                            @click="${this._handleSelect.bind(this, row)}"
                          >
                            <iron-icon
                              icon="${row.selected
                ? "icons:check-box"
                : "icons:check-box-outline-blank"}"
                              class="action-icon check-icon"
                            ></iron-icon>
                          </button>
                        </ui5-table-cell>
                      `
            : null}
                  ${this.columns.map((col) => {
            if (col.field == "lockDate") {
                return html `
                        <ui5-table-cell align="left">
                          ${getLocalTime(_get(row, col.field))}
                        </ui5-table-cell>
                      `;
            }
            if (col.field == "status") {
                return html `
                        <ui5-table-cell>
                          <button
                            data-action="properties"
                            @click="${this._handleAction.bind(this, row)}"
                          >
                            <iron-icon
                              icon="vaadin:file-text-o"
                              title="${this.string[row.status]}"
                              class="action-icon file-icon"
                              data-status="${row.status}"
                            ></iron-icon>
                          </button>
                        </ui5-table-cell>
                      `;
            }
            else {
                return html `
                        <ui5-table-cell align="left">
                          ${col.renderer
                    ? cellRenderers[col.renderer](row) // use the renderer callback from columns.js
                    : _get(row, col.field) // Or just render the static value
                }
                        </ui5-table-cell>
                      `;
            }
        })}
                </ui5-table-row>
              `)}
          </ui5-table>
        </vaadin-context-menu>
        <div
          class="flex-spacer has-menu"
          @contextmenu="${this._handleRightClick.bind(this, null)}"
        ></div>
      </div>
    `;
        return template;
    }
};
__decorate([
    property({ type: Array })
], WexTable.prototype, "columns", void 0);
__decorate([
    property({ type: Array })
], WexTable.prototype, "rows", void 0);
__decorate([
    property({ type: Boolean })
], WexTable.prototype, "enableSelect", void 0);
__decorate([
    property({ type: Boolean })
], WexTable.prototype, "enableMenu", void 0);
__decorate([
    property({ type: Array })
], WexTable.prototype, "menuItems", void 0);
__decorate([
    property({ type: String })
], WexTable.prototype, "defaultHeight", void 0);
__decorate([
    property({ type: Object })
], WexTable.prototype, "activeFilters", void 0);
__decorate([
    property({ type: Object })
], WexTable.prototype, "cellRenderers", void 0);
WexTable = __decorate([
    customElement("wex-table")
], WexTable);
export { WexTable };
//# sourceMappingURL=wex-table.js.map