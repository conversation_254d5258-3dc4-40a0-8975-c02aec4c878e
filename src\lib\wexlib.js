import { storeInstance } from "store/index.js";
import { httpError } from "store/httpError.js";
import * as util from "lib/util.ts";
import pubsub from "pubsub-js";

export const keepOxyWexAlive = async (interval) => {
  pubsub.publish("requestout");
  try {
    const response = await fetch(
      `/oxywex/plugins-dispatcher/login?maxinterval=${interval}&action=keepalive`
    );
    if (!response.ok) {
      storeInstance.dispatch("logoutRequest");
      throw Error();
    }
  } catch (e) {
    console.error("wexlib keepOxyWexAlive", e);
  } finally {
    pubsub.publish("requestin");
  }
};

export const getSessionInfo = async () => {
  pubsub.publish("requestout");
  try {
    const response = await fetch("/lwa/jrest/GetSessionInfo");
    if (!response.ok) {
      storeInstance.dispatch("logoutRequest");
      throw Error();
    }
    const { interval, keepAlive, shutDownSystem } = await response.json();
    if (shutDownSystem) {
      storeInstance.dispatch("logoutRequest");
      return;
    }
    await keepOxyWexAlive(interval);
    setTimeout(getSessionInfo, keepAlive * 1000);
  } catch (e) {
    console.error("wexlib getSessionInfo", e);
  } finally {
    pubsub.publish("requestin");
  }
};

export const _claimToggle = function (task) {
  if (task.isClaimed) {
    var openFilesList = util.getAllOpenFiles(storeInstance.state);
    if (openFilesList) {
      storeInstance.dispatch("GetReachableFilesForTask", {
        task: task,
        resLblIdCSV: openFilesList.join(","),
        callback: _reachableFilesUnclaimCallback,
      });
    } else {
      storeInstance.dispatch("unclaimTask", task.taskInstId);
    }
  } else {
    storeInstance.dispatch("claimTask", task.taskInstId);
  }
};

export const _finishTaskAction = function (task) {
  var state = storeInstance.state;
  var openFilesList = util.getAllOpenFiles(state);
  if (openFilesList) {
    storeInstance.dispatch("GetReachableFilesForTask", {
      task: task,
      resLblIdCSV: openFilesList.join(","),
      callback: _reachableFilesCallback,
    });
  } else {
    //close all reachable
    storeInstance.dispatch("getTaskFinishForm", {
      processInstId: task.processInstId,
      taskId: task.taskId,
      source: "tasks",
      reachableFilesList: [],
    });
  }
};

export const _reachableFilesUnclaimCallback = function (fileList, task) {
  var string = storeInstance.state[storeInstance.state.langCode];
  if (_numDirtyFilesInEditor(fileList) > 0) {
    storeInstance.dispatch("setState", {
      property: "message_dialog_open",
      value: { message: string["_savebeforefinish"], callback: null },
    });
  } else {
    storeInstance.dispatch("setState", {
      property: "close_reachable_dialog_open",
      value: {
        task: task,
        filelist: fileList,
        callback: _closeReachableConfirmed,
      },
    });
  }
};

export const _closeReachableConfirmed = function (task, filelist) {
  _closeReachableFiles(filelist);
  storeInstance.dispatch("unclaimTask", task.taskInstId);
};

export const _reachableFilesCallback = function (fileList, task) {
  var string = storeInstance.state[storeInstance.state.langCode];
  if (_numDirtyFilesInEditor(fileList) > 0) {
    storeInstance.dispatch("setState", {
      property: "message_dialog_open",
      value: { message: string["_savebeforefinish"], callback: null },
    });
  } else {
    _closeReachableFiles(fileList);
    storeInstance.dispatch("getTaskFinishForm", {
      processInstId: task.processInstId,
      taskId: task.taskId,
      source: "tasks",
      reachableFilesList: fileList,
    });
  }
};

export const lockReachableMaps = async function (resLblId) {
  try {
    pubsub.publish("requestout");
    let response = await fetch(
      "/lwa/jrest/LockReachableMaps?resLBLIdMap=" + resLblId
    );

    if (!response.ok) {
      throw Error();
    }

    let data = await response.json();
    if (data.length > 0) {
      Store.dispatch("setState", {
        property: "lock_ditamap_fail_dialog",
        value: data,
      });
    }
  } catch (e) {
    console.error("wexlib lockReachableMaps", e);
  } finally {
    pubsub.publish("requestin");
  }
};

export const _finishTask = function (e) {
  _finishTaskAction(e.currentTarget.item);
};

export const _numDirtyFilesInEditor = function (fileList) {
  var retval = 0;
  var state = storeInstance.state;
  for (var i = 0; i < state.editors.length; i++) {
    for (var k = 0; k < fileList.length; k++) {
      var resLblId = state.editors[i].file.resLblId;
      if (fileList[k].resLblId == resLblId) {
        if (state.editors[i].editor.children[1].contentWindow.wex.isDirty()) {
          retval++;
        }
      }
    }
  }
  //navigators
  for (var i = 0; i < state.navigators.length; i++) {
    if (state.navigators[i].file != "") {
      for (var k = 0; k < fileList.length; k++) {
        if (fileList[k].resLblId == state.navigators[i].file.resLblId) {
          if (state.navigators[i].file.dirty) {
            retval++;
          }
        }
      }
    }
  }
  return retval;
};

export const _closeReachableFiles = function (fileList) {
  var state = storeInstance.state;
  for (var i = 0; i < state.editors.length; i++) {
    for (var k = 0; k < fileList.length; k++) {
      var resLblId = state.editors[i].file.resLblId;
      if (fileList[k].resLblId == resLblId) {
        // < 5.4, no longer necessary to check in, handled by oxygen web author plugin
        // if (state.editors[i].file.isDitabase) {
        //   storeInstance.dispatch(
        //     "unlockDitabase",
        //     state.editors[i].file.resLblId
        //   );
        // }
        // if (state.unlock_on_close) {
        //   storeInstance.dispatch("checkInEditorTopic", {
        //     resLblId: resLblId,
        //     close: true,
        //   });
        // }
        storeInstance.dispatch("closeEditor", resLblId);
      }
    }
  }
  //navigators
  for (var i = 0; i < state.navigators.length; i++) {
    if (state.navigators[i].file != "") {
      for (var k = 0; k < fileList.length; k++) {
        if (fileList[k].resLblId == state.navigators[i].file.resLblId) {
          //close this file
          storeInstance.dispatch(
            "closeNavigator",
            state.navigators[i].file.resLblId
          );
          if (
            state[state.navigators[i].file.resLblId + "_open_submaps"] !==
            undefined
          ) {
            var subMaps =
              state[state.navigators[i].file.resLblId + "_open_submaps"];
            if (subMaps.length > 0) {
              for (var j = 0; j < subMaps.length; j++) {
                storeInstance.dispatch("closeNavigator", subMaps[j]);
              }
            }
          }
        }
      }
    }
  }
};

export const previewNewWindow = function (e) {
  var params = e;
  var aParams = [];
  if (params.resLblId) {
    aParams.push("resLblId=" + params.resLblId);
    aParams.push("aod=" + params.aod);
    aParams.push("mimeType=" + params.mimeType);
    if (params.hasOwnProperty("rootmapId")) {
      aParams.push("rootmapId=" + params.rootmapId);
    }
    if (params.hasOwnProperty("profileId")) {
      aParams.push("profileId=" + params.profileId);
    }
    if (params.hasOwnProperty("previewStyle")) {
      aParams.push("previewStyle=" + params.previewStyle);
    }
    //var windowUrl = window.location.origin + "/wex/preview.html?" + aParams.join("&");
    var windowUrl =
      window.location.origin +
      "/wex/" +
      params.langCode +
      "/preview?" +
      aParams.join("&");
    window.open(windowUrl);
  }
};

export const getDefaultProjectByFileId = (resLblId = null) => {
  return new Promise(async (resolve, reject) => {
    try {
      if (!resLblId) throw Error();
      const query = `resourceId=${resLblId}`;
      const res = await fetch(
        `/lwa/jrest/GetDefaultProjectForResource?${query}`
      );
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      if (!data.length) throw Error();
      // TEMP ??
      const obj = {};
      obj.branchId = data[0].m_branchId;
      obj.branchName = data[0].m_branchName;
      obj.description = data[0].m_description;
      obj.languageCode = data[0].m_srcLangCode.m_langCode;
      obj.languageId = data[0].m_srcLangId;
      obj.name = data[0].m_name;
      obj.projCatId = data[0].m_projCategoryId;
      obj.projCatName = data[0].m_projCategoryName;
      obj.projectContributeCollectionId =
        data[0].m_contributeCollId.m_collectionId;
      obj.projectHomeCollectionId = data[0].m_homeCollId.m_collectionId;
      obj.projectId = data[0].m_projectId;
      resolve(obj);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/* request file meta data
 * @param id - string, file id (seqId)
 * @response - array, list of file meta data (object)
 */
export const requestFileInfoBySeqId = function (id) {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await fetch(
        `/lwa/jrest/GetMetaDataForResSeqIdList?cdlResSeqIds=${id}&setCapability=true`
      );
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      const file = data.shift();
      resolve(file);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request file meta data
 * @param id - string, file id (resLblId)
 * @response - array, list of file meta data (object)
 */
export const requestFileInfoById = function (id) {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await fetch(
        `/lwa/jrest/GetMetaDataForResList?cdlResLblIds=${id}&setCapability=true`
      );
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      const file = data.shift();
      resolve(file);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request file as text data
 * @param id - string, file id (resLblId)
 * @response - xml, file as text data
 */
export const getResource = function (id) {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await fetch(`/lwa/jrest/GetResource?resLblEqId=${id}`);
      if (!res.ok) {
        httpError(res);
        throw Error;
      }
      const data = await res.text();
      resolve(data);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/**
 * basically a getResource call that returns a blob instead of text
 *
 * @param {number} id
 * @returns resource as blob
 */
export const getPublishOutput = async function (id) {
  try {
    const res = await fetch(`/lwa/jrest/GetResource?resLblEqId=${id}`);
    if (!res.ok) {
      httpError(res);
      throw Error();
    }
    const data = await res.blob();
    return data;
  } catch (err) {
    console.error(err);
    throw err;
  }
};

/*
 * request list of users relative to current user
 * @param data - object
 * * currUser - string, name of current user
 * * currList - array, list of currently selected users (object)
 * @response - array, list of users (object)
 */
export const requestUsers = function (data) {
  const { currUser, currList } = data;
  return new Promise(async (resolve, reject) => {
    if (!currUser || !currList) resolve([]);
    const currNames = currList.map((x) => x.name);
    try {
      const res = await fetch(`/lwa/jrest/GetUsers?userName=${currUser}`);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      // prep data
      const users = data
        .map((x) => {
          return {
            name: x.userName,
            selected: currNames.includes(x.userName),
            active: false,
          };
        })
        .sort((a, b) => {
          let c = a.name.toLowerCase();
          let d = b.name.toLowerCase();
          if (c > d) return 1;
          if (c < d) return -1;
          else return 0;
        });
      resolve(users);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request list of files
 * @param users - array, list of users (object)
 * @response - array, list of files (object)
 */
export const requestLockedFiles = function (users = []) {
  const names = users.map((x) => encodeURIComponent(x.name)).join(",");
  return new Promise(async (resolve, reject) => {
    if (!users.length) resolve([]);
    try {
      //`/lwa/jrest/GetLockedResources?csvUsers=${names}`
      const res = await fetch(
        `/oxywex/plugins-dispatcher/lockMgr?action=getLockedResources&csvUsers=${names}`
      );
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const files = await res.json();
      const retFiles = [];
      files.forEach((x) => retFiles.push(x.resmetadata)); //clean up the shape difference between this and the other call
      resolve(retFiles);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request to unlock locked files
 * @param files - array, list of files (object)
 */
export const requestUnlockFiles = function (files, override) {
  return new Promise(async (resolve, reject) => {
    if (!files.length) resolve();
    const ids = files.map((x) => encodeURIComponent(x.resLblId)).join(",");
    try {
      const res = await fetch(
        `/lwa/jrest/UnlockAll?csvResLblIds=${ids}&bForceRoomLocks=` +
          override.toString()
      );
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      resolve();
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request to check user admin capability
 * @param name - string, name of user to check
 * @param group - string, name of group to check
 */
export const isUserInGroup = function (name = "", group = "") {
  return new Promise(async (resolve, reject) => {
    if (!name.length || !group.length) reject();
    const query = `userName=${name}&csvGroupNames=${group}`;
    try {
      const res = await fetch(`/lwa/jrest/IsUserInGroups?${query}`);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      const val = data.status == "true";
      resolve(val);
    } catch (err) {
      reject();
    }
  });
};

export const getAllowedLockModes = async (reslblId) => {
  try {
    const response = await fetch(
      `/lwa/jrest/GetAllowedLockModes?resLBLId=${reslblId}`
    );
    if (!response.ok) {
      httpError(response);
      throw new Error();
    }
    return (await response).json();
  } catch (e) {
    console.error("wexlib getAllowedLocModes", e);
  }
};

/*
 * request general properties
 * @param prop - object
 */
export const reqGetProperty = function (data = {}) {
  const { property, taskId } = data;
  return new Promise(async (resolve, reject) => {
    const query = `propName=${property}&taskId=${taskId}`;
    try {
      const res = await fetch(`/lwa/jrest/GetEffectiveProperty?${query}`);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      // res.multiValued ? (data.values = [data.values]) : null;
      resolve(data.values);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

export const getActiveTask = async (processId) => {
  const response = fetch(`/lwa/jrest/GetActiveTask?processInstId=${processId}`);
  if (!response.ok) {
    throw Error();
  }
  return response.json();
};

export const getProcessInstances = async (
  getActive = true,
  getInactive = true
) => {
  let response = await fetch(
    `/lwa/jrest/GetProcessInstances?getActive=${getActive}&getInactive=${getInactive}`
  );
  if (!response.ok) {
    throw Error();
  }
  return response.json();
};

export const getCshResource = (lang, id) => {
  return new Promise(async (resolve, reject) => {
    const query = `langCode=${lang}&identifier=${id}&isIdKey=true`;
    try {
      const res = await fetch(`/lwa/jrest/GetCshResource?${query}`);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.text();
      resolve(data);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
  //
};

/*
 * request to get proj categories
 * @response - array, list of proj categories (object)
 */
export const getProjCategories = () => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await fetch(
        `/lwa/jrest/GetProjectCategories?projCategoryId=-1`
      );
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const temp = await res.json();
      // TEMP may change when api is updated
      const data = temp.map((x) => {
        const res = {
          projectCategoryId: x.m_projectCategoryId,
          name: x.m_name,
          description: x.m_description,
        };
        return res;
      });
      resolve(data);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request to get projects by proj category id
 * @param id - string, proj category id
 * @response - array, list of projects (object)
 */
export const getProjectsByProjCategoryId = (id = -1) => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await fetch(`/lwa/jrest/GetProjects?projCategory=${id}`);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      data.forEach((x) => (x.selected = false));
      resolve(data);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request  to get pub definitions by project id
 * @param id - string, project id
 * @response - array, list of pub definitions list (object)
 */
export const getPubDefsByProjectId = (id = -1) => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await fetch(
        `/lwa/jrest/GetPublishDefOverviews?projectId=${id}`
      );
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      resolve(data);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/**
 * request to get all publish definitions
 */
export const getAllPubDefs = async () => {
  try {
    const res = await fetch("/lwa/jrest/GetPublishDefs?projectId=-1");
    if (!res.ok) {
      httpError(res);
      throw Error();
    }
    const data = await res.json();
    return data;
  } catch (err) {
    console.error(err);
  }
};

/*
 * request to get pub definition
 * @param id - string, pub definition id
 * @response - object, pub definition
 */
export const getPubDef = (id = -1) => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await fetch(`/lwa/jrest/GetPublishDef?pubdefId=${id}`);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      const list = [];
      data.lstSelectedOutputFormats.forEach((x) => {
        if (!x.lstPublishOutputs) return;
        x.lstPublishOutputs.forEach((y) => {
          const item = {
            publishEngineId: x.publishEngineId,
            publishEngineOutputId: x.publishEngineOutputId,
            outputTypeId: y.outputTypeId,
            name: y.name,
          };
          list.push(item);
        });
      });
      data.lstOutputFormats = list;
      resolve(data);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request to add new pub definition
 * @param data - object
 * * name - string, pub definition name
 * * description - string, pub definition decsription
 * * projectId - string, associated project id
 */
export const createPubDef = (data = {}) => {
  return new Promise(async (resolve, reject) => {
    try {
      const { name, desc, projectId, pubContent, pubLangs, pubOutputs } = data;

      const lstPubdefPubContent = pubContent.map((content) => {
        return {
          pubContentId: content.pubContentId,
        };
      });

      const lstPubdefLangDto = pubLangs.map((lang) => {
        return {
          langId: lang.langId,
        };
      });

      const lstPubdefEngOutputDto = pubOutputs.map((output) => {
        return {
          pubdefEngOutputId: 1,
          engineId: output.publishEngineId,
          outputTypeId: output.outputTypeId,
        };
      });

      const pubdefDto = {
        pubdefId: -1,
        name,
        desc,
        projectId,
        dvActionResLblSeqId: -1,
        lstPubdefPubContent,
        lstPubdefLangDto,
        lstPubdefEngOutputDto,
      };

      const addPublishPack = JSON.stringify({ XdNgpPubdefDto: pubdefDto });
      const body = `addPublishPack=${encodeURIComponent(addPublishPack)}`;
      const res = await fetch("/lwa/jrest/AddPublishDef", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: body,
      });
      // console.log("XDocs Create PubDef response:", res);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      resolve();
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

export const editPubDef = async (data = {}) => {
  try {
    console.log("edit pubdef data", data);
    const { name, desc, projectId, pubContent, pubLangs, pubOutputs } = data;
    const pubdefId = data.pubDefId || -1;

    const lstPubdefPubContent = pubContent.map((content) => {
      return {
        pubContentId: content.pubContentId,
      };
    });

    const lstPubdefLangDto = pubLangs.map((lang) => {
      return {
        langId: lang.langId,
      };
    });

    const lstPubdefEngOutputDto = pubOutputs.map((output) => {
      return {
        pubdefEngOutputId: 1,
        engineId: output.publishEngineId,
        outputTypeId: output.outputTypeId,
      };
    });

    console.log("lstPubdefPubContent", lstPubdefPubContent);
    console.log("lstPubdefLangDto", lstPubdefLangDto);
    console.log("lstPubdefEngOutputDto", lstPubdefEngOutputDto);

    const pubdefDto = {
      pubdefId,
      name,
      desc,
      projectId,
      dvActionResLblSeqId: -1,
      lstPubdefPubContent,
      lstPubdefLangDto,
      lstPubdefEngOutputDto,
    };

    console.log("CHECK THIS FOR PUBDEFID:", pubdefDto);

    const addPublishPack = JSON.stringify({ XdNgpPubdefDto: pubdefDto });
    const body = `updatePublishPack=${encodeURIComponent(addPublishPack)}`;
    const res = await fetch("/lwa/jrest/UpdatePublishDef", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: body,
    });
    console.log("XDocs Create PubDef response:", res);
    if (!res.ok) {
      httpError(res);
      throw Error();
    }
  } catch (e) {
    console.error("editPubDef in wexlib", e);
  }
};

export const rmPubDef = (id = -1) => {
  return new Promise(async (resolve, reject) => {
    try {
      if (id < 0) throw RangeError();
      const path = "/lwa/jrest/RemovePublishDef";
      const body = `pubDefId=${encodeURIComponent(id)}`;
      const res = await fetch(path, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: body,
      });
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      resolve();
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * TODO
 * request to set pub definition name
 * @param id - string, pub definition id
 * @param name - string, pub definition name
 */
export const setPubDefName = (id, name) => {
  return new Promise(async (resolve, reject) => {
    try {
      // const res = await fetch(
      //   `/lwa/jrest/GetPublishDef?pubdefId=${id}`
      // );
      // if (!res.ok) {
      //   httpError(res);
      //   throw Error();
      // }
      resolve();
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request to get pub contents by project id
 * @param id - string, project id
 * @response - array, list of pub contents (object)
 */
export const getProjPubContents = (id = -1) => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await fetch(`/lwa/jrest/GetPubContentDtos?projectId=${id}`);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      resolve(data);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request to set pub contents
 * @param data - object
 * * pubDefId - string, pub definition id
 * * items - array, list of pub contents (object)
 */
export const setPubDefPubContents = (data = {}) => {
  return new Promise(async (resolve, reject) => {
    try {
      const ids = data.items
        ? data.items.reduce((a, b) => [...a, b.pubContentId], [])
        : [];
      const csv = ids.join(",");
      const body = `pubdefId=${data.pubDefId}` + `&csvPubContentIds=${csv}`;
      const res = await fetch("/lwa/jrest/SetPublishEnginePubContents", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: body,
      });
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      resolve();
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request to get project languages by project id
 * @param id - string, project id
 * @response - array, list of proj languages (object)
 */
export const getProjLanguages = (id = -1) => {
  return new Promise(async (resolve, reject) => {
    try {
      const res = await fetch(`/lwa/jrest/GetProjectLangs?projectId=${id}`);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      const data = await res.json();
      resolve(data);
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request to set languages
 * @param data - object
 * data object {
 *  pubDefId,
 *  items: array of selected languages
 * }
 * * pubDefId - string, pub definition id
 * * items - array, list of languages (object)
 */
export const setPubDefLanguages = (data = {}) => {
  return new Promise(async (resolve, reject) => {
    try {
      const ids = data.items
        ? data.items.reduce((a, b) => [...a, b.langId], [])
        : [];
      let body = `pubdefId=${data.pubDefId}`;
      if (ids.length > 0) {
        const csv = ids.join(",");
        body += `&csvLanguageIds=${csv}`;
      }
      const res = await fetch("/lwa/jrest/SetPublishEngineLangs", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: body,
      });
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      resolve();
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/** request to get presentation profiles
 *
 * @returns {Promise<Array>} the presentation profiles data
 */
export const getPresentationProfiles = async () => {
  const postParams = {
    procType: "Pres",
  };

  try {
    const res = await fetch("/lwa/jrest/GetPublishProfiles", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams(postParams).toString(),
    });

    if (!res.ok) {
      httpError(res);
      throw Error();
    }

    const data = await res.json();
    return data;
  } catch (err) {
    console.error(err);
  }
};

/** request to get processing profiles
 *
 * @returns {Promise<Array>} the processing profiles data
 */
export const getProcessingProfiles = async () => {
  const postParams = {
    procType: "Proc",
  };

  try {
    const res = await fetch("/lwa/jrest/GetPublishProfiles", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams(postParams).toString(),
    });

    if (!res.ok) {
      httpError(res);
      throw Error();
    }

    const data = await res.json();
    return data;
  } catch (err) {
    console.error(err);
  }
};

/** request to get engine output
 *
 * @returns {Promise<Array>} list of pub engine outputs (object)
 */
export const getPubEngineOutputs = async () => {
  try {
    const res = await fetch(`/lwa/jrest/GetAllPublishEngineOutputs`);
    if (!res.ok) {
      httpError(res);
      throw Error();
    }
    const data = await res.json();
    // console.log("outputs raw:", data);
    const ret = [];
    data.forEach((x) => {
      x.lstPublishOutputs.forEach((y) => {
        const item = {
          publishEngineId: x.publishEngineId,
          publishEngineOutputId: x.publishEngineOutputId,
          outputTypeId: y.outputTypeId,
          name: y.name,
        };
        ret.push(item);
      });
    });
    return ret;
  } catch (err) {
    console.error(err);
  }
};

/*
 * request to set engine output
 * @param data - object
 * * pubDefId - string, pub definition id
 * * items - array, list of languages (object)
 */
export const setPubDefEngineOutputs = (data = {}) => {
  return new Promise(async (resolve, reject) => {
    try {
      const { pubDefId, items } = data;
      const list = items.map((x) => {
        const ret = {
          engineId: x.publishEngineId,
          outputTypeId: x.outputTypeId,
          pubdefEngOutputId: x.publishEngineOutputId,
          pubdefId: pubDefId,
        };
        return ret;
      });
      const json = {
        XdNgpPubdefEngOutputList: {
          lstPEODtos: list,
        },
      };
      const body =
        `pubdefId=${pubDefId}` + `&pubdefOutputsJSon=${JSON.stringify(json)}`;
      const res = await fetch("/lwa/jrest/SetPublishEngineOutputs", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: body,
      });
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      resolve();
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/*
 * request to run (publish) pub definitions
 * @param items - array, pub definitions
 * @response - array, list of pub definition jobs (object)
 */
export const runPubDefs = (items = []) => {
  const csv = items.map((x) => x.pubDefId).join(",");
  return new Promise(async (resolve, reject) => {
    try {
      const query = `csvPublishDefIds=${csv}`;
      const res = await fetch(`/lwa/jrest/RunPublishDefs?${query}`);
      if (!res.ok) {
        httpError(res);
        throw Error();
      }
      resolve();
    } catch (err) {
      console.error(err);
      reject();
    }
  });
};

/**
 * Request to get pub definition jobs
 * @returns {Promise<Array>} List of pub definition jobs (object)
 *
 * DTO location:
 * V:\XDocsHybrid\src\com\bluestream\xdocs\common\publish\nextgen\pubrun\NgpPubJobDto.java
 *
 * interface defjob {
 * pubJobId: number;
 * creatorUserId: number;
 * jobStartDate: string;
 *
 * creatorUserName: string;
 * outputJobTotal: number;
 * outputJobSuccessCount: number;
 * outputJobFailureCount: number;
 * outputJobInProgressCount: number;
 *
 *
 * }
 * */
export const getPubDefJobs = async () => {
  try {
    const res = await fetch(`/lwa/jrest/GetPublishJobs`);
    if (!res.ok) {
      httpError(res);
      throw new Error(`HTTP error! Status: ${res.status}`);
    }
    const data = await res.json();
    return data.map((item) => {
      const defJob = {
        pubdefName: "Adhoc Publish Job", // insert and overwrite for def jobs
        ...item,
        jobStartDate: util.getLocalTime(item.jobStartDate),
      };
      return defJob;
    });
  } catch (err) {
    console.error("Failed to fetch pub definition jobs:", err);
    throw err;
  }
};

/**
 * Request to get pub output jobs
 * @param - id = string, pub output job id
 * @returns {Promise<Array>} List of pub output jobs (object)
 */
export const getPubDefOutputJobs = async (id = -1) => {
  try {
    const query = `pubJobId=${id}`;
    const res = await fetch(`/lwa/jrest/GetOutputJobs?${query}`);
    if (!res.ok) {
      httpError(res);
      throw new Error(`HTTP error! Status: ${res.status}`);
    }
    const data = await res.json();
    return data.map((item) => {
      const {
        dPubContentName,
        langCode,
        outputTypeName,
        outputJobInfoId,
        outputFileName,
        lblSeqIdOutput,
      } = item;
      const { pctComplete } = item.jobRunInfo;
      const { startDate } = item.jobRunInfo.jsJobRunDto;
      const outputJob = {
        // ...item,
        lblSeqIdOutput,
        outputJobInfoId,
        dPubContentName,
        outputFileName: outputFileName.split("/").pop().split("\\").pop(),
        startDate,
        definition: "NYI",
        endDate: "NYI",
        ditaval: "NYI",
        langCode,
        outputTypeName,
        pctComplete: pctComplete + "%",
      };
      return outputJob;
    });
  } catch (err) {
    console.error(err);
    throw err;
  }
};
