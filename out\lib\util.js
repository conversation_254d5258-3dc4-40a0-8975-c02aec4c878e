import moment from "moment";
import pubsub from "pubsub-js";
import * as capabilities from "lib/capability.js";
import { storeInstance as Store } from "store/index.js";
const editableMimetypes = ["text/xml", "application/xml"];
/*,'application/mathml+xml','image/svg+xml'*/
const capEnum = {
    author: 4,
    contributor: 3,
    review: 2,
    default: 1,
};
export const log = function (msg) {
    //console.log(msg);
};
export const deepClone = function (obj) {
    return structuredClone(obj);
};
export const can = function (cap, action) {
    return capabilities.default[cap.toLowerCase()].includes(action);
};
export const updatePageLocation = function (tab, newurl, push) {
    if (push) {
        history.pushState(null, null, newurl);
    }
    pubsub.publish("updateTabUrl", { tab: tab, url: newurl });
};
export const isDitamap = function (file) {
    let res = false;
    if (file && file.ditaClass) {
        res = file.ditaClass.includes("map/map");
    }
    return res;
};
/**
 * Creates an insert Action object.
 * These are used to match adding nodes to jsMap tree with inserting to xml.
 * @param {"insertBefore" | "appendChild"} action - How the node is to be inserted
 * @param {HTMLElement} insertRef - The reference node
 */
export const createInsertAction = function (action, insertRef) {
    return { action, insertRef };
};
export const isFileEditable = function (file) {
    if (file.mimeType) {
        if (file.mimeType == "text/xml") {
            if (file.hasOwnProperty("ditaClass")) {
                if (file.ditaClass?.includes("topic/topic")) {
                    return true;
                }
                if (file.ditaClass?.includes("map/map")) {
                    return true;
                }
                return false;
            }
            else {
                return true; //fine all text/xml files are editable
            }
        }
        else {
            return editableMimetypes.includes(file.mimeType);
        }
    }
    else {
        if (file.name.substr(file.name.length - 3) == "jpg") {
            return false;
        }
        return true;
    }
};
// ????
export const isFilePreviewable = function (file) {
    let url = location.href;
    if (url.includes("/preview") && url.includes("resLblId")) {
        return false;
    }
    if (file.mimeType) {
        return true;
        //return previeableMimetypes.includes(file.mimeType);
    }
    else {
        return true;
    }
};
export const getEffectiveCap = function (localCap) {
    let globalCap = Store.state.wex_user.globalCapability;
    if (!localCap)
        return globalCap;
    if (capEnum[globalCap.toLowerCase()] > capEnum[localCap.toLowerCase()]) {
        return globalCap;
    }
    else {
        return localCap;
    }
};
export const redirectToSSOLogin = function () {
    let pathname = window.location.pathname;
    let searchString = window.location.search || "";
    var compRedir = encodeURIComponent("?redir=" + pathname + searchString);
    var redir = encodeURI("/lwa/jrest/LogonWex" + compRedir);
    window.location =
        "/lwa/jrest/Auth/IDPLogin?appId=wex&idpclient=choose&redirecturi=" + redir;
    // see bug https://bluestreamdev.atlassian.net/browse/X5-1523
    window.addEventListener("load", () => {
        location.reload();
    });
};
export const getCapValue = (cap) => {
    return cap ? capEnum[cap.toLowerCase()] : 1;
};
// three steps:
// 1) Does menu item apply to file based on mimetype or ditaclass or origin
// 2) Is operation allowed due to file status (locked, dirty, etc)
// 3) Is operation allowed due to user permissions
export const buildFileMenu = function (file, menuItems, origin, globalCap, taskCap, isClaimed) {
    const items = structuredClone(menuItems);
    const hr = { component: "hr" };
    // need this elsewhere ..
    if (file) {
        items.preview.disabled = !isFilePreviewable(file);
        items.previewNewWindow.disabled = !isFilePreviewable(file);
        items.edit_topic.disabled = !isFileEditable(file) || isDitamap(file);
        items.edit_ditamap.disabled = !isDitamap(file);
        items.open_ditamap.disabled = !isDitamap(file);
    }
    else {
        // based on browse as origin
        items.properties.disabled = true;
        items.preview.disabled = true;
        items.previewNewWindow.disabled = true;
        items.edit_topic.disabled = true;
        items.edit_ditamap.disabled = true;
        items.open_ditamap.disabled = true;
        items.cut.disabled = true;
        items.copy.disabled = true;
        // items.rename.disabled = true;
        items.delete.disabled = true;
    }
    let res = [];
    res.push(items.properties);
    res.push(hr);
    switch (origin) {
        case "fileselect":
            res.push(items.highlightFileFolder);
            break;
        case "filepackage":
            // if (!isClaimed) {
            //   items.edit_topic.disabled = true;
            //   items.edit_ditamap.disabled = true;
            //   items.removeMenuItem.disabled = true;
            //   items.toggleDoneMenuItem.disabled = true;
            // }
            res.push(items.preview);
            res.push(items.previewNewWindow);
            res.push(items.edit_topic);
            res.push(items.edit_ditamap);
            res.push(items.open_ditamap);
            if (can(globalCap, "browse")) {
                res.push(items.openfilelocationMenuItem);
            }
            if (can(getEffectiveCap(taskCap), "filePackageRemoveFile")) {
                res.push(items.removeMenuItem);
            }
            res.push(items.toggleDoneMenuItem);
            break;
        case "editorfilepackage":
            res.push(items.preview);
            res.push(items.previewNewWindow);
            res.push(items.edit_topic);
            res.push(items.edit_ditamap);
            res.push(items.open_ditamap);
            if (can(globalCap, "browse")) {
                res.push(items.openfilelocationMenuItem);
            }
            res.push(items.toggleDoneMenuItem);
            break;
        case "search":
            res.push(items.preview);
            res.push(items.previewNewWindow);
            res.push(items.edit_topic);
            res.push(items.edit_ditamap);
            res.push(items.open_ditamap);
            if (can(globalCap, "browse")) {
                res.push(items.openfilelocationMenuItem);
            }
            break;
        case "browse":
            res.push(items.preview);
            res.push(items.previewNewWindow);
            res.push(items.edit_topic);
            res.push(items.edit_ditamap);
            res.push(items.open_ditamap);
            res.push(items.export_files);
            res.push(items.publish);
            res.push(hr);
            res.push(items.cut);
            res.push(items.copy);
            res.push(items.paste);
            // res.push(items.rename);
            if (can(globalCap, "deleteFile")) {
                res.push(hr);
                res.push(items.delete);
            }
            break;
        case "reports":
            res.push(items.preview);
            res.push(items.previewNewWindow);
            res.push(items.edit_topic);
            res.push(items.edit_ditamap);
            res.push(items.open_ditamap);
            break;
        case "mru":
            res.push(items.preview);
            res.push(items.previewNewWindow);
            res.push(items.edit_topic);
            res.push(items.edit_ditamap);
            res.push(items.open_ditamap);
            break;
        case "versionlist":
            res.push(items.preview);
            res.push(items.previewNewWindow);
            // res.push(items.edit_topic);
            // res.push(items.edit_ditamap);
            res.push(items.open_ditamap);
            if (can(globalCap, "browse")) {
                res.push(items.openfilelocationMenuItem);
            }
            res.push(items.editHeadMenuItem);
            break;
    }
    return res;
};
export const notifyUser = (type, message, duration = 5) => {
    pubsub.publish("notifyUser", { type, message, duration });
};
export const getFileLockStatus = (file, user) => {
    console.log("in lock status function");
    let status = "_not_locked";
    if (file.hasOwnProperty("lockOwner")) {
        if (!file.lockOwner) {
            // do nothing
        }
        else if (file.lockOwner == "concurrent") {
            status = "_locked_concurrent";
        }
        else if (file.lockOwner != user.userName) {
            status = "_locked_by_other";
        }
        else if (file.lockOwner == user.userName) {
            if (file.lockLocation.startsWith("wex:exs:room") ||
                file.lockLocation.startsWith("wex:cc:room")) {
                status = "_room_locked_by_user";
            }
            else if (file.lockLocation == user.checkoutLoc) {
                status = "_sesh_locked_by_user";
            }
            else {
                status = "_xdocs_locked_by_user";
            }
        }
    }
    if (file.hasOwnProperty("doesRoomExist")) {
        if (!file.doesRoomExist) {
            if (file.lockLocation.includes("wex:exs:room") ||
                file.lockLocation.includes("wex:cc:room")) {
                status = "_rogue_lock";
            }
        }
    }
    return status;
};
export const calculateLockedStatus = function (row, user) {
    const status = getFileLockStatus(row, user);
    row.status = status;
    switch (status) {
        case "_not_locked":
            row.iconClass = "";
            row.iconTitle = "_not_locked";
            break;
        case "_locked_concurrent":
            row.iconClass = "concurrent";
            row.iconTitle = "_locked_concurrent";
            break;
        case "_locked_by_other":
            row.iconClass = "lockedbyother";
            row.iconTitle = "_locked_by_other";
            break;
        case "_room_locked_by_user":
            row.iconClass = "lockedbyselfhere";
            row.iconTitle = "_room_locked_by_user";
            break;
        case "_sesh_locked_by_user":
            row.iconClass = "lockedbyselfhere";
            row.iconTitle = "_sesh_locked_by_user";
            break;
        case "_xdocs_locked_by_user":
            row.iconClass = "lockedbyselfelsewhere";
            row.iconTitle = "_xdocs_locked_by_user";
            break;
        case "_rogue_lock":
            row.iconClass = "roguelock";
            row.iconTitle = "_xdocs_rogue_lock";
    }
    return row;
};
export const transformFilepackage = function (data, user) {
    var tmpArray = []; //convert to standard wex file object
    for (var i = 0; i < data.length; i++) {
        var tmp = data[i].metadata;
        tmp.reviewed = data[i].reviewed;
        tmpArray.push(calculateLockedStatus(tmp, user));
    }
    return tmpArray;
};
export const transformVersionListResults = function (data, user) {
    var resultSet = [];
    //first get the first resMeta section you find
    if (data.length > 0) {
        var resMeta = null;
        for (var i = 0; i <= data.length; i++) {
            if (data[i].resMetaData) {
                resMeta = structuredClone(data[i].resMetaData);
                resMeta.verCreateDate = null; // do not clone this data
                resMeta.verCreator = null;
                resMeta.verNum = null;
                break;
            }
        }
        //now loop through items adding resMeta to the items that don't have one.
        for (var i = 0; i <= data.length - 1; i++) {
            var item = {};
            if (!data[i].resMetaData) {
                item = structuredClone(resMeta);
            }
            else {
                item = structuredClone(data[i].resMetaData);
            }
            item.mimeType =
                "text/xml"; /* HACK just to continue development until GetVersionHistory call is updated to include mimeType.*/
            if (data[i].hasOwnProperty("itemDate")) {
                item.itemDate = data[i].itemDate;
            }
            if (data[i].hasOwnProperty("labelName")) {
                item.labelName = data[i].labelName;
                item.verNum = data[i].labelName;
            }
            if (data[i].hasOwnProperty("labelId")) {
                item.labelId = data[i].labelId;
            }
            if (data[i].hasOwnProperty("now")) {
                item.now = data[i].now;
            }
            resultSet.push(calculateLockedStatus(item, user));
        }
    }
    var tmp = structuredClone(resultSet[0]);
    tmp.head = true;
    tmp.now = true;
    tmp.itemDate = "now";
    tmp.verNum = "now";
    resultSet.unshift(tmp);
    return resultSet;
};
export const transformSearchResults = function (data) {
    let user = Store.state.wex_user;
    let resultSet = [];
    if (data.resultRow) {
        let colIdx = {
            title: data.columnMeta.map((x) => x["colName"]).indexOf("Title"),
            name: data.columnMeta.map((x) => x["colName"]).indexOf("Name"),
            lockOwner: data.columnMeta.map((x) => x["colName"]).indexOf("LockOwner"),
            createDate: data.columnMeta
                .map((x) => x["colName"])
                .indexOf("CreateDate"),
            activeProcesses: data.columnMeta
                .map((x) => x["colName"])
                .indexOf("ActiveProcesses"),
            fileStatus: data.columnMeta
                .map((x) => x["colName"])
                .indexOf("FileStatus"),
            numConnections: data.columnMeta
                .map((x) => x["colName"])
                .indexOf("NumComments"),
            size: data.columnMeta.map((x) => x["colName"]).indexOf("Size"),
            fileId: data.columnMeta.map((x) => x["colName"]).indexOf("FileId"),
            folderPath: data.columnMeta
                .map((x) => x["colName"])
                .indexOf("FolderPath"),
            hitText: data.columnMeta.map((x) => x["colName"]).indexOf("HitText"),
        };
        for (let i = 0; i < data.resultRow.length; i++) {
            var fileRow = {};
            fileRow.title = data.resultRow[i].columnValues[colIdx.title].value;
            fileRow.name = data.resultRow[i].columnValues[colIdx.name].value;
            if (colIdx.hitText != -1) {
                fileRow.hitText = data.resultRow[i].columnValues[colIdx.hitText].value;
            }
            else {
                fileRow.hitText = "";
            }
            if (!fileRow.title) {
                //pdf's and images have no title
                fileRow.title = fileRow.name;
            }
            if (fileRow.name) {
                fileRow.lcname = fileRow.name.toLowerCase();
            }
            if (fileRow.title) {
                fileRow.lctitle = fileRow.title.toLowerCase();
            }
            fileRow.lockOwner =
                data.resultRow[i].columnValues[colIdx.lockOwner].value;
            fileRow.verCreateDate =
                data.resultRow[i].columnValues[colIdx.createDate].value;
            fileRow.activeProcesses =
                data.resultRow[i].columnValues[colIdx.activeProcesses].value;
            fileRow.fileStatus =
                data.resultRow[i].columnValues[colIdx.fileStatus].value;
            fileRow.numConnections =
                data.resultRow[i].columnValues[colIdx.numConnections].value;
            fileRow.size = data.resultRow[i].columnValues[colIdx.size].value;
            fileRow.fileId = data.resultRow[i].columnValues[colIdx.fileId].value;
            fileRow.folderPath =
                data.resultRow[i].columnValues[colIdx.folderPath].value;
            //from resInfo
            fileRow.ditaClass = data.resultRow[i].resInfo["ditaClass"];
            fileRow.mimeType = data.resultRow[i].resInfo["mimeType"];
            fileRow.isXml = data.resultRow[i].resInfo["isXml"];
            fileRow.resLblId = data.resultRow[i].resInfo["resLblId"];
            fileRow.resPathId = data.resultRow[i].resInfo["resPathId"];
            fileRow.lineageId = data.resultRow[i].resInfo["lineageId"];
            fileRow.lockDate = data.resultRow[i].resInfo["lockDate"];
            fileRow.lockLocation = data.resultRow[i].resInfo["lockLocation"];
            fileRow.rootElementName = data.resultRow[i].resInfo["rootElementName"];
            resultSet.push(calculateLockedStatus(fileRow, user));
        }
    }
    return resultSet;
};
export const getIndex = function (data, fldName) {
    return data.columnMeta
        .map(function (d) {
        return d["colName"];
    })
        .indexOf(fldName);
};
export const prepTree = function (data, collectionId) {
    recursTree(data, collectionId);
    return data;
};
const openParents = function (node) {
    if (node.parent) {
        node.parent.open = true;
        openParents(node.parent);
    }
};
const recursTree = function (node, id, parent) {
    if (parent) {
        node.parent = parent;
    }
    if (node.id == id) {
        node.selected = true;
        openParents(node);
    }
    if (node.children) {
        for (var i = 0; i < node.children.length; i++) {
            recursTree(node.children[i], id, node);
        }
    }
};
function isNumber(n) {
    return /^-?[\d.]+(?:e-?\d+)?$/.test(n);
}
const isBoolean = (val) => "boolean" === typeof val;
export const columnSort = function (property) {
    var sortOrder = 1;
    if (property[0] === "-") {
        sortOrder = -1;
        property = property.substr(1);
    }
    return function (a, b) {
        /* next line works with strings and numbers,
         * and you may want to customize it to your needs
         */
        if (isNumber(a[property])) {
            var aP = parseFloat(a[property]);
            var bP = parseFloat(b[property]);
        }
        else {
            if (moment(a[property], moment.ISO_8601, true).isValid()) {
                var aP = moment(a[property], moment.ISO_8601, true);
                var bP = moment(b[property], moment.ISO_8601, true);
            }
            else {
                if (isBoolean(a[property])) {
                    var aP = a[property];
                    var bP = b[property];
                }
                else {
                    var aP = a[property] ? a[property].toLowerCase() : "";
                    var bP = b[property] ? b[property].toLowerCase() : "";
                }
            }
        }
        var result = aP < bP ? -1 : aP > bP ? 1 : 0;
        return result * sortOrder;
    };
};
export const changeColumnSort = function (prop, direction, cols) {
    var currIdx = null;
    var newIdx = null;
    for (var i = 0; i < cols.length; i++) {
        if (cols[i].sort != "none") {
            currIdx = i;
            break;
        }
    }
    for (var i = 0; i < cols.length; i++) {
        if (cols[i].fieldname == prop) {
            newIdx = i;
            break;
        }
    }
    cols[currIdx].sort = "none";
    cols[newIdx].sort = direction;
    return cols;
};
export const getSortColPropertyName = function (cols, defaultProperty) {
    var retval = defaultProperty;
    for (var i = 0; i < cols.length; i++) {
        if (cols[i].sort != "none") {
            if (cols[i].sort == "asc") {
                retval = cols[i].fieldname;
            }
            else {
                retval = "-" + cols[i].fieldname;
            }
        }
    }
    return retval;
};
export const collectionId2FolderName = function (folder, collectionId, path = "") {
    if (folder.id == collectionId) {
        let path = folder;
        return path;
    }
    else if (folder.children != null) {
        let result = null;
        for (let i = 0; result == null && i < folder.children.length; i++) {
            result = collectionId2FolderName(folder.children[i], collectionId);
        }
        return result;
    }
    return null;
};
export const getAllOpenFiles = function (state) {
    var fileList = [];
    for (var i = 0; i < state.editors.length; i++) {
        var resLblId = state.editors[i].file.resLblId;
        fileList.push(resLblId);
    }
    for (var i = 0; i < state.navigators.length; i++) {
        if (state.navigators[i].file != "") {
            fileList.push(state.navigators[i].file.resLblId);
        }
    }
    if (fileList.length > 0) {
        return fileList;
    }
    else {
        return null;
    }
};
export const getGlobalProperty = function (propName, props) {
    if (props) {
        for (var i = 0; i < props.length; i++) {
            if (props[i].name == propName) {
                if (props[i].value == "false") {
                    return false;
                }
                if (props[i].value == "true") {
                    return true;
                }
                return props[i].value;
            }
        }
    }
};
export const sortJson = function (jsonArray, prop, asc) {
    jsonArray.sort(function (a, b) {
        if (asc) {
            return a[prop].toLowerCase() > b[prop].toLowerCase()
                ? 1
                : a[prop].toLowerCase() < b[prop].toLowerCase()
                    ? -1
                    : 0;
        }
        else {
            return b[prop].toLowerCase() > a[prop].toLowerCase()
                ? 1
                : b[prop].toLowerCase() < a[prop].toLowerCase()
                    ? -1
                    : 0;
        }
    });
    return jsonArray;
};
export const getLangObjectFromId = function (langs, langId) {
    for (var i = 0; i < langs.length; i++) {
        if (langs[i].langId == langId) {
            return langs[i];
        }
    }
    return null;
};
export const getBranchObjectFromId = function (branches, branchId) {
    for (var i = 0; i < branches.length; i++) {
        if (branches[i].branchId == branchId) {
            return branches[i];
        }
    }
    return null;
};
export const getProjectObjectFromId = function (projects, projectId) {
    for (var i = 0; i < projects.length; i++) {
        if ((projects[i].projectId = projectId)) {
            return projects[i];
        }
    }
    return null;
};
export const _getFolderObjectFromId = function (root, collId) {
    var stack = [], node, ii;
    stack.push(root);
    while (stack.length > 0) {
        node = stack.pop();
        if (node.id == collId) {
            // Found it!
            return node;
        }
        else if (node.children && node.children.length) {
            for (ii = 0; ii < node.children.length; ii += 1) {
                stack.push(node.children[ii]);
            }
        }
    }
    // Didn't find it. Return null.
    return null;
};
export const getFolderObjById = function (root, id) {
    if (isNaN(id))
        return null;
    // deep clone
    const copy = structuredClone(root);
    const stack = copy.children;
    while (stack.length > 0) {
        const node = stack.pop();
        if (node.id == id) {
            return node;
        }
        else {
            if (node.children) {
                node.children.forEach((x) => stack.push(x));
            }
        }
    }
    return null;
};
export const getFolderIdByPath = function (root, path) {
    if (!root || !path)
        return -1;
    // deep clone
    const copy = structuredClone(root);
    const stack = copy.children;
    let currPath = "/";
    while (stack.length > 0) {
        const node = stack.pop();
        node.path = currPath + node.name;
        if (path.startsWith(node.path)) {
            currPath = node.path;
            if (currPath == path) {
                return node.id;
            }
            else {
                if (node.children) {
                    node.children.forEach((x) => stack.push(x));
                }
                currPath += "/";
            }
        }
    }
    return -1;
};
export const getFolderPath = function (file) {
    let temp = file.resPathId.split("/");
    temp.pop();
    const res = temp.join("/");
    return res;
};
export const getLanguages = async () => {
    try {
        const response = await fetch("/lwa/jrest/GetConfiguredLangs");
        const languages = await response.json();
        if (!response.ok) {
            httpError(response, Store);
            throw new Error(response.statusText); //to bail out of promise chain
        }
        return languages;
    }
    catch (e) {
        console.log(e);
    }
    finally {
        PubSub.publish("requestin", null);
    }
};
export const myIntersection = (set1, set2) => {
    return [...set1].filter((value) => set2.has(value));
};
const getTimezone = () => {
    const date = new Date();
    const timeZoneShort = new Intl.DateTimeFormat(Store.state.langCode, {
        timeZoneName: "short",
    })
        .formatToParts(date)
        .find((part) => part.type === "timeZoneName").value;
    return timeZoneShort;
};
export const getLocalTime = (utcString) => {
    let time = "N/A";
    if (utcString) {
        let utc = new Date(utcString);
        time = `${utc.toLocaleString()} ${getTimezone()}`;
    }
    return time;
};
/** Determines what languages are availble to the wex application
 * It is the intersection of Xdocs languages and the available language tables in wex
 * */
export const getAvailableLanguages = async () => {
    const languages = await getLanguages();
    const langcodes = languages.map((lang) => lang.langCode);
    const langcodesSet = new Set(langcodes);
    const wexStoreKeySet = new Set(Object.keys(Store.state));
    const avaliableLanguagesSet = myIntersection(langcodesSet, wexStoreKeySet);
    return [...avaliableLanguagesSet];
};
//# sourceMappingURL=util.js.map