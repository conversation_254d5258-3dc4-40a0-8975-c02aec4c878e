var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css, customElement } from "lit-element";
import { storeInstance } from "store/index.js";
import "../../wex-folders.js";
import "../../wex-select-file-files.js";
import "bds-tree";
import { property } from "lit/decorators.js";
let WexDialogContentFileExplorer = class WexDialogContentFileExplorer extends LitElement {
    constructor() {
        super();
        this.state = {};
        this.filesEl = null;
        this.string = {};
        this.foldersData = [];
        this.selectedFolder = null;
        this.selectedFile = null;
        this.selectedFilePathId = "";
        this._handleFolderSelected = (e) => {
            console.log("CHECK THIS:", e.detail);
            if (this.selectedFolder?.id != e.detail.node.id) {
                this._clearSelectedFile();
                storeInstance.dispatch("setState", {
                    property: "select_file_dialog_folderitem",
                    value: e.detail.node,
                });
                this.selectedFolder = e.detail.node;
            }
            this.filesEl.refreshData();
        };
    }
    connectedCallback() {
        super.connectedCallback();
        this.state = storeInstance.state;
        this.string = this.state[this.state.langCode];
        this._subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        this.foldersData = this.state.global_folders;
        this.treeConfig = {
            initialNodeId: "2",
            labelPath: "name",
            idPath: "id",
            childPath: "children",
        };
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this._subscription);
    }
    stateChange(state) {
        this.state = state;
        this.string = state[state.langCode];
        this.foldersData = state.global_folders;
        this.selectedFolder = state.select_file_dialog_folderitem;
        this._updateSelectedFile(state);
    }
    refreshFilesList() {
        const filesEl = this.shadowRoot.querySelector("wex-select-file-files");
        if (filesEl) {
            filesEl.refreshData();
        }
    }
    _updateSelectedFile(state) {
        this.selectedFile = state.select_file_selected_file;
        this.selectedFilePathId = this.selectedFile
            ? this.selectedFile.resPathId
            : "";
    }
    _clearSelectedFile() {
        this.selectedFile = null;
        this.selectedFilePathId = "";
    }
    render() {
        return html `
    <wex-row-item class="container" height="100%">
      <div id="fldr">
        <bds-tree
          .config=${this.treeConfig}
          .root=${this.foldersData}
          @treeClick="${this._handleFolderSelected.bind(this)}"
        ></bds-tree>
      </div>
      <div id="files">
        <wex-select-file-files id="file-list"></wex-select-file-files>
      </div>
    </wex-row-item>`;
    }
    static get styles() {
        return css `
      :host {
        display: flex;
        flex: 1;
        min-height: 0;
      }
      .container {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 0;
        gap: 0;
        flex-shrink: 0;
        margin-bottom: 1rem;
        margin-top: 1rem;
        max-height: 100%;
        border: 1px inset grey;
      }

      #fldr,
      #files {
        min-height: 0;
        overflow: auto;
      }

      #fldr {
        padding: 0;
        flex: 3;
        overflow-y: auto;
        overflow-x: auto;
        height: 100%;
      }

      #files {
        flex: 5;
        overflow-x: auto;
        overflow-y: auto;
        height: 100%;
        border-left: 1px solid grey;
      }
    `;
    }
};
__decorate([
    property({ type: Object })
], WexDialogContentFileExplorer.prototype, "string", void 0);
__decorate([
    property({ type: Array })
], WexDialogContentFileExplorer.prototype, "foldersData", void 0);
__decorate([
    property({ type: Object })
], WexDialogContentFileExplorer.prototype, "selectedFolder", void 0);
__decorate([
    property({ type: Object })
], WexDialogContentFileExplorer.prototype, "selectedFile", void 0);
__decorate([
    property({ type: String })
], WexDialogContentFileExplorer.prototype, "selectedFilePathId", void 0);
WexDialogContentFileExplorer = __decorate([
    customElement("wex-dialog-content-file-explorer")
], WexDialogContentFileExplorer);
//# sourceMappingURL=file-explorer.js.map