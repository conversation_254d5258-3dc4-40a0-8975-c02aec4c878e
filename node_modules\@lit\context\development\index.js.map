{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAEL,mBAAmB,IAAI,YAAY,GACpC,MAAM,gCAAgC,CAAC;AAExC,OAAO,EAIL,aAAa,GACd,MAAM,yBAAyB,CAAC;AAEjC,OAAO,EAAC,eAAe,EAAC,MAAM,uCAAuC,CAAC;AACtE,OAAO,EAAC,eAAe,EAAC,MAAM,uCAAuC,CAAC;AACtE,OAAO,EAAC,WAAW,EAAC,MAAM,uBAAuB,CAAC;AAElD,OAAO,EAAC,OAAO,EAAC,MAAM,6BAA6B,CAAC;AACpD,OAAO,EAAC,OAAO,EAAC,MAAM,6BAA6B,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nexport {\n  ContextCallback,\n  ContextRequestEvent as ContextEvent,\n} from './lib/context-request-event.js';\n\nexport {\n  Context,\n  ContextKey,\n  ContextType,\n  createContext,\n} from './lib/create-context.js';\n\nexport {ContextConsumer} from './lib/controllers/context-consumer.js';\nexport {ContextProvider} from './lib/controllers/context-provider.js';\nexport {ContextRoot} from './lib/context-root.js';\n\nexport {provide} from './lib/decorators/provide.js';\nexport {consume} from './lib/decorators/consume.js';\n"]}