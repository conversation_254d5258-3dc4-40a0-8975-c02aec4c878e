import { DitamapTree } from "../../lib/jsDitamap/ditamap-tree";
import { PHONES_TREE } from "./data/json-trees";
import { PHONES_1_BOOKMAP } from "./files/phones_1_bookmap";
import { expect } from "@open-wc/testing";
describe("Building JSON tree from xml", () => {
    it("builds phones tree", () => {
        let document = new DOMParser().parseFromString(PHONES_1_BOOKMAP, "application/xml");
        let xml = document.documentElement;
        let registry = new Map();
        registry.set("/Content/Phone1_Bookmap_xi1577_1_1.ditamap", xml);
        let tree = new DitamapTree("/Content/Phone1_Bookmap_xi1577_1_1.ditamap", registry);
        expect(PHONES_TREE).to.deep.equal(pruneTree(tree.root, [
            "type",
            "title",
            "href",
            "mapName",
            "mapPath",
            "tagName",
        ]));
    });
    it.skip("builds a tree from a simple bookmap");
});
describe("Map reference", () => {
    it("part with href and format=ditamap => true", () => {
        let document = new DOMParser().parseFromString(`<part href="/Content/submap_phones_xi1609_1_1.ditamap" format="ditamap"
		class="- map/topicref bookmap/part " />`, "application/xml");
        expect(DitamapTree.isMap(document.documentElement)).to.be.true;
    });
    it("mapref with href and format=ditamap => true", () => {
        let document = new DOMParser().parseFromString(`<mapref format="ditamap" href="/Content/subject_scheme_Atts_sample_xi1608_1_1.ditamap"
        class="+ map/topicref mapgroup-d/mapref " />`, "application/xml");
        expect(DitamapTree.isMap(document.documentElement)).to.be.true;
    });
});
describe("Content reference", () => {
    it("topicref => true", () => {
        let document = new DOMParser().parseFromString(`<topicref href="/Content/begin_xi1612_1_1.xml" class="- map/topicref " />`, "application/xml");
        expect(DitamapTree.isContent(document.documentElement)).to.be.true;
    });
    it("chapter => true", () => {
        let document = new DOMParser().parseFromString(`<chapter href="/Content/Introduction_xi1674_1_1.xml" class="- map/topicref bookmap/chapter">`, "application/xml");
        expect(DitamapTree.isContent(document.documentElement)).to.be.true;
    });
});
describe("Get map name (href)", () => {
    it("If the element is a bookmap, use the rootMapName", () => {
        let document = new DOMParser().parseFromString(`<bookmap xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/"
        id="xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0" xml:lang="en-US"
        class="- map/map bookmap/bookmap ">`, "application/xml");
        expect(DitamapTree.getMapName(document.documentElement, "/Content/Phone1_Bookmap_xi1577_1_1.ditamap")).to.equal("/Content/Phone1_Bookmap_xi1577_1_1.ditamap");
    });
});
describe("Bookmap quirks", () => {
    it.skip("doesn't add a part to a part");
});
const pruneTree = (current, fields) => {
    if (!current)
        return null;
    let pruned = {};
    fields.forEach((field) => {
        if (current[field] != undefined)
            pruned[field] = current[field];
    });
    pruned.children = current.children.map((child) => pruneTree(child, fields));
    return pruned;
};
//# sourceMappingURL=ditamap_test.js.map