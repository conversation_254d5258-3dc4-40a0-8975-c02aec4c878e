{"version": 3, "file": "context-provider.js", "sources": ["../../src/lib/controllers/context-provider.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ContextRequestEvent} from '../context-request-event.js';\nimport {ValueNotifier} from '../value-notifier.js';\nimport type {Context, ContextType} from '../create-context.js';\nimport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from '@lit/reactive-element';\n\ndeclare global {\n  interface HTMLElementEventMap {\n    /**\n     * A 'context-provider' event can be emitted by any element which hosts\n     * a context provider to indicate it is available for use.\n     */\n    'context-provider': ContextProviderEvent<Context<unknown, unknown>>;\n  }\n}\n\nexport class ContextProviderEvent<\n  C extends Context<unknown, unknown>,\n> extends Event {\n  readonly context: C;\n  readonly contextTarget: Element;\n\n  /**\n   *\n   * @param context the context which this provider can provide\n   * @param contextTarget the original context target of the provider\n   */\n  constructor(context: C, contextTarget: Element) {\n    super('context-provider', {bubbles: true, composed: true});\n    this.context = context;\n    this.contextTarget = contextTarget;\n  }\n}\n\nexport interface Options<C extends Context<unknown, unknown>> {\n  context: C;\n  initialValue?: ContextType<C>;\n}\n\ntype ReactiveElementHost = Partial<ReactiveControllerHost> & HTMLElement;\n\n/**\n * A ReactiveController which adds context provider behavior to a\n * custom element.\n *\n * This controller simply listens to the `context-request` event when\n * the host is connected to the DOM and registers the received callbacks\n * against its observable Context implementation.\n *\n * The controller may also be attached to any HTML element in which case it's\n * up to the user to call hostConnected() when attached to the DOM. This is\n * done automatically for any custom elements implementing\n * ReactiveControllerHost.\n */\nexport class ContextProvider<\n    T extends Context<unknown, unknown>,\n    HostElement extends ReactiveElementHost = ReactiveElementHost,\n  >\n  extends ValueNotifier<ContextType<T>>\n  implements ReactiveController\n{\n  protected readonly host: HostElement;\n  private readonly context: T;\n\n  constructor(host: HostElement, options: Options<T>);\n  /** @deprecated Use new ContextProvider(host, options) */\n  constructor(host: HostElement, context: T, initialValue?: ContextType<T>);\n  constructor(\n    host: HostElement,\n    contextOrOptions: T | Options<T>,\n    initialValue?: ContextType<T>\n  ) {\n    super(\n      (contextOrOptions as Options<T>).context !== undefined\n        ? (contextOrOptions as Options<T>).initialValue\n        : initialValue\n    );\n    this.host = host;\n    if ((contextOrOptions as Options<T>).context !== undefined) {\n      this.context = (contextOrOptions as Options<T>).context;\n    } else {\n      this.context = contextOrOptions as T;\n    }\n    this.attachListeners();\n    this.host.addController?.(this);\n  }\n\n  onContextRequest = (\n    ev: ContextRequestEvent<Context<unknown, unknown>>\n  ): void => {\n    // Only call the callback if the context matches.\n    if (ev.context !== this.context) {\n      return;\n    }\n    // Also, in case an element is a consumer AND a provider\n    // of the same context, we want to avoid the element to self-register.\n    const consumerHost = ev.contextTarget ?? ev.composedPath()[0];\n    if (consumerHost === this.host) {\n      return;\n    }\n    ev.stopPropagation();\n    this.addCallback(ev.callback, consumerHost, ev.subscribe);\n  };\n\n  /**\n   * When we get a provider request event, that means a child of this element\n   * has just woken up. If it's a provider of our context, then we may need to\n   * re-parent our subscriptions, because is a more specific provider than us\n   * for its subtree.\n   */\n  onProviderRequest = (\n    ev: ContextProviderEvent<Context<unknown, unknown>>\n  ): void => {\n    // Ignore events when the context doesn't match.\n    if (ev.context !== this.context) {\n      return;\n    }\n    // Also, in case an element is a consumer AND a provider\n    // of the same context it shouldn't provide to itself.\n    const childProviderHost = ev.contextTarget ?? ev.composedPath()[0];\n    if (childProviderHost === this.host) {\n      return;\n    }\n    // Re-parent all of our subscriptions in case this new child provider\n    // should take them over.\n    const seen = new Set<unknown>();\n    for (const [callback, {consumerHost}] of this.subscriptions) {\n      // Prevent infinite loops in the case where a one host element\n      // is providing the same context multiple times.\n      //\n      // While normally it's a no-op to attempt to re-parent a subscription\n      // that already has its proper parent, in the case where there's more\n      // than one ValueProvider for the same context on the same hostElement,\n      // they will each call the consumer, and since they will each have their\n      // own dispose function, a well behaved consumer will notice the change\n      // in dispose function and call their old one.\n      //\n      // This will cause the subscriptions to thrash, but worse, without this\n      // set check here, we can end up in an infinite loop, as we add and remove\n      // the same subscriptions onto the end of the map over and over.\n      if (seen.has(callback)) {\n        continue;\n      }\n      seen.add(callback);\n      consumerHost.dispatchEvent(\n        new ContextRequestEvent(this.context, consumerHost, callback, true)\n      );\n    }\n    ev.stopPropagation();\n  };\n\n  private attachListeners() {\n    this.host.addEventListener('context-request', this.onContextRequest);\n    this.host.addEventListener('context-provider', this.onProviderRequest);\n  }\n\n  hostConnected(): void {\n    // emit an event to signal a provider is available for this context\n    this.host.dispatchEvent(new ContextProviderEvent(this.context, this.host));\n  }\n}\n"], "names": ["ContextProviderEvent", "Event", "constructor", "context", "contextTarget", "super", "bubbles", "composed", "this", "ContextProvider", "ValueNotifier", "host", "contextOrOptions", "initialValue", "undefined", "onContextRequest", "ev", "consumerHost", "<PERSON><PERSON><PERSON>", "stopPropagation", "addCallback", "callback", "subscribe", "onProviderRequest", "seen", "Set", "subscriptions", "has", "add", "dispatchEvent", "ContextRequestEvent", "attachListeners", "addController", "addEventListener", "hostConnected"], "mappings": ";;;;;GAwBM,MAAOA,UAEHC,MASR,WAAAC,CAAYC,EAAYC,GACtBC,MAAM,mBAAoB,CAACC,SAAS,EAAMC,UAAU,IACpDC,KAAKL,QAAUA,EACfK,KAAKJ,cAAgBA,CACtB,EAuBG,MAAOK,UAIHC,EASR,WAAAR,CACES,EACAC,EACAC,GAEAR,WAC+CS,IAA5CF,EAAgCT,QAC5BS,EAAgCC,aACjCA,GAYRL,KAAAO,iBACEC,IAGA,GAAIA,EAAGb,UAAYK,KAAKL,QACtB,OAIF,MAAMc,EAAeD,EAAGZ,eAAiBY,EAAGE,eAAe,GACvDD,IAAiBT,KAAKG,OAG1BK,EAAGG,kBACHX,KAAKY,YAAYJ,EAAGK,SAAUJ,EAAcD,EAAGM,WAAU,EAS3Dd,KAAAe,kBACEP,IAGA,GAAIA,EAAGb,UAAYK,KAAKL,QACtB,OAKF,IAD0Ba,EAAGZ,eAAiBY,EAAGE,eAAe,MACtCV,KAAKG,KAC7B,OAIF,MAAMa,EAAO,IAAIC,IACjB,IAAK,MAAOJ,GAAUJ,aAACA,MAAkBT,KAAKkB,cAcxCF,EAAKG,IAAIN,KAGbG,EAAKI,IAAIP,GACTJ,EAAaY,cACX,IAAIC,EAAoBtB,KAAKL,QAASc,EAAcI,GAAU,KAGlEL,EAAGG,iBAAiB,EAvEpBX,KAAKG,KAAOA,OACqCG,IAA5CF,EAAgCT,QACnCK,KAAKL,QAAWS,EAAgCT,QAEhDK,KAAKL,QAAUS,EAEjBJ,KAAKuB,kBACLvB,KAAKG,KAAKqB,gBAAgBxB,KAC3B,CAkEO,eAAAuB,GACNvB,KAAKG,KAAKsB,iBAAiB,kBAAmBzB,KAAKO,kBACnDP,KAAKG,KAAKsB,iBAAiB,mBAAoBzB,KAAKe,kBACrD,CAED,aAAAW,GAEE1B,KAAKG,KAAKkB,cAAc,IAAI7B,EAAqBQ,KAAKL,QAASK,KAAKG,MACrE"}