var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { provide, createContext } from "@lit/context";
import { html, LitElement } from "lit";
import { customElement } from "lit/decorators";
// Only the key "store" matters here as that is what is used in the context consumer
const mockStore = createContext(Symbol("store"));
let StoreWrapper = class StoreWrapper extends LitElement {
    constructor() {
        super(...arguments);
        this.store = {};
        this.globalStore = this.store;
    }
    render() {
        return html `<div><slot></slot></div>`;
    }
};
__decorate([
    provide({ context: mockStore })
], StoreWrapper.prototype, "globalStore", void 0);
StoreWrapper = __decorate([
    customElement("mock-app")
], StoreWrapper);
//# sourceMappingURL=store-wrapper.js.map