import { LitElement, html, css } from "lit";
import { storeInstance } from "store/index.js";
import * as wexlib from "../../lib/wexlib.js";
import "../base/col-item";
import "../common/dialog.js";
import "../common/select.js";
import "../base/row-item";

class WexDialogBrowsePublish extends LitElement {
  static get properties() {
    return {
      dialogOpenData: { type: Object },
      state: { type: Object },
      string: { type: Object },
      // outputFormat: { type: Object }, // watching property here prevents it from appearing on this
    };
  }

  static styles = css`
    .more-item {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 1rem;
      height: 2.25rem;
      border: 1px solid var(--clr-gray-light);
    }
  `;

  constructor() {
    super();
    this.dialogOpenData = null;
    this.state = {};
    this.string = {};

    this.publication = null;
    this.presentationProfile = {};
    this.processingProfile = {};
    this.outputFormat = null; // falsy value to disable submit button

    this.presentationProfiles = [];
    this.processingProfiles = [];
    this.outputFormats = [];
    this.dialogSettings = {
      headerText: "Run Adhoc Publishing Job",
      property: "browse_publish_dialog_open",
      confirmLabel: "Publish",
      cancelLabel: "Cancel",
      width: "40%",
    };
  }

  connectedCallback() {
    super.connectedCallback();
    this.subscription = storeInstance.subscribe((state) => {
      /*wex-dialog-browse-publish*/
      this.stateChange(state);
    });
    this.state = storeInstance.state;
    this.string = this.state[this.state.langCode];
    this.dialogOpenData = this.state.browse_publish_dialog_open;
    this.setPresentationProfiles();
    this.setProcessingProfiles();
    this.setOutputFormats();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this.subscription);
  }

  updated(changedProperties) {
    const dialog = this.shadowRoot.querySelector("ui5-dialog");
    if (
      dialog &&
      changedProperties.has("dialogOpenData") &&
      this.dialogOpenData
    ) {
      // dialog.show();
      dialog.open = true;
    }
  }

  async setPresentationProfiles() {
    const res = await wexlib.getPresentationProfiles();
    this.presentationProfiles = res;
    this.presentationProfile = this.presentationProfiles[0]; // also set the current profile
  }

  async setProcessingProfiles() {
    const res = await wexlib.getProcessingProfiles();
    this.processingProfiles = res;
    this.processingProfile = this.processingProfiles[0]; // set default here now because it's defined
  }

  async setOutputFormats() {
    const res = await wexlib.getPubEngineOutputs();
    this.outputFormats = res;
  }

  stateChange(state) {
    this.state = state;
    this.string = this.state[this.state.langCode];
    this.dialogOpenData = state.browse_publish_dialog_open;
  }

  bodyTemplate() {
    // <h3>${this._string["_project_name"]}</h3>

    return html`
      <wex-row-item>
        <wex-col-item>
          <h3>Map</h3>
          <div class="more-item">
            <span title="Map">${this.state.browse_selected_file?.name}</span>
          </div>
        </wex-col-item>
      </wex-row-item>

      <wex-row-item>
        <wex-col-item>
          <h3>Presentation Profile</h3>
          <wex-select
            .showPlaceholder="${false}"
            .options="${this.presentationProfiles}"
            .selectedOption="${this.presentationProfile}"
            @selection-changed="${(e) =>
              this.handleSelectionChange(
                e.detail.selectedOption,
                "presentationProfile"
              )}"
          ></wex-select>
        </wex-col-item>
      </wex-row-item>

      <wex-row-item>
        <wex-col-item>
          <h3>Processing Profile</h3>
          <wex-select
            .showPlaceholder="${false}"
            .options="${this.processingProfiles}"
            .selectedOption="${this.processingProfile}"
            @selection-changed="${(e) =>
              this.handleSelectionChange(
                e.detail.selectedOption,
                "processingProfile"
              )}"
          ></wex-select>
        </wex-col-item>
      </wex-row-item>

      <wex-row-item>
        <wex-col-item>
          <h3>Output Format</h3>
          <wex-select
            .options="${this.outputFormats}"
            .selectedOption="${this.outputFormat}"
            @selection-changed="${(e) =>
              this.handleSelectionChange(
                e.detail.selectedOption,
                "outputFormat"
              )}"
          ></wex-select>
        </wex-col-item>
      </wex-row-item>
    `;
  }

  render() {
    // <ui5-dialog header-text="${this.string["_browse_publish_dialog"]}">
    return html`
      <wex-dialog
        .confirmDisabled="${!this.outputFormat}"
        .dialogSettings="${this.dialogSettings}"
        .open="${!!this.dialogOpenData}"
        .onConfirm="${this.publish}"
      >
        ${this.bodyTemplate()}
      </wex-dialog>
    `;
  }

  handleSelectionChange(selectedOption, property) {
    if (!Object.hasOwn(this, property)) {
      throw new Error(
        `Property "${property}" does not exist on this component.`
      );
    }

    this[property] = selectedOption;
    this.requestUpdate();
  }

  publish = () => {
    console.log("publish");
    console.log("publication", this.state.browse_selected_file);
    console.log("outputFormat", this.outputFormat);
    console.log("presentationProfile", this.presentationProfile);
    console.log("processingProfile", this.processingProfile);

    try {
      const publicationContentId = -1;
      const resLblIdMap = this.state.browse_selected_file.lineageId;
      const procProfileLblId = this.processingProfile.profileLblId;
      const presProfileLblId = this.presentationProfile.profileLblId;
      const publishEngineId = this.outputFormat.publishEngineId;
      const outputTypeId = this.outputFormat.outputTypeId;
      const outputFileName = "test";

      console.log("resLblIdMap", resLblIdMap);

      const body = new URLSearchParams({
        publicationContentId,
        procProfileLblId,
        presProfileLblId,
        publishEngineId,
        outputFileName,
        outputTypeId,
        resLblIdMap,
      });
      console.log("body", body);

      const res = fetch("/lwa/jrest/RunAdHocPublish", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body,
      });
    } catch (e) {
      console.error(e);
    }
  };
}

customElements.define("wex-dialog-browse-publish", WexDialogBrowsePublish);
