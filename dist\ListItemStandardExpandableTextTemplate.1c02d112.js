function e(e,t,i,o){Object.defineProperty(e,t,{get:i,set:o,enumerable:!0,configurable:!0})}var t=globalThis.parcelRequire94c2,i=t.register;i("i9xPF",function(i,o){e(i.exports,"default",()=>n);var r=t("lyEkX"),a=t("1AVCi");function n(e){let{className:t,text:i,maxCharacters:o,part:n}=e;return(0,r.jsx)(a.default,{part:n,class:t,text:i,maxCharacters:o})}}),i("1AVCi",function(i,o){e(i.exports,"default",()=>m);var r,a=t("hAJdp"),n=t("c6CCt"),s=t("bHGHH"),l=t("hzmJ2"),d=t("i1AiN"),p=t("24kPe"),c=t("d0KFw"),u=t("TUBXI"),x=t("lHZ6T"),h=t("IaA5D"),f=function(e,t,i,o){var r,a=arguments.length,n=a<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(n=(a<3?r(n):a>3?r(t,i,n):r(t,i))||n);return a>3&&n&&Object.defineProperty(t,i,n),n};let _=r=class extends a.default{constructor(){super(...arguments),this.maxCharacters=100,this.overflowMode="InPlace",this.emptyIndicatorMode="Off",this._expanded=!1}getFocusDomRef(){return this._usePopover?this.shadowRoot?.querySelector("[ui5-responsive-popover]"):this.shadowRoot?.querySelector("[ui5-link]")}get _displayedText(){return this._expanded&&!this._usePopover?this.text:this.text?.substring(0,this.maxCharacters)}get _maxCharactersExceeded(){return(this.text?.length||0)>this.maxCharacters}get _usePopover(){return this.overflowMode===c.default.Popover}get _ellipsisText(){return this._expanded&&!this._usePopover?" ":"... "}get _textForToggle(){return this._expanded?r.i18nBundle.getText(u.EXPANDABLE_TEXT_SHOW_LESS):r.i18nBundle.getText(u.EXPANDABLE_TEXT_SHOW_MORE)}get _closeButtonText(){return r.i18nBundle.getText(u.EXPANDABLE_TEXT_CLOSE)}get _accessibilityAttributesForToggle(){return this._usePopover?{expanded:this._expanded,hasPopup:"dialog"}:{expanded:this._expanded}}get _accessibleNameForToggle(){if(this._usePopover)return this._expanded?r.i18nBundle.getText(u.EXPANDABLE_TEXT_SHOW_LESS_POPOVER_ARIA_LABEL):r.i18nBundle.getText(u.EXPANDABLE_TEXT_SHOW_MORE_POPOVER_ARIA_LABEL)}_handlePopoverClose(){(0,p.isPhone)()||(this._expanded=!1)}_handleToggleClick(){this._expanded=!this._expanded}_handleCloseButtonClick(e){this._expanded=!1,e.stopPropagation()}};f([(0,s.default)()],_.prototype,"text",void 0),f([(0,s.default)({type:Number})],_.prototype,"maxCharacters",void 0),f([(0,s.default)()],_.prototype,"overflowMode",void 0),f([(0,s.default)()],_.prototype,"emptyIndicatorMode",void 0),f([(0,s.default)({type:Boolean})],_.prototype,"_expanded",void 0),f([(0,l.default)("@ui5/webcomponents")],_,"i18nBundle",void 0),(_=r=f([(0,n.default)({tag:"ui5-expandable-text",renderer:d.default,styles:h.default,template:x.default})],_)).define();var m=_}),i("d0KFw",function(t,i){e(t.exports,"default",()=>a),(o=r||(r={})).InPlace="InPlace",o.Popover="Popover";var o,r,a=r}),i("lHZ6T",function(i,o){e(i.exports,"default",()=>d);var r=t("lyEkX"),a=t("89OZf"),n=t("jiNAP"),s=t("iQEdW"),l=t("e9Sj6");function d(){return(0,r.jsxs)("div",{children:[(0,r.jsx)(a.default,{class:"ui5-exp-text-text",emptyIndicatorMode:this.emptyIndicatorMode,children:this._displayedText}),this._maxCharactersExceeded&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{class:"ui5-exp-text-ellipsis",children:this._ellipsisText}),(0,r.jsx)(n.default,{id:"toggle",class:"ui5-exp-text-toggle",accessibleRole:"Button",accessibleName:this._accessibleNameForToggle,accessibilityAttributes:this._accessibilityAttributesForToggle,onClick:this._handleToggleClick,children:this._textForToggle}),this._usePopover&&(0,r.jsxs)(l.default,{open:this._expanded,opener:"toggle",accessibleNameRef:"popover-text",contentOnlyOnDesktop:!0,_hideHeader:!0,class:"ui5-exp-text-popover",onClose:this._handlePopoverClose,children:[(0,r.jsx)(a.default,{id:"popover-text",children:this.text}),(0,r.jsx)("div",{slot:"footer",class:"ui5-exp-text-footer",children:(0,r.jsx)(s.default,{design:"Transparent",onClick:this._handleCloseButtonClick,children:this._closeButtonText})})]})]})]})}}),i("89OZf",function(i,o){e(i.exports,"default",()=>y);var r,a=t("hAJdp"),n=t("c6CCt"),s=t("bHGHH"),l=t("aaoB5"),d=t("i1AiN"),p=t("jlowE"),c=t("hzmJ2"),u=t("6T3KG"),x=t("l7y0l"),h=t("bCP4l"),f=t("TUBXI"),_=t("7vw7Y"),m=function(e,t,i,o){var r,a=arguments.length,n=a<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(n=(a<3?r(n):a>3?r(t,i,n):r(t,i))||n);return a>3&&n&&Object.defineProperty(t,i,n),n};let g=r=class extends a.default{constructor(){super(...arguments),this.maxLines=1/0,this.emptyIndicatorMode="Off"}onBeforeRendering(){this.style.setProperty((0,p.getScopedVarName)("--_ui5_text_max_lines"),`${this.maxLines}`)}get hasText(){return(0,u.default)(this.text)}get _renderEmptyIndicator(){return!this.hasText&&this.emptyIndicatorMode===x.default.On}get _emptyIndicatorAriaLabel(){return r.i18nBundle.getText(f.EMPTY_INDICATOR_ACCESSIBLE_TEXT)}get _emptyIndicatorSymbol(){return r.i18nBundle.getText(f.EMPTY_INDICATOR_SYMBOL)}};m([(0,s.default)({type:Number})],g.prototype,"maxLines",void 0),m([(0,s.default)()],g.prototype,"emptyIndicatorMode",void 0),m([(0,l.default)({type:Node,default:!0})],g.prototype,"text",void 0),m([(0,c.default)("@ui5/webcomponents")],g,"i18nBundle",void 0),(g=r=m([(0,n.default)({tag:"ui5-text",renderer:d.default,template:h.default,styles:_.default})],g)).define();var y=g}),i("l7y0l",function(t,i){e(t.exports,"default",()=>a),(o=r||(r={})).Off="Off",o.On="On";var o,r,a=r}),i("bCP4l",function(i,o){e(i.exports,"default",()=>a);var r=t("lyEkX");function a(){return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("span",{children:this._renderEmptyIndicator?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"empty-indicator","aria-hidden":"true",children:this._emptyIndicatorSymbol}),(0,r.jsx)("span",{className:"empty-indicator-aria-label",children:this._emptyIndicatorAriaLabel})]}):(0,r.jsx)("slot",{})})})}}),i("7vw7Y",function(i,o){e(i.exports,"default",()=>s);var r=t("yeBrr"),a=t("5ZfjE"),n=t("fW3zH");(0,r.registerThemePropertiesLoader)("@ui5/webcomponents-theming","sap_horizon",async()=>a.default),(0,r.registerThemePropertiesLoader)("@ui5/webcomponents","sap_horizon",async()=>n.default);var s=`:host{max-width:100%;font-size:var(--sapFontSize);font-family:var(--sapFontFamily);color:var(--sapTextColor);line-height:normal;cursor:text;overflow:hidden}:host([max-lines="1"]){display:inline-block;text-overflow:ellipsis;white-space:nowrap}:host(:not([max-lines="1"])){display:-webkit-box;-webkit-line-clamp:var(--_ui5-v2-11-0_text_max_lines);line-clamp:var(--_ui5-v2-11-0_text_max_lines);-webkit-box-orient:vertical;white-space:normal;word-wrap:break-word}.empty-indicator-aria-label{position:absolute!important;clip:rect(1px,1px,1px,1px);user-select:none;left:0;top:0;font-size:0}
`}),i("IaA5D",function(i,o){e(i.exports,"default",()=>s);var r=t("yeBrr"),a=t("5ZfjE"),n=t("fW3zH");(0,r.registerThemePropertiesLoader)("@ui5/webcomponents-theming","sap_horizon",async()=>a.default),(0,r.registerThemePropertiesLoader)("@ui5/webcomponents","sap_horizon",async()=>n.default);var s=`:host{display:inline-block;font-family:var(--sapFontFamily);font-size:var(--sapFontSize);color:var(--sapTextColor)}:host([hidden]){display:none}.ui5-exp-text-text{display:inline}.ui5-exp-text-text,.ui5-exp-text-toggle{font-family:inherit;font-size:inherit}.ui5-exp-text-text,.ui5-exp-text-ellipsis{color:inherit}.ui5-exp-text-popover::part(content){padding-inline:1rem}.ui5-exp-text-footer{width:100%;display:flex;align-items:center;justify-content:flex-end}
`});
//# sourceMappingURL=ListItemStandardExpandableTextTemplate.1c02d112.js.map
