import { LitElement, html, css, PropertyValues } from "lit";
import "./bds-treenode";
import { repeat } from "lit/directives/repeat.js";
import get from "lodash-es/get.js";
import { customElement, property } from "lit/decorators.js";
import { provide } from "@lit/context";
import {
  TreeConfig,
  treeConfigContext,
  TreeController,
  treeControllerContext,
} from "./tree-helpers";
import { Config } from "types";

/**
 *  Folder Tree Component
 *  Events
 *  - @fires dragAndDrop - fired when a node is dragged and dropped onto other node
 *  - @fires siblingDragAndDrop - fired when a node is dragged and dropped onto a node reorder space
 *  - @fires prevDragAndDrop - fired when a node is dragged and dropped onto space at the top of a list
 *  - @fires treeChange - fired when a node is clicked
 */

@customElement("bds-tree")
export class BDSTree<T> extends LitElement {
  /**
   * Stores the entire tree stricture
   */
  @property({ type: Array })
  root: T | null = null;
  /**
   * Tree customizations
   */
  @property({ type: Object })
  config: Config = {};
  /**
   * Pre-selected keys
   */
  @property({ type: Array })
  selectedKeys?: string[];
  /**
   * Pre-expanded keys
   */
  @property({ type: Array })
  expandedKeys?: string[];
  // ---- Contexts ----- //
  @provide({ context: treeConfigContext })
  treeConfig = new TreeConfig(this.config);
  @provide({ context: treeControllerContext })
  treeController = new TreeController<T>(this, this.treeConfig);

  static get styles() {
    return css`
      ul {
        margin-left: 0;
        list-style: none;
        text-indent: -1.25rem;
        padding: 0;
      }
    `;
  }

  constructor() {
    super();
  }

  // set default state
  connectedCallback() {
    super.connectedCallback();
  }

  // Sync with props before render
  willUpdate(changedProps: PropertyValues) {
    if (!this.root) {
      return;
    }

    if (changedProps.has("config")) {
      this.treeConfig.setConfig(this.config);
      this.treeController.setConfig(this.config);

      if (this.treeConfig.initialNodeId) {
        let result = this.treeController.findNodeByKey(
          this.root,
          this.treeConfig.initialNodeId
        );
        console.log("RESULT", this.root, result)
        if (result) {
          this.treeController.setTree(result.path, [
            String(get(result.node, this.treeConfig.idPath)),
          ]);
        }
      }
    }
  }

  dispatchDragAndDrop(dragKey, dropKey) {
    let options = {
      detail: {
        dragKey: dragKey,
        dropKey: dropKey,
      },
      bubbles: true,
      composed: true,
    };
    this.dispatchEvent(new CustomEvent("dragAndDrop", options));
  }

  dispatchSiblingDragAndDrop(dragKey, dropKey) {
    let options = {
      detail: {
        dragKey: dragKey,
        dropKey: dropKey,
      },
      bubbles: true,
      composed: true,
    };
    this.dispatchEvent(new CustomEvent("siblingDragAndDrop", options));
  }

  dispatchPrevDragAndDrop(dragKey, dropKey) {
    let options = {
      detail: {
        dragKey: dragKey,
        dropKey: dropKey,
      },
      bubbles: true,
      composed: true,
    };
    this.dispatchEvent(new CustomEvent("prevDragAndDrop", options));
  }

  handleDragAndDrop(e) {
    this.dispatchDragAndDrop(e.detail.dragKey, e.detail.dropKey);
  }

  handleSiblingDragAndDrop(e) {
    this.dispatchSiblingDragAndDrop(e.detail.dragKey, e.detail.dropKey);
  }

  render() {
    if (!this.root) {
      return;
    }

    if (this.treeConfig.showRoot || !Array.isArray(this.root.children)) {
      return html` <ul
        style="margin:0;"
        role="tree"
        aria-labelledby="tree_label"
      >
        <bds-treenode
          @dragAndDrop=${this.handleDragAndDrop}
          key=${get(this.root, this.treeConfig.idPath)}
          .node=${this.root}
          .isRootNode=${true}
          .level=${1}
          label=${get(this.root, this.treeConfig.labelPath)}
          ._data=${get(this.root, this.treeConfig.dataPath)}
          .path=${[get(this.root, this.treeConfig.idPath)]}
        >
        </bds-treenode>
      </ul>`;
    }

    return html`
      <ul style="margin:0;" role="tree" aria-labelledby="tree_label">
        ${repeat(
          this.root.children,
          (node) => get(node, this.treeConfig.idPath),
          (node) => html` <bds-treenode
            @dragAndDrop=${this.handleDragAndDrop}
            key=${get(node, this.treeConfig.idPath)}
            .node=${node}
            .isRootNode=${true}
            .level=${1}
            label=${get(node, this.treeConfig.labelPath)}
            ._data=${get(node, this.treeConfig.dataPath)}
            .path=${[get(node, this.treeConfig.idPath)]}
          >
          </bds-treenode>`
        )}
      </ul>
    `;
  }
}
