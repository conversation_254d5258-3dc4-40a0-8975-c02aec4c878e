{"version": 3, "file": "wex-editor-mru.js", "sourceRoot": "", "sources": ["../../src/components/wex-editor-mru.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AACvC,OAAO,EAAE,aAAa,EAAY,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,aAAa,IAAI,KAAK,EAAE,MAAM,gBAAgB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,KAAK,IAAI,MAAM,aAAa,CAAC;AACpC,OAAO,KAAK,MAAM,MAAM,eAAe,CAAC;AAGxC,IAAM,YAAY,GAAlB,MAAM,YAAa,SAAQ,UAAU;IACnC,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5C,iBAAiB;YACjB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED,YAAY;QACV,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IACnE,CAAC;IAED,WAAW,CAAC,KAAK;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,aAAa;QACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU;YAC/B,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE;YAClD,CAAC,CAAC,EAAE,CAAC;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,GAAG,CACX,CAAC,GAAG,EAAE,EAAE,CACN,CAAC,GAAG,CAAC,UAAU;gBACb,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAClE,CAAC;QACJ,CAAC;IACH,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAA;;;;;;;;;;;;;;;;;;;;sBAoBO,IAAI,CAAC,KAAK,CAAC,mBAAmB;aACzC,4BAA4B;;;;;;;;;;;;qBAYlB,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,YAAY;YACxD,IAAI,CAAC,KAAK,CAAC,mBAAmB;iBAC3B,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qBA4DlB,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,oBAAoB;YAC/D,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;YACtD,GAAG;yBACc,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,aAAa,GAAG,IAAI;YACnE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;YACtD,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAsCiB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;;;;;4BAKhC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;4BAInC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;6BAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;;;;;;4BAMtB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;;;iCAGf,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;;;;cAIvC,IAAI,CAAC,IAAI,CAAC,GAAG,CACb,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAA;iCACa,IAAI,CAAC,QAAQ;4CACF,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU;YACxD,CAAC,CAAC,aAAa;YACf,CAAC,CAAC,EAAE;2BACG,IAAI;+BACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;4BACjC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;kCAC3B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC;;oBAEpD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;iCACV,CACpB;;;;KAIR,CAAC;IACJ,CAAC;IAED,YAAY,CAAC,GAAG;QACd,OAAO,IAAI,CAAA;;;;;4BAKa,GAAG,CAAC,SAAS;mBACtB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;;;mBAG1B,GAAG;;mBAEH,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;mBACvB,GAAG,CAAC,QAAQ;;;;;mBAKZ,GAAG;;mBAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;mBACpB,GAAG,CAAC,QAAQ,aAAa,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;YACxD,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,MAAM;;;;gBAIJ,GAAG,CAAC,IAAI;;sBAEF,CAAC;IACrB,CAAC;IAED,sBAAsB,CAAC,CAAC;QACtB,uCAAuC;QACvC,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QACxD,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAChC,CAAC,CAAC,aAAa,CAAC,IAAI,EACpB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAC1B,KAAK,EACL,YAAY,CACb,CAAC;YACF,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC;YAEnC,IAAI,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC;gBACxC,IACE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAQ;oBAC5C,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAC7B,CAAC;oBACD,OAAO;gBACT,CAAC;YACH,CAAC;YACD,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,0BAA0B;gBACpC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;aAC5B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,mBAAmB,CAAC,CAAC;QACnB,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC5B,KAAK,YAAY;gBACf,KAAK,CAAC,QAAQ,CACZ,uBAAuB,EACvB,IAAI,CAAC,KAAK,CAAC,wBAAwB,CACpC,CAAC;gBACF,MAAM;YACR,KAAK,QAAQ;gBACX,KAAK,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBAClE,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,CAAC,EAAE,CACP,OAAO;oBACL,IAAI,CAAC,KAAK,CAAC,QAAQ;oBACnB,oBAAoB;oBACpB,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAQ;oBAC5C,OAAO;oBACP,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,aAAa,CACpD,CAAC;gBACF,MAAM;YACR,KAAK,kBAAkB;gBACrB,MAAM,CAAC,gBAAgB,CAAC;oBACtB,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAQ;oBACtD,GAAG,EAAE,KAAK;oBACV,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAQ;oBACtD,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;iBAC9B,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,cAAc;gBACjB,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE;oBACjC,MAAM,EAAE,cAAc;oBACtB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB;iBAC1C,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,cAAc;gBACjB,+BAA+B;gBAC/B,wCAAwC;gBACxC,uBAAuB;gBACvB,MAAM;gBACN,wBAAwB;gBACxB,wDAAwD;gBACxD,KAAK;gBACL,yBAAyB;gBACzB,2CAA2C;gBAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED,cAAc,CAAC,CAAC;QACd,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,0BAA0B;YACpC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;SAC5B,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,iBAAiB,CAAC,CAAC;QACjB,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;QAC9B,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QACtE,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACnB,QAAQ,IAAI,CAAC,EAAE,EAAE,CAAC;gBAChB,KAAK,SAAS;oBACZ,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACzB,QAAQ,EAAE,0BAA0B;wBACpC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;qBAC5B,CAAC,CAAC;oBACH,MAAM,CAAC,EAAE,CACP,OAAO;wBACL,IAAI,CAAC,KAAK,CAAC,QAAQ;wBACnB,oBAAoB;wBACpB,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAQ;wBAC5C,OAAO;wBACP,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,aAAa;wBACjD,oBAAoB,CACvB,CAAC;oBACF,MAAM;gBACR,KAAK,MAAM;oBACT,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACzB,QAAQ,EAAE,0BAA0B;wBACpC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;qBAC5B,CAAC,CAAC;oBACH,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,MAAM;gBACR,KAAK,YAAY;oBACf,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;wBACzB,QAAQ,EAAE,0BAA0B;wBACpC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;qBAC5B,CAAC,CAAC;oBACH,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;wBACzB,IAAI,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC;4BACxC,IACE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAQ;gCAC5C,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAC7B,CAAC;gCACD,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oCACzB,QAAQ,EAAE,0BAA0B;oCACpC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;iCAC5B,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;gCACzB,QAAQ,EAAE,0BAA0B;gCACpC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;6BAC5B,CAAC,CAAC;wBACL,CAAC;wBACD,KAAK,CAAC,QAAQ,CACZ,uBAAuB,EACvB,IAAI,CAAC,KAAK,CAAC,wBAAwB,CACpC,CAAC;wBACF,MAAM;oBACR,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC;oBACxC,IACE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,QAAQ;wBAC5C,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAC7B,CAAC;wBACD,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;4BACzB,QAAQ,EAAE,0BAA0B;4BACpC,KAAK,EAAE,IAAI;yBACZ,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;gBACH,CAAC;gBACD,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACzB,QAAQ,EAAE,0BAA0B;oBACpC,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,IAAI;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,WAAW;QACT,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;IACvE,CAAC;CACF,CAAA;AAzaK,YAAY;IADjB,aAAa,CAAC,gBAAgB,CAAC;GAC1B,YAAY,CAyajB", "sourcesContent": ["import { LitElement, html } from \"lit\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\nimport { storeInstance as Store } from \"store/index.js\";\r\nimport { Router } from \"@vaadin/router\";\r\nimport * as util from \"lib/util.ts\";\r\nimport * as wexlib from \"lib/wexlib.js\";\r\n\r\n@customElement(\"wex-editor-mru\")\r\nclass WexEditorMru extends LitElement {\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this.subscription = Store.subscribe((state) => {\r\n      /*WexEditLayout*/\r\n      this.stateChange(state);\r\n    });\r\n    this.state = Store.state;\r\n    this.string = this.state[this.state.langCode];\r\n    this.maps = [];\r\n    this._marshellRows();\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    Store.unsubscribe(this.subscription);\r\n  }\r\n\r\n  firstUpdated() {\r\n    this.contextMenu = this.shadowRoot.querySelector(\"#contextmenu\");\r\n  }\r\n\r\n  stateChange(state) {\r\n    this.state = state;\r\n    this.string = this.state[this.state.langCode];\r\n    this._marshellRows();\r\n  }\r\n\r\n  _marshellRows() {\r\n    this.rows = this.state.editor_mru\r\n      ? structuredClone(this.state.editor_mru).reverse()\r\n      : [];\r\n    if (this.state.editor_mru_selected_file) {\r\n      this.rows.map(\r\n        (row) =>\r\n          (row.isSelected =\r\n            row.resLblId == this.state.editor_mru_selected_file.resLblId)\r\n      );\r\n    }\r\n  }\r\n\r\n  render() {\r\n    return html`\r\n      <style>\r\n        .lockedbyother{color:red;}\r\n        .lockedbyselfhere{color:green;}\r\n        .lockedbyselfelsewhere{color:orange;}\r\n\r\n        .rowselected{\r\n            --sapList_Background: var(--row-selected-background);\r\n        }\r\n\r\n        #notasks{\r\n            font-style: oblique;\r\n            opacity: 0.5;\r\n            text-align:center;\r\n            margin-top:50px;\r\n        }\r\n        .datarow{cursor:pointer;}\r\n        .ditamap{font-weight:bold;}\r\n\r\n        #filepackageheader{\r\n            height: ${this.state.global_screen_specs\r\n          .edit_filepackageHeaderHeight}px !important;\r\n            color:#000;\r\n            text-align:center;\r\n            display:flex;\r\n            flex-direction: row;\r\n            justify-content: space-between;\r\n            flex-wrap: nowrap\r\n        }\r\n        #filepackageheader>div{flex-shrink: 1;}\r\n\r\n\r\n        #mrutablecontainer{\r\n            height:${this.state.global_screen_specs.layoutHeight -\r\n        this.state.global_screen_specs\r\n          .edit_filepackageHeaderHeight}px; !important;\r\n            overflow: auto;\r\n        }\r\n\r\n        #filerole{\r\n            display: inline-block;;\r\n            margin-left:-12px;\r\n            cursor: pointer;\r\n            }\r\n\r\n        #filerole ui5-radio-button{\r\n\r\n            }\r\n\r\n        .ui5button{margin-top:7px;}\r\n        #nextprev{\r\n            line-height:30px;\r\n            padding-top:10px;\r\n            color:#32363A;\r\n            margin-right:-6px;\r\n        }\r\n\r\n        #nexticon{cursor:pointer;}\r\n        #previcon{cursor:pointer;}\r\n        #nexticon[disabled]{cursor:forbidden;color:#ccc;}\r\n        #previcon[disabled]{cursor:forbidden;color:#ccc;}\r\n\r\n        .reviewedicon{\r\n            --iron-icon-width:16px;\r\n        }\r\n        .hide{visibility: hidden;}\r\n        .sortbuttoncontainer{\r\n            display:flex;\r\n            flex-direction:column;\r\n        }\r\n        .sortbuttoncontainer iron-icon{\r\n            margin-left: .25rem;\r\n            --iron-icon-width:.9rem\r\n        }\r\n        iron-icon.asc{margin-bottom:-.6rem}\r\n        iron-icon.desc{margin-top:-.6rem}\r\n        iron-icon.muted{\r\n            color: #ccc;\r\n        }\r\n\r\n        .colhead{\r\n            line-height: 1.4rem;\r\n            display:flex;\r\n            flex-direction: row;\r\n            align-items:center;\r\n\r\n        }\r\n        .colhead>span:first-child{\r\n            font-size:.8rem;\r\n        }\r\n        .iconcontatiner{\r\n            height:1.4rem;\r\n            overflow:hidden;\r\n            }\r\n        .sidecomponent{\r\n            width: ${(this.state.global_screen_specs.edit_leftSideWidthPx -\r\n          this.state.global_screen_specs.edit_side_tabs_width) *\r\n        0.9}px;\r\n            min-width: ${(this.state.global_screen_specs.viewportWidth * 0.25 -\r\n          this.state.global_screen_specs.edit_side_tabs_width) *\r\n        0.9}px;\r\n        }\r\n        #sidecomponentparent{\r\n            display:flex;\r\n            flex-direction:column;\r\n            justify-content: space-between;\r\n            align-items:center;\r\n            background-color:#F2F2F2;\r\n        }\r\n        ui5-table-cell>div{\r\n            white-space: nowrap;\r\n        }\r\n        .disabled{\r\n            color:#ccc;\r\n            cursor:forbidden;\r\n        }\r\n        *[disabled]{\r\n            color:#ccc;\r\n            cursor:forbidden;\r\n        }\r\n        #nextprev ui5-button{\r\n            height:1.5rem;\r\n            width:2rem;\r\n        }\r\n        ui5-button>iron-icon{\r\n            width:15px;\r\n        }\r\n\r\n        .fileicon, .previewicon, .editicon, .reviewedicon{width: 1rem;}\r\n        .fileicon:hover, .previewicon:hover, .editicon:hover, reviewedicon:hover{color:#000;}\r\n        #mruheader{\r\n            background-color: #fff;\r\n            height: 50px;\r\n            line-height: 50px;\r\n            color: var(--_ui5_tc_headeritem_text_selected_color);\r\n            text-align:center;\r\n            }\r\n      </style>\r\n      <div id=\"mruheader\">${this.string[\"_mostrecentlyused\"]}</div>\r\n      <div id=\"mrutablecontainer\">\r\n        <vaadin-context-menu\r\n          id=\"contextmenu\"\r\n          selector=\".has-menu\"\r\n          @item-selected=\"${this._contextmenuclicked.bind(this)}\"\r\n        >\r\n          <ui5-table\r\n            id=\"filepackagetable\"\r\n            no-data-text=\"${this.string[\"_nofileseditormru\"]}\"\r\n            ?show-no-data=\"${this.rows.length == 0}\"\r\n            sticky-column-header\r\n          >\r\n            <ui5-table-column\r\n              class=\"foo\"\r\n              slot=\"columns\"\r\n              popin-text=\"${this.string[\"_name\"]}\"\r\n            >\r\n              <span class=\"colhead\">\r\n                <span class=\"\">${this.string[\"_name\"]}</span>\r\n              </span>\r\n            </ui5-table-column>\r\n\r\n            ${this.rows.map(\r\n              (item) =>\r\n                html` <ui5-table-row\r\n                 data-reslblid=${item.resLblId}\r\n                  class=\"datarow has-menu ${item.mapClass} ${item.isSelected\r\n                    ? \"rowselected\"\r\n                    : \"\"}\"\r\n                  .item=\"${item}\"\r\n                  @dblclick=\"${this._defaultAction.bind(this)}\"\r\n                  @click=\"${this._handleRowClicked.bind(this)}\"\r\n                  @contextmenu=\"${this._handleRowRightClicked.bind(this)}\"\r\n                >\r\n                  ${this._rowTemplate(item)}\r\n                </ui5-table-row>`\r\n            )}\r\n          </ui5-table>\r\n        </vaadin-context-menu>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  _rowTemplate(itm) {\r\n    return html`<ui5-table-cell align=\"left\"\r\n      ><div>\r\n        <iron-icon\r\n          icon=\"vaadin:file-text-o\"\r\n          id=\"properties\"\r\n          class=\"fileicon ${itm.iconClass}\"\r\n          title=\"${this.string[\"_properties\"]}\"\r\n        ></iron-icon>\r\n        <iron-icon\r\n          .item=\"${itm}\"\r\n          icon=\"vaadin:eye\"\r\n          title=\"${this.string[\"_preview\"]}\"\r\n          class=\"${itm.selected} previewicon\"\r\n          id=\"preview\"\r\n        >\r\n        </iron-icon>\r\n        <iron-icon\r\n          .item=\"${itm}\"\r\n          icon=\"vaadin:edit\"\r\n          title=\"${this.string[\"_edit\"]}\"\r\n          class=\"${itm.selected} editicon ${util.isFileEditable(itm)\r\n            ? \"\"\r\n            : \"hide\"}\"\r\n          id=\"edit\"\r\n        >\r\n        </iron-icon>\r\n        &nbsp;${itm.name}\r\n      </div>\r\n    </ui5-table-cell>`;\r\n  }\r\n\r\n  _handleRowRightClicked(e) {\r\n    //modify conditional context menu items\r\n    var effectiveCap = this.state.wex_user.globalCapability;\r\n    if (e.currentTarget.item) {\r\n      var menuItems = util.buildFileMenu(\r\n        e.currentTarget.item,\r\n        this.state.menus.file_menu,\r\n        \"mru\",\r\n        effectiveCap\r\n      );\r\n      menuItems.map((item) => (item.text = this.string[item.label]));\r\n      this.contextMenu.items = menuItems;\r\n\r\n      if (this.state.editor_mru_selected_file) {\r\n        if (\r\n          this.state.editor_mru_selected_file.resLblId ==\r\n          e.currentTarget.item.resLblId\r\n        ) {\r\n          return;\r\n        }\r\n      }\r\n      Store.dispatch(\"setState\", {\r\n        property: \"editor_mru_selected_file\",\r\n        value: e.currentTarget.item,\r\n      });\r\n    }\r\n  }\r\n\r\n  _contextmenuclicked(e) {\r\n    switch (e.detail.value.name) {\r\n      case \"properties\":\r\n        Store.dispatch(\r\n          \"filePropertiesRequest\",\r\n          this.state.editor_mru_selected_file\r\n        );\r\n        break;\r\n      case \"delete\":\r\n        Store.dispatch(\"deleteFile\", this.state.editor_mru_selected_file);\r\n        break;\r\n      case \"edit\":\r\n        this._editAction();\r\n        break;\r\n      case \"preview\":\r\n        Router.go(\r\n          \"/wex/\" +\r\n            this.state.langCode +\r\n            \"/preview?resLblId=\" +\r\n            this.state.editor_mru_selected_file.resLblId +\r\n            \"&aod=\" +\r\n            this.state.editor_mru_selected_file.verCreateDate\r\n        );\r\n        break;\r\n      case \"previewnewwindow\":\r\n        wexlib.previewNewWindow({\r\n          resLblId: this.state.editor_mru_selected_file.resLblId,\r\n          aod: \"now\",\r\n          mimeType: this.state.editor_mru_selected_file.mimeType,\r\n          langCode: this.state.langCode,\r\n        });\r\n        break;\r\n      case \"open_ditamap\":\r\n        Store.dispatch(\"handleFileAction\", {\r\n          action: \"open_ditamap\",\r\n          file: this.state.editor_mru_selected_file,\r\n        });\r\n        break;\r\n      case \"edit_ditamap\":\r\n        // Store.dispatch(\"setState\", {\r\n        //   property: \"editor_side_active_tab\",\r\n        //   value: \"ditamaps\",\r\n        // });\r\n        // var tmp = JSON.parse(\r\n        //   JSON.stringify(this.state.editor_mru_selected_file)\r\n        // );\r\n        // tmp.isDitabase = true;\r\n        // Store.dispatch(\"checkOutDitabase\", tmp);\r\n        this._editAction();\r\n    }\r\n  }\r\n\r\n  _defaultAction(e) {\r\n    Store.dispatch(\"setState\", {\r\n      property: \"editor_mru_selected_file\",\r\n      value: e.currentTarget.item,\r\n    });\r\n    this._editAction();\r\n  }\r\n\r\n  _handleRowClicked(e) {\r\n    const path = e.composedPath();\r\n    var iconClicked = path[0].tagName == \"IRON-ICON\" && !path[0].disabled;\r\n    if (iconClicked) {\r\n      var icon = path[0];\r\n      switch (icon.id) {\r\n        case \"preview\":\r\n          Store.dispatch(\"setState\", {\r\n            property: \"editor_mru_selected_file\",\r\n            value: e.currentTarget.item,\r\n          });\r\n          Router.go(\r\n            \"/wex/\" +\r\n              this.state.langCode +\r\n              \"/preview?resLblId=\" +\r\n              this.state.editor_mru_selected_file.resLblId +\r\n              \"&aod=\" +\r\n              this.state.editor_mru_selected_file.verCreateDate +\r\n              \"&previewStyle=html\"\r\n          );\r\n          break;\r\n        case \"edit\":\r\n          Store.dispatch(\"setState\", {\r\n            property: \"editor_mru_selected_file\",\r\n            value: e.currentTarget.item,\r\n          });\r\n          this._editAction();\r\n          break;\r\n        case \"properties\":\r\n          Store.dispatch(\"setState\", {\r\n            property: \"editor_mru_selected_file\",\r\n            value: e.currentTarget.item,\r\n          });\r\n          if (e.currentTarget.item) {\r\n            if (this.state.editor_mru_selected_file) {\r\n              if (\r\n                this.state.editor_mru_selected_file.resLblId !=\r\n                e.currentTarget.item.resLblId\r\n              ) {\r\n                Store.dispatch(\"setState\", {\r\n                  property: \"editor_mru_selected_file\",\r\n                  value: e.currentTarget.item,\r\n                });\r\n              }\r\n            } else {\r\n              Store.dispatch(\"setState\", {\r\n                property: \"editor_mru_selected_file\",\r\n                value: e.currentTarget.item,\r\n              });\r\n            }\r\n            Store.dispatch(\r\n              \"filePropertiesRequest\",\r\n              this.state.editor_mru_selected_file\r\n            );\r\n            break;\r\n          }\r\n      }\r\n    } else {\r\n      if (e.currentTarget.item) {\r\n        if (this.state.editor_mru_selected_file) {\r\n          if (\r\n            this.state.editor_mru_selected_file.resLblId ==\r\n            e.currentTarget.item.resLblId\r\n          ) {\r\n            Store.dispatch(\"setState\", {\r\n              property: \"editor_mru_selected_file\",\r\n              value: null,\r\n            });\r\n            return;\r\n          }\r\n        }\r\n        Store.dispatch(\"setState\", {\r\n          property: \"editor_mru_selected_file\",\r\n          value: e.currentTarget.item,\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  _editAction() {\r\n    Store.dispatch(\"checkEditFile\", this.state.editor_mru_selected_file);\r\n  }\r\n}\r\n"]}