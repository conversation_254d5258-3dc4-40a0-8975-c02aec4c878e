import { DitamapNode } from "./ditamap-node";
export declare class DitamapTree {
    /**
     * Map of all the stored ditamap xml
     */
    workspace: Map<string, HTMLElement>;
    /**
     * Underlying tree data structure
     */
    root: DitamapNode | null;
    /**
     * Name of the root map
     */
    rootMapName: string | null;
    constructor(rootMapName: string, files?: Map<string, HTMLElement>);
    buildTree(mapName: string, parent?: DitamapNode | null): DitamapNode | null;
    _fetchMap(mapName: string): Promise<HTMLElement>;
    _attachNodeToParent(node: DitamapNode, parent: DitamapNode | null): void;
    /**
     * Builds json tree from xml
     * @param rootHtml - parsed xml element
     */
    _treeBfs(rootHtml: HTMLElement): DitamapNode;
    /**
     * Checks if an html element is a ditamap
     * @param element
     */
    static isMap(element: HTMLElement): boolean;
    /**
     * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)
     * @param element
     */
    static isContent(element: HTMLElement): boolean;
    /**
     * Decorates a tree map node with information from the xml
     * @param element - xml element
     */
    _decorateMapNode(element: HTMLElement, containingMap: string | null): DitamapNode;
    static getMapName(element: HTMLElement, rootMapName?: string): string | undefined;
    static getMapTitle(element: HTMLElement): string;
    _removeXmlNoise(str: string): string;
}
//# sourceMappingURL=ditamap-tree.d.ts.map