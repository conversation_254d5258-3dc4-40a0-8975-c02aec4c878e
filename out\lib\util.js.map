{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/lib/util.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,MAAM,MAAM,WAAW,CAAC;AAC/B,OAAO,KAAK,YAAY,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,aAAa,IAAI,KAAK,EAAE,MAAM,gBAAgB,CAAC;AAGxD,MAAM,iBAAiB,GAAG,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;AAC1D,6CAA6C;AAE7C,MAAM,OAAO,GAAG;IACd,MAAM,EAAE,CAAC;IACT,WAAW,EAAE,CAAC;IACd,MAAM,EAAE,CAAC;IACT,OAAO,EAAE,CAAC;CACX,CAAC;AAEF,MAAM,CAAC,MAAM,GAAG,GAAG,UAAU,GAAG;IAC9B,mBAAmB;AACrB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,UAAU,GAAG;IACpC,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,GAAG,GAAG,UAAU,GAAe,EAAE,MAAc;IAC1D,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG,UAAU,GAAG,EAAE,MAAM,EAAE,IAAI;IAC3D,IAAI,IAAI,EAAE,CAAC;QACT,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC;IACD,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC;AAC5D,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,UAAU,IAAI;IACrC,IAAI,GAAG,GAAG,KAAK,CAAC;IAChB,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QAC3B,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,UAAU,MAAM,EAAE,SAAS;IAC3D,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;AAC/B,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAG,UAAU,IAAI;IAC1C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;gBACrC,IAAI,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;oBAC5C,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,IAAI,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBACxC,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC,CAAC,sCAAsC;YACrD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAEF,OAAO;AACP,MAAM,CAAC,MAAM,iBAAiB,GAAG,UAAU,IAAI;IAC7C,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;IACxB,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QACzD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;QACZ,qDAAqD;IACvD,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,UAAU,QAAQ;IAC/C,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IACtD,IAAI,CAAC,QAAQ;QAAE,OAAO,SAAS,CAAC;IAChC,IAAI,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;QACvE,OAAO,SAAS,CAAC;IACnB,CAAC;SAAM,CAAC;QACN,OAAO,QAAQ,CAAC;IAClB,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAG;IAChC,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;IACxC,IAAI,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,IAAI,EAAE,CAAC;IAChD,IAAI,SAAS,GAAG,kBAAkB,CAAC,SAAS,GAAG,QAAQ,GAAG,YAAY,CAAC,CAAC;IACxE,IAAI,KAAK,GAAG,SAAS,CAAC,qBAAqB,GAAG,SAAS,CAAC,CAAC;IACzD,MAAM,CAAC,QAAQ;QACb,kEAAkE,GAAG,KAAK,CAAC;IAE7E,6DAA6D;IAC7D,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;QACnC,QAAQ,CAAC,MAAM,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,EAAE;IACjC,OAAO,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEF,eAAe;AACf,2EAA2E;AAC3E,kEAAkE;AAClE,kDAAkD;AAClD,MAAM,CAAC,MAAM,aAAa,GAAG,UAC3B,IAAI,EACJ,SAAS,EACT,MAAM,EACN,SAAS,EACT,OAAO,EACP,SAAS;IAET,MAAM,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;IACzC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;IAE/B,yBAAyB;IACzB,IAAI,IAAI,EAAE,CAAC;QACT,KAAK,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAClD,KAAK,CAAC,gBAAgB,CAAC,QAAQ,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC3D,KAAK,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;QACrE,KAAK,CAAC,YAAY,CAAC,QAAQ,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC/C,KAAK,CAAC,YAAY,CAAC,QAAQ,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;SAAM,CAAC;QACN,4BAA4B;QAC5B,KAAK,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QACjC,KAAK,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC9B,KAAK,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvC,KAAK,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QACjC,KAAK,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC;QACnC,KAAK,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC;QACnC,KAAK,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC1B,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC3B,gCAAgC;QAChC,KAAK,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC3B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,YAAY;YACf,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACpC,MAAM;QACR,KAAK,aAAa;YAChB,oBAAoB;YACpB,sCAAsC;YACtC,wCAAwC;YACxC,0CAA0C;YAC1C,8CAA8C;YAC9C,IAAI;YACJ,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,IAAI,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC;gBAC3D,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;YACjC,CAAC;YACD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACnC,MAAM;QACR,KAAK,mBAAmB;YACtB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,IAAI,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC3C,CAAC;YACD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACnC,MAAM;QACR,KAAK,QAAQ;YACX,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,IAAI,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC3C,CAAC;YACD,MAAM;QACR,KAAK,QAAQ;YACX,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACb,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACpB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACrB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACtB,0BAA0B;YAC1B,IAAI,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE,CAAC;gBACjC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACb,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACzB,CAAC;YACD,MAAM;QACR,KAAK,SAAS;YACZ,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,MAAM;QACR,KAAK,KAAK;YACR,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,MAAM;QACR,KAAK,aAAa;YAChB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjC,8BAA8B;YAC9B,gCAAgC;YAChC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC7B,IAAI,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC3C,CAAC;YACD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjC,MAAM;IACV,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,GAAG,CAAC,EAAE,EAAE;IACxD,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC5D,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;IAC9C,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACvC,IAAI,MAAM,GAAG,aAAa,CAAC;IAE3B,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,aAAa;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,SAAS,IAAI,YAAY,EAAE,CAAC;YAC1C,MAAM,GAAG,oBAAoB,CAAC;QAChC,CAAC;aAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,GAAG,kBAAkB,CAAC;QAC9B,CAAC;aAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3C,IACE,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,cAAc,CAAC;gBAC5C,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,CAAC,EAC3C,CAAC;gBACD,MAAM,GAAG,sBAAsB,CAAC;YAClC,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,GAAG,sBAAsB,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,uBAAuB,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IACD,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,IACE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC;gBAC1C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,EACzC,CAAC;gBACD,MAAM,GAAG,aAAa,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG,UAAU,GAAG,EAAE,IAAI;IACtD,MAAM,MAAM,GAAG,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAC5C,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;IACpB,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,aAAa;YAChB,GAAG,CAAC,SAAS,GAAG,EAAE,CAAC;YACnB,GAAG,CAAC,SAAS,GAAG,aAAa,CAAC;YAC9B,MAAM;QACR,KAAK,oBAAoB;YACvB,GAAG,CAAC,SAAS,GAAG,YAAY,CAAC;YAC7B,GAAG,CAAC,SAAS,GAAG,oBAAoB,CAAC;YACrC,MAAM;QACR,KAAK,kBAAkB;YACrB,GAAG,CAAC,SAAS,GAAG,eAAe,CAAC;YAChC,GAAG,CAAC,SAAS,GAAG,kBAAkB,CAAC;YACnC,MAAM;QACR,KAAK,sBAAsB;YACzB,GAAG,CAAC,SAAS,GAAG,kBAAkB,CAAC;YACnC,GAAG,CAAC,SAAS,GAAG,sBAAsB,CAAC;YACvC,MAAM;QACR,KAAK,sBAAsB;YACzB,GAAG,CAAC,SAAS,GAAG,kBAAkB,CAAC;YACnC,GAAG,CAAC,SAAS,GAAG,sBAAsB,CAAC;YACvC,MAAM;QACR,KAAK,uBAAuB;YAC1B,GAAG,CAAC,SAAS,GAAG,uBAAuB,CAAC;YACxC,GAAG,CAAC,SAAS,GAAG,uBAAuB,CAAC;YACxC,MAAM;QACR,KAAK,aAAa;YAChB,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC;YAC5B,GAAG,CAAC,SAAS,GAAG,mBAAmB,CAAC;IACxC,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAAG,UAAU,IAAI,EAAE,IAAI;IACtD,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAC,qCAAqC;IACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC3B,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAChC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;IAClD,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAAG,UAAU,IAAI,EAAE,IAAI;IAC7D,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,8CAA8C;IAC9C,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACpB,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBACxB,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBAC/C,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC,yBAAyB;gBACvD,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;gBAC1B,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;gBACtB,MAAM;YACR,CAAC;QACH,CAAC;QAED,yEAAyE;QACzE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBACzB,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,CAAC,QAAQ;gBACX,UAAU,CAAC,CAAC,mGAAmG;YACjH,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YACnC,CAAC;YACD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;gBACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAClC,CAAC;YACD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YACjC,CAAC;YACD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YACzB,CAAC;YACD,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IACD,IAAI,GAAG,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;IAChB,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;IACf,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC;IACrB,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;IACnB,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACvB,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG,UAAU,IAAI;IAClD,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;IAChC,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,IAAI,MAAM,GAAG;YACX,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YAChE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YAC9D,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC;YACxE,UAAU,EAAE,IAAI,CAAC,UAAU;iBACxB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;iBACxB,OAAO,CAAC,YAAY,CAAC;YACxB,eAAe,EAAE,IAAI,CAAC,UAAU;iBAC7B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;iBACxB,OAAO,CAAC,iBAAiB,CAAC;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;iBACxB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;iBACxB,OAAO,CAAC,YAAY,CAAC;YACxB,cAAc,EAAE,IAAI,CAAC,UAAU;iBAC5B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;iBACxB,OAAO,CAAC,aAAa,CAAC;YACzB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YAC9D,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;YAClE,UAAU,EAAE,IAAI,CAAC,UAAU;iBACxB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;iBACxB,OAAO,CAAC,YAAY,CAAC;YACxB,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;SACrE,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;YACnE,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;YACjE,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC;gBACzB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;YACzE,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC;YACvB,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;gBACnB,gCAAgC;gBAChC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;YAC/B,CAAC;YACD,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACjB,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC9C,CAAC;YACD,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAClB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAChD,CAAC;YAED,OAAO,CAAC,SAAS;gBACf,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;YACzD,OAAO,CAAC,aAAa;gBACnB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;YAC1D,OAAO,CAAC,eAAe;gBACrB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC;YAC/D,OAAO,CAAC,UAAU;gBAChB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;YAC1D,OAAO,CAAC,cAAc;gBACpB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC;YAC9D,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;YACjE,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;YACrE,OAAO,CAAC,UAAU;gBAChB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;YAE1D,cAAc;YACd,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC3D,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACzD,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACnD,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACzD,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC3D,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC3D,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACzD,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACjE,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAEvE,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAU,IAAI,EAAE,OAAO;IAC7C,OAAO,IAAI,CAAC,UAAU;SACnB,GAAG,CAAC,UAAU,CAAC;QACd,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC;IACtB,CAAC,CAAC;SACD,OAAO,CAAC,OAAO,CAAC,CAAC;AACtB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAU,IAAI,EAAE,YAAY;IAClD,UAAU,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IAC/B,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,UAAU,IAAI;IAChC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACxB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,UAAU,IAAI,EAAE,EAAE,EAAE,MAAM;IAC3C,IAAI,MAAM,EAAE,CAAC;QACX,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IACD,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,WAAW,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAEF,SAAS,QAAQ,CAAC,CAAC;IACjB,OAAO,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC;AACD,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,KAAK,OAAO,GAAG,CAAC;AAEpD,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,QAAQ;IAC1C,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QACxB,SAAS,GAAG,CAAC,CAAC,CAAC;QACf,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IACD,OAAO,UAAU,CAAC,EAAE,CAAC;QACnB;;WAEG;QACH,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;YAC1B,IAAI,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjC,IAAI,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,IAAI,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;gBACzD,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACpD,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,IAAI,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;oBAC3B,IAAI,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;oBACrB,IAAI,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;gBACvB,CAAC;qBAAM,CAAC;oBACN,IAAI,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtD,IAAI,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,OAAO,MAAM,GAAG,SAAS,CAAC;IAC5B,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,UAAU,IAAI,EAAE,SAAS,EAAE,IAAI;IAC7D,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,IAAI,MAAM,GAAG,IAAI,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,MAAM,EAAE,CAAC;YAC3B,OAAO,GAAG,CAAC,CAAC;YACZ,MAAM;QACR,CAAC;IACH,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,GAAG,CAAC,CAAC;YACX,MAAM;QACR,CAAC;IACH,CAAC;IACD,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC;IAC5B,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC;IAC9B,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG,UAAU,IAAI,EAAE,eAAe;IACnE,IAAI,MAAM,GAAG,eAAe,CAAC;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC1B,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAG,UACrC,MAAM,EACN,YAAY,EACZ,IAAI,GAAG,EAAE;IAET,IAAI,MAAM,CAAC,EAAE,IAAI,YAAY,EAAE,CAAC;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;QACnC,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,IAAI,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAClE,MAAM,GAAG,uBAAuB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QACrE,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,UAAU,KAAK;IAC5C,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC9C,IAAI,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC9C,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACjD,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;YACnC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IACD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,OAAO,QAAQ,CAAC;IAClB,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAG,UAAU,QAAQ,EAAE,KAAK;IACxD,IAAI,KAAK,EAAE,CAAC;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC9B,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,OAAO,EAAE,CAAC;oBAC9B,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,MAAM,EAAE,CAAC;oBAC7B,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAU,SAAS,EAAE,IAAI,EAAE,GAAG;IACpD,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;QAC3B,IAAI,GAAG,EAAE,CAAC;YACR,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;gBAClD,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;oBAC/C,CAAC,CAAC,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC,CAAC;QACR,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;gBAClD,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;oBAC/C,CAAC,CAAC,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC,CAAC;QACR,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAG,UAAU,KAAK,EAAE,MAAM;IACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG,UAAU,QAAQ,EAAE,QAAQ;IAC/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,QAAQ,EAAE,CAAC;YACrC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG,UAAU,QAAQ,EAAE,SAAS;IACjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,EAAE,CAAC;YACxC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG,UAAU,IAAI,EAAE,MAAM;IAC1D,IAAI,KAAK,GAAG,EAAE,EACZ,IAAI,EACJ,EAAE,CAAC;IACL,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,EAAE,IAAI,MAAM,EAAE,CAAC;YACtB,YAAY;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACjD,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC;gBAChD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IACD,+BAA+B;IAC/B,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,UAAU,IAAI,EAAE,EAAE;IAChD,IAAI,KAAK,CAAC,EAAE,CAAC;QAAE,OAAO,IAAI,CAAC;IAE3B,aAAa;IACb,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;IACnC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC;IAE5B,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAG,UAAU,IAAI,EAAE,IAAI;IACnD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;QAAE,OAAO,CAAC,CAAC,CAAC;IAC9B,aAAa;IACb,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;IACnC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC5B,IAAI,QAAQ,GAAG,GAAG,CAAC;IAEnB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QACjC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;YACrB,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC,EAAE,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,CAAC;gBACD,QAAQ,IAAI,GAAG,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG,UAAU,IAAI;IACzC,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC,GAAG,EAAE,CAAC;IACX,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;IACrC,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAC9D,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,8BAA8B;QACtE,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;YAAS,CAAC;QACT,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,GAAG,EAAE;IACvB,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;IACxB,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;QAClE,YAAY,EAAE,OAAO;KACtB,CAAC;SACC,aAAa,CAAC,IAAI,CAAC;SACnB,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc,CAAC,CAAC,KAAK,CAAC;IACtD,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,EAAE;IACxC,IAAI,IAAI,GAAG,KAAK,CAAC;IACjB,IAAI,SAAS,EAAE,CAAC;QACd,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9B,IAAI,GAAG,GAAG,GAAG,CAAC,cAAc,EAAE,IAAI,WAAW,EAAE,EAAE,CAAC;IACpD,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF;;KAEK;AACL,MAAM,CAAC,MAAM,qBAAqB,GAAG,KAAK,IAAI,EAAE;IAC9C,MAAM,SAAS,GAAG,MAAM,YAAY,EAAE,CAAC;IACvC,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzD,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;IACxC,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IACzD,MAAM,qBAAqB,GAAG,cAAc,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;IAC3E,OAAO,CAAC,GAAG,qBAAqB,CAAC,CAAC;AACpC,CAAC,CAAC", "sourcesContent": ["import moment from \"moment\";\r\nimport pubsub from \"pubsub-js\";\r\nimport * as capabilities from \"lib/capability.js\";\r\nimport { storeInstance as Store } from \"store/index.js\";\r\nimport { Capability } from \"@bds/types\";\r\n\r\nconst editableMimetypes = [\"text/xml\", \"application/xml\"];\r\n/*,'application/mathml+xml','image/svg+xml'*/\r\n\r\nconst capEnum = {\r\n  author: 4,\r\n  contributor: 3,\r\n  review: 2,\r\n  default: 1,\r\n};\r\n\r\nexport const log = function (msg) {\r\n  //console.log(msg);\r\n};\r\n\r\nexport const deepClone = function (obj) {\r\n  return structuredClone(obj);\r\n};\r\n\r\nexport const can = function (cap: Capability, action: string): boolean {\r\n  return capabilities.default[cap.toLowerCase()].includes(action);\r\n};\r\n\r\nexport const updatePageLocation = function (tab, newurl, push) {\r\n  if (push) {\r\n    history.pushState(null, null, newurl);\r\n  }\r\n  pubsub.publish(\"updateTabUrl\", { tab: tab, url: newurl });\r\n};\r\n\r\nexport const isDitamap = function (file) {\r\n  let res = false;\r\n  if (file && file.ditaClass) {\r\n    res = file.ditaClass.includes(\"map/map\");\r\n  }\r\n  return res;\r\n};\r\n\r\n/**\r\n * Creates an insert Action object.\r\n * These are used to match adding nodes to jsMap tree with inserting to xml.\r\n * @param {\"insertBefore\" | \"appendChild\"} action - How the node is to be inserted\r\n * @param {HTMLElement} insertRef - The reference node\r\n */\r\nexport const createInsertAction = function (action, insertRef) {\r\n  return { action, insertRef };\r\n};\r\n\r\nexport const isFileEditable = function (file) {\r\n  if (file.mimeType) {\r\n    if (file.mimeType == \"text/xml\") {\r\n      if (file.hasOwnProperty(\"ditaClass\")) {\r\n        if (file.ditaClass?.includes(\"topic/topic\")) {\r\n          return true;\r\n        }\r\n        if (file.ditaClass?.includes(\"map/map\")) {\r\n          return true;\r\n        }\r\n        return false;\r\n      } else {\r\n        return true; //fine all text/xml files are editable\r\n      }\r\n    } else {\r\n      return editableMimetypes.includes(file.mimeType);\r\n    }\r\n  } else {\r\n    if (file.name.substr(file.name.length - 3) == \"jpg\") {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n};\r\n\r\n// ????\r\nexport const isFilePreviewable = function (file) {\r\n  let url = location.href;\r\n  if (url.includes(\"/preview\") && url.includes(\"resLblId\")) {\r\n    return false;\r\n  }\r\n  if (file.mimeType) {\r\n    return true;\r\n    //return previeableMimetypes.includes(file.mimeType);\r\n  } else {\r\n    return true;\r\n  }\r\n};\r\n\r\nexport const getEffectiveCap = function (localCap) {\r\n  let globalCap = Store.state.wex_user.globalCapability;\r\n  if (!localCap) return globalCap;\r\n  if (capEnum[globalCap.toLowerCase()] > capEnum[localCap.toLowerCase()]) {\r\n    return globalCap;\r\n  } else {\r\n    return localCap;\r\n  }\r\n};\r\n\r\nexport const redirectToSSOLogin = function () {\r\n  let pathname = window.location.pathname;\r\n  let searchString = window.location.search || \"\";\r\n  var compRedir = encodeURIComponent(\"?redir=\" + pathname + searchString);\r\n  var redir = encodeURI(\"/lwa/jrest/LogonWex\" + compRedir);\r\n  window.location =\r\n    \"/lwa/jrest/Auth/IDPLogin?appId=wex&idpclient=choose&redirecturi=\" + redir;\r\n\r\n  // see bug https://bluestreamdev.atlassian.net/browse/X5-1523\r\n  window.addEventListener(\"load\", () => {\r\n    location.reload();\r\n  });\r\n};\r\n\r\nexport const getCapValue = (cap) => {\r\n  return cap ? capEnum[cap.toLowerCase()] : 1;\r\n};\r\n\r\n// three steps:\r\n// 1) Does menu item apply to file based on mimetype or ditaclass or origin\r\n// 2) Is operation allowed due to file status (locked, dirty, etc)\r\n// 3) Is operation allowed due to user permissions\r\nexport const buildFileMenu = function (\r\n  file,\r\n  menuItems,\r\n  origin,\r\n  globalCap,\r\n  taskCap,\r\n  isClaimed\r\n) {\r\n  const items = structuredClone(menuItems);\r\n  const hr = { component: \"hr\" };\r\n\r\n  // need this elsewhere ..\r\n  if (file) {\r\n    items.preview.disabled = !isFilePreviewable(file);\r\n    items.previewNewWindow.disabled = !isFilePreviewable(file);\r\n    items.edit_topic.disabled = !isFileEditable(file) || isDitamap(file);\r\n    items.edit_ditamap.disabled = !isDitamap(file);\r\n    items.open_ditamap.disabled = !isDitamap(file);\r\n  } else {\r\n    // based on browse as origin\r\n    items.properties.disabled = true;\r\n    items.preview.disabled = true;\r\n    items.previewNewWindow.disabled = true;\r\n    items.edit_topic.disabled = true;\r\n    items.edit_ditamap.disabled = true;\r\n    items.open_ditamap.disabled = true;\r\n    items.cut.disabled = true;\r\n    items.copy.disabled = true;\r\n    // items.rename.disabled = true;\r\n    items.delete.disabled = true;\r\n  }\r\n\r\n  let res = [];\r\n  res.push(items.properties);\r\n  res.push(hr);\r\n  switch (origin) {\r\n    case \"fileselect\":\r\n      res.push(items.highlightFileFolder);\r\n      break;\r\n    case \"filepackage\":\r\n      // if (!isClaimed) {\r\n      //   items.edit_topic.disabled = true;\r\n      //   items.edit_ditamap.disabled = true;\r\n      //   items.removeMenuItem.disabled = true;\r\n      //   items.toggleDoneMenuItem.disabled = true;\r\n      // }\r\n      res.push(items.preview);\r\n      res.push(items.previewNewWindow);\r\n      res.push(items.edit_topic);\r\n      res.push(items.edit_ditamap);\r\n      res.push(items.open_ditamap);\r\n      if (can(globalCap, \"browse\")) {\r\n        res.push(items.openfilelocationMenuItem);\r\n      }\r\n      if (can(getEffectiveCap(taskCap), \"filePackageRemoveFile\")) {\r\n        res.push(items.removeMenuItem);\r\n      }\r\n      res.push(items.toggleDoneMenuItem);\r\n      break;\r\n    case \"editorfilepackage\":\r\n      res.push(items.preview);\r\n      res.push(items.previewNewWindow);\r\n      res.push(items.edit_topic);\r\n      res.push(items.edit_ditamap);\r\n      res.push(items.open_ditamap);\r\n      if (can(globalCap, \"browse\")) {\r\n        res.push(items.openfilelocationMenuItem);\r\n      }\r\n      res.push(items.toggleDoneMenuItem);\r\n      break;\r\n    case \"search\":\r\n      res.push(items.preview);\r\n      res.push(items.previewNewWindow);\r\n      res.push(items.edit_topic);\r\n      res.push(items.edit_ditamap);\r\n      res.push(items.open_ditamap);\r\n      if (can(globalCap, \"browse\")) {\r\n        res.push(items.openfilelocationMenuItem);\r\n      }\r\n      break;\r\n    case \"browse\":\r\n      res.push(items.preview);\r\n      res.push(items.previewNewWindow);\r\n      res.push(items.edit_topic);\r\n      res.push(items.edit_ditamap);\r\n      res.push(items.open_ditamap);\r\n      res.push(items.export_files);\r\n      res.push(items.publish);\r\n      res.push(hr);\r\n      res.push(items.cut);\r\n      res.push(items.copy);\r\n      res.push(items.paste);\r\n      // res.push(items.rename);\r\n      if (can(globalCap, \"deleteFile\")) {\r\n        res.push(hr);\r\n        res.push(items.delete);\r\n      }\r\n      break;\r\n    case \"reports\":\r\n      res.push(items.preview);\r\n      res.push(items.previewNewWindow);\r\n      res.push(items.edit_topic);\r\n      res.push(items.edit_ditamap);\r\n      res.push(items.open_ditamap);\r\n      break;\r\n    case \"mru\":\r\n      res.push(items.preview);\r\n      res.push(items.previewNewWindow);\r\n      res.push(items.edit_topic);\r\n      res.push(items.edit_ditamap);\r\n      res.push(items.open_ditamap);\r\n      break;\r\n    case \"versionlist\":\r\n      res.push(items.preview);\r\n      res.push(items.previewNewWindow);\r\n      // res.push(items.edit_topic);\r\n      // res.push(items.edit_ditamap);\r\n      res.push(items.open_ditamap);\r\n      if (can(globalCap, \"browse\")) {\r\n        res.push(items.openfilelocationMenuItem);\r\n      }\r\n      res.push(items.editHeadMenuItem);\r\n      break;\r\n  }\r\n\r\n  return res;\r\n};\r\n\r\nexport const notifyUser = (type, message, duration = 5) => {\r\n  pubsub.publish(\"notifyUser\", { type, message, duration });\r\n};\r\n\r\nexport const getFileLockStatus = (file, user) => {\r\n  console.log(\"in lock status function\");\r\n  let status = \"_not_locked\";\r\n\r\n  if (file.hasOwnProperty(\"lockOwner\")) {\r\n    if (!file.lockOwner) {\r\n      // do nothing\r\n    } else if (file.lockOwner == \"concurrent\") {\r\n      status = \"_locked_concurrent\";\r\n    } else if (file.lockOwner != user.userName) {\r\n      status = \"_locked_by_other\";\r\n    } else if (file.lockOwner == user.userName) {\r\n      if (\r\n        file.lockLocation.startsWith(\"wex:exs:room\") ||\r\n        file.lockLocation.startsWith(\"wex:cc:room\")\r\n      ) {\r\n        status = \"_room_locked_by_user\";\r\n      } else if (file.lockLocation == user.checkoutLoc) {\r\n        status = \"_sesh_locked_by_user\";\r\n      } else {\r\n        status = \"_xdocs_locked_by_user\";\r\n      }\r\n    }\r\n  }\r\n  if (file.hasOwnProperty(\"doesRoomExist\")) {\r\n    if (!file.doesRoomExist) {\r\n      if (\r\n        file.lockLocation.includes(\"wex:exs:room\") ||\r\n        file.lockLocation.includes(\"wex:cc:room\")\r\n      ) {\r\n        status = \"_rogue_lock\";\r\n      }\r\n    }\r\n  }\r\n  return status;\r\n};\r\n\r\nexport const calculateLockedStatus = function (row, user) {\r\n  const status = getFileLockStatus(row, user);\r\n  row.status = status;\r\n  switch (status) {\r\n    case \"_not_locked\":\r\n      row.iconClass = \"\";\r\n      row.iconTitle = \"_not_locked\";\r\n      break;\r\n    case \"_locked_concurrent\":\r\n      row.iconClass = \"concurrent\";\r\n      row.iconTitle = \"_locked_concurrent\";\r\n      break;\r\n    case \"_locked_by_other\":\r\n      row.iconClass = \"lockedbyother\";\r\n      row.iconTitle = \"_locked_by_other\";\r\n      break;\r\n    case \"_room_locked_by_user\":\r\n      row.iconClass = \"lockedbyselfhere\";\r\n      row.iconTitle = \"_room_locked_by_user\";\r\n      break;\r\n    case \"_sesh_locked_by_user\":\r\n      row.iconClass = \"lockedbyselfhere\";\r\n      row.iconTitle = \"_sesh_locked_by_user\";\r\n      break;\r\n    case \"_xdocs_locked_by_user\":\r\n      row.iconClass = \"lockedbyselfelsewhere\";\r\n      row.iconTitle = \"_xdocs_locked_by_user\";\r\n      break;\r\n    case \"_rogue_lock\":\r\n      row.iconClass = \"roguelock\";\r\n      row.iconTitle = \"_xdocs_rogue_lock\";\r\n  }\r\n  return row;\r\n};\r\n\r\nexport const transformFilepackage = function (data, user) {\r\n  var tmpArray = []; //convert to standard wex file object\r\n  for (var i = 0; i < data.length; i++) {\r\n    var tmp = data[i].metadata;\r\n    tmp.reviewed = data[i].reviewed;\r\n    tmpArray.push(calculateLockedStatus(tmp, user));\r\n  }\r\n  return tmpArray;\r\n};\r\n\r\nexport const transformVersionListResults = function (data, user) {\r\n  var resultSet = [];\r\n  //first get the first resMeta section you find\r\n  if (data.length > 0) {\r\n    var resMeta = null;\r\n    for (var i = 0; i <= data.length; i++) {\r\n      if (data[i].resMetaData) {\r\n        resMeta = structuredClone(data[i].resMetaData);\r\n        resMeta.verCreateDate = null; // do not clone this data\r\n        resMeta.verCreator = null;\r\n        resMeta.verNum = null;\r\n        break;\r\n      }\r\n    }\r\n\r\n    //now loop through items adding resMeta to the items that don't have one.\r\n    for (var i = 0; i <= data.length - 1; i++) {\r\n      var item = {};\r\n      if (!data[i].resMetaData) {\r\n        item = structuredClone(resMeta);\r\n      } else {\r\n        item = structuredClone(data[i].resMetaData);\r\n      }\r\n      item.mimeType =\r\n        \"text/xml\"; /* HACK just to continue development until GetVersionHistory call is updated to include mimeType.*/\r\n      if (data[i].hasOwnProperty(\"itemDate\")) {\r\n        item.itemDate = data[i].itemDate;\r\n      }\r\n      if (data[i].hasOwnProperty(\"labelName\")) {\r\n        item.labelName = data[i].labelName;\r\n        item.verNum = data[i].labelName;\r\n      }\r\n      if (data[i].hasOwnProperty(\"labelId\")) {\r\n        item.labelId = data[i].labelId;\r\n      }\r\n      if (data[i].hasOwnProperty(\"now\")) {\r\n        item.now = data[i].now;\r\n      }\r\n      resultSet.push(calculateLockedStatus(item, user));\r\n    }\r\n  }\r\n  var tmp = structuredClone(resultSet[0]);\r\n  tmp.head = true;\r\n  tmp.now = true;\r\n  tmp.itemDate = \"now\";\r\n  tmp.verNum = \"now\";\r\n  resultSet.unshift(tmp);\r\n  return resultSet;\r\n};\r\n\r\nexport const transformSearchResults = function (data) {\r\n  let user = Store.state.wex_user;\r\n  let resultSet = [];\r\n  if (data.resultRow) {\r\n    let colIdx = {\r\n      title: data.columnMeta.map((x) => x[\"colName\"]).indexOf(\"Title\"),\r\n      name: data.columnMeta.map((x) => x[\"colName\"]).indexOf(\"Name\"),\r\n      lockOwner: data.columnMeta.map((x) => x[\"colName\"]).indexOf(\"LockOwner\"),\r\n      createDate: data.columnMeta\r\n        .map((x) => x[\"colName\"])\r\n        .indexOf(\"CreateDate\"),\r\n      activeProcesses: data.columnMeta\r\n        .map((x) => x[\"colName\"])\r\n        .indexOf(\"ActiveProcesses\"),\r\n      fileStatus: data.columnMeta\r\n        .map((x) => x[\"colName\"])\r\n        .indexOf(\"FileStatus\"),\r\n      numConnections: data.columnMeta\r\n        .map((x) => x[\"colName\"])\r\n        .indexOf(\"NumComments\"),\r\n      size: data.columnMeta.map((x) => x[\"colName\"]).indexOf(\"Size\"),\r\n      fileId: data.columnMeta.map((x) => x[\"colName\"]).indexOf(\"FileId\"),\r\n      folderPath: data.columnMeta\r\n        .map((x) => x[\"colName\"])\r\n        .indexOf(\"FolderPath\"),\r\n      hitText: data.columnMeta.map((x) => x[\"colName\"]).indexOf(\"HitText\"),\r\n    };\r\n\r\n    for (let i = 0; i < data.resultRow.length; i++) {\r\n      var fileRow = {};\r\n      fileRow.title = data.resultRow[i].columnValues[colIdx.title].value;\r\n      fileRow.name = data.resultRow[i].columnValues[colIdx.name].value;\r\n      if (colIdx.hitText != -1) {\r\n        fileRow.hitText = data.resultRow[i].columnValues[colIdx.hitText].value;\r\n      } else {\r\n        fileRow.hitText = \"\";\r\n      }\r\n\r\n      if (!fileRow.title) {\r\n        //pdf's and images have no title\r\n        fileRow.title = fileRow.name;\r\n      }\r\n      if (fileRow.name) {\r\n        fileRow.lcname = fileRow.name.toLowerCase();\r\n      }\r\n      if (fileRow.title) {\r\n        fileRow.lctitle = fileRow.title.toLowerCase();\r\n      }\r\n\r\n      fileRow.lockOwner =\r\n        data.resultRow[i].columnValues[colIdx.lockOwner].value;\r\n      fileRow.verCreateDate =\r\n        data.resultRow[i].columnValues[colIdx.createDate].value;\r\n      fileRow.activeProcesses =\r\n        data.resultRow[i].columnValues[colIdx.activeProcesses].value;\r\n      fileRow.fileStatus =\r\n        data.resultRow[i].columnValues[colIdx.fileStatus].value;\r\n      fileRow.numConnections =\r\n        data.resultRow[i].columnValues[colIdx.numConnections].value;\r\n      fileRow.size = data.resultRow[i].columnValues[colIdx.size].value;\r\n      fileRow.fileId = data.resultRow[i].columnValues[colIdx.fileId].value;\r\n      fileRow.folderPath =\r\n        data.resultRow[i].columnValues[colIdx.folderPath].value;\r\n\r\n      //from resInfo\r\n      fileRow.ditaClass = data.resultRow[i].resInfo[\"ditaClass\"];\r\n      fileRow.mimeType = data.resultRow[i].resInfo[\"mimeType\"];\r\n      fileRow.isXml = data.resultRow[i].resInfo[\"isXml\"];\r\n      fileRow.resLblId = data.resultRow[i].resInfo[\"resLblId\"];\r\n      fileRow.resPathId = data.resultRow[i].resInfo[\"resPathId\"];\r\n      fileRow.lineageId = data.resultRow[i].resInfo[\"lineageId\"];\r\n      fileRow.lockDate = data.resultRow[i].resInfo[\"lockDate\"];\r\n      fileRow.lockLocation = data.resultRow[i].resInfo[\"lockLocation\"];\r\n      fileRow.rootElementName = data.resultRow[i].resInfo[\"rootElementName\"];\r\n\r\n      resultSet.push(calculateLockedStatus(fileRow, user));\r\n    }\r\n  }\r\n  return resultSet;\r\n};\r\n\r\nexport const getIndex = function (data, fldName) {\r\n  return data.columnMeta\r\n    .map(function (d) {\r\n      return d[\"colName\"];\r\n    })\r\n    .indexOf(fldName);\r\n};\r\n\r\nexport const prepTree = function (data, collectionId) {\r\n  recursTree(data, collectionId);\r\n  return data;\r\n};\r\n\r\nconst openParents = function (node) {\r\n  if (node.parent) {\r\n    node.parent.open = true;\r\n    openParents(node.parent);\r\n  }\r\n};\r\n\r\nconst recursTree = function (node, id, parent) {\r\n  if (parent) {\r\n    node.parent = parent;\r\n  }\r\n  if (node.id == id) {\r\n    node.selected = true;\r\n    openParents(node);\r\n  }\r\n  if (node.children) {\r\n    for (var i = 0; i < node.children.length; i++) {\r\n      recursTree(node.children[i], id, node);\r\n    }\r\n  }\r\n};\r\n\r\nfunction isNumber(n) {\r\n  return /^-?[\\d.]+(?:e-?\\d+)?$/.test(n);\r\n}\r\nconst isBoolean = (val) => \"boolean\" === typeof val;\r\n\r\nexport const columnSort = function (property) {\r\n  var sortOrder = 1;\r\n  if (property[0] === \"-\") {\r\n    sortOrder = -1;\r\n    property = property.substr(1);\r\n  }\r\n  return function (a, b) {\r\n    /* next line works with strings and numbers,\r\n     * and you may want to customize it to your needs\r\n     */\r\n    if (isNumber(a[property])) {\r\n      var aP = parseFloat(a[property]);\r\n      var bP = parseFloat(b[property]);\r\n    } else {\r\n      if (moment(a[property], moment.ISO_8601, true).isValid()) {\r\n        var aP = moment(a[property], moment.ISO_8601, true);\r\n        var bP = moment(b[property], moment.ISO_8601, true);\r\n      } else {\r\n        if (isBoolean(a[property])) {\r\n          var aP = a[property];\r\n          var bP = b[property];\r\n        } else {\r\n          var aP = a[property] ? a[property].toLowerCase() : \"\";\r\n          var bP = b[property] ? b[property].toLowerCase() : \"\";\r\n        }\r\n      }\r\n    }\r\n    var result = aP < bP ? -1 : aP > bP ? 1 : 0;\r\n    return result * sortOrder;\r\n  };\r\n};\r\n\r\nexport const changeColumnSort = function (prop, direction, cols) {\r\n  var currIdx = null;\r\n  var newIdx = null;\r\n  for (var i = 0; i < cols.length; i++) {\r\n    if (cols[i].sort != \"none\") {\r\n      currIdx = i;\r\n      break;\r\n    }\r\n  }\r\n\r\n  for (var i = 0; i < cols.length; i++) {\r\n    if (cols[i].fieldname == prop) {\r\n      newIdx = i;\r\n      break;\r\n    }\r\n  }\r\n  cols[currIdx].sort = \"none\";\r\n  cols[newIdx].sort = direction;\r\n  return cols;\r\n};\r\n\r\nexport const getSortColPropertyName = function (cols, defaultProperty) {\r\n  var retval = defaultProperty;\r\n  for (var i = 0; i < cols.length; i++) {\r\n    if (cols[i].sort != \"none\") {\r\n      if (cols[i].sort == \"asc\") {\r\n        retval = cols[i].fieldname;\r\n      } else {\r\n        retval = \"-\" + cols[i].fieldname;\r\n      }\r\n    }\r\n  }\r\n  return retval;\r\n};\r\n\r\nexport const collectionId2FolderName = function (\r\n  folder,\r\n  collectionId,\r\n  path = \"\"\r\n) {\r\n  if (folder.id == collectionId) {\r\n    let path = folder;\r\n    return path;\r\n  } else if (folder.children != null) {\r\n    let result = null;\r\n    for (let i = 0; result == null && i < folder.children.length; i++) {\r\n      result = collectionId2FolderName(folder.children[i], collectionId);\r\n    }\r\n    return result;\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const getAllOpenFiles = function (state) {\r\n  var fileList = [];\r\n  for (var i = 0; i < state.editors.length; i++) {\r\n    var resLblId = state.editors[i].file.resLblId;\r\n    fileList.push(resLblId);\r\n  }\r\n  for (var i = 0; i < state.navigators.length; i++) {\r\n    if (state.navigators[i].file != \"\") {\r\n      fileList.push(state.navigators[i].file.resLblId);\r\n    }\r\n  }\r\n  if (fileList.length > 0) {\r\n    return fileList;\r\n  } else {\r\n    return null;\r\n  }\r\n};\r\n\r\nexport const getGlobalProperty = function (propName, props) {\r\n  if (props) {\r\n    for (var i = 0; i < props.length; i++) {\r\n      if (props[i].name == propName) {\r\n        if (props[i].value == \"false\") {\r\n          return false;\r\n        }\r\n        if (props[i].value == \"true\") {\r\n          return true;\r\n        }\r\n        return props[i].value;\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport const sortJson = function (jsonArray, prop, asc) {\r\n  jsonArray.sort(function (a, b) {\r\n    if (asc) {\r\n      return a[prop].toLowerCase() > b[prop].toLowerCase()\r\n        ? 1\r\n        : a[prop].toLowerCase() < b[prop].toLowerCase()\r\n        ? -1\r\n        : 0;\r\n    } else {\r\n      return b[prop].toLowerCase() > a[prop].toLowerCase()\r\n        ? 1\r\n        : b[prop].toLowerCase() < a[prop].toLowerCase()\r\n        ? -1\r\n        : 0;\r\n    }\r\n  });\r\n  return jsonArray;\r\n};\r\n\r\nexport const getLangObjectFromId = function (langs, langId) {\r\n  for (var i = 0; i < langs.length; i++) {\r\n    if (langs[i].langId == langId) {\r\n      return langs[i];\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const getBranchObjectFromId = function (branches, branchId) {\r\n  for (var i = 0; i < branches.length; i++) {\r\n    if (branches[i].branchId == branchId) {\r\n      return branches[i];\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const getProjectObjectFromId = function (projects, projectId) {\r\n  for (var i = 0; i < projects.length; i++) {\r\n    if ((projects[i].projectId = projectId)) {\r\n      return projects[i];\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const _getFolderObjectFromId = function (root, collId) {\r\n  var stack = [],\r\n    node,\r\n    ii;\r\n  stack.push(root);\r\n\r\n  while (stack.length > 0) {\r\n    node = stack.pop();\r\n    if (node.id == collId) {\r\n      // Found it!\r\n      return node;\r\n    } else if (node.children && node.children.length) {\r\n      for (ii = 0; ii < node.children.length; ii += 1) {\r\n        stack.push(node.children[ii]);\r\n      }\r\n    }\r\n  }\r\n  // Didn't find it. Return null.\r\n  return null;\r\n};\r\n\r\nexport const getFolderObjById = function (root, id) {\r\n  if (isNaN(id)) return null;\r\n\r\n  // deep clone\r\n  const copy = structuredClone(root);\r\n  const stack = copy.children;\r\n\r\n  while (stack.length > 0) {\r\n    const node = stack.pop();\r\n    if (node.id == id) {\r\n      return node;\r\n    } else {\r\n      if (node.children) {\r\n        node.children.forEach((x) => stack.push(x));\r\n      }\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const getFolderIdByPath = function (root, path) {\r\n  if (!root || !path) return -1;\r\n  // deep clone\r\n  const copy = structuredClone(root);\r\n  const stack = copy.children;\r\n  let currPath = \"/\";\r\n\r\n  while (stack.length > 0) {\r\n    const node = stack.pop();\r\n    node.path = currPath + node.name;\r\n    if (path.startsWith(node.path)) {\r\n      currPath = node.path;\r\n      if (currPath == path) {\r\n        return node.id;\r\n      } else {\r\n        if (node.children) {\r\n          node.children.forEach((x) => stack.push(x));\r\n        }\r\n        currPath += \"/\";\r\n      }\r\n    }\r\n  }\r\n  return -1;\r\n};\r\n\r\nexport const getFolderPath = function (file) {\r\n  let temp = file.resPathId.split(\"/\");\r\n  temp.pop();\r\n  const res = temp.join(\"/\");\r\n  return res;\r\n};\r\n\r\nexport const getLanguages = async () => {\r\n  try {\r\n    const response = await fetch(\"/lwa/jrest/GetConfiguredLangs\");\r\n    const languages = await response.json();\r\n    if (!response.ok) {\r\n      httpError(response, Store);\r\n      throw new Error(response.statusText); //to bail out of promise chain\r\n    }\r\n    return languages;\r\n  } catch (e) {\r\n    console.log(e);\r\n  } finally {\r\n    PubSub.publish(\"requestin\", null);\r\n  }\r\n};\r\n\r\nexport const myIntersection = (set1, set2) => {\r\n  return [...set1].filter((value) => set2.has(value));\r\n};\r\n\r\nconst getTimezone = () => {\r\n  const date = new Date();\r\n  const timeZoneShort = new Intl.DateTimeFormat(Store.state.langCode, {\r\n    timeZoneName: \"short\",\r\n  })\r\n    .formatToParts(date)\r\n    .find((part) => part.type === \"timeZoneName\").value;\r\n  return timeZoneShort;\r\n};\r\n\r\nexport const getLocalTime = (utcString) => {\r\n  let time = \"N/A\";\r\n  if (utcString) {\r\n    let utc = new Date(utcString);\r\n    time = `${utc.toLocaleString()} ${getTimezone()}`;\r\n  }\r\n  return time;\r\n};\r\n\r\n/** Determines what languages are availble to the wex application\r\n * It is the intersection of Xdocs languages and the available language tables in wex\r\n * */\r\nexport const getAvailableLanguages = async () => {\r\n  const languages = await getLanguages();\r\n  const langcodes = languages.map((lang) => lang.langCode);\r\n  const langcodesSet = new Set(langcodes);\r\n  const wexStoreKeySet = new Set(Object.keys(Store.state));\r\n  const avaliableLanguagesSet = myIntersection(langcodesSet, wexStoreKeySet);\r\n  return [...avaliableLanguagesSet];\r\n};\r\n"]}