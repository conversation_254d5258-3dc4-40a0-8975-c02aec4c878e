import { LitElement, html, css } from "lit";
import { property, state, customElement } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
// import * as wexlib from "lib/wexlib.js";

import "../base/col-item";
import "../base/row-item";
import "../base/fixed-item";
import "../base/icon";
import "../base/multi-combobox";

@customElement("wex-publish-definitions-filter-bar")
export class WexPublishDefinitionsFilterBar extends LitElement {
  @property({ type: Array }) pubDefs: any[] | null = null;
  @property({ type: Array }) projects: any[] | null = null;
  @property({ type: Array }) categories: any[] | null = null;

  @state() string: any | null = null;

  private filterOptions: any[] | null = null;
  // private cachedFilterOptions: any[] | null = null;
  private activeFilters: any = {};
  private subscription: any | null = null;

  static get styles() {
    return css``;
  }

  connectedCallback() {
    super.connectedCallback();
    const state = storeInstance.state;
    // this._state = storeInstance.state;
    this.string = state[state.langCode];
    this.subscription = storeInstance.subscribe((state: any) => {
      this.stateChange(state);
    });

    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this.subscription);
  }

  stateChange(state: any) {
    // this._state = state;
    this.string = state[state.langCode];
  }

  async _init() {
    try {
      this.projects = await storeInstance.waitFor(
        "_state.pages.publish.projects"
      );
      this.categories = await storeInstance.waitFor(
        "_state.pages.publish.categories"
      );
      this._generateFilterOptions();
      // console.log("this.categories", this.categories);
      // console.log("this.projects", this.projects);
    } catch (error) {
      console.error("Error during initialization:", error);
    }
  }

  _generateFilterOptions() {
    const filterOptions = {
      category: {
        placeholder: "Category",
        mcbItems: this.categories?.map((category) => category.name),
      },
      projectName: {
        placeholder: "Project Name",
        // mcbItems: ["rgewrger", "ergwergre"],
        mcbItems: this.projects?.map((project) => project.name),
      },
    };
    this.filterOptions = filterOptions;
  }

  _createPubDef() {
    storeInstance.dispatch("setState", {
      property: "publish_defs_create_dialog_open",
      value: {
        mode: "Create",
        projects: this.projects,
      },
    });
  }

  _handleFilterChange(e: CustomEvent) {
    const { selectedItems, comboBoxGroup } = e.detail;

    let activeFilters = {};

    // this works for separate comboboxes
    if (comboBoxGroup) {
      activeFilters = { ...this.activeFilters };
      activeFilters[comboBoxGroup] = selectedItems.map((item) => item);
    } else {
      // console.log("NOOOOO COMBOBOX");
      // // this works for grouped comboboxes
      // // activeFilters = {};
      // activeFilters = selectedItems.reduce((acc, item) => {
      //   const group = item.dataset.group;
      //   const text = item.text;
      //   if (!acc[group]) acc[group] = [];
      //   acc[group].push(text);
      //   return acc;
      // }, {});
    }

    this.activeFilters = activeFilters;
    console.log("CHECK THIS", activeFilters);
    this.dispatchEvent(
      new CustomEvent("set-active-filters", {
        detail: { activeFilters },
        bubbles: true,
        composed: true,
      })
    );
  }

  _handleCategorySelect = (e) => {
    console.log("NYI: category select needs setup and testing", e);
  };

  render() {
    return html`
      <wex-row-item height="42px">
        <wex-fixed-item>
          <wex-icon
            icon="add"
            title="${this.string["_create_pubdef"]}"
            @click="${this._createPubDef.bind(this)}"
          ></wex-icon>
        </wex-fixed-item>
        <wex-col-item>
          <wex-multi-combobox
            .mcbData="${this.filterOptions}"
            @selection-change="${this._handleCategorySelect}"
            .dataGroup=${"category"}
            .initialSelectedItems=${this.activeFilters}
          >
          </wex-multi-combobox>
        </wex-col-item>
        <wex-col-item>
          <wex-multi-combobox
            .mcbData="${this.filterOptions}"
            @selection-change="${this._handleFilterChange}"
            dataGroup="projectName"
            .initialSelectedItems=${this.activeFilters}
          >
          </wex-multi-combobox>
        </wex-col-item>
      </wex-row-item>
    `;
  }
}
