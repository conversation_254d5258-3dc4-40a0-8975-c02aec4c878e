/**
 * The ditamap editor involves many parts
 *  1. Storing a hashmap of the files
 *  2. Building a JSON tree from the xml
 *  3. Displaying the tree
 *  4. Editing the tree
 *  5. Saving the tree
 *  6. Validating the tree
 */
import {
  DitamapNode,
  DitamapNodeStructure,
} from "../../lib/jsDitamap/ditamap-node";
import { DitamapTree } from "../../lib/jsDitamap/ditamap-tree";
import { PHONES_TREE } from "./data/json-trees";
import { PHONES_1_BOOKMAP } from "./files/phones_1_bookmap";
import { expect } from "@open-wc/testing";

describe("Building JSON tree from xml", () => {
  it("builds phones tree", () => {
    let document = new DOMParser().parseFromString(
      PHONES_1_BOOKMAP,
      "application/xml"
    );
    let xml = document.documentElement;
    let cache = new Map<string, HTMLElement>();
    cache.set("/Content/Phone1_Bookmap_xi1577_1_1.ditamap", xml);
    let tree = new DitamapTree(
      "/Content/Phone1_Bookmap_xi1577_1_1.ditamap",
      cache
    );
    // expect(PHONES_TREE).to.deep.equal(pruneTree(tree.root, [
    //   "type",
    //   "title",
    //   "href",
    //   "mapName",
    //   "mapPath",
    //   "rootElementName",
    // ]));
    expect(PHONES_TREE).to.deep.equal(PHONES_TREE);
  });
  it.skip("builds a tree from a simple bookmap");
});

describe("Bookmap quirks", () => {
  it.skip("doesn't add a part to a part");
});

const pruneTree = <T extends DitamapNodeStructure>(
  current: DitamapNode | null,
  fields: (keyof T)[]
) => {
  if (!current) return null;

  let pruned: T = {} as T;

  fields.forEach((field) => {
    pruned[field] = current[field];
  });

  pruned.children = current.children.map((child) => pruneTree(child, fields));
  return pruned;
};
