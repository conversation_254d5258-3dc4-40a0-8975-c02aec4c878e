/**
 * The ditamap editor involves many parts
 *  1. Storing a hashmap of the files
 *  2. Building a JSON tree from the xml
 *  3. Displaying the tree
 *  4. Editing the tree
 *  5. Saving the tree
 *  6. Validating the tree
 */


describe("Building JSON tree from xml", () => {
    it("builds a tree from a simple ditamap", () => {

    })
    it.skip("builds a tree from a simple bookmap")
})

describe("Bookmap quirks", () => {
    it.skip("doesn't add a part to a part")
});