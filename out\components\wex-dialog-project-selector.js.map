{"version": 3, "file": "wex-dialog-project-selector.js", "sourceRoot": "", "sources": ["../../src/components/wex-dialog-project-selector.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAgD,MAAM,KAAK,CAAC;AAC/E,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAC3C,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAE/C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,iCAAiC,CAAC;AACzC,wDAAwD;AACxD,sDAAsD;AACtD,2DAA2D;AAE3D,MAAM,OAAO,wBAAyB,SAAQ,UAAU;IACtD;QACE,KAAK,EAAE,CAAC;IACV,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YACpD,4BAA4B;YAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;IACxE,CAAC;IAED,oBAAoB;QAClB,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,WAAW,CAAC,KAAK;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACf,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAC5B,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;gBAC/B,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;gBAClB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACpB,SAAS,EAAE,CAAC,CAAC;YACb,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;SAC9C,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACvE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;oBACpB,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;iBACjC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAEhE,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;QAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC;gBAClD,qBAAqB;gBACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACjD,IACE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;wBAC7B,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,SAAS,EACvC,CAAC;wBACD,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC;gBACD,iBAAiB;gBACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAC1C,UAAU,EAAE;oBACV,OAAO,EAAE,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC;gBACjE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,QAAQ;QACN,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC;IAED,2BAA2B,CAAC,GAAG;QAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,SAAS,IAAI,GAAG,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,6BAA6B;QAC3B,OAAO,IAAI,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAoDQ,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;;;uBAG7B,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;;;;;uBAK/B,IAAI,CAAC,sBAAsB;;cAEpC,IAAI,CAAC,WAAW,CAAC,GAAG,CACpB,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAA;2BACO,IAAI;uBACR,OAAO,IAAI,CAAC,SAAS,EAAE;8BAChB,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC;qBACzD,IAAI,CAAC,WAAW;kBACnB,CACL;;;uBAGU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;;;;;;2BAMnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;;cAE3C,IAAI,CAAC,UAAU,EAAE;cACjB,IAAI,CAAC,YAAY,CAAC,GAAG,CACrB,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAA;2BACO,IAAI;uBACR,WAAW,IAAI,CAAC,SAAS,EAAE;+BACnB,IAAI,IAAI,IAAI,CAAC,eAAe;2BAChC,IAAI,CAAC,SAAS;iCACR,IAAI,CAAC,UAAU;YAC9B,IAAI;YACJ,IAAI,CAAC,YAAY;YACjB,KAAK;YACL,IAAI,CAAC,WAAW;qBACb,IAAI,CAAC,IAAI;kBACZ,CACL;;;;;;;;;yBASY,CAAC,IAAI,CAAC,eAAe;sBACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;eACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;;;;;sBAKX,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;eAClC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;;;;KAIhC,CAAC;IACJ,CAAC;IACD,UAAU;QACR,OAAO,IAAI,CAAA;qBACM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;mBAC1B,IAAI,CAAC,eAAe,IAAI,KAAK;SACvC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;MACtB,CAAC;IACL,CAAC;IACD,sBAAsB,CAAC,CAAC;QACtB,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC5B,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;gBACjC,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,oBAAoB;oBAC9B,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI;iBACpC,CAAC,CAAC;gBACH,8EAA8E;gBAC9E,OAAO;YACT,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAClC,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,oBAAoB;gBAC9B,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI;aACpC,CAAC,CAAC;QACL,CAAC;QACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,gBAAgB;YAC1B,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI;SACpC,CAAC,CAAC;IACL,CAAC;IAED,cAAc,CAAC,CAAC;QACd,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QAC5C,CAAC;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,YAAY,CAAC,CAAC;QACZ,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9D,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CACrC,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAC5D,CAAC;YACF,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;YAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,aAAa,CAAC,QAAQ,CAAC,kBAAkB,EAAE;gBACzC,GAAG,EAAE,MAAM;gBACX,OAAO,EAAE,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe;aACrE,CAAC,CAAC;QACL,CAAC;QACD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,aAAa;YACvB,KAAK,EAAE,IAAI,CAAC,eAAe;SAC5B,CAAC,CAAC;IACL,CAAC;IACD,WAAW;QACT,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,qBAAqB;YAC/B,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;CACF;AAED,kFAAkF", "sourcesContent": ["import { LitElement, TemplateResult, property, customElement, css } from \"lit\";\r\nimport { html, render } from \"lit/html.js\";\r\nimport { storeInstance } from \"store/index.js\";\r\n\r\nimport \"@ui5/webcomponents/dist/Select.js\";\r\nimport \"@ui5/webcomponents/dist/Option.js\";\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\nimport \"@ui5/webcomponents/dist/Dialog.js\";\r\nimport \"@ui5/webcomponents/dist/Label.js\";\r\nimport \"@ui5/webcomponents/dist/Input.js\";\r\nimport \"@ui5/webcomponents/dist/List.js\";\r\n// import \"@ui5/webcomponents/dist/StandardListItem.js\";\r\n// import \"@ui5/webcomponents/dist/CustomListItem.js\";\r\n// import \"@ui5/webcomponents/dist/GroupHeaderListItem.js\";\r\n\r\nexport class WexDialogProjectSelector extends LitElement {\r\n  constructor() {\r\n    super();\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this.subscription = storeInstance.subscribe((state) => {\r\n      /*WexDialogProjectSelector*/\r\n      this.stateChange(state);\r\n    });\r\n    this.state = storeInstance.state;\r\n    this.string = this.state[this.state.langCode];\r\n    this.projectCats = [];\r\n    this.projectsList = [];\r\n    this.open = false;\r\n    this.myRender();\r\n    this.projectDialog = this.shadowRoot.querySelector(\"#project-dialog\");\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  stateChange(state) {\r\n    this.state = state;\r\n    this.string = this.state[this.state.langCode];\r\n    if (this.state.project_dialog_open) {\r\n      if (!this.open) {\r\n        storeInstance.dispatch(\"getProjects\");\r\n        this.open = true;\r\n        this.projectDialog.show();\r\n      }\r\n      if (this.state.global_projects) {\r\n        this.prepData();\r\n        this.myRender();\r\n      }\r\n    } else {\r\n      if (this.open) {\r\n        this.open = false;\r\n        this.projectDialog.close();\r\n      }\r\n    }\r\n  }\r\n\r\n  prepData() {\r\n    this.projectCats = [];\r\n    this.projectCats.push({\r\n      projCatId: -1,\r\n      projCatName: this.string[\"_all_project_cats\"],\r\n    });\r\n    this.state.global_projects.map((project) => {\r\n      if (!this.projectCats.some((el) => el.projCatId === project.projCatId)) {\r\n        this.projectCats.push({\r\n          projCatId: project.projCatId,\r\n          projCatName: project.projCatName,\r\n        });\r\n      }\r\n    });\r\n\r\n    this.projectsList = structuredClone(this.state.global_projects);\r\n\r\n    this.selectedProjectCatIdx = 0;\r\n    if (this.state.selectedProjectCat) {\r\n      if (this.state.selectedProjectCat.projCatId != -1) {\r\n        //preselect drop down\r\n        for (var i = 0; i < this.projectCats.length; i++) {\r\n          if (\r\n            this.projectCats[i].projCatId ==\r\n            this.state.selectedProjectCat.projCatId\r\n          ) {\r\n            this.selectedProjectCatIdx = i;\r\n          }\r\n        }\r\n        //filter projects\r\n        this.projectsList = this.projectsList.filter(\r\n          function (el) {\r\n            return el.projCatId == this.state.selectedProjectCat.projCatId;\r\n          }.bind(this)\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  myRender() {\r\n    render(this.projectSelectorDialogTemplate(), this.shadowRoot);\r\n  }\r\n\r\n  _projectCatSelectorSelected(val) {\r\n    if (this.state.selectedProjectCat) {\r\n      return this.state.selectedProjectCat.projCatId == val;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  projectSelectorDialogTemplate() {\r\n    return html`\r\n      <style>\r\n        #project-dialog{\r\n            width: 40%;\r\n        }\r\n        .project-form{\r\n            flex-direction: column;\r\n            justify-content: space-evenly;\r\n            align-items: flex-start;\r\n            padding:30px 15% 30px 15%;\r\n        }\r\n        .project-form > div {\r\n            display: grid;\r\n\r\n        }\r\n        [inner-input] {\r\n            color: inherit;\r\n            font-style: normal;\r\n            -webkit-appearance: none;\r\n            line-height: normal;\r\n            box-sizing: border-box;\r\n            min-width: 3rem;\r\n            text-overflow: ellipsis;\r\n            font-size: inherit;\r\n            font-family: inherit;\r\n            background: transparent;\r\n            border-width: initial;\r\n            border-style: none;\r\n            border-color: initial;\r\n            border-image: initial;\r\n            flex: 1 1 0%;\r\n            outline: none;\r\n        }\r\n        .dialog-footer{\r\n            display: flex;\r\n            align-items: center;\r\n            padding: .5rem;\r\n            }\r\n        }\r\n        .hide{display:none!important;}\r\n        #message{\r\n            margin:5px;width:90%;\r\n        }\r\n        ui5-label{text-align:left;}\r\n        #projectcatselector{width:100%;margin-bottom:30px;}\r\n        ui5-button{\r\n            margin:1rem;\r\n            }\r\n      </style>\r\n\r\n      <ui5-dialog\r\n        id=\"project-dialog\"\r\n        header-text=\"${this.string[\"_selectproject\"]}\"\r\n      >\r\n        <section class=\"project-form\">\r\n          <ui5-label>${this.string[\"_projectcatlable\"]}</ui5-label>\r\n          <ui5-select\r\n            id=\"projectcatselector\"\r\n            value-state=\"Information\"\r\n            class=\"headeritem ui5-content-density-compact\"\r\n            @change=\"${this._setProjectCatSelected}\"\r\n          >\r\n            ${this.projectCats.map(\r\n              (item) =>\r\n                html`<ui5-option\r\n                  .item=\"${item}\"\r\n                  id=${`cat-${item.projCatId}`}\r\n                  ?selected=${this._projectCatSelectorSelected(item.projCatId)}\r\n                  >${item.projCatName}</ui5-option\r\n                >`\r\n            )}\r\n          </ui5-select>\r\n\r\n          <ui5-label>${this.string[\"_project\"]}</ui5-label>\r\n          <ui5-list\r\n            id=\"projectsselector\"\r\n            class=\"full-width  ui5-content-density-compact\"\r\n            style=\"height: 300px\"\r\n            infinite-scroll\r\n            @item-click=\"${this._selectProject.bind(this)}\"\r\n          >\r\n            ${this._allOption()}\r\n            ${this.projectsList.map(\r\n              (item) =>\r\n                html`<ui5-li\r\n                  .item=\"${item}\"\r\n                  id=${`project-${item.projectId}`}\r\n                  ?selected=\"${item == this.selectedProject}\"\r\n                  value=\"${item.projCatId}\"\r\n                  description=\"${item.branchName +\r\n                  \": \" +\r\n                  item.languageCode +\r\n                  \" : \" +\r\n                  item.description}\"\r\n                  >${item.name}</ui5-li\r\n                >`\r\n            )}\r\n          </ui5-list>\r\n        </section>\r\n\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <div style=\"flex: 1;\"></div>\r\n          <ui5-button\r\n            id=\"selectbtn\"\r\n            design=\"Emphasized\"\r\n            ?disabled=\"${!this.selectedProject}\"\r\n            @click=\"${this._saveProject.bind(this)}\"\r\n            >${this.string[\"_ok\"]}</ui5-button\r\n          >\r\n          <ui5-button\r\n            id=\"closeDialogButton\"\r\n            design=\"Emphasized\"\r\n            @click=\"${this.closeDialog.bind(this)}\"\r\n            >${this.string[\"_cancel\"]}</ui5-button\r\n          >\r\n        </div>\r\n      </ui5-dialog>\r\n    `;\r\n  }\r\n  _allOption() {\r\n    return html`<ui5-li\r\n      description=\"${this.string[\"_projects\"]}\"\r\n      ?selected=\"${this.selectedProject == \"all\"}\"\r\n      >${this.string[\"_all\"]}</ui5-li\r\n    >`;\r\n  }\r\n  _setProjectCatSelected(e) {\r\n    if (e.detail.selectedOption) {\r\n      if (e.detail.selectedOption.item) {\r\n        storeInstance.dispatch(\"setState\", {\r\n          property: \"selectedProjectCat\",\r\n          value: e.detail.selectedOption.item,\r\n        });\r\n        //storeInstance.dispatch(\"setProjectCatFilter\", e.detail.selectedOption.item);\r\n        return;\r\n      }\r\n    }\r\n    if (this.state.selectedProjectCat) {\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"selectedProjectCat\",\r\n        value: e.detail.selectedOption.item,\r\n      });\r\n    }\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"mru_projectcat\",\r\n      value: e.detail.selectedOption.item,\r\n    });\r\n  }\r\n\r\n  _selectProject(e) {\r\n    if (!e.detail.item.item) {\r\n      this.selectedProject = \"all\";\r\n    } else {\r\n      this.selectedProject = e.detail.item.item;\r\n    }\r\n    this.myRender();\r\n  }\r\n\r\n  _saveProject(e) {\r\n    if (this.state.project_dialog_open.hasOwnProperty(\"callback\")) {\r\n      this.state.project_dialog_open.callback(\r\n        this.selectedProject == \"all\" ? null : this.selectedProject\r\n      );\r\n      this.closeDialog();\r\n    } else {\r\n      var tmpFor = this.state.project_dialog_open;\r\n      this.closeDialog();\r\n      storeInstance.dispatch(\"setProjectFilter\", {\r\n        for: tmpFor,\r\n        project: this.selectedProject == \"all\" ? null : this.selectedProject,\r\n      });\r\n    }\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"mru_project\",\r\n      value: this.selectedProject,\r\n    });\r\n  }\r\n  closeDialog() {\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"project_dialog_open\",\r\n      value: null,\r\n    });\r\n  }\r\n}\r\n\r\n// customElements.define(\"wex-dialog-project-selector\", WexDialogProjectSelector);\r\n"]}