{"alias": {"store": "./src/store", "resources": "./src/resources", "lib": "./src/lib", "@ui5/webcomponents-base/jsx-runtime": "@ui5/webcomponents-base/dist/jsx-runtime.js"}, "type": "module", "dependencies": {"@bds/types": "file:../../../component/bdsWebComp/bds-types", "@lit/context": "^1.1.6", "@material/tab-bar": "^8.0.0", "@polymer/iron-icons": "^3.0.1", "@polymer/iron-pages": "^3.0.1", "@polymer/iron-resizable-behavior": "^3.0.1", "@polymer/paper-badge": "^3.1.0", "@polymer/paper-dropdown-menu": "^3.1.0", "@polymer/paper-item": "^3.0.1", "@polymer/paper-listbox": "^3.0.1", "@polymer/paper-toast": "^3.0.1", "@polymer/prism-element": "^3.0.1", "@ui5/webcomponents": "^2.10.0", "@ui5/webcomponents-base": "^2.10.0", "@ui5/webcomponents-compat": "^2.10.0", "@ui5/webcomponents-fiori": "^2.10.0", "@ui5/webcomponents-icons": "^2.10.0", "@vaadin/router": "^1.7.1", "@vaadin/vaadin-button": "^2.4.0", "@vaadin/vaadin-context-menu": "^4.5.0", "@vaadin/vaadin-date-picker": "^4.4.1", "@vaadin/vaadin-details": "^1.2.0", "@vaadin/vaadin-icons": "^4.3.1", "@vaadin/vaadin-list-box": "^1.4.0", "@vaadin/vaadin-menu-bar": "^1.2.0", "@vaadin/vaadin-select": "^2.4.0", "@vaadin/vaadin-split-layout": "^4.3.0", "@vaadin/vaadin-tabs": "^3.2.0", "@web/dev-server-import-maps": "^0.2.1", "bds-tree": "file:../../../component/bdsWebComp/tree-v2", "beedle": "^0.8.1", "elix": "12.1.2", "heavy-navbar": "0.0.16", "jelement-menu": "^1.0.0", "lit": "^3.3.0", "lodash.get": "^4.4.2", "moment": "^2.29.1", "nvm": "0.0.4", "parcel": "^2.15.2", "prettier": "^3.5.3", "pubsub-js": "^1.8.0", "scroll-tabs": "^1.0.1", "typescript": "^5.8.3", "vaadin-progress-bar": "^1.0.0-alpha2"}, "devDependencies": {"@esm-bundle/chai": "^4.3.4", "@open-wc/testing": "^4.0.0", "@rollup/plugin-alias": "^5.1.1", "@types/mocha": "^10.0.10", "@types/sinon": "^17.0.4", "@web/dev-server-esbuild": "^1.0.4", "@web/dev-server-legacy": "^2.1.1", "@web/test-runner": "^0.20.2", "@web/test-runner-playwright": "^0.11.0", "cssnano": "^4.1.10", "i": "^0.3.7", "npm": "^10.9.2", "playwright": "^1.52.0", "sinon": "^20.0.0", "snowpack": "^2.0.0-beta.16"}, "scripts": {"dev": "parcel src/index.html --public-url /wex/ ", "build": "parcel build src/index.html  --public-url /wex/ ", "build:test": "tsc", "build:test:watch": "tsc --watch", "test": "wtr", "test:watch": "wtr --watch"}}