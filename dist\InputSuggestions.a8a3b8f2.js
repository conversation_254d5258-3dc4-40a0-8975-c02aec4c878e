function e(e,t,s,i){Object.defineProperty(e,t,{get:s,set:i,enumerable:!0,configurable:!0})}var t=globalThis.parcelRequire94c2,s=t.register;s("epwvW",function(s,i){e(s.exports,"default",()=>c);var n=t("3e6om");t("lbnaY"),t("kOmhP");var o=t("5Fd8G"),l=t("65idz"),a=t("TUBXI");class h{get template(){return o.default}constructor(e,t,s,i){this.component=e,this.slotName=t,this.handleFocus=i,this.highlight=s,this.selectedItemIndex=-1}onUp(e,t){e.preventDefault();let s=!this.isOpened&&this._hasValueState&&-1===t?0:t;return this._handleItemNavigation(!1,s),!0}onDown(e,t){e.preventDefault();let s=!this.isOpened&&this._hasValueState&&-1===t?0:t;return this._handleItemNavigation(!0,s),!0}onSpace(e){return!!this._isItemOnTarget()&&(e.preventDefault(),this.onItemSelected(this._selectedItem,!0),!0)}onEnter(e){return this._isGroupItem?(e.preventDefault(),!1):!!this._isItemOnTarget()&&(this.onItemSelected(this._selectedItem,!0),!0)}onPageUp(e){e.preventDefault();let t=this.selectedItemIndex-10>-1;return this._hasValueState&&!t?this._focusValueState():this._moveItemSelection(this.selectedItemIndex,t?this.selectedItemIndex-=10:this.selectedItemIndex=0),!0}onPageDown(e){e.preventDefault();let t=this._getItems(),s=t.length-1,i=this.selectedItemIndex+10<=s;return this._hasValueState&&!t?this._focusValueState():this._moveItemSelection(this.selectedItemIndex,i?this.selectedItemIndex+=10:this.selectedItemIndex=s),!0}onHome(e){return(e.preventDefault(),this._hasValueState)?this._focusValueState():this._moveItemSelection(this.selectedItemIndex,this.selectedItemIndex=0),!0}onEnd(e){e.preventDefault();let t=this._getItems().length-1;return this._hasValueState&&!t?this._focusValueState():this._moveItemSelection(this.selectedItemIndex,this.selectedItemIndex=t),!0}onTab(){return!!this._isItemOnTarget()&&(this.onItemSelected(this._selectedItem,!0),!0)}toggle(e,t){(void 0!==e?e:!this.isOpened())?this._getComponent().open=!0:this.close(t.preventFocusRestore)}get _selectedItem(){return this._getNonGroupItems().find(e=>e.selected)}_isScrollable(){let e=this._getScrollContainer();return e.offsetHeight<e.scrollHeight}close(e=!1){let t=this._getItems()&&this._getItems()[this.selectedItemIndex];this._getComponent().open=!1;let s=this._getPicker();s.preventFocusRestore=e,s.open=!1,t&&t.focused&&(t.focused=!1)}updateSelectedItemPosition(e){this.selectedItemIndex=e}onItemSelected(e,t){let s=this._getNonGroupItems();e&&(this.accInfo={isGroup:e.hasAttribute("ui5-suggestion-item-group"),currentPos:s.indexOf(e)+1,listSize:s.length,itemText:e.text||"",additionalText:e.additionalText},this._getComponent().onItemSelected(e,t),this._getComponent().open=!1)}onItemSelect(e){this._getComponent().onItemSelect(e)}onItemPress(e){let t,s="ui5-item-click"===e.type;(!s||e.detail.item.selected)&&(!this._handledPress||s)&&(s&&e.detail.item.selected?(t=e.detail.item,this._handledPress=!0):t=e.detail.selectedItems[0],this.onItemSelected(t,!1))}_onClose(){this._handledPress=!1}_isItemOnTarget(){return this.isOpened()&&null!==this.selectedItemIndex&&-1!==this.selectedItemIndex&&!this._isGroupItem}get _isGroupItem(){let e=this._getItems();return!!e&&!!e[this.selectedItemIndex]&&e[this.selectedItemIndex].hasAttribute("ui5-suggestion-item-group")}isOpened(){return!!this._getPicker()?.open}_handleItemNavigation(e,t){this.selectedItemIndex=t,this._getItems().length&&(e?this._selectNextItem():this._selectPreviousItem())}_selectNextItem(){let e=this._getItems().length,t=this.selectedItemIndex;if(this._hasValueState&&-1===t&&!this.component._isValueStateFocused)return void this._focusValueState();(-1===t&&!this._hasValueState||this.component._isValueStateFocused)&&(this._clearValueStateFocus(),this.selectedItemIndex=-1),-1!==t&&t+1>e-1||this._moveItemSelection(t,++this.selectedItemIndex)}_selectPreviousItem(){let e=this._getItems(),t=this.selectedItemIndex;if(this._hasValueState&&0===t&&!this.component._isValueStateFocused){this.component.hasSuggestionItemSelected=!1,this.component._isValueStateFocused=!0,this.selectedItemIndex=0,e[0].focused=!1,e[0].hasAttribute("ui5-suggestion-item")&&(e[0].selected=!1);return}if(this.component._isValueStateFocused){this.component.focused=!0,this.component._isValueStateFocused=!1,this.selectedItemIndex=0;return}if(-1!==t&&null!==t){if(t-1<0){(e[t].hasAttribute("ui5-suggestion-item")||e[t].hasAttribute("ui5-suggestion-item-custom"))&&(e[t].selected=!1),e[t].focused=!1,this.component.focused=!0,this.component.hasSuggestionItemSelected=!1,this.selectedItemIndex-=1;return}this._moveItemSelection(t,--this.selectedItemIndex)}}_moveItemSelection(e,t){let s=this._getItems(),i=s[t],n=s[e],o=this._getNonGroupItems(),l=i.hasAttribute("ui5-suggestion-item-group");if(!i)return;this.component.focused=!1,this._clearValueStateFocus();let a=this._getItems()[this.selectedItemIndex];if(this.accInfo={isGroup:l,currentPos:s.indexOf(i)+1,itemText:(l?a.headerText:a.text)||""},(i.hasAttribute("ui5-suggestion-item")||i.hasAttribute("ui5-suggestion-item-custom"))&&(this.accInfo.additionalText=i.additionalText||"",this.accInfo.currentPos=o.indexOf(i)+1,this.accInfo.listSize=o.length),n&&(n.focused=!1),(n?.hasAttribute("ui5-suggestion-item")||n?.hasAttribute("ui5-suggestion-item-custom"))&&(n.selected=!1),i&&(i.focused=!0,l||(i.selected=!0),this.handleFocus&&i.focus()),this.component.hasSuggestionItemSelected=!0,this.onItemSelect(i),!this._isItemIntoView(i)){let e=this._isGroupItem?i.shadowRoot.querySelector("[ui5-li-group-header]"):i;this._scrollItemIntoView(e)}}_deselectItems(){this._getItems().forEach(e=>{e.hasAttribute("ui5-suggestion-item")&&(e.selected=!1),e.focused=!1})}_clearItemFocus(){let e=this._getItems().find(e=>e.focused);e&&(e.focused=!1)}_isItemIntoView(e){let t=e.getDomRef().getBoundingClientRect(),s=this._getComponent().getDomRef().getBoundingClientRect(),i=window.innerHeight||document.documentElement.clientHeight,n=0;return this._hasValueState&&(n=this._getPicker().querySelector("[slot=header]").getBoundingClientRect().height),t.top+h.SCROLL_STEP<=i&&t.top>=s.top+n}_scrollItemIntoView(e){e.scrollIntoView({behavior:"auto",block:"nearest",inline:"nearest"})}_getScrollContainer(){return this._scrollContainer||(this._scrollContainer=this._getPicker().shadowRoot.querySelector(".ui5-popup-content")),this._scrollContainer}_getItems(){return this._getComponent().getSlottedNodes("suggestionItems").flatMap(e=>e.hasAttribute("ui5-suggestion-item-group")?[e,...e.items]:[e])}_getNonGroupItems(){return this._getItems().filter(e=>!e.hasAttribute("ui5-suggestion-item-group"))}_getComponent(){return this.component}_getList(){return this._getPicker().querySelector("[ui5-list]")}_getListWidth(){return this._getList()?.offsetWidth}_getPicker(){return this._getComponent().shadowRoot.querySelector("[ui5-responsive-popover]")}get itemSelectionAnnounce(){if(!this.accInfo)return"";if(this.accInfo.isGroup)return`${h.i18nBundle.getText(a.LIST_ITEM_GROUP_HEADER)} ${this.accInfo.itemText}`;let e=h.i18nBundle.getText(a.LIST_ITEM_POSITION,this.accInfo.currentPos||0,this.accInfo.listSize||0);return`${this.accInfo.additionalText} ${e}`}hightlightInput(e,t){return(0,n.default)(e,t)}get _hasValueState(){return this.component.hasValueStateMessage}_focusValueState(){this.component._isValueStateFocused=!0,this.component.focused=!1,this.component.hasSuggestionItemSelected=!1,this.selectedItemIndex=0,this.component.value=this.component.typedInValue,this._deselectItems()}_clearValueStateFocus(){this.component._isValueStateFocused=!1}_clearSelectedSuggestionAndaccInfo(){this.accInfo=void 0,this.selectedItemIndex=0}}h.SCROLL_STEP=60,l.default.SuggestionsClass=h;var c=h}),s("3e6om",function(s,i){e(s.exports,"default",()=>a);var n=t("hNhGB"),o=t("2LNch");function l(e,t,s,i){return e.replaceAll(RegExp((0,n.default)(t),`${i?"i":""}g`),s)}var a=function(e,t){if(!e||!t)return e;let s=s=>{let[i,n]=s.split("");for(;e.indexOf(s)>=0||t.indexOf(s)>=0;)s=`${i}${s}${n}`;return s},i=s("12"),n=s("34"),a=(0,o.default)(l(e,t,e=>`${i}${e}${n}`,!0));return[[i,"<b>"],[n,"</b>"]].forEach(([e,t])=>{a=l(a,e,t,!1)}),a}}),s("hNhGB",function(t,s){e(t.exports,"default",()=>i);var i=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}),s("kOmhP",function(e,s){var i=t("c6CCt"),n=t("aaoB5"),o=t("809gB"),l=t("c9ojM"),a=function(e,t,s,i){var n,o=arguments.length,l=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,s):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)l=Reflect.decorate(e,t,s,i);else for(var a=e.length-1;a>=0;a--)(n=e[a])&&(l=(o<3?n(l):o>3?n(t,s,l):n(t,s))||l);return o>3&&l&&Object.defineProperty(t,s,l),l};let h=class extends o.default{};a([(0,n.default)({default:!0,invalidateOnChildChange:!0,type:HTMLElement})],h.prototype,"items",void 0),(h=a([(0,i.default)({tag:"ui5-suggestion-item-group",template:l.default})],h)).define()}),s("5Fd8G",function(s,i){e(s.exports,"default",()=>d);var n=t("lyEkX"),o=t("65idz"),l=t("6XEKD"),a=t("kmcVJ"),h=t("aDnpv"),c=t("e9Sj6"),r=t("iQEdW"),u=t("adKC4");function d(e){let t=e?.suggestionsList||m,s=e?.valueStateMessage,i=e?.valueStateMessageInputIcon;return(0,n.jsxs)(c.default,{class:this.classes.popover,hideArrow:!0,preventFocusRestore:!0,preventInitialFocus:!0,placement:"Bottom",horizontalAlign:"Start",tabindex:-1,style:this.styles.suggestionsPopover,onOpen:this._afterOpenPicker,onClose:this._afterClosePicker,onScroll:this._scroll,open:this.open,opener:this,accessibleName:this._popupLabel,children:[this._isPhone&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{slot:"header",class:"ui5-responsive-popover-header",children:[(0,n.jsxs)("div",{class:"row",children:[(0,n.jsx)("span",{children:this._headerTitleText}),(0,n.jsx)(r.default,{class:"ui5-responsive-popover-close-btn",icon:a.default,design:"Transparent",onClick:this._closePicker})]}),(0,n.jsx)("div",{class:"row",children:(0,n.jsx)("div",{class:"input-root-phone native-input-wrapper",children:(0,n.jsx)(o.default,{class:"ui5-input-inner-phone",type:this.inputType,value:this.value,showClearIcon:this.showClearIcon,placeholder:this.placeholder,onInput:this._handleInput,onChange:this._handleChange})})})]}),this.hasValueStateMessage&&(0,n.jsxs)("div",{class:this.classes.popoverValueState,style:this.styles.suggestionPopoverHeader,children:[(0,n.jsx)(l.default,{class:"ui5-input-value-state-message-icon",name:i?.call(this)}),this.open&&s?.call(this)]})]}),!this._isPhone&&this.hasValueStateMessage&&(0,n.jsxs)("div",{slot:"header",class:{"ui5-responsive-popover-header":!0,"ui5-responsive-popover-header--focused":this._isValueStateFocused,...this.classes.popoverValueState},style:this.styles.suggestionPopoverHeader,children:[(0,n.jsx)(l.default,{class:"ui5-input-value-state-message-icon",name:i?.call(this)}),this.open&&s?.call(this)]}),t.call(this),this._isPhone&&(0,n.jsx)("div",{slot:"footer",class:"ui5-responsive-popover-footer",children:(0,n.jsx)(r.default,{design:"Transparent",onClick:this._closePicker,children:this._suggestionsOkButtonText})})]})}function m(){return(0,n.jsx)(h.default,{accessibleRole:u.default.ListBox,separators:this.suggestionSeparators,selectionMode:"Single",onMouseDown:this.onItemMouseDown,onItemClick:this._handleSuggestionItemPress,onSelectionChange:this._handleSelectionChange,children:(0,n.jsx)("slot",{})})}});
//# sourceMappingURL=InputSuggestions.a8a3b8f2.js.map
