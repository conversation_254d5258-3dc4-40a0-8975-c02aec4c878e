import { Capability } from "@bds/types";
export declare const log: (msg: any) => void;
export declare const deepClone: (obj: any) => any;
export declare const can: (cap: Capability, action: string) => boolean;
export declare const updatePageLocation: (tab: any, newurl: any, push: any) => void;
export declare const isDitamap: (file: any) => boolean;
/**
 * Creates an insert Action object.
 * These are used to match adding nodes to jsMap tree with inserting to xml.
 * @param {"insertBefore" | "appendChild"} action - How the node is to be inserted
 * @param {HTMLElement} insertRef - The reference node
 */
export declare const createInsertAction: (action: any, insertRef: any) => {
    action: any;
    insertRef: any;
};
export declare const isFileEditable: (file: any) => boolean;
export declare const isFilePreviewable: (file: any) => boolean;
export declare const getEffectiveCap: (localCap: any) => any;
export declare const redirectToSSOLogin: () => void;
export declare const getCapValue: (cap: any) => any;
export declare const buildFileMenu: (file: any, menuItems: any, origin: any, globalCap: any, taskCap: any, isClaimed: any) => any[];
export declare const notifyUser: (type: any, message: any, duration?: number) => void;
export declare const getFileLockStatus: (file: any, user: any) => string;
export declare const calculateLockedStatus: (row: any, user: any) => any;
export declare const transformFilepackage: (data: any, user: any) => any[];
export declare const transformVersionListResults: (data: any, user: any) => any[];
export declare const transformSearchResults: (data: any) => any[];
export declare const getIndex: (data: any, fldName: any) => any;
export declare const prepTree: (data: any, collectionId: any) => any;
export declare const columnSort: (property: any) => (a: any, b: any) => number;
export declare const changeColumnSort: (prop: any, direction: any, cols: any) => any;
export declare const getSortColPropertyName: (cols: any, defaultProperty: any) => any;
export declare const collectionId2FolderName: (folder: any, collectionId: any, path?: string) => any;
export declare const getAllOpenFiles: (state: any) => any[] | null;
export declare const getGlobalProperty: (propName: any, props: any) => any;
export declare const sortJson: (jsonArray: any, prop: any, asc: any) => any;
export declare const getLangObjectFromId: (langs: any, langId: any) => any;
export declare const getBranchObjectFromId: (branches: any, branchId: any) => any;
export declare const getProjectObjectFromId: (projects: any, projectId: any) => any;
export declare const _getFolderObjectFromId: (root: any, collId: any) => any;
export declare const getFolderObjById: (root: any, id: any) => any;
export declare const getFolderIdByPath: (root: any, path: any) => any;
export declare const getFolderPath: (file: any) => any;
export declare const getLanguages: () => Promise<any>;
export declare const myIntersection: (set1: any, set2: any) => any[];
export declare const getLocalTime: (utcString: any) => string;
/** Determines what languages are availble to the wex application
 * It is the intersection of Xdocs languages and the available language tables in wex
 * */
export declare const getAvailableLanguages: () => Promise<any[]>;
//# sourceMappingURL=util.d.ts.map