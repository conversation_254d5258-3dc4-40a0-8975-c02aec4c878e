import { LitElement, html, css, customElement } from "lit-element";
import { storeInstance } from "store/index.js";
import "../../wex-folders.js";
import "../../wex-select-file-files.js";
import "bds-tree";
import { property } from "lit/decorators.js";
import { Collection, FileMeta } from "@bds/types";

@customElement("wex-dialog-content-file-explorer")
class WexDialogContentFileExplorer extends LitElement {
  state: any = {};
  _subscription: any;
  filesEl: Element | null = null;
  @property({ type: Object }) string: Record<string, string> = {};
  @property({ type: Array }) foldersData: Collection[] = [];
  @property({ type: Object }) selectedFolder: Collection | null = null;
  @property({ type: Object }) selectedFile: FileMeta | null = null;
  @property({ type: String }) selectedFilePathId: string = "";
  treeConfig: any;

  constructor() {
    super();
  }
  connectedCallback() {
    super.connectedCallback();
    this.state = storeInstance.state;
    this.string = this.state[this.state.langCode];
    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });
    this.foldersData = this.state.global_folders;
    this.treeConfig = {
      initialNodeId: "2",
      labelPath: "name",
      idPath: "id",
      childPath: "children",
    };
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  stateChange(state) {
    this.state = state;
    this.string = state[state.langCode];
    this.foldersData = state.global_folders;
    this.selectedFolder = state.select_file_dialog_folderitem;
    this._updateSelectedFile(state);
  }

  refreshFilesList() {
    const filesEl = this.shadowRoot!.querySelector("wex-select-file-files");
    if (filesEl) {
      filesEl.refreshData();
    }
  }

  _updateSelectedFile(state) {
    this.selectedFile = state.select_file_selected_file;
    this.selectedFilePathId = this.selectedFile
      ? this.selectedFile.resPathId
      : "";
  }

  _clearSelectedFile() {
    this.selectedFile = null;
    this.selectedFilePathId = "";
  }

  _handleFolderSelected = (e) => {
    console.log("CHECK THIS:", e.detail);

    if (this.selectedFolder?.id != e.detail.node.id) {
      this._clearSelectedFile();
      storeInstance.dispatch("setState", {
        property: "select_file_dialog_folderitem",
        value: e.detail.node,
      });
      this.selectedFolder = e.detail.node;
    }
    this.filesEl.refreshData();
  };

  render() {
    return html`
    <wex-row-item class="container" height="100%">
      <div id="fldr">
        <bds-tree
          .config=${this.treeConfig}
          .root=${this.foldersData}
          @treeClick="${this._handleFolderSelected.bind(this)}"
        ></bds-tree>
      </div>
      <div id="files">
        <wex-select-file-files id="file-list"></wex-select-file-files>
      </div>
    </wex-row-item>`;
  }

  static get styles() {
    return css`
      :host {
        display: flex;
        flex: 1;
        min-height: 0;
      }
      .container {
        display: flex;
        flex-direction: column;
        flex: 1;
        min-height: 0;
        gap: 0;
        flex-shrink: 0;
        margin-bottom: 1rem;
        margin-top: 1rem;
        max-height: 100%;
        border: 1px inset grey;
      }

      #fldr,
      #files {
        min-height: 0;
        overflow: auto;
      }

      #fldr {
        padding: 0;
        flex: 3;
        overflow-y: auto;
        overflow-x: auto;
        height: 100%;
      }

      #files {
        flex: 5;
        overflow-x: auto;
        overflow-y: auto;
        height: 100%;
        border-left: 1px solid grey;
      }
    `;
  }

  // protected createRenderRoot(): Element | ShadowRoot {
  //   return this;
  // }
}
