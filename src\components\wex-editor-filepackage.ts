import { LitElement, html, css } from "lit";
import { render } from "lit/html.js";
import { storeInstance as Store } from "store/index.js";
import { Router } from "@vaadin/router/dist/vaadin-router.js";
import * as util from "lib/util.ts";
import * as wexlib from "lib/wexlib.js";

import "@ui5/webcomponents-compat/dist/Table.js";
import "@ui5/webcomponents-compat/dist/TableRow.js";
import "@ui5/webcomponents-compat/dist/TableCell.js";
import "@ui5/webcomponents-compat/dist/TableColumn.js";
import "@ui5/webcomponents/dist/Icon.js";
import "@vaadin/vaadin-context-menu/vaadin-context-menu.js";
import "@vaadin/vaadin-list-box/vaadin-list-box.js";
import "@vaadin/vaadin-item/vaadin-item.js";
import "@vaadin/vaadin-icons/vaadin-icons.js";
import "@ui5/webcomponents/dist/RadioButton";
import { customElement, property } from "lit/decorators";

@customElement("wex-editor-filepackage")
class WexEditorFilepackage extends LitElement {
  state: any;
  subscription: any;
  @property({ type: Object }) string: Record<string, string> = {};
  @property({ type: Array }) columns;
  @property({ type: Array }) rows;
  @property({ type: Object }) contextMenu;
  @property({ type: Object }) primaryRadioBtn;
  @property({ type: Object }) taskInstId;
  @property({ type: Object }) lang: string = "en-US";

  constructor() {
    super();
  }

  connectedCallback() {
    this.subscription = Store.subscribe((state) => {
      /*WexEditFilepackage*/
      this.stateChange(state);
    });
    this.state = Store.state;
    if (this.state.editor_selected_task) {
      this.refreshData();
    }
  }

  firstUpdated() {
    this._marshellColumns();
    this._marshellRows();
    if (this.shadowRoot) {
      this.contextMenu = this.shadowRoot.querySelector("#contextmenu");
      this.primaryRadioBtn = this.shadowRoot.querySelector("#primary");
    }
  }

  disconnectedCallback() {
    Store.unsubscribe(this.subscription);
  }

  refreshData() {
    Store.dispatch("getEditorFilepackage", this.state.editor_selected_task);
  }

  stateChange(state) {
    this.state = state;
    this.string = this.state[this.state.langCode];

    //re localize condition
    if (this.lang != this.state.langCode) {
      this.lang = this.state.langCode;
    }
    //refresh data condition
    if (this._isNewTask()) {
      this.taskInstId = this.state.editor_selected_task.taskInstId;
      this.refreshData();
    }
  }

  _isNewTask() {
    if (this.state.editor_selected_task) {
      if (this.state.editor_selected_task.taskInstId != this.taskInstId) {
        return true;
      }
    }
  }

  _isClaimed() {
    if (this.state.editor_selected_task) {
      return this.state.editor_selected_task.isClaimed;
    }
  }

  _marshellColumns() {
    //filter for active col headings
    this.columns = this.state.editor_filepackage_columns.filter(
      function (el) {
        return el.order > 0;
      }.bind(this)
    );
    //sort by column order
    this.columns.sort(function (a, b) {
      if (a.order < b.order) {
        return -1;
      }
      if (a.order > b.order) {
        return 1;
      }
      return 0;
    });
    //localize
    this.columns.map((col) => (col.heading = this.string[col.title]));
  }

  _marshellRows() {
    this.rows = [];
    if (this.state.editor_filepackage_file_list) {
      this.rows = JSON.parse(
        JSON.stringify(this.state.editor_filepackage_file_list)
      );
      for (var i = 0; i < this.rows.length; i++) {
        this.rows[i].isSelected = false;
        if (this.state.editor_selected_file) {
          if (
            this.rows[i].resLblId == this.state.editor_selected_file.resLblId
          ) {
            this.rows[i].isSelected = true;
          }
        }
        if (util.isDitamap(this.rows[i])) {
          this.rows[i].mapClass = " ditamap ";
        }
      }
      //apply claimed filter
    }
    this.rows.sort(
      util.columnSort(util.getSortColPropertyName(this.columns, "name"))
    );
  }

  _disableNextPrev() {
    if (this.rows.length > 1 && this.state.editor_selected_file) {
      return false;
    } else {
      return true;
    }
  }

  /* Templates */
  render() {
    return html`
      <style>
        .lockedbyother{color:red;}
        .lockedbyselfhere{color:green;}
        .lockedbyselfelsewhere{color:orange;}

        .rowselected{
            --sapList_Background: var(--row-selected-background);
        }

        #notasks{
            font-style: oblique;
            opacity: 0.5;
            text-align:center;
            margin-top:50px;
        }
        .datarow{cursor:pointer;}
        .ditamap{font-weight:bold;}

        #filepackageheader{
            height: ${this.state.global_screen_specs
          .edit_filepackageHeaderHeight}px !important;
            color:#000;
            text-align:center;
            display:flex;
            flex-direction: row;
            justify-content: space-between;
            flex-wrap: nowrap
        }
        #filepackageheader>div{flex-shrink: 1;}


        #filepackagetablecontainer{
            height:${this.state.global_screen_specs.layoutHeight -
        (this.state.global_screen_specs.edit_filepackageHeaderHeight +
          this.state.global_screen_specs.edit_controlsHeight *
            2)}px; !important;
            overflow: auto;
        }


        #filerole{
            display: inline-block;;
            margin-left:-12px;
            cursor: pointer;
            }


        #filerole{
            display: inline-block;;
            margin-left:-12px;
            cursor: pointer;
            }


        #nextprev{
            line-height:30px;
            padding-top:10px;
            color:#32363A;
            margin-right:-6px;
        }

        #nextprev{
            line-height:30px;
            padding-top:10px;
            color:#32363A;
            margin-right:-6px;
        }

        #nexticon{cursor:pointer;}
        #previcon{cursor:pointer;}
        #nexticon[disabled]{cursor:forbidden;color:#ccc;}
        #previcon[disabled]{cursor:forbidden;color:#ccc;}

        .reviewedicon{
            --iron-icon-width:16px;
        }
        .hide{visibility: hidden;}
        .sortbuttoncontainer{
            display:flex;
            flex-direction:column;
        }
        .sortbuttoncontainer iron-icon{
            margin-left: .25rem;
            --iron-icon-width:.9rem
        }
        iron-icon.asc{margin-bottom:-.6rem}
        iron-icon.desc{margin-top:-.6rem}
        iron-icon.muted{
            color: #ccc;
        }

        .colhead{
            line-height: 1.4rem;
            display:flex;
            flex-direction: row;
            align-items:center;

        }
        .colhead>span:first-child{
            font-size:.8rem;
        }
        .iconcontatiner{
            height:1.4rem;
            overflow:hidden;
            }
        .sidecomponent{
        }
        #sidecomponentparent{
            display: flex;
            justify-content: center;
            background-color:#F2F2F2;
            padding: 0.5rem 0 0;
        }
        ui5-table-cell>div{
            white-space: nowrap;
        }
        .disabled{
            color:#ccc;
            cursor:forbidden;
        }
        *[disabled]{
            color:#ccc;
            cursor:forbidden;
        }
        #nextprev ui5-button{
            height:1.5rem;
            width:2rem;
        }
        ui5-button>iron-icon{
            width:15px;
        }

        .fileicon, .previewicon, .editicon, .reviewedicon{width: 1rem;}
        .fileicon:hover, .previewicon:hover, .editicon:hover, reviewedicon:hover{color:#000;}
      </style>
      <div id="sidecomponentparent">
        <ui5-radio-button
          ?disabled="${!this._isClaimed()}"
          id="primary"
          text="${this.string["_primary"]}"
          @change="${this._fileRoleChange.bind(this)}"
          ?checked="${this.state.editor_filepackage_display_primary}"
          name="GroupA"
          title="${this.string["_displayonlyprimaryfiles"]}"
        ></ui5-radio-button>
        <ui5-radio-button
          ?disabled="${!this._isClaimed()}"
          id="refernce"
          text="${this.string["_reference"]}"
          @change="${this._fileRoleChange.bind(this)}"
          ?checked="${!this.state.editor_filepackage_display_primary}"
          name="GroupA"
          title="${this.string["_displayonlyreferencefiles"]}"
        ></ui5-radio-button>
        <!-- <div id="nextprev">
            <ui5-button
              ?disabled="${!this._isClaimed() || this._disableNextPrev()}"
              id="previcon"
              @click="${this._loadPrev.bind(this)}"
              title="${this.string["_prev"]}"
            >
              <iron-icon icon="vaadin:caret-left"></iron-icon>
            </ui5-button>
            <ui5-button
              ?disabled="${!this._isClaimed() || this._disableNextPrev()}"
              id="nexticon"
              @click="${this._loadNext.bind(this)}"
              title="${this.string["_next"]}"
            >
              <iron-icon icon="vaadin:caret-right"></iron-icon>
            </ui5-button>
          </div> -->
      </div>
      <div id="filepackagetablecontainer">
        <vaadin-context-menu
          ?disabled="${!this._isClaimed()}"
          id="contextmenu"
          selector=".has-menu"
          .items="${this.fileContextNenu}"
          @item-selected="${this._contextmenuclicked.bind(this)}"
        >
          <ui5-table
            id="filepackagetable"
            no-data-text="${this.string[
              this.state.editor_filepackage_display_primary
                ? "_noprimaryfiles"
                : "_noreferencefiles"
            ]}"
            ?show-no-data="${this.rows.length == 0}"
            sticky-column-header
          >
            ${this.columns.map((col) => {
              if (col.fieldname == "name") {
                return html`<ui5-table-column
                  class="foo"
                  slot="columns"
                  popin-text="${col.heading}"
                >
                  <span class="colhead">
                    <span class="${this._isClaimed() ? "" : "disabled"}"
                      >${col.heading}</span
                    >

                    <span class="sortbuttoncontainer">
                      <span
                        class="iconcontatiner"
                        .colproperty="${col.fieldname}"
                        title="asc"
                        @click="${this._sortColumn.bind(this)}"
                        ><iron-icon
                          class="asc ${col.sort != "asc" ? "muted" : ""}"
                          icon="vaadin:caret-up"
                          ?disabled="${!this._isClaimed()}"
                        ></iron-icon
                      ></span>
                      <span
                        class="iconcontatiner"
                        .colproperty="${col.fieldname}"
                        title="desc"
                        @click="${this._sortColumn.bind(this)}"
                        ><iron-icon
                          class="desc ${col.sort != "desc" ? "muted" : ""}"
                          icon="vaadin:caret-down"
                          ?disabled="${!this._isClaimed()}"
                        ></iron-icon
                      ></span>
                    </span>
                  </span>
                </ui5-table-column> `;
              }
            })}
            ${this.rows.map(
              (item) =>
                html` <ui5-table-row
                  ?disabled="${!this._isClaimed()}"
                  class="datarow has-menu ${this._isClaimed()
                    ? ""
                    : "disabled"} ${item.mapClass} ${item.isSelected
                    ? "rowselected"
                    : ""}"
                  .item="${item}"
                  @dblclick="${this._defaultAction.bind(this)}"
                  @click="${this._handleRowClicked.bind(this)}"
                  @contextmenu="${this._handleRowRightClicked.bind(this)}"
                >
                  ${this.columns.map((col) => this._rowTemplate(col, item))}
                </ui5-table-row>`
            )}
          </ui5-table>
        </vaadin-context-menu>
      </div>
    `;
  }

  _defaultAction(e) {
    if (!this._isClaimed()) return;

    Store.dispatch("setState", {
      property: "editor_selected_file",
      value: e.currentTarget.item,
    });
    this._editSelectedFile();
  }

  _sortColumn(e) {
    if (!this._isClaimed()) {
      return;
    }
    var direction = e.currentTarget.title;
    if (e.currentTarget.colproperty) {
      var cols = util.changeColumnSort(
        e.currentTarget.colproperty,
        direction,
        structuredClone(this.state.editor_filepackage_columns)
      );
      Store.dispatch("setState", {
        property: "editor_filepackage_columns",
        value: cols,
      });
    }
  }

  _rowTemplate(col, itm) {
    //<ui5-checkbox ?checked="${itm.reviewd}" .item="${itm}" @clicked="${this._toggleReviewed.bind(this)}"></ui5-checkbox>
    /*
        if(col.fieldname=='reviewed'){
            return html`<ui5-table-cell  align='center'  ?disabled="${!this._isClaimed()}"><div class="reviewedicon">
                
            </div></ui5-table-cell>`;
        } else {
            */
    if (col.fieldname == "name") {
      return html`<ui5-table-cell align="left" ?disabled="${!this._isClaimed()}"
        ><div>
          <iron-icon
            ?disabled="${!this._isClaimed()}"
            @click="${this._toggleReviewed.bind(this)}"
            .item="${itm}"
            class="reviewedicon"
            icon="${itm.reviewed ? "vaadin:circle" : "vaadin:circle-thin"}"
          ></iron-icon>
          <iron-icon
            ?disabled="${!this._isClaimed()}"
            icon="vaadin:file-text-o"
            id="properties"
            class="fileicon ${itm.iconClass}"
            title="${this.string[itm.iconTitle]}"
          ></iron-icon>
          <iron-icon
            .item="${itm}"
            icon="vaadin:eye"
            title="${this.string["_preview"]}"
            class="${itm.selected} previewicon"
            id="preview"
          >
          </iron-icon>
          <iron-icon
            .item="${itm}"
            ?disabled=${!this.state.editor_selected_task.isClaimed}
            icon="vaadin:edit"
            title="${!this.state.editor_selected_task.isClaimed
              ? this.string["_claimtoedit"]
              : this.string["_edit"]}  "
            class="${itm.selected} ${!this.state.editor_selected_task.isClaimed
              ? "disabled"
              : ""} editicon ${util.isFileEditable(itm) ? "" : "hide"}"
            id="edit"
          >
          </iron-icon>
          &nbsp;${itm[col.fieldname]}
        </div>
      </ui5-table-cell>`;
    } /*else {
                return html`<ui5-table-cell align='left'><div >&nbsp;${itm[col.fieldname]}</div></ui5-table-cell>`;
         }       }
         */
  }

  _toggleReviewed(e) {
    if (!this._isClaimed()) {
      return;
    }
    e.preventDefault();
    e.stopPropagation();
    this._toggleReviewedAction(
      this.state.editor_selected_task,
      e.currentTarget.item
    );
  }

  _toggleReviewedAction(selectedTask, selectedFile) {
    var payload = {
      origin: "task",
      processInstId: selectedTask.processInstId,
      TaskDefId: selectedTask.taskId,
      resLblId: selectedFile.resLblId,
      reviewed: !selectedFile.reviewed,
    };
    Store.dispatch("setTaskFileReviewedStatus", payload);
  }

  _loadPrev() {
    if (!this._isClaimed()) {
      return;
    }
    var rowIdxToSelect = null;
    if (!this.state.editor_selected_file) {
      rowIdxToSelect = 0;
    } else {
      for (var i = 0; i < this.rows.length; i++) {
        if (this.rows[i].isSelected) {
          if (i == 0) {
            rowIdxToSelect = this.rows.length - 1;
          } else {
            rowIdxToSelect = i - 1;
          }
          break;
        }
      }
    }
    if (rowIdxToSelect !== null) {
      Store.dispatch("setState", {
        property: "editor_selected_file",
        value: this.rows[rowIdxToSelect],
      });
      this._editSelectedFile();
    }
  }

  _loadNext() {
    if (!this._isClaimed()) {
      return;
    }
    var rowIdxToSelect = null;
    if (!this.state.editor_selected_file) {
      rowIdxToSelect = 0;
    } else {
      for (var i = 0; i < this.rows.length; i++) {
        if (this.rows[i].isSelected) {
          if (i == this.rows.length - 1) {
            rowIdxToSelect = 0;
          } else {
            rowIdxToSelect = i + 1;
          }
          break;
        }
      }
    }
    if (rowIdxToSelect !== null) {
      Store.dispatch("setState", {
        property: "editor_selected_file",
        value: this.rows[rowIdxToSelect],
      });
      this._editSelectedFile();
    }
  }

  _fileRoleChange(e) {
    Store.dispatch("setState", {
      property: "editor_filepackage_display_primary",
      value: this.primaryRadioBtn.selected,
    });
    Store.dispatch("getEditorFilepackage", this.state.editor_selected_task);
  }

  _filePackageAction(e) {
    switch (e.currentTarget.id) {
      case "addfile":
        Store.dispatch("setState", {
          property: "select_file_dialog_open",
          value: {
            payload: this.state.editor_selected_task,
          },
        });
        break;
      case "importfile":
        Store.dispatch("setState", {
          property: "import_dialog_open",
          value: this.state.editor_selected_task,
        });
        break;
      case "createfile":
        Store.dispatch("setState", {
          property: "create_file_dialog_open",
          value: {
            target: "editfilepackage",
            payload: this.state.editor_selected_task,
          },
        });
        break;
    }
  }

  _contextmenuclicked(e) {
    if (!this._isClaimed()) {
      return;
    }
    switch (e.detail.value.name) {
      case "properties":
        Store.dispatch(
          "filePropertiesRequest",
          this.state.editor_selected_file
        );
        break;
      case "preview":
        Router.go(
          "/wex/" +
            this.state.langCode +
            "/preview?resLblId=" +
            this.state.editor_selected_file.resLblId +
            "&aod=" +
            this.state.editor_selected_file.verCreateDate
        );
        break;
      case "edit":
        this._editSelectedFile();
        break;
      case "remove":
        //Store.dispatch("removeFileFromPackage", {ownOnly:true});
        break;
      case "edit_ditamap":
        // Store.dispatch("setState", {
        //   property: "editor_side_active_tab",
        //   value: "ditamaps",
        // });
        // var tmp = structuredClone(this.state.editor_selected_file);
        // tmp.isDitabase = true;
        // Store.dispatch("checkOutDitabase", tmp);
        this._editSelectedFile();
        break;
      case "previewnewwindow":
        wexlib.previewNewWindow({
          resLblId: this.state.editor_selected_file.resLblId,
          aod: "now",
          mimeType: this.state.editor_selected_file.mimeType,
          langCode: this.state.langCode,
        });
        break;
      case "openfilelocation":
        var aTmp = this.state.editor_selected_file.resPathId.split("/");
        aTmp.pop();
        var folderPath = aTmp.join("/");
        this._folderByPath(this.state.global_folders, folderPath, null, this);
        //Store.dispatch("setState", {property: "select_file_highlight_folder" , value: this.state.search_selected_file.folderPath } )
        break;
      case "toggledone":
        this._toggleReviewedAction(
          this.state.editor_selected_task,
          this.itemRightClicked
        ); //The reviewed status can be changed without updateing the item, so must use the captured item from the right click
        break;
      case "open_ditamap":
        Store.dispatch("handleFileAction", {
          action: "open_ditamap",
          file: this.state.editor_selected_file,
        });
        break;
    }
  }

  _editSelectedFile() {
    Store.dispatch("checkEditFile", this.state.editor_selected_file);
  }

  _folderByPath(node, seekPath, path, c) {
    if (path) {
      node.path = path + node.name;
    } else {
      node.path = "";
    }
    if (node.path == seekPath) {
      Store.dispatch("setState", {
        property: "browse_selected_folder",
        value: node,
      });
      Store.dispatch("setState", {
        property: "browse_selected_file",
        value: this.state.editor_selected_file,
      });
      Router.go("/wex/" + this.state.langCode + "/browse");
    } else {
      if (node.hasOwnProperty("children")) {
        for (var i = 0; i < node.children.length; i++) {
          c._folderByPath(node.children[i], seekPath, node.path + "/", c);
        }
      }
    }
  }

  _handleRowRightClicked(e) {
    if (!this._isClaimed()) {
      return;
    }
    //modify conditional context menu items
    if (e.currentTarget.item) {
      this.itemRightClicked = e.currentTarget.item;
      var menuItems = util.buildFileMenu(
        e.currentTarget.item,
        this.state.menus.file_menu,
        "editorfilepackage",
        this.state.wex_user.globalCapability,
        this.state.editor_selected_task.userWexCapability
      );
      menuItems.map((item) => (item.text = this.string[item.label]));
      this.contextMenu.items = menuItems;

      if (this.state.editor_selected_file) {
        if (
          this.state.editor_selected_file.resLblId ==
          e.currentTarget.item.resLblId
        ) {
          return;
        }
      }
      Store.dispatch("setState", {
        property: "editor_selected_file",
        value: e.currentTarget.item,
      });
    }
  }

  _handleRowClicked(e) {
    if (!this._isClaimed()) {
      return;
    }
    const path = e.composedPath();
    var iconClicked = path[0].tagName == "IRON-ICON" && !path[0].disabled;
    if (iconClicked) {
      var icon = path[0];
      switch (icon.id) {
        case "preview":
          Store.dispatch("setState", {
            property: "editor_selected_file",
            value: e.currentTarget.item,
          });
          Router.go(
            "/wex/" +
              this.state.langCode +
              "/preview?resLblId=" +
              this.state.editor_selected_file.resLblId +
              "&aod=" +
              this.state.editor_selected_file.verCreateDate +
              "&previewStyle=html"
          );
          break;
        case "edit":
          Store.dispatch("setState", {
            property: "editor_selected_file",
            value: e.currentTarget.item,
          });
          this._editSelectedFile();
          break;
        case "properties":
          Store.dispatch("setState", {
            property: "editor_selected_file",
            value: e.currentTarget.item,
          });
          if (e.currentTarget.item) {
            if (this.state.editor_selected_file) {
              if (
                this.state.editor_selected_file.resLblId !=
                e.currentTarget.item.resLblId
              ) {
                Store.dispatch("setState", {
                  property: "editor_selected_file",
                  value: e.currentTarget.item,
                });
              }
            } else {
              Store.dispatch("setState", {
                property: "editor_selected_file",
                value: e.currentTarget.item,
              });
            }
            Store.dispatch(
              "filePropertiesRequest",
              this.state.editor_selected_file
            );
            break;
          }
      }
    } else {
      if (e.currentTarget.item) {
        if (this.state.editor_selected_file) {
          if (
            this.state.editor_selected_file.resLblId ==
            e.currentTarget.item.resLblId
          ) {
            Store.dispatch("setState", {
              property: "editor_selected_file",
              value: null,
            });
            return;
          }
        }
        Store.dispatch("setState", {
          property: "editor_selected_file",
          value: e.currentTarget.item,
        });
      }
    }
  }
}
