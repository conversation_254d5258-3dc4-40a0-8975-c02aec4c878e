{"version": 3, "file": "context-provider.js", "sourceRoot": "", "sources": ["../../../src/lib/controllers/context-provider.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,mBAAmB,EAAC,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAC,aAAa,EAAC,MAAM,sBAAsB,CAAC;AAiBnD,MAAM,OAAO,oBAEX,SAAQ,KAAK;IAIb;;;;OAIG;IACH,YAAY,OAAU,EAAE,aAAsB;QAC5C,KAAK,CAAC,kBAAkB,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;CACF;AASD;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,eAIX,SAAQ,aAA6B;IASrC,YACE,IAAiB,EACjB,gBAAgC,EAChC,YAA6B;QAE7B,KAAK,CACF,gBAA+B,CAAC,OAAO,KAAK,SAAS;YACpD,CAAC,CAAE,gBAA+B,CAAC,YAAY;YAC/C,CAAC,CAAC,YAAY,CACjB,CAAC;QAWJ,qBAAgB,GAAG,CACjB,EAAkD,EAC5C,EAAE;YACR,iDAAiD;YACjD,IAAI,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YACD,wDAAwD;YACxD,sEAAsE;YACtE,MAAM,YAAY,GAAG,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9D,IAAI,YAAY,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YACD,EAAE,CAAC,eAAe,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;QAC5D,CAAC,CAAC;QAEF;;;;;WAKG;QACH,sBAAiB,GAAG,CAClB,EAAmD,EAC7C,EAAE;YACR,gDAAgD;YAChD,IAAI,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YACD,wDAAwD;YACxD,sDAAsD;YACtD,MAAM,iBAAiB,GAAG,EAAE,CAAC,aAAa,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;YACnE,IAAI,iBAAiB,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YACD,qEAAqE;YACrE,yBAAyB;YACzB,MAAM,IAAI,GAAG,IAAI,GAAG,EAAW,CAAC;YAChC,KAAK,MAAM,CAAC,QAAQ,EAAE,EAAC,YAAY,EAAC,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC5D,8DAA8D;gBAC9D,gDAAgD;gBAChD,EAAE;gBACF,qEAAqE;gBACrE,qEAAqE;gBACrE,uEAAuE;gBACvE,wEAAwE;gBACxE,uEAAuE;gBACvE,8CAA8C;gBAC9C,EAAE;gBACF,uEAAuE;gBACvE,0EAA0E;gBAC1E,gEAAgE;gBAChE,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvB,SAAS;gBACX,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACnB,YAAY,CAAC,aAAa,CACxB,IAAI,mBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC,CACpE,CAAC;YACJ,CAAC;YACD,EAAE,CAAC,eAAe,EAAE,CAAC;QACvB,CAAC,CAAC;QAxEA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAK,gBAA+B,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC3D,IAAI,CAAC,OAAO,GAAI,gBAA+B,CAAC,OAAO,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,GAAG,gBAAqB,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAkEO,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACrE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACzE,CAAC;IAED,aAAa;QACX,mEAAmE;QACnE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7E,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ContextRequestEvent} from '../context-request-event.js';\nimport {ValueNotifier} from '../value-notifier.js';\nimport type {Context, ContextType} from '../create-context.js';\nimport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from '@lit/reactive-element';\n\ndeclare global {\n  interface HTMLElementEventMap {\n    /**\n     * A 'context-provider' event can be emitted by any element which hosts\n     * a context provider to indicate it is available for use.\n     */\n    'context-provider': ContextProviderEvent<Context<unknown, unknown>>;\n  }\n}\n\nexport class ContextProviderEvent<\n  C extends Context<unknown, unknown>,\n> extends Event {\n  readonly context: C;\n  readonly contextTarget: Element;\n\n  /**\n   *\n   * @param context the context which this provider can provide\n   * @param contextTarget the original context target of the provider\n   */\n  constructor(context: C, contextTarget: Element) {\n    super('context-provider', {bubbles: true, composed: true});\n    this.context = context;\n    this.contextTarget = contextTarget;\n  }\n}\n\nexport interface Options<C extends Context<unknown, unknown>> {\n  context: C;\n  initialValue?: ContextType<C>;\n}\n\ntype ReactiveElementHost = Partial<ReactiveControllerHost> & HTMLElement;\n\n/**\n * A ReactiveController which adds context provider behavior to a\n * custom element.\n *\n * This controller simply listens to the `context-request` event when\n * the host is connected to the DOM and registers the received callbacks\n * against its observable Context implementation.\n *\n * The controller may also be attached to any HTML element in which case it's\n * up to the user to call hostConnected() when attached to the DOM. This is\n * done automatically for any custom elements implementing\n * ReactiveControllerHost.\n */\nexport class ContextProvider<\n    T extends Context<unknown, unknown>,\n    HostElement extends ReactiveElementHost = ReactiveElementHost,\n  >\n  extends ValueNotifier<ContextType<T>>\n  implements ReactiveController\n{\n  protected readonly host: HostElement;\n  private readonly context: T;\n\n  constructor(host: HostElement, options: Options<T>);\n  /** @deprecated Use new ContextProvider(host, options) */\n  constructor(host: HostElement, context: T, initialValue?: ContextType<T>);\n  constructor(\n    host: HostElement,\n    contextOrOptions: T | Options<T>,\n    initialValue?: ContextType<T>\n  ) {\n    super(\n      (contextOrOptions as Options<T>).context !== undefined\n        ? (contextOrOptions as Options<T>).initialValue\n        : initialValue\n    );\n    this.host = host;\n    if ((contextOrOptions as Options<T>).context !== undefined) {\n      this.context = (contextOrOptions as Options<T>).context;\n    } else {\n      this.context = contextOrOptions as T;\n    }\n    this.attachListeners();\n    this.host.addController?.(this);\n  }\n\n  onContextRequest = (\n    ev: ContextRequestEvent<Context<unknown, unknown>>\n  ): void => {\n    // Only call the callback if the context matches.\n    if (ev.context !== this.context) {\n      return;\n    }\n    // Also, in case an element is a consumer AND a provider\n    // of the same context, we want to avoid the element to self-register.\n    const consumerHost = ev.contextTarget ?? ev.composedPath()[0];\n    if (consumerHost === this.host) {\n      return;\n    }\n    ev.stopPropagation();\n    this.addCallback(ev.callback, consumerHost, ev.subscribe);\n  };\n\n  /**\n   * When we get a provider request event, that means a child of this element\n   * has just woken up. If it's a provider of our context, then we may need to\n   * re-parent our subscriptions, because is a more specific provider than us\n   * for its subtree.\n   */\n  onProviderRequest = (\n    ev: ContextProviderEvent<Context<unknown, unknown>>\n  ): void => {\n    // Ignore events when the context doesn't match.\n    if (ev.context !== this.context) {\n      return;\n    }\n    // Also, in case an element is a consumer AND a provider\n    // of the same context it shouldn't provide to itself.\n    const childProviderHost = ev.contextTarget ?? ev.composedPath()[0];\n    if (childProviderHost === this.host) {\n      return;\n    }\n    // Re-parent all of our subscriptions in case this new child provider\n    // should take them over.\n    const seen = new Set<unknown>();\n    for (const [callback, {consumerHost}] of this.subscriptions) {\n      // Prevent infinite loops in the case where a one host element\n      // is providing the same context multiple times.\n      //\n      // While normally it's a no-op to attempt to re-parent a subscription\n      // that already has its proper parent, in the case where there's more\n      // than one ValueProvider for the same context on the same hostElement,\n      // they will each call the consumer, and since they will each have their\n      // own dispose function, a well behaved consumer will notice the change\n      // in dispose function and call their old one.\n      //\n      // This will cause the subscriptions to thrash, but worse, without this\n      // set check here, we can end up in an infinite loop, as we add and remove\n      // the same subscriptions onto the end of the map over and over.\n      if (seen.has(callback)) {\n        continue;\n      }\n      seen.add(callback);\n      consumerHost.dispatchEvent(\n        new ContextRequestEvent(this.context, consumerHost, callback, true)\n      );\n    }\n    ev.stopPropagation();\n  };\n\n  private attachListeners() {\n    this.host.addEventListener('context-request', this.onContextRequest);\n    this.host.addEventListener('context-provider', this.onProviderRequest);\n  }\n\n  hostConnected(): void {\n    // emit an event to signal a provider is available for this context\n    this.host.dispatchEvent(new ContextProviderEvent(this.context, this.host));\n  }\n}\n"]}