import { LitElement, html, css } from "lit";
import { storeInstance } from "store/index.js";
// import * as wexlib from "lib/wexlib.js";

import "../../components/base/col-item";
import "../../components/base/row-item";
import "../../components/base/icon";

import "../../components/publish/definitions-filter-bar";
import "../../components/publish/definitions-action-bar.js";

import "@ui5/webcomponents/dist/Button.js";

class WexPublishDefinitionsPane extends LitElement {
  static get properties() {
    return {
      state: { type: Object },
      //   _categories: { type: Array },
      projects: { type: Array },
      pubDefs: { type: Array },
      _isLoading: { type: Boolean },
      activeFilters: { type: Object },
      filterOptions: { type: Object },
    };
  }

  static get styles() {
    return css`
      :host {
        display: flex;
        flex: 1;
        width: 100%;
      }
    `;
    // max-height: 80%;
  }

  constructor() {
    super();
    this.state = {};
    this.string = {};
    this._subscription = null;

    // private properties
    // this._categories = [];
    // this.projects = [];
    this.pubDefs = [];

    this._isLoading = false;

    // internal properties
    this._selectedProjectId = null;
    this._selectedPubDefId = null;

    // new
    this.pubDefColumns = [];
    this.activeFilters = {};
  }

  connectedCallback() {
    super.connectedCallback();
    const state = storeInstance.state;
    this.state = state;
    this.string = state[state.langCode];
    this.subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  stateChange(state) {
    this.state = state;
    this.string = state[state.langCode];

    // Update reactive properties from state so Lit notices changes
    this.pubDefs = state._state?.pages?.publish?.pubDefs || [];
    this.projects = state._state?.pages?.publish?.projects || [];
  }

  async _init() {
    try {
      this.pubDefColumns = this.state.columns.publishDefs;
      this.projects = await storeInstance.waitFor(
        "_state.pages.publish.projects"
      );
      this.pubDefs = await storeInstance.waitFor(
        "_state.pages.publish.pubDefs"
      );
    } catch (error) {
      console.error("Error during initialization:", error);
    }
  }

  updated(changedProps) {
    if (changedProps.has("pubDefs")) {
      if (this.pubDefs.filter((pubDef) => pubDef.selected).length === 1) {
        this.dispatchEvent(
          new CustomEvent("single-pubdef-selected", {
            detail: this.pubDefs.find((pubDef) => pubDef.selected),
            bubbles: true,
            composed: true,
          })
        );
      } else {
        this.dispatchEvent(
          new CustomEvent("reset-selected-pubdef", {
            bubbles: true,
            composed: true,
          })
        );
      }
    }
  }

  //   async _handleContextMenu(e) {
  //   try {
  //     if (!e.detail) return;
  //     const { name: type } = e.detail.value;
  //     if (!type) return;

  //     if (type == "delete") {
  //       await wexlib.rmPubDef(this._focusedPubDefId);
  //       if (this.pubDef.pubDefId == this._focusedPubDefId) {
  //         this.pubDef = null;
  //       }
  //       this._focusedPubDefId = null;
  //       this._handleClick("project", { projectId: this._selectedProjectId });
  //       util.notifyUser(
  //         "positive",
  //         this.string["_msg_publish_defs_delete_pubdef_success"]
  //       );
  //     }
  //   } catch {}
  // }

  // category
  // change to filter type TODO
  // async _handleCategorySelect(type = null, e) {
  //   try {
  //     if (!type) return;
  //     if (type == "category") {
  //       const id = e.detail.selectedOption.value;
  //       this.projects = await wexlib.getProjectsByProjCategoryId(id);
  //       if (this.pubDef) {
  //         this._selectedProjectId = this.pubDef.projectId;
  //         this._handleClick("project", { projectId: this.pubDef.projectId });
  //       }
  //     }
  //   } catch {}
  // }

  async _handleDefRowClick(e) {
    try {
      this._isLoading = true;
      const data = e.detail;
      if (!data) return;

      const obj = { ...data.value };

      const rows = [...this.pubDefs];

      if (e.detail.ctrlKey) {
        rows.forEach((row) => {
          row.selected = row.selected;
          if (row.pubDefId == obj.pubDefId) row.selected = !row.selected;
        });
        this.pubDefs = rows;

        this._selectedPubDefId = null;
        return;
      }

      rows.forEach((row) => {
        row.selected = row.pubDefId == obj.pubDefId;
      });
      this._selectedPubDefId = rows.find((row) => row.selected).pubDefId;

      this.pubDefs = rows;
    } catch (err) {
      console.error("error", err);
    } finally {
      this._isLoading = false;
      const selectedPubDefs = this.pubDefs.filter((pubDef) => pubDef.selected);
      if (selectedPubDefs.length === 1) {
        this._selectedProjectId = selectedPubDefs[0].projectId;
      }
      this.requestUpdate();
    }
  }

  // combo bar
  _renderComboBar() {
    if (this.pubDefs.filter((row) => row.selected).length) {
      return html`<wex-publish-definitions-action-bar
        .projects=${this.projects}
        .pubDefs=${this.pubDefs}
        @select-all-definitions=${this._handleSelectAllDefinitions}
        @clear-selected-definitions=${this._handleClearSelectedDefinitions}
      ></wex-publish-definitions-action-bar>`;
      // } else if (this.pubDefs.length) {
      //   return html`<wex-publish-definitions-filter-bar
      //     .pubDefs=${this.pubDefs}
      //     .projects=${this.projects}
      //     .categories=${this.categories}
      //     @set-active-filters=${(e) =>
      //       (this.activeFilters = e.detail.activeFilters)}
      //   ></wex-publish-definitions-filter-bar>`;
    } else {
      // console.error("Cannot render combo bar");
      return html`<wex-publish-definitions-filter-bar
        .pubDefs=${this.pubDefs}
        .projects=${this.projects}
        .categories=${this.categories}
        @set-active-filters=${this._handleSetActiveFilters}
      ></wex-publish-definitions-filter-bar>`;
      // @set-active-filters=${(e) =>
      //   (this.activeFilters = e.detail.activeFilters)}
    }
  }

  _handleSetActiveFilters(e) {
    console.log("_handleSetActiveFilters: e.detail", e.detail);
    const { activeFilters } = e.detail;
    // console.log("_handleSetActiveFilters: activeFilters", activeFilters);

    this.activeFilters = activeFilters;
    console.log(
      "_handleSetActiveFilters: this.activeFilters",
      this.activeFilters
    );
  }

  _handleSelectAllDefinitions = (e) => {
    const rows = this.pubDefs.map((row) => ({ ...row, selected: true }));
    this.pubDefs = rows;
  };

  _handleClearSelectedDefinitions = (e) => {
    const rows = this.pubDefs.map((row) => ({ ...row, selected: false }));
    this.pubDefs = rows;
  };

  // definitions table
  _renderDefsTable() {
    return html`
      <wex-table
        id="publish-defs-table"
        defaultHeight="75vh"
        .activeFilters=${this.activeFilters}
        .columns="${this.pubDefColumns}"
        .rows="${this.pubDefs}"
        @click=${this._handleDefRowClick.bind(this)}
      ></wex-table>
    `;
  }

  render() {
    return html`
      <wex-col-item justifyContent="flex-start">
        ${this._renderComboBar()} ${this._renderDefsTable()}
      </wex-col-item>
    `;
  }

  // _selectDefinition(definition) {
  //   this.selectedDefinition = definition;
  //   this.dispatchEvent(
  //     new CustomEvent("definition-selected", { detail: definition })
  //   );
  // }
}

customElements.define(
  "wex-publish-definitions-pane",
  WexPublishDefinitionsPane
);
