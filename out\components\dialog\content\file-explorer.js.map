{"version": 3, "file": "file-explorer.js", "sourceRoot": "", "sources": ["../../../../src/components/dialog/content/file-explorer.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AACnE,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,sBAAsB,CAAC;AAC9B,OAAO,gCAAgC,CAAC;AACxC,OAAO,UAAU,CAAC;AAClB,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,IAAM,4BAA4B,GAAlC,MAAM,4BAA6B,SAAQ,UAAU;IAWnD;QACE,KAAK,EAAE,CAAC;QAXV,UAAK,GAAQ,EAAE,CAAC;QAEhB,YAAO,GAAmB,IAAI,CAAC;QACH,WAAM,GAA2B,EAAE,CAAC;QACrC,gBAAW,GAAiB,EAAE,CAAC;QAC9B,mBAAc,GAAsB,IAAI,CAAC;QACzC,iBAAY,GAAoB,IAAI,CAAC;QACrC,uBAAkB,GAAW,EAAE,CAAC;QAsD5D,0BAAqB,GAAG,CAAC,CAAC,EAAE,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;YAErC,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAChD,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACjC,QAAQ,EAAE,+BAA+B;oBACzC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI;iBACrB,CAAC,CAAC;gBACH,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;YACtC,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC7B,CAAC,CAAC;IA7DF,CAAC;IACD,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YACrD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG;YAChB,aAAa,EAAE,GAAG;YAClB,SAAS,EAAE,MAAM;YACjB,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,UAAU;SACtB,CAAC;IACJ,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,WAAW,CAAC,KAAK;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC;QACxC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,6BAA6B,CAAC;QAC1D,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED,gBAAgB;QACd,MAAM,OAAO,GAAG,IAAI,CAAC,UAAW,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;QACxE,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,WAAW,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAED,mBAAmB,CAAC,KAAK;QACvB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,yBAAyB,CAAC;QACpD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY;YACzC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS;YAC7B,CAAC,CAAC,EAAE,CAAC;IACT,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;IAC/B,CAAC;IAgBD,MAAM;QACJ,OAAO,IAAI,CAAA;;;;oBAIK,IAAI,CAAC,UAAU;kBACjB,IAAI,CAAC,WAAW;wBACV,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;oBAMzC,CAAC;IACnB,CAAC;IAED,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwCT,CAAC;IACJ,CAAC;CAKF,CAAA;AAvI6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;4DAAqC;AACrC;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;iEAAgC;AAC9B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;oEAA0C;AACzC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;kEAAsC;AACrC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wEAAiC;AARxD,4BAA4B;IADjC,aAAa,CAAC,kCAAkC,CAAC;GAC5C,4BAA4B,CA2IjC", "sourcesContent": ["import { LitElement, html, css, customElement } from \"lit-element\";\r\nimport { storeInstance } from \"store/index.js\";\r\nimport \"../../wex-folders.js\";\r\nimport \"../../wex-select-file-files.js\";\r\nimport \"bds-tree\";\r\nimport { property } from \"lit/decorators.js\";\r\nimport { Collection, FileMeta } from \"@bds/types\";\r\n\r\n@customElement(\"wex-dialog-content-file-explorer\")\r\nclass WexDialogContentFileExplorer extends LitElement {\r\n  state: any = {};\r\n  _subscription: any;\r\n  filesEl: Element | null = null;\r\n  @property({ type: Object }) string: Record<string, string> = {};\r\n  @property({ type: Array }) foldersData: Collection[] = [];\r\n  @property({ type: Object }) selectedFolder: Collection | null = null;\r\n  @property({ type: Object }) selectedFile: FileMeta | null = null;\r\n  @property({ type: String }) selectedFilePathId: string = \"\";\r\n  treeConfig: any;\r\n\r\n  constructor() {\r\n    super();\r\n  }\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this.state = storeInstance.state;\r\n    this.string = this.state[this.state.langCode];\r\n    this._subscription = storeInstance.subscribe((state) => {\r\n      this.stateChange(state);\r\n    });\r\n    this.foldersData = this.state.global_folders;\r\n    this.treeConfig = {\r\n      initialNodeId: \"2\",\r\n      labelPath: \"name\",\r\n      idPath: \"id\",\r\n      childPath: \"children\",\r\n    };\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this._subscription);\r\n  }\r\n\r\n  stateChange(state) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.foldersData = state.global_folders;\r\n    this.selectedFolder = state.select_file_dialog_folderitem;\r\n    this._updateSelectedFile(state);\r\n  }\r\n\r\n  refreshFilesList() {\r\n    const filesEl = this.shadowRoot!.querySelector(\"wex-select-file-files\");\r\n    if (filesEl) {\r\n      filesEl.refreshData();\r\n    }\r\n  }\r\n\r\n  _updateSelectedFile(state) {\r\n    this.selectedFile = state.select_file_selected_file;\r\n    this.selectedFilePathId = this.selectedFile\r\n      ? this.selectedFile.resPathId\r\n      : \"\";\r\n  }\r\n\r\n  _clearSelectedFile() {\r\n    this.selectedFile = null;\r\n    this.selectedFilePathId = \"\";\r\n  }\r\n\r\n  _handleFolderSelected = (e) => {\r\n    console.log(\"CHECK THIS:\", e.detail);\r\n\r\n    if (this.selectedFolder?.id != e.detail.node.id) {\r\n      this._clearSelectedFile();\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"select_file_dialog_folderitem\",\r\n        value: e.detail.node,\r\n      });\r\n      this.selectedFolder = e.detail.node;\r\n    }\r\n    this.filesEl.refreshData();\r\n  };\r\n\r\n  render() {\r\n    return html`\r\n    <wex-row-item class=\"container\" height=\"100%\">\r\n      <div id=\"fldr\">\r\n        <bds-tree\r\n          .config=${this.treeConfig}\r\n          .root=${this.foldersData}\r\n          @treeClick=\"${this._handleFolderSelected.bind(this)}\"\r\n        ></bds-tree>\r\n      </div>\r\n      <div id=\"files\">\r\n        <wex-select-file-files id=\"file-list\"></wex-select-file-files>\r\n      </div>\r\n    </wex-row-item>`;\r\n  }\r\n\r\n  static get styles() {\r\n    return css`\r\n      :host {\r\n        display: flex;\r\n        flex: 1;\r\n        min-height: 0;\r\n      }\r\n      .container {\r\n        display: flex;\r\n        flex-direction: column;\r\n        flex: 1;\r\n        min-height: 0;\r\n        gap: 0;\r\n        flex-shrink: 0;\r\n        margin-bottom: 1rem;\r\n        margin-top: 1rem;\r\n        max-height: 100%;\r\n        border: 1px inset grey;\r\n      }\r\n\r\n      #fldr,\r\n      #files {\r\n        min-height: 0;\r\n        overflow: auto;\r\n      }\r\n\r\n      #fldr {\r\n        padding: 0;\r\n        flex: 3;\r\n        overflow-y: auto;\r\n        overflow-x: auto;\r\n        height: 100%;\r\n      }\r\n\r\n      #files {\r\n        flex: 5;\r\n        overflow-x: auto;\r\n        overflow-y: auto;\r\n        height: 100%;\r\n        border-left: 1px solid grey;\r\n      }\r\n    `;\r\n  }\r\n\r\n  // protected createRenderRoot(): Element | ShadowRoot {\r\n  //   return this;\r\n  // }\r\n}\r\n"]}