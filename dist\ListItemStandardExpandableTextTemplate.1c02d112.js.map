{"mappings": "A,S,E,C,C,C,C,C,C,C,E,O,c,C,E,E,C,I,E,I,E,W,C,E,a,C,C,E,C,I,E,A,W,iB,C,E,E,Q,C,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,S,E,E,SEWc,SAAA,EAEb,CAA2C,EAE3C,GAAM,CAAA,UACL,CAAS,CAAA,KACT,CAAI,CAAA,cACJ,CAAa,CAAA,KACb,CAAI,CACJ,CAAG,EAEJ,MACC,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAc,CACd,KAAM,EACN,MAAO,EACP,KAAM,EACN,cAAe,CAAa,EAG/B,C,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,S,C,C,C,C,C,C,C,E,I,E,E,U,M,C,E,E,E,E,A,O,E,E,O,wB,C,E,G,E,G,A,U,O,S,A,Y,O,Q,Q,C,E,Q,Q,C,E,E,E,Q,I,I,E,E,M,C,E,G,E,I,C,E,C,C,E,A,G,C,E,A,C,E,E,E,G,E,E,E,E,E,G,E,E,E,G,C,E,O,E,G,G,O,c,C,E,E,G,C,EE+BA,IAAM,EAAc,EAApB,cAA6B,EAAA,OAAA,CAA7B,aAAA,C,K,I,WAgBC,IAAA,CAAA,aAAa,CAAG,IAQhB,IAAA,CAAA,YAAY,CAAoC,UAQhD,IAAA,CAAA,kBAAkB,CAAgC,MAGlD,IAAA,CAAA,SAAS,CAAG,CAAA,CAgFb,CA3EC,gBAAA,QACC,AAAI,IAAI,CAAC,WAAW,CACZ,IAAI,CAAC,UAAU,EAAE,cAAc,4BAGhC,IAAI,CAAC,UAAU,EAAE,cAAc,aACvC,CAEA,IAAI,gBAAJ,QACC,AAAI,IAAI,CAAC,SAAS,EAAI,CAAC,IAAI,CAAC,WAAW,CAC/B,IAAI,CAAC,IAAI,CAGV,IAAI,CAAC,IAAI,EAAE,UAAU,EAAG,IAAI,CAAC,aAAa,CAClD,CAEA,IAAI,wBAAJ,CACC,MAAO,AAAC,CAAA,IAAI,CAAC,IAAI,EAAE,QAAU,CAAA,EAAK,IAAI,CAAC,aAAa,AACrD,CAEA,IAAI,aAAJ,CACC,OAAO,IAAI,CAAC,YAAY,GAAK,AAAA,EAAA,OAAA,CAA2B,OAAO,AAChE,CAEA,IAAI,eAAJ,QACC,AAAI,IAAI,CAAC,SAAS,EAAI,CAAC,IAAI,CAAC,WAAW,CAC/B,IAGD,MACR,CAEA,IAAI,gBAAJ,CACC,OAAO,IAAI,CAAC,SAAS,CAAG,EAAe,UAAU,CAAC,OAAO,CAAC,EAAA,yBAAA,EAA6B,EAAe,UAAU,CAAC,OAAO,CAAC,EAAA,yBAAA,CAC1H,CAEA,IAAI,kBAAJ,CACC,OAAO,EAAe,UAAU,CAAC,OAAO,CAAC,EAAA,qBAAA,CAC1C,CAEA,IAAI,mCAAJ,QACC,AAAI,IAAI,CAAC,WAAW,CACZ,CACN,SAAU,IAAI,CAAC,SAAS,CACxB,SAAU,QACV,EAGK,CACN,SAAU,IAAI,CAAC,SAAS,AACxB,CACF,CAEA,IAAI,0BAAJ,CACC,GAAI,IAAI,CAAC,WAAW,CACnB,OAAO,IAAI,CAAC,SAAS,CAAG,EAAe,UAAU,CAAC,OAAO,CAAC,EAAA,4CAAA,EAAgD,EAAe,UAAU,CAAC,OAAO,CAAC,EAAA,4CAAA,CAI9I,CAEA,qBAAA,CACK,AAAC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,KACJ,CAAA,IAAI,CAAC,SAAS,CAAG,CAAA,CADlB,CAGD,CAEA,oBAAA,CACC,IAAI,CAAC,SAAS,CAAG,CAAC,IAAI,CAAC,SAAS,AACjC,CAEA,wBAAwB,CAAkC,CAA1D,CACC,IAAI,CAAC,SAAS,CAAG,CAAA,EACjB,EAAE,eAAe,EAClB,CACA,EA3GA,EAAA,CADC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,IACa,CAAA,EAAA,SAAA,CAAA,OAAA,KAAA,GAQd,EAAA,CADC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAS,CAAE,KAAM,MAAM,GACJ,CAAA,EAAA,SAAA,CAAA,gBAAA,KAAA,GAQpB,EAAA,CADC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,IACwD,CAAA,EAAA,SAAA,CAAA,eAAA,KAAA,GAQzD,EAAA,CADC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,IACuD,CAAA,EAAA,SAAA,CAAA,qBAAA,KAAA,GAGxD,EAAA,CADC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAS,CAAE,KAAM,OAAO,GACP,CAAA,EAAA,SAAA,CAAA,YAAA,KAAA,GAGX,EAAA,CADN,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAK,sBACwB,CAAA,EAAA,aAAA,KAAA,GA+E/B,AArHM,CAAA,EAAA,EAAA,EAAA,CANL,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAc,CACd,IAAK,sBACL,SAAU,EAAA,OAAA,CACV,OAAQ,EAAA,OAAA,CACR,SAAU,EAAA,OAAA,AACV,GACK,CAAA,EAAA,EAqHS,MAAM,GAErB,IAAA,EAAe,C,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,GE3Kd,CALI,EAAA,GAAA,CAAA,EAA0B,CAAA,CAAA,GAK9B,OAAA,CAAA,UAMA,EAAA,OAAA,CAAA,UAGD,IAdK,EAAA,EAcL,EAAe,C,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,S,E,E,S,E,E,S,E,E,S,E,E,SEZD,SAAA,IACb,MACC,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAA,MAAA,CAAA,SAAA,CACC,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAI,CACJ,MAAM,oBACN,mBAAoB,IAAI,CAAC,kBAAkB,CAAA,SAE1C,IAAI,CAAC,cAAc,AAAA,GAGpB,IAAI,CAAC,sBAAsB,EAAI,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAA,EAAA,QAAA,CAAA,CAAA,SAAA,CAC/B,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,OAAA,CAAM,MAAM,wBAAuB,SAAE,IAAI,CAAC,aAAa,AAAA,GACvD,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAI,CACJ,GAAG,SACH,MAAM,sBACN,eAAe,SACf,eAAgB,IAAI,CAAC,wBAAwB,CAC7C,wBAAyB,IAAI,CAAC,iCAAiC,CAC/D,QAAS,IAAI,CAAC,kBAAkB,CAAA,SAE/B,IAAI,CAAC,cAAc,AAAA,GAGpB,IAAI,CAAC,WAAW,EAChB,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAC,EAAA,OAAA,CAAiB,CACjB,KAAM,IAAI,CAAC,SAAS,CACpB,OAAO,SACP,kBAAkB,eAClB,qBAAsB,CAAA,EACtB,YAAa,CAAA,EACb,MAAM,uBACN,QAAS,IAAI,CAAC,mBAAmB,CAAA,SAAA,CAEjC,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAI,CAAC,GAAG,eAAc,SAAE,IAAI,CAAC,IAAI,AAAA,GAClC,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,MAAA,CACC,KAAK,SACL,MAAM,sBAAqB,SAE3B,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAM,CACN,OAAO,cACP,QAAS,IAAI,CAAC,uBAAuB,CAAA,SAEpC,IAAI,CAAC,gBAAgB,AAAA,EACd,GACJ,AAAA,GACa,AAAA,GAEnB,AAAA,EAIN,C,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,S,C,C,C,C,C,C,C,E,I,E,E,U,M,C,E,E,E,E,A,O,E,E,O,wB,C,E,G,E,G,A,U,O,S,A,Y,O,Q,Q,C,E,Q,Q,C,E,E,E,Q,I,I,E,E,M,C,E,G,E,I,C,E,C,C,E,A,G,C,E,A,C,E,E,E,G,E,E,E,E,E,G,E,E,E,G,C,E,O,E,G,G,O,c,C,E,E,G,C,EEFA,IAAM,EAAI,EAAV,cAAmB,EAAA,OAAA,CAAnB,aAAA,C,K,I,WAOC,IAAA,CAAA,QAAQ,CAAW,IASnB,IAAA,CAAA,kBAAkB,CAAgC,KA+BnD,CAnBC,mBAAA,CACC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,AAAA,CAAA,EAAA,EAAA,gBAAA,AAAA,EAAiB,yBAA0B,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAA,CAAE,CACrF,CAEA,IAAI,SAAJ,CACC,MAAO,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAgB,IAAI,CAAC,IAAI,CACjC,CAEA,IAAI,uBAAJ,CACC,MAAO,CAAC,IAAI,CAAC,OAAO,EAAI,IAAI,CAAC,kBAAkB,GAAK,AAAA,EAAA,OAAA,CAAuB,EAAE,AAC9E,CAEA,IAAI,0BAAJ,CACC,OAAO,EAAK,UAAU,CAAC,OAAO,CAAC,EAAA,+BAAA,CAChC,CAEA,IAAI,uBAAJ,CACC,OAAO,EAAK,UAAU,CAAC,OAAO,CAAC,EAAA,sBAAA,CAChC,CACA,EAxCA,EAAA,CADC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAS,CAAE,KAAM,MAAM,GACI,CAAA,EAAA,SAAA,CAAA,WAAA,KAAA,GAS5B,EAAA,CADC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,IACuD,CAAA,EAAA,SAAA,CAAA,qBAAA,KAAA,GAOxD,EAAA,CADC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAK,CAAE,KAAM,KAAM,QAAW,CAAA,CAAI,GAChB,CAAA,EAAA,SAAA,CAAA,OAAA,KAAA,GAGZ,EAAA,CADN,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAK,sBACwB,CAAA,EAAA,aAAA,KAAA,GAuB/B,AAjDM,CAAA,EAAA,EAAA,EAAA,CANL,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAc,CACd,IAAK,WACL,SAAU,EAAA,OAAA,CACV,SAAU,EAAA,OAAA,CACV,OAAA,EAAA,OAAA,AACA,GACK,CAAA,EAAA,EAiDD,MAAM,GAEX,IAAA,EAAe,C,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,GEjGd,CALI,EAAA,GAAA,CAAA,EAAsB,CAAA,CAAA,GAK1B,GAAA,CAAA,MAMA,EAAA,EAAA,CAAA,KAGD,IAdK,EAAA,EAcL,EAAe,C,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,SEhBD,SAAA,IACb,MAAO,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,EAAA,QAAA,CAAA,CAAA,SACN,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,OAAA,CAAA,SACG,IAAI,CAAC,qBAAqB,CAC3B,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAA,EAAA,QAAA,CAAA,CAAA,SAAA,CACC,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,OAAA,CAAM,UAAU,kBAAiB,cAAa,OAAM,SAAG,IAAI,CAAC,qBAAqB,AAAA,GACjF,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,OAAA,CAAM,UAAU,6BAA4B,SAAG,IAAI,CAAC,wBAAwB,AAAA,GAAS,AAAA,GAGtF,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,OAAA,CAAA,EAAa,EAER,EAET,C,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,S,E,E,S,E,E,SEVA,AAAA,CAAA,EAAA,EAAA,6BAAA,AAAA,EAA8B,6BAA8B,cAAe,SAAY,EAAA,OAAA,EACvF,AAAA,CAAA,EAAA,EAAA,6BAAA,AAAA,EAA8B,qBAAsB,cAAe,SAAY,EAAA,OAAA,EAC/E,IAAA,EAAe,CAAf;AACC,CAAA,A,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,S,E,E,S,E,E,SEHD,AAAA,CAAA,EAAA,EAAA,6BAAA,AAAA,EAA8B,6BAA8B,cAAe,SAAY,EAAA,OAAA,EACvF,AAAA,CAAA,EAAA,EAAA,6BAAA,AAAA,EAA8B,qBAAsB,cAAe,SAAY,EAAA,OAAA,EAC/E,IAAA,EAAe,CAAf;AACC,CAAA,A", "sources": ["<anon>", "node_modules/@ui5/webcomponents/dist/features/ListItemStandardExpandableTextTemplate.js", "node_modules/@ui5/webcomponents/src/features/ListItemStandardExpandableTextTemplate.tsx", "node_modules/@ui5/webcomponents/dist/ExpandableText.js", "node_modules/@ui5/webcomponents/src/ExpandableText.ts", "node_modules/@ui5/webcomponents/dist/types/ExpandableTextOverflowMode.js", "node_modules/@ui5/webcomponents/src/types/ExpandableTextOverflowMode.ts", "node_modules/@ui5/webcomponents/dist/ExpandableTextTemplate.js", "node_modules/@ui5/webcomponents/src/ExpandableTextTemplate.tsx", "node_modules/@ui5/webcomponents/dist/Text.js", "node_modules/@ui5/webcomponents/src/Text.ts", "node_modules/@ui5/webcomponents/dist/types/TextEmptyIndicatorMode.js", "node_modules/@ui5/webcomponents/src/types/TextEmptyIndicatorMode.ts", "node_modules/@ui5/webcomponents/dist/TextTemplate.js", "node_modules/@ui5/webcomponents/src/TextTemplate.tsx", "node_modules/@ui5/webcomponents/dist/generated/themes/Text.css.js", "node_modules/@ui5/webcomponents/src/generated/themes/Text.css.ts", "node_modules/@ui5/webcomponents/dist/generated/themes/ExpandableText.css.js", "node_modules/@ui5/webcomponents/src/generated/themes/ExpandableText.css.ts"], "sourcesContent": ["\nfunction $parcel$export(e, n, v, s) {\n  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});\n}\n\n      var $parcel$global = globalThis;\n    var parcelRequire = $parcel$global[\"parcelRequire94c2\"];\nvar parcelRegister = parcelRequire.register;\nparcelRegister(\"i9xPF\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $d3734fa6fadfc02c$export$2e2bcd8739ae039);\n\nvar $lyEkX = parcelRequire(\"lyEkX\");\n\nvar $1AVCi = parcelRequire(\"1AVCi\");\nfunction $d3734fa6fadfc02c$export$2e2bcd8739ae039(injectedProps) {\n    const { className: className, text: text, maxCharacters: maxCharacters, part: part } = injectedProps;\n    return (0, $lyEkX.jsx)((0, $1AVCi.default), {\n        part: part,\n        class: className,\n        text: text,\n        maxCharacters: maxCharacters\n    });\n}\n\n});\nparcelRegister(\"1AVCi\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $1295d8b36d5cb5a0$export$2e2bcd8739ae039);\n\nvar $hAJdp = parcelRequire(\"hAJdp\");\n\nvar $c6CCt = parcelRequire(\"c6CCt\");\n\nvar $bHGHH = parcelRequire(\"bHGHH\");\n\nvar $hzmJ2 = parcelRequire(\"hzmJ2\");\n\nvar $i1AiN = parcelRequire(\"i1AiN\");\n\nvar $24kPe = parcelRequire(\"24kPe\");\n\nvar $d0KFw = parcelRequire(\"d0KFw\");\n\nvar $TUBXI = parcelRequire(\"TUBXI\");\n\nvar $lHZ6T = parcelRequire(\"lHZ6T\");\n\nvar $IaA5D = parcelRequire(\"IaA5D\");\nvar $1295d8b36d5cb5a0$var$__decorate = undefined && undefined.__decorate || function(decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar $1295d8b36d5cb5a0$var$ExpandableText_1;\n/**\n * @class\n *\n * ### Overview\n *\n * The `ui5-expandable-text` component allows displaying a large body of text in a small space. It provides an \"expand/collapse\" functionality, which shows/hides potentially truncated text.\n *\n * ### Usage\n *\n * #### When to use:\n * - To accommodate long texts in limited space, for example in list items, table cell texts, or forms\n *\n * #### When not to use:\n * - The content is critical for the user. In this case use short descriptions that can fit in\n * - Strive to provide short and meaningful texts to avoid excessive number of \"Show More\" links on the page\n *\n * ### Responsive Behavior\n *\n * On phones, if the component is configured to display the full text in a popover, the popover will appear in full screen.\n *\n * ### ES6 Module Import\n *\n * `import \"@ui5/webcomponents/dist/ExpandableText\";`\n *\n * @constructor\n * @extends UI5Element\n * @public\n * @since 2.6.0\n */ let $1295d8b36d5cb5a0$var$ExpandableText = $1295d8b36d5cb5a0$var$ExpandableText_1 = class ExpandableText extends (0, $hAJdp.default) {\n    constructor(){\n        super(...arguments);\n        /**\n         * Maximum number of characters to be displayed initially. If the text length exceeds this limit, the text will be truncated with an ellipsis, and the \"More\" link will be displayed.\n         * @default 100\n         * @public\n         */ this.maxCharacters = 100;\n        /**\n         * Determines how the full text will be displayed.\n         * @default \"InPlace\"\n         * @public\n         */ this.overflowMode = \"InPlace\";\n        /**\n         * Specifies if an empty indicator should be displayed when there is no text.\n         * @default \"Off\"\n         * @public\n         */ this.emptyIndicatorMode = \"Off\";\n        this._expanded = false;\n    }\n    getFocusDomRef() {\n        if (this._usePopover) return this.shadowRoot?.querySelector(\"[ui5-responsive-popover]\");\n        return this.shadowRoot?.querySelector(\"[ui5-link]\");\n    }\n    get _displayedText() {\n        if (this._expanded && !this._usePopover) return this.text;\n        return this.text?.substring(0, this.maxCharacters);\n    }\n    get _maxCharactersExceeded() {\n        return (this.text?.length || 0) > this.maxCharacters;\n    }\n    get _usePopover() {\n        return this.overflowMode === (0, $d0KFw.default).Popover;\n    }\n    get _ellipsisText() {\n        if (this._expanded && !this._usePopover) return \" \";\n        return \"... \";\n    }\n    get _textForToggle() {\n        return this._expanded ? $1295d8b36d5cb5a0$var$ExpandableText_1.i18nBundle.getText((0, $TUBXI.EXPANDABLE_TEXT_SHOW_LESS)) : $1295d8b36d5cb5a0$var$ExpandableText_1.i18nBundle.getText((0, $TUBXI.EXPANDABLE_TEXT_SHOW_MORE));\n    }\n    get _closeButtonText() {\n        return $1295d8b36d5cb5a0$var$ExpandableText_1.i18nBundle.getText((0, $TUBXI.EXPANDABLE_TEXT_CLOSE));\n    }\n    get _accessibilityAttributesForToggle() {\n        if (this._usePopover) return {\n            expanded: this._expanded,\n            hasPopup: \"dialog\"\n        };\n        return {\n            expanded: this._expanded\n        };\n    }\n    get _accessibleNameForToggle() {\n        if (this._usePopover) return this._expanded ? $1295d8b36d5cb5a0$var$ExpandableText_1.i18nBundle.getText((0, $TUBXI.EXPANDABLE_TEXT_SHOW_LESS_POPOVER_ARIA_LABEL)) : $1295d8b36d5cb5a0$var$ExpandableText_1.i18nBundle.getText((0, $TUBXI.EXPANDABLE_TEXT_SHOW_MORE_POPOVER_ARIA_LABEL));\n        return undefined;\n    }\n    _handlePopoverClose() {\n        if (!(0, $24kPe.isPhone)()) this._expanded = false;\n    }\n    _handleToggleClick() {\n        this._expanded = !this._expanded;\n    }\n    _handleCloseButtonClick(e) {\n        this._expanded = false;\n        e.stopPropagation();\n    }\n};\n$1295d8b36d5cb5a0$var$__decorate([\n    (0, $bHGHH.default)()\n], $1295d8b36d5cb5a0$var$ExpandableText.prototype, \"text\", void 0);\n$1295d8b36d5cb5a0$var$__decorate([\n    (0, $bHGHH.default)({\n        type: Number\n    })\n], $1295d8b36d5cb5a0$var$ExpandableText.prototype, \"maxCharacters\", void 0);\n$1295d8b36d5cb5a0$var$__decorate([\n    (0, $bHGHH.default)()\n], $1295d8b36d5cb5a0$var$ExpandableText.prototype, \"overflowMode\", void 0);\n$1295d8b36d5cb5a0$var$__decorate([\n    (0, $bHGHH.default)()\n], $1295d8b36d5cb5a0$var$ExpandableText.prototype, \"emptyIndicatorMode\", void 0);\n$1295d8b36d5cb5a0$var$__decorate([\n    (0, $bHGHH.default)({\n        type: Boolean\n    })\n], $1295d8b36d5cb5a0$var$ExpandableText.prototype, \"_expanded\", void 0);\n$1295d8b36d5cb5a0$var$__decorate([\n    (0, $hzmJ2.default)(\"@ui5/webcomponents\")\n], $1295d8b36d5cb5a0$var$ExpandableText, \"i18nBundle\", void 0);\n$1295d8b36d5cb5a0$var$ExpandableText = $1295d8b36d5cb5a0$var$ExpandableText_1 = $1295d8b36d5cb5a0$var$__decorate([\n    (0, $c6CCt.default)({\n        tag: \"ui5-expandable-text\",\n        renderer: (0, $i1AiN.default),\n        styles: (0, $IaA5D.default),\n        template: (0, $lHZ6T.default)\n    })\n], $1295d8b36d5cb5a0$var$ExpandableText);\n$1295d8b36d5cb5a0$var$ExpandableText.define();\nvar $1295d8b36d5cb5a0$export$2e2bcd8739ae039 = $1295d8b36d5cb5a0$var$ExpandableText;\n\n});\nparcelRegister(\"d0KFw\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $978f79e8630ab9e6$export$2e2bcd8739ae039);\n/**\n * Overflow Mode.\n * @public\n */ var $978f79e8630ab9e6$var$ExpandableTextOverflowMode;\n(function(ExpandableTextOverflowMode) {\n    /**\n     * Overflowing text is appended in-place.\n     * @public\n     */ ExpandableTextOverflowMode[\"InPlace\"] = \"InPlace\";\n    /**\n     * Full text is displayed in a popover.\n     * @public\n     */ ExpandableTextOverflowMode[\"Popover\"] = \"Popover\";\n})($978f79e8630ab9e6$var$ExpandableTextOverflowMode || ($978f79e8630ab9e6$var$ExpandableTextOverflowMode = {}));\nvar $978f79e8630ab9e6$export$2e2bcd8739ae039 = $978f79e8630ab9e6$var$ExpandableTextOverflowMode;\n\n});\n\nparcelRegister(\"lHZ6T\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $fcdd0457d3b4ca5c$export$2e2bcd8739ae039);\n\nvar $lyEkX = parcelRequire(\"lyEkX\");\n\nvar $89OZf = parcelRequire(\"89OZf\");\n\nvar $jiNAP = parcelRequire(\"jiNAP\");\n\nvar $iQEdW = parcelRequire(\"iQEdW\");\n\nvar $e9Sj6 = parcelRequire(\"e9Sj6\");\nfunction $fcdd0457d3b4ca5c$export$2e2bcd8739ae039() {\n    return (0, $lyEkX.jsxs)(\"div\", {\n        children: [\n            (0, $lyEkX.jsx)((0, $89OZf.default), {\n                class: \"ui5-exp-text-text\",\n                emptyIndicatorMode: this.emptyIndicatorMode,\n                children: this._displayedText\n            }),\n            this._maxCharactersExceeded && (0, $lyEkX.jsxs)((0, $lyEkX.Fragment), {\n                children: [\n                    (0, $lyEkX.jsx)(\"span\", {\n                        class: \"ui5-exp-text-ellipsis\",\n                        children: this._ellipsisText\n                    }),\n                    (0, $lyEkX.jsx)((0, $jiNAP.default), {\n                        id: \"toggle\",\n                        class: \"ui5-exp-text-toggle\",\n                        accessibleRole: \"Button\",\n                        accessibleName: this._accessibleNameForToggle,\n                        accessibilityAttributes: this._accessibilityAttributesForToggle,\n                        onClick: this._handleToggleClick,\n                        children: this._textForToggle\n                    }),\n                    this._usePopover && (0, $lyEkX.jsxs)((0, $e9Sj6.default), {\n                        open: this._expanded,\n                        opener: \"toggle\",\n                        accessibleNameRef: \"popover-text\",\n                        contentOnlyOnDesktop: true,\n                        _hideHeader: true,\n                        class: \"ui5-exp-text-popover\",\n                        onClose: this._handlePopoverClose,\n                        children: [\n                            (0, $lyEkX.jsx)((0, $89OZf.default), {\n                                id: \"popover-text\",\n                                children: this.text\n                            }),\n                            (0, $lyEkX.jsx)(\"div\", {\n                                slot: \"footer\",\n                                class: \"ui5-exp-text-footer\",\n                                children: (0, $lyEkX.jsx)((0, $iQEdW.default), {\n                                    design: \"Transparent\",\n                                    onClick: this._handleCloseButtonClick,\n                                    children: this._closeButtonText\n                                })\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n}\n\n});\nparcelRegister(\"89OZf\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $5f06b6e729921e17$export$2e2bcd8739ae039);\n\nvar $hAJdp = parcelRequire(\"hAJdp\");\n\nvar $c6CCt = parcelRequire(\"c6CCt\");\n\nvar $bHGHH = parcelRequire(\"bHGHH\");\n\nvar $aaoB5 = parcelRequire(\"aaoB5\");\n\nvar $i1AiN = parcelRequire(\"i1AiN\");\n\nvar $jlowE = parcelRequire(\"jlowE\");\n\nvar $hzmJ2 = parcelRequire(\"hzmJ2\");\n\nvar $6T3KG = parcelRequire(\"6T3KG\");\n\nvar $l7y0l = parcelRequire(\"l7y0l\");\n\nvar $bCP4l = parcelRequire(\"bCP4l\");\n\nvar $TUBXI = parcelRequire(\"TUBXI\");\n\nvar $7vw7Y = parcelRequire(\"7vw7Y\");\nvar $5f06b6e729921e17$var$__decorate = undefined && undefined.__decorate || function(decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar $5f06b6e729921e17$var$Text_1;\n/**\n * @class\n *\n * <h3>Overview</h3>\n *\n * The `ui5-text` component displays text that can be used in any content area of an application.\n *\n * <h3>Usage</h3>\n *\n * - Use the `ui5-text` if you want to display text inside a form, table, or any other content area.\n * - Do not use the `ui5-text` if you need to reference input type of components (use ui5-label).\n *\n * <h3>Responsive behavior</h3>\n *\n * The `ui5-text` component is fully adaptive to all screen sizes.\n * By default, the text will wrap when the space is not enough.\n * In addition, the component supports truncation via the <code>max-lines</code> property,\n * by defining the number of lines the text should wrap before start truncating.\n *\n * <h3>ES6 Module Import</h3>\n *\n * <code>import \"@ui5/webcomponents/dist/Text\";</code>\n *\n * @constructor\n * @extends UI5Element\n * @public\n * @since 2.0.0\n */ let $5f06b6e729921e17$var$Text = $5f06b6e729921e17$var$Text_1 = class Text extends (0, $hAJdp.default) {\n    constructor(){\n        super(...arguments);\n        /**\n         * Defines the number of lines the text should wrap before it truncates.\n         * @default Infinity\n         * @public\n         */ this.maxLines = Infinity;\n        /**\n         * Specifies if an empty indicator should be displayed when there is no text.\n         * @default \"Off\"\n         * @since 2.2.0\n         * @public\n         */ this.emptyIndicatorMode = \"Off\";\n    }\n    onBeforeRendering() {\n        this.style.setProperty((0, $jlowE.getScopedVarName)(\"--_ui5_text_max_lines\"), `${this.maxLines}`);\n    }\n    get hasText() {\n        return (0, $6T3KG.default)(this.text);\n    }\n    get _renderEmptyIndicator() {\n        return !this.hasText && this.emptyIndicatorMode === (0, $l7y0l.default).On;\n    }\n    get _emptyIndicatorAriaLabel() {\n        return $5f06b6e729921e17$var$Text_1.i18nBundle.getText((0, $TUBXI.EMPTY_INDICATOR_ACCESSIBLE_TEXT));\n    }\n    get _emptyIndicatorSymbol() {\n        return $5f06b6e729921e17$var$Text_1.i18nBundle.getText((0, $TUBXI.EMPTY_INDICATOR_SYMBOL));\n    }\n};\n$5f06b6e729921e17$var$__decorate([\n    (0, $bHGHH.default)({\n        type: Number\n    })\n], $5f06b6e729921e17$var$Text.prototype, \"maxLines\", void 0);\n$5f06b6e729921e17$var$__decorate([\n    (0, $bHGHH.default)()\n], $5f06b6e729921e17$var$Text.prototype, \"emptyIndicatorMode\", void 0);\n$5f06b6e729921e17$var$__decorate([\n    (0, $aaoB5.default)({\n        type: Node,\n        \"default\": true\n    })\n], $5f06b6e729921e17$var$Text.prototype, \"text\", void 0);\n$5f06b6e729921e17$var$__decorate([\n    (0, $hzmJ2.default)(\"@ui5/webcomponents\")\n], $5f06b6e729921e17$var$Text, \"i18nBundle\", void 0);\n$5f06b6e729921e17$var$Text = $5f06b6e729921e17$var$Text_1 = $5f06b6e729921e17$var$__decorate([\n    (0, $c6CCt.default)({\n        tag: \"ui5-text\",\n        renderer: (0, $i1AiN.default),\n        template: (0, $bCP4l.default),\n        styles: $7vw7Y.default\n    })\n], $5f06b6e729921e17$var$Text);\n$5f06b6e729921e17$var$Text.define();\nvar $5f06b6e729921e17$export$2e2bcd8739ae039 = $5f06b6e729921e17$var$Text;\n\n});\nparcelRegister(\"l7y0l\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $f604a10420b21ab3$export$2e2bcd8739ae039);\n/**\n * Empty Indicator Mode.\n * @public\n */ var $f604a10420b21ab3$var$TextEmptyIndicatorMode;\n(function(TextEmptyIndicatorMode) {\n    /**\n     * Empty indicator is never rendered.\n     * @public\n     */ TextEmptyIndicatorMode[\"Off\"] = \"Off\";\n    /**\n     * Empty indicator is rendered always when the component's content is empty.\n     * @public\n     */ TextEmptyIndicatorMode[\"On\"] = \"On\";\n})($f604a10420b21ab3$var$TextEmptyIndicatorMode || ($f604a10420b21ab3$var$TextEmptyIndicatorMode = {}));\nvar $f604a10420b21ab3$export$2e2bcd8739ae039 = $f604a10420b21ab3$var$TextEmptyIndicatorMode;\n\n});\n\nparcelRegister(\"bCP4l\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $876adb9214f850e8$export$2e2bcd8739ae039);\n\nvar $lyEkX = parcelRequire(\"lyEkX\");\nfunction $876adb9214f850e8$export$2e2bcd8739ae039() {\n    return (0, $lyEkX.jsx)((0, $lyEkX.Fragment), {\n        children: (0, $lyEkX.jsx)(\"span\", {\n            children: this._renderEmptyIndicator ? (0, $lyEkX.jsxs)((0, $lyEkX.Fragment), {\n                children: [\n                    (0, $lyEkX.jsx)(\"span\", {\n                        className: \"empty-indicator\",\n                        \"aria-hidden\": \"true\",\n                        children: this._emptyIndicatorSymbol\n                    }),\n                    (0, $lyEkX.jsx)(\"span\", {\n                        className: \"empty-indicator-aria-label\",\n                        children: this._emptyIndicatorAriaLabel\n                    })\n                ]\n            }) : (0, $lyEkX.jsx)(\"slot\", {})\n        })\n    });\n}\n\n});\n\nparcelRegister(\"7vw7Y\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $577459857ec0804f$export$2e2bcd8739ae039);\n\nvar $yeBrr = parcelRequire(\"yeBrr\");\n\nvar $5ZfjE = parcelRequire(\"5ZfjE\");\n\nvar $fW3zH = parcelRequire(\"fW3zH\");\n(0, $yeBrr.registerThemePropertiesLoader)(\"@ui5/webcomponents-theming\", \"sap_horizon\", async ()=>(0, $5ZfjE.default));\n(0, $yeBrr.registerThemePropertiesLoader)(\"@ui5/webcomponents\", \"sap_horizon\", async ()=>(0, $fW3zH.default));\nvar $577459857ec0804f$export$2e2bcd8739ae039 = `:host{max-width:100%;font-size:var(--sapFontSize);font-family:var(--sapFontFamily);color:var(--sapTextColor);line-height:normal;cursor:text;overflow:hidden}:host([max-lines=\"1\"]){display:inline-block;text-overflow:ellipsis;white-space:nowrap}:host(:not([max-lines=\"1\"])){display:-webkit-box;-webkit-line-clamp:var(--_ui5-v2-11-0_text_max_lines);line-clamp:var(--_ui5-v2-11-0_text_max_lines);-webkit-box-orient:vertical;white-space:normal;word-wrap:break-word}.empty-indicator-aria-label{position:absolute!important;clip:rect(1px,1px,1px,1px);user-select:none;left:0;top:0;font-size:0}\n`;\n\n});\n\n\n\nparcelRegister(\"IaA5D\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $084c515c39094a3a$export$2e2bcd8739ae039);\n\nvar $yeBrr = parcelRequire(\"yeBrr\");\n\nvar $5ZfjE = parcelRequire(\"5ZfjE\");\n\nvar $fW3zH = parcelRequire(\"fW3zH\");\n(0, $yeBrr.registerThemePropertiesLoader)(\"@ui5/webcomponents-theming\", \"sap_horizon\", async ()=>(0, $5ZfjE.default));\n(0, $yeBrr.registerThemePropertiesLoader)(\"@ui5/webcomponents\", \"sap_horizon\", async ()=>(0, $fW3zH.default));\nvar $084c515c39094a3a$export$2e2bcd8739ae039 = `:host{display:inline-block;font-family:var(--sapFontFamily);font-size:var(--sapFontSize);color:var(--sapTextColor)}:host([hidden]){display:none}.ui5-exp-text-text{display:inline}.ui5-exp-text-text,.ui5-exp-text-toggle{font-family:inherit;font-size:inherit}.ui5-exp-text-text,.ui5-exp-text-ellipsis{color:inherit}.ui5-exp-text-popover::part(content){padding-inline:1rem}.ui5-exp-text-footer{width:100%;display:flex;align-items:center;justify-content:flex-end}\n`;\n\n});\n\n\n\n\n//# sourceMappingURL=ListItemStandardExpandableTextTemplate.1c02d112.js.map\n", "import { jsx as _jsx } from \"@ui5/webcomponents-base/jsx-runtime\";\nimport ExpandableText from \"../ExpandableText.js\";\n/**\n * Provides a template for rendering text with the ExpandableText component\n * when wrappingType is set to \"Normal\".\n *\n * @param {object} injectedProps - The configuration options for the expandable text\n * @returns {JSX.Element} The rendered ExpandableText component\n */\nexport default function ListItemStandardExpandableTextTemplate(injectedProps) {\n    const { className, text, maxCharacters, part } = injectedProps;\n    return (_jsx(ExpandableText, { part: part, class: className, text: text, maxCharacters: maxCharacters }));\n}\n//# sourceMappingURL=ListItemStandardExpandableTextTemplate.js.map", "import ExpandableText from \"../ExpandableText.js\";\nimport type ListItemStandard from \"../ListItemStandard.js\";\nimport type { ExpandableTextTemplateParams } from \"../types/ExpandableTextTemplateParams.js\";\n\n/**\n * Provides a template for rendering text with the ExpandableText component\n * when wrappingType is set to \"Normal\".\n *\n * @param {object} injectedProps - The configuration options for the expandable text\n * @returns {JSX.Element} The rendered ExpandableText component\n */\nexport default function ListItemStandardExpandableTextTemplate(\n\tthis: ListItemStandard,\n\tinjectedProps: ExpandableTextTemplateParams\n): JSX.Element {\n\tconst {\n\t\tclassName,\n\t\ttext,\n\t\tmaxCharacters,\n\t\tpart\n\t} = injectedProps;\n\n\treturn (\n\t\t<ExpandableText\n\t\t\tpart={part}\n\t\t\tclass={className}\n\t\t\ttext={text}\n\t\t\tmaxCharacters={maxCharacters}\n\t\t/>\n\t);\n}\n", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar ExpandableText_1;\nimport UI5Element from \"@ui5/webcomponents-base/dist/UI5Element.js\";\nimport customElement from \"@ui5/webcomponents-base/dist/decorators/customElement.js\";\nimport property from \"@ui5/webcomponents-base/dist/decorators/property.js\";\nimport i18n from \"@ui5/webcomponents-base/dist/decorators/i18n.js\";\nimport jsxRender from \"@ui5/webcomponents-base/dist/renderer/JsxRenderer.js\";\nimport { isPhone } from \"@ui5/webcomponents-base/dist/Device.js\";\nimport ExpandableTextOverflowMode from \"./types/ExpandableTextOverflowMode.js\";\nimport { EXPANDABLE_TEXT_SHOW_LESS, EXPANDABLE_TEXT_SHOW_MORE, EXPANDABLE_TEXT_CLOSE, EXPANDABLE_TEXT_SHOW_LESS_POPOVER_ARIA_LABEL, EXPANDABLE_TEXT_SHOW_MORE_POPOVER_ARIA_LABEL, } from \"./generated/i18n/i18n-defaults.js\";\n// Template\nimport ExpandableTextTemplate from \"./ExpandableTextTemplate.js\";\n// Styles\nimport ExpandableTextCss from \"./generated/themes/ExpandableText.css.js\";\n/**\n * @class\n *\n * ### Overview\n *\n * The `ui5-expandable-text` component allows displaying a large body of text in a small space. It provides an \"expand/collapse\" functionality, which shows/hides potentially truncated text.\n *\n * ### Usage\n *\n * #### When to use:\n * - To accommodate long texts in limited space, for example in list items, table cell texts, or forms\n *\n * #### When not to use:\n * - The content is critical for the user. In this case use short descriptions that can fit in\n * - Strive to provide short and meaningful texts to avoid excessive number of \"Show More\" links on the page\n *\n * ### Responsive Behavior\n *\n * On phones, if the component is configured to display the full text in a popover, the popover will appear in full screen.\n *\n * ### ES6 Module Import\n *\n * `import \"@ui5/webcomponents/dist/ExpandableText\";`\n *\n * @constructor\n * @extends UI5Element\n * @public\n * @since 2.6.0\n */\nlet ExpandableText = ExpandableText_1 = class ExpandableText extends UI5Element {\n    constructor() {\n        super(...arguments);\n        /**\n         * Maximum number of characters to be displayed initially. If the text length exceeds this limit, the text will be truncated with an ellipsis, and the \"More\" link will be displayed.\n         * @default 100\n         * @public\n         */\n        this.maxCharacters = 100;\n        /**\n         * Determines how the full text will be displayed.\n         * @default \"InPlace\"\n         * @public\n         */\n        this.overflowMode = \"InPlace\";\n        /**\n         * Specifies if an empty indicator should be displayed when there is no text.\n         * @default \"Off\"\n         * @public\n         */\n        this.emptyIndicatorMode = \"Off\";\n        this._expanded = false;\n    }\n    getFocusDomRef() {\n        if (this._usePopover) {\n            return this.shadowRoot?.querySelector(\"[ui5-responsive-popover]\");\n        }\n        return this.shadowRoot?.querySelector(\"[ui5-link]\");\n    }\n    get _displayedText() {\n        if (this._expanded && !this._usePopover) {\n            return this.text;\n        }\n        return this.text?.substring(0, this.maxCharacters);\n    }\n    get _maxCharactersExceeded() {\n        return (this.text?.length || 0) > this.maxCharacters;\n    }\n    get _usePopover() {\n        return this.overflowMode === ExpandableTextOverflowMode.Popover;\n    }\n    get _ellipsisText() {\n        if (this._expanded && !this._usePopover) {\n            return \" \";\n        }\n        return \"... \";\n    }\n    get _textForToggle() {\n        return this._expanded ? ExpandableText_1.i18nBundle.getText(EXPANDABLE_TEXT_SHOW_LESS) : ExpandableText_1.i18nBundle.getText(EXPANDABLE_TEXT_SHOW_MORE);\n    }\n    get _closeButtonText() {\n        return ExpandableText_1.i18nBundle.getText(EXPANDABLE_TEXT_CLOSE);\n    }\n    get _accessibilityAttributesForToggle() {\n        if (this._usePopover) {\n            return {\n                expanded: this._expanded,\n                hasPopup: \"dialog\",\n            };\n        }\n        return {\n            expanded: this._expanded,\n        };\n    }\n    get _accessibleNameForToggle() {\n        if (this._usePopover) {\n            return this._expanded ? ExpandableText_1.i18nBundle.getText(EXPANDABLE_TEXT_SHOW_LESS_POPOVER_ARIA_LABEL) : ExpandableText_1.i18nBundle.getText(EXPANDABLE_TEXT_SHOW_MORE_POPOVER_ARIA_LABEL);\n        }\n        return undefined;\n    }\n    _handlePopoverClose() {\n        if (!isPhone()) {\n            this._expanded = false;\n        }\n    }\n    _handleToggleClick() {\n        this._expanded = !this._expanded;\n    }\n    _handleCloseButtonClick(e) {\n        this._expanded = false;\n        e.stopPropagation();\n    }\n};\n__decorate([\n    property()\n], ExpandableText.prototype, \"text\", void 0);\n__decorate([\n    property({ type: Number })\n], ExpandableText.prototype, \"maxCharacters\", void 0);\n__decorate([\n    property()\n], ExpandableText.prototype, \"overflowMode\", void 0);\n__decorate([\n    property()\n], ExpandableText.prototype, \"emptyIndicatorMode\", void 0);\n__decorate([\n    property({ type: Boolean })\n], ExpandableText.prototype, \"_expanded\", void 0);\n__decorate([\n    i18n(\"@ui5/webcomponents\")\n], ExpandableText, \"i18nBundle\", void 0);\nExpandableText = ExpandableText_1 = __decorate([\n    customElement({\n        tag: \"ui5-expandable-text\",\n        renderer: jsxRender,\n        styles: ExpandableTextCss,\n        template: ExpandableTextTemplate,\n    })\n], ExpandableText);\nExpandableText.define();\nexport default ExpandableText;\n//# sourceMappingURL=ExpandableText.js.map", "import UI5Element from \"@ui5/webcomponents-base/dist/UI5Element.js\";\nimport customElement from \"@ui5/webcomponents-base/dist/decorators/customElement.js\";\nimport property from \"@ui5/webcomponents-base/dist/decorators/property.js\";\nimport i18n from \"@ui5/webcomponents-base/dist/decorators/i18n.js\";\nimport jsxRender from \"@ui5/webcomponents-base/dist/renderer/JsxRenderer.js\";\nimport type I18nBundle from \"@ui5/webcomponents-base/dist/i18nBundle.js\";\nimport { isPhone } from \"@ui5/webcomponents-base/dist/Device.js\";\nimport type { UI5CustomEvent } from \"@ui5/webcomponents-base\";\nimport type { LinkAccessibilityAttributes } from \"./Link.js\";\nimport ExpandableTextOverflowMode from \"./types/ExpandableTextOverflowMode.js\";\nimport type But<PERSON> from \"./Button.js\";\nimport type TextEmptyIndicatorMode from \"./types/TextEmptyIndicatorMode.js\";\nimport {\n\tEXPANDABLE_TEXT_SHOW_LESS,\n\tEXPANDABLE_TEXT_SHOW_MORE,\n\tEXPANDABLE_TEXT_CLOSE,\n\tEXPANDABLE_TEXT_SHOW_LESS_POPOVER_ARIA_LABEL,\n\tEXPANDABLE_TEXT_SHOW_MORE_POPOVER_ARIA_LABEL,\n} from \"./generated/i18n/i18n-defaults.js\";\n\n// Template\nimport ExpandableTextTemplate from \"./ExpandableTextTemplate.js\";\n\n// Styles\nimport ExpandableTextCss from \"./generated/themes/ExpandableText.css.js\";\n\n/**\n * @class\n *\n * ### Overview\n *\n * The `ui5-expandable-text` component allows displaying a large body of text in a small space. It provides an \"expand/collapse\" functionality, which shows/hides potentially truncated text.\n *\n * ### Usage\n *\n * #### When to use:\n * - To accommodate long texts in limited space, for example in list items, table cell texts, or forms\n *\n * #### When not to use:\n * - The content is critical for the user. In this case use short descriptions that can fit in\n * - Strive to provide short and meaningful texts to avoid excessive number of \"Show More\" links on the page\n *\n * ### Responsive Behavior\n *\n * On phones, if the component is configured to display the full text in a popover, the popover will appear in full screen.\n *\n * ### ES6 Module Import\n *\n * `import \"@ui5/webcomponents/dist/ExpandableText\";`\n *\n * @constructor\n * @extends UI5Element\n * @public\n * @since 2.6.0\n */\n@customElement({\n\ttag: \"ui5-expandable-text\",\n\trenderer: jsxRender,\n\tstyles: ExpandableTextCss,\n\ttemplate: ExpandableTextTemplate,\n})\nclass ExpandableText extends UI5Element {\n\t/**\n\t * Text of the component.\n\t *\n\t * @default undefined\n\t * @public\n\t */\n\t@property()\n\ttext?: string;\n\n\t/**\n\t * Maximum number of characters to be displayed initially. If the text length exceeds this limit, the text will be truncated with an ellipsis, and the \"More\" link will be displayed.\n\t * @default 100\n\t * @public\n\t */\n\t@property({ type: Number })\n\tmaxCharacters = 100;\n\n\t/**\n\t * Determines how the full text will be displayed.\n\t * @default \"InPlace\"\n\t * @public\n\t */\n\t@property()\n\toverflowMode: `${ExpandableTextOverflowMode}` = \"InPlace\"\n\n\t/**\n\t * Specifies if an empty indicator should be displayed when there is no text.\n\t * @default \"Off\"\n\t * @public\n\t */\n\t@property()\n\temptyIndicatorMode: `${TextEmptyIndicatorMode}` = \"Off\";\n\n\t@property({ type: Boolean })\n\t_expanded = false;\n\n\t@i18n(\"@ui5/webcomponents\")\n\tstatic i18nBundle: I18nBundle;\n\n\tgetFocusDomRef(): HTMLElement | undefined {\n\t\tif (this._usePopover) {\n\t\t\treturn this.shadowRoot?.querySelector(\"[ui5-responsive-popover]\") as HTMLElement;\n\t\t}\n\n\t\treturn this.shadowRoot?.querySelector(\"[ui5-link]\") as HTMLElement;\n\t}\n\n\tget _displayedText() {\n\t\tif (this._expanded && !this._usePopover) {\n\t\t\treturn this.text;\n\t\t}\n\n\t\treturn this.text?.substring(0, this.maxCharacters);\n\t}\n\n\tget _maxCharactersExceeded() {\n\t\treturn (this.text?.length || 0) > this.maxCharacters;\n\t}\n\n\tget _usePopover() {\n\t\treturn this.overflowMode === ExpandableTextOverflowMode.Popover;\n\t}\n\n\tget _ellipsisText() {\n\t\tif (this._expanded && !this._usePopover) {\n\t\t\treturn \" \";\n\t\t}\n\n\t\treturn \"... \";\n\t}\n\n\tget _textForToggle() {\n\t\treturn this._expanded ? ExpandableText.i18nBundle.getText(EXPANDABLE_TEXT_SHOW_LESS) : ExpandableText.i18nBundle.getText(EXPANDABLE_TEXT_SHOW_MORE);\n\t}\n\n\tget _closeButtonText() {\n\t\treturn ExpandableText.i18nBundle.getText(EXPANDABLE_TEXT_CLOSE);\n\t}\n\n\tget _accessibilityAttributesForToggle(): LinkAccessibilityAttributes {\n\t\tif (this._usePopover) {\n\t\t\treturn {\n\t\t\t\texpanded: this._expanded,\n\t\t\t\thasPopup: \"dialog\",\n\t\t\t};\n\t\t}\n\n\t\treturn {\n\t\t\texpanded: this._expanded,\n\t\t};\n\t}\n\n\tget _accessibleNameForToggle() {\n\t\tif (this._usePopover) {\n\t\t\treturn this._expanded ? ExpandableText.i18nBundle.getText(EXPANDABLE_TEXT_SHOW_LESS_POPOVER_ARIA_LABEL) : ExpandableText.i18nBundle.getText(EXPANDABLE_TEXT_SHOW_MORE_POPOVER_ARIA_LABEL);\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\t_handlePopoverClose() {\n\t\tif (!isPhone()) {\n\t\t\tthis._expanded = false;\n\t\t}\n\t}\n\n\t_handleToggleClick() {\n\t\tthis._expanded = !this._expanded;\n\t}\n\n\t_handleCloseButtonClick(e: UI5CustomEvent<Button, \"click\">) {\n\t\tthis._expanded = false;\n\t\te.stopPropagation();\n\t}\n}\n\nExpandableText.define();\n\nexport default ExpandableText;\n", "/**\n * Overflow Mode.\n * @public\n */\nvar ExpandableTextOverflowMode;\n(function (ExpandableTextOverflowMode) {\n    /**\n     * Overflowing text is appended in-place.\n     * @public\n     */\n    ExpandableTextOverflowMode[\"InPlace\"] = \"InPlace\";\n    /**\n     * Full text is displayed in a popover.\n     * @public\n     */\n    ExpandableTextOverflowMode[\"Popover\"] = \"Popover\";\n})(ExpandableTextOverflowMode || (ExpandableTextOverflowMode = {}));\nexport default ExpandableTextOverflowMode;\n//# sourceMappingURL=ExpandableTextOverflowMode.js.map", "/**\n * Overflow Mode.\n * @public\n */\nenum ExpandableTextOverflowMode {\n\t/**\n\t * Overflowing text is appended in-place.\n\t * @public\n\t */\n\tInPlace = \"InPlace\",\n\n\t/**\n\t * Full text is displayed in a popover.\n\t * @public\n\t */\n\tPopover = \"Popover\",\n}\n\nexport default ExpandableTextOverflowMode;\n", "import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"@ui5/webcomponents-base/jsx-runtime\";\nimport Text from \"./Text.js\";\nimport Link from \"./Link.js\";\nimport Button from \"./Button.js\";\nimport ResponsivePopover from \"./ResponsivePopover.js\";\nexport default function ExpandableTextTemplate() {\n    return (_jsxs(\"div\", { children: [_jsx(Text, { class: \"ui5-exp-text-text\", emptyIndicatorMode: this.emptyIndicatorMode, children: this._displayedText }), this._maxCharactersExceeded && _jsxs(_Fragment, { children: [_jsx(\"span\", { class: \"ui5-exp-text-ellipsis\", children: this._ellipsisText }), _jsx(Link, { id: \"toggle\", class: \"ui5-exp-text-toggle\", accessibleRole: \"Button\", accessibleName: this._accessibleNameForToggle, accessibilityAttributes: this._accessibilityAttributesForToggle, onClick: this._handleToggleClick, children: this._textForToggle }), this._usePopover &&\n                        _jsxs(ResponsivePopover, { open: this._expanded, opener: \"toggle\", accessibleNameRef: \"popover-text\", contentOnlyOnDesktop: true, _hideHeader: true, class: \"ui5-exp-text-popover\", onClose: this._handlePopoverClose, children: [_jsx(Text, { id: \"popover-text\", children: this.text }), _jsx(\"div\", { slot: \"footer\", class: \"ui5-exp-text-footer\", children: _jsx(Button, { design: \"Transparent\", onClick: this._handleCloseButtonClick, children: this._closeButtonText }) })] })] })] }));\n}\n//# sourceMappingURL=ExpandableTextTemplate.js.map", "import type ExpandableText from \"./ExpandableText.js\";\nimport Text from \"./Text.js\";\nimport Link from \"./Link.js\";\nimport Button from \"./Button.js\";\nimport ResponsivePopover from \"./ResponsivePopover.js\";\n\nexport default function ExpandableTextTemplate(this: ExpandableText) {\n\treturn (\n\t\t<div>\n\t\t\t<Text\n\t\t\t\tclass=\"ui5-exp-text-text\"\n\t\t\t\temptyIndicatorMode={this.emptyIndicatorMode}\n\t\t\t>\n\t\t\t\t{this._displayedText}\n\t\t\t</Text>\n\n\t\t\t{this._maxCharactersExceeded && <>\n\t\t\t\t<span class=\"ui5-exp-text-ellipsis\">{this._ellipsisText}</span>\n\t\t\t\t<Link\n\t\t\t\t\tid=\"toggle\"\n\t\t\t\t\tclass=\"ui5-exp-text-toggle\"\n\t\t\t\t\taccessibleRole=\"Button\"\n\t\t\t\t\taccessibleName={this._accessibleNameForToggle}\n\t\t\t\t\taccessibilityAttributes={this._accessibilityAttributesForToggle}\n\t\t\t\t\tonClick={this._handleToggleClick}\n\t\t\t\t>\n\t\t\t\t\t{this._textForToggle}\n\t\t\t\t</Link>\n\n\t\t\t\t{this._usePopover &&\n\t\t\t\t\t<ResponsivePopover\n\t\t\t\t\t\topen={this._expanded}\n\t\t\t\t\t\topener=\"toggle\"\n\t\t\t\t\t\taccessibleNameRef=\"popover-text\"\n\t\t\t\t\t\tcontentOnlyOnDesktop={true}\n\t\t\t\t\t\t_hideHeader={true}\n\t\t\t\t\t\tclass=\"ui5-exp-text-popover\"\n\t\t\t\t\t\tonClose={this._handlePopoverClose}\n\t\t\t\t\t>\n\t\t\t\t\t\t<Text id=\"popover-text\">{this.text}</Text>\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tslot=\"footer\"\n\t\t\t\t\t\t\tclass=\"ui5-exp-text-footer\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tdesign=\"Transparent\"\n\t\t\t\t\t\t\t\tonClick={this._handleCloseButtonClick}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{this._closeButtonText}\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</ResponsivePopover>\n\t\t\t\t}\n\t\t\t</>\n\t\t\t}\n\t\t</div>\n\t);\n}\n", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar Text_1;\nimport UI5Element from \"@ui5/webcomponents-base/dist/UI5Element.js\";\nimport customElement from \"@ui5/webcomponents-base/dist/decorators/customElement.js\";\nimport property from \"@ui5/webcomponents-base/dist/decorators/property.js\";\nimport slot from \"@ui5/webcomponents-base/dist/decorators/slot.js\";\nimport jsxRenderer from \"@ui5/webcomponents-base/dist/renderer/JsxRenderer.js\";\nimport { getScopedVarName } from \"@ui5/webcomponents-base/dist/CustomElementsScope.js\";\nimport i18n from \"@ui5/webcomponents-base/dist/decorators/i18n.js\";\nimport willShowContent from \"@ui5/webcomponents-base/dist/util/willShowContent.js\";\nimport TextEmptyIndicatorMode from \"./types/TextEmptyIndicatorMode.js\";\n// Template\nimport TextTemplate2 from \"./TextTemplate.js\";\nimport { EMPTY_INDICATOR_SYMBOL, EMPTY_INDICATOR_ACCESSIBLE_TEXT, } from \"./generated/i18n/i18n-defaults.js\";\n// Styles\nimport styles from \"./generated/themes/Text.css.js\";\n/**\n * @class\n *\n * <h3>Overview</h3>\n *\n * The `ui5-text` component displays text that can be used in any content area of an application.\n *\n * <h3>Usage</h3>\n *\n * - Use the `ui5-text` if you want to display text inside a form, table, or any other content area.\n * - Do not use the `ui5-text` if you need to reference input type of components (use ui5-label).\n *\n * <h3>Responsive behavior</h3>\n *\n * The `ui5-text` component is fully adaptive to all screen sizes.\n * By default, the text will wrap when the space is not enough.\n * In addition, the component supports truncation via the <code>max-lines</code> property,\n * by defining the number of lines the text should wrap before start truncating.\n *\n * <h3>ES6 Module Import</h3>\n *\n * <code>import \"@ui5/webcomponents/dist/Text\";</code>\n *\n * @constructor\n * @extends UI5Element\n * @public\n * @since 2.0.0\n */\nlet Text = Text_1 = class Text extends UI5Element {\n    constructor() {\n        super(...arguments);\n        /**\n         * Defines the number of lines the text should wrap before it truncates.\n         * @default Infinity\n         * @public\n         */\n        this.maxLines = Infinity;\n        /**\n         * Specifies if an empty indicator should be displayed when there is no text.\n         * @default \"Off\"\n         * @since 2.2.0\n         * @public\n         */\n        this.emptyIndicatorMode = \"Off\";\n    }\n    onBeforeRendering() {\n        this.style.setProperty(getScopedVarName(\"--_ui5_text_max_lines\"), `${this.maxLines}`);\n    }\n    get hasText() {\n        return willShowContent(this.text);\n    }\n    get _renderEmptyIndicator() {\n        return !this.hasText && this.emptyIndicatorMode === TextEmptyIndicatorMode.On;\n    }\n    get _emptyIndicatorAriaLabel() {\n        return Text_1.i18nBundle.getText(EMPTY_INDICATOR_ACCESSIBLE_TEXT);\n    }\n    get _emptyIndicatorSymbol() {\n        return Text_1.i18nBundle.getText(EMPTY_INDICATOR_SYMBOL);\n    }\n};\n__decorate([\n    property({ type: Number })\n], Text.prototype, \"maxLines\", void 0);\n__decorate([\n    property()\n], Text.prototype, \"emptyIndicatorMode\", void 0);\n__decorate([\n    slot({ type: Node, \"default\": true })\n], Text.prototype, \"text\", void 0);\n__decorate([\n    i18n(\"@ui5/webcomponents\")\n], Text, \"i18nBundle\", void 0);\nText = Text_1 = __decorate([\n    customElement({\n        tag: \"ui5-text\",\n        renderer: jsxRenderer,\n        template: TextTemplate2,\n        styles,\n    })\n], Text);\nText.define();\nexport default Text;\n//# sourceMappingURL=Text.js.map", "import UI5Element from \"@ui5/webcomponents-base/dist/UI5Element.js\";\nimport customElement from \"@ui5/webcomponents-base/dist/decorators/customElement.js\";\nimport property from \"@ui5/webcomponents-base/dist/decorators/property.js\";\nimport slot from \"@ui5/webcomponents-base/dist/decorators/slot.js\";\nimport jsxRenderer from \"@ui5/webcomponents-base/dist/renderer/JsxRenderer.js\";\nimport { getScopedVarName } from \"@ui5/webcomponents-base/dist/CustomElementsScope.js\";\nimport i18n from \"@ui5/webcomponents-base/dist/decorators/i18n.js\";\nimport type I18nBundle from \"@ui5/webcomponents-base/dist/i18nBundle.js\";\nimport willShowContent from \"@ui5/webcomponents-base/dist/util/willShowContent.js\";\nimport TextEmptyIndicatorMode from \"./types/TextEmptyIndicatorMode.js\";\n// Template\nimport TextTemplate2 from \"./TextTemplate.js\";\n\nimport {\n\tEMPTY_INDICATOR_SYMBOL,\n\tEMPTY_INDICATOR_ACCESSIBLE_TEXT,\n} from \"./generated/i18n/i18n-defaults.js\";\n\n// Styles\nimport styles from \"./generated/themes/Text.css.js\";\n\n/**\n * @class\n *\n * <h3>Overview</h3>\n *\n * The `ui5-text` component displays text that can be used in any content area of an application.\n *\n * <h3>Usage</h3>\n *\n * - Use the `ui5-text` if you want to display text inside a form, table, or any other content area.\n * - Do not use the `ui5-text` if you need to reference input type of components (use ui5-label).\n *\n * <h3>Responsive behavior</h3>\n *\n * The `ui5-text` component is fully adaptive to all screen sizes.\n * By default, the text will wrap when the space is not enough.\n * In addition, the component supports truncation via the <code>max-lines</code> property,\n * by defining the number of lines the text should wrap before start truncating.\n *\n * <h3>ES6 Module Import</h3>\n *\n * <code>import \"@ui5/webcomponents/dist/Text\";</code>\n *\n * @constructor\n * @extends UI5Element\n * @public\n * @since 2.0.0\n */\n@customElement({\n\ttag: \"ui5-text\",\n\trenderer: jsxRenderer,\n\ttemplate: TextTemplate2,\n\tstyles,\n})\nclass Text extends UI5Element {\n\t/**\n\t * Defines the number of lines the text should wrap before it truncates.\n\t * @default Infinity\n\t * @public\n\t */\n\t@property({ type: Number })\n\tmaxLines: number = Infinity;\n\n\t/**\n\t * Specifies if an empty indicator should be displayed when there is no text.\n\t * @default \"Off\"\n\t * @since 2.2.0\n\t * @public\n\t */\n\t@property()\n\temptyIndicatorMode: `${TextEmptyIndicatorMode}` = \"Off\";\n\n\t/**\n\t * Defines the text of the component.\n\t * @public\n\t */\n\t@slot({ type: Node, \"default\": true })\n\ttext!: Array<Node>;\n\n\t@i18n(\"@ui5/webcomponents\")\n\tstatic i18nBundle: I18nBundle;\n\n\tonBeforeRendering() {\n\t\tthis.style.setProperty(getScopedVarName(\"--_ui5_text_max_lines\"), `${this.maxLines}`);\n\t}\n\n\tget hasText() {\n\t\treturn willShowContent(this.text);\n\t}\n\n\tget _renderEmptyIndicator() {\n\t\treturn !this.hasText && this.emptyIndicatorMode === TextEmptyIndicatorMode.On;\n\t}\n\n\tget _emptyIndicatorAriaLabel() {\n\t\treturn Text.i18nBundle.getText(EMPTY_INDICATOR_ACCESSIBLE_TEXT);\n\t}\n\n\tget _emptyIndicatorSymbol() {\n\t\treturn Text.i18nBundle.getText(EMPTY_INDICATOR_SYMBOL);\n\t}\n}\n\nText.define();\n\nexport default Text;\n", "/**\n * Empty Indicator Mode.\n * @public\n */\nvar TextEmptyIndicatorMode;\n(function (TextEmptyIndicatorMode) {\n    /**\n     * Empty indicator is never rendered.\n     * @public\n     */\n    TextEmptyIndicatorMode[\"Off\"] = \"Off\";\n    /**\n     * Empty indicator is rendered always when the component's content is empty.\n     * @public\n     */\n    TextEmptyIndicatorMode[\"On\"] = \"On\";\n})(TextEmptyIndicatorMode || (TextEmptyIndicatorMode = {}));\nexport default TextEmptyIndicatorMode;\n//# sourceMappingURL=TextEmptyIndicatorMode.js.map", "/**\n * Empty Indicator Mode.\n * @public\n */\nenum TextEmptyIndicatorMode {\n\t/**\n\t * Empty indicator is never rendered.\n\t * @public\n\t */\n\tOff = \"Off\",\n\n\t/**\n\t * Empty indicator is rendered always when the component's content is empty.\n\t * @public\n\t */\n\tOn = \"On\",\n}\n\nexport default TextEmptyIndicatorMode;\n", "import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"@ui5/webcomponents-base/jsx-runtime\";\nexport default function TextTemplate() {\n    return _jsx(_Fragment, { children: _jsx(\"span\", { children: this._renderEmptyIndicator ?\n                _jsxs(_Fragment, { children: [_jsx(\"span\", { className: \"empty-indicator\", \"aria-hidden\": \"true\", children: this._emptyIndicatorSymbol }), _jsx(\"span\", { className: \"empty-indicator-aria-label\", children: this._emptyIndicatorAriaLabel })] })\n                :\n                    _jsx(\"slot\", {}) }) });\n}\n//# sourceMappingURL=TextTemplate.js.map", "import type Text from \"./Text.js\";\n\nexport default function TextTemplate(this: Text) {\n\treturn <>\n\t\t<span>\n\t\t\t{ this._renderEmptyIndicator ?\n\t\t\t\t<>\n\t\t\t\t\t<span className=\"empty-indicator\" aria-hidden=\"true\">{ this._emptyIndicatorSymbol }</span>\n\t\t\t\t\t<span className=\"empty-indicator-aria-label\">{ this._emptyIndicatorAriaLabel }</span>\n\t\t\t\t</>\n\t\t\t\t:\n\t\t\t\t<slot></slot>\n\t\t\t}\n\t\t</span>\n\t</>;\n}\n", "import { registerThemePropertiesLoader } from \"@ui5/webcomponents-base/dist/asset-registries/Themes.js\";\nimport defaultThemeBase from \"@ui5/webcomponents-theming/dist/generated/themes/sap_horizon/parameters-bundle.css.js\";\nimport defaultTheme from \"./sap_horizon/parameters-bundle.css.js\";\nregisterThemePropertiesLoader(\"@ui5/webcomponents-theming\", \"sap_horizon\", async () => defaultThemeBase);\nregisterThemePropertiesLoader(\"@ui5/webcomponents\", \"sap_horizon\", async () => defaultTheme);\nexport default `:host{max-width:100%;font-size:var(--sapFontSize);font-family:var(--sapFontFamily);color:var(--sapTextColor);line-height:normal;cursor:text;overflow:hidden}:host([max-lines=\"1\"]){display:inline-block;text-overflow:ellipsis;white-space:nowrap}:host(:not([max-lines=\"1\"])){display:-webkit-box;-webkit-line-clamp:var(--_ui5-v2-11-0_text_max_lines);line-clamp:var(--_ui5-v2-11-0_text_max_lines);-webkit-box-orient:vertical;white-space:normal;word-wrap:break-word}.empty-indicator-aria-label{position:absolute!important;clip:rect(1px,1px,1px,1px);user-select:none;left:0;top:0;font-size:0}\n`;\n//# sourceMappingURL=Text.css.js.map", "import { registerThemePropertiesLoader } from \"@ui5/webcomponents-base/dist/asset-registries/Themes.js\";\n\nimport defaultThemeBase from \"@ui5/webcomponents-theming/dist/generated/themes/sap_horizon/parameters-bundle.css.js\";\nimport defaultTheme from \"./sap_horizon/parameters-bundle.css.js\";\n\nregisterThemePropertiesLoader(\"@ui5/webcomponents-theming\", \"sap_horizon\", async () => defaultThemeBase);\nregisterThemePropertiesLoader(\"@ui5/webcomponents\", \"sap_horizon\", async () => defaultTheme);\nexport default `:host{max-width:100%;font-size:var(--sapFontSize);font-family:var(--sapFontFamily);color:var(--sapTextColor);line-height:normal;cursor:text;overflow:hidden}:host([max-lines=\"1\"]){display:inline-block;text-overflow:ellipsis;white-space:nowrap}:host(:not([max-lines=\"1\"])){display:-webkit-box;-webkit-line-clamp:var(--_ui5-v2-11-0_text_max_lines);line-clamp:var(--_ui5-v2-11-0_text_max_lines);-webkit-box-orient:vertical;white-space:normal;word-wrap:break-word}.empty-indicator-aria-label{position:absolute!important;clip:rect(1px,1px,1px,1px);user-select:none;left:0;top:0;font-size:0}\n`", "import { registerThemePropertiesLoader } from \"@ui5/webcomponents-base/dist/asset-registries/Themes.js\";\nimport defaultThemeBase from \"@ui5/webcomponents-theming/dist/generated/themes/sap_horizon/parameters-bundle.css.js\";\nimport defaultTheme from \"./sap_horizon/parameters-bundle.css.js\";\nregisterThemePropertiesLoader(\"@ui5/webcomponents-theming\", \"sap_horizon\", async () => defaultThemeBase);\nregisterThemePropertiesLoader(\"@ui5/webcomponents\", \"sap_horizon\", async () => defaultTheme);\nexport default `:host{display:inline-block;font-family:var(--sapFontFamily);font-size:var(--sapFontSize);color:var(--sapTextColor)}:host([hidden]){display:none}.ui5-exp-text-text{display:inline}.ui5-exp-text-text,.ui5-exp-text-toggle{font-family:inherit;font-size:inherit}.ui5-exp-text-text,.ui5-exp-text-ellipsis{color:inherit}.ui5-exp-text-popover::part(content){padding-inline:1rem}.ui5-exp-text-footer{width:100%;display:flex;align-items:center;justify-content:flex-end}\n`;\n//# sourceMappingURL=ExpandableText.css.js.map", "import { registerThemePropertiesLoader } from \"@ui5/webcomponents-base/dist/asset-registries/Themes.js\";\n\nimport defaultThemeBase from \"@ui5/webcomponents-theming/dist/generated/themes/sap_horizon/parameters-bundle.css.js\";\nimport defaultTheme from \"./sap_horizon/parameters-bundle.css.js\";\n\nregisterThemePropertiesLoader(\"@ui5/webcomponents-theming\", \"sap_horizon\", async () => defaultThemeBase);\nregisterThemePropertiesLoader(\"@ui5/webcomponents\", \"sap_horizon\", async () => defaultTheme);\nexport default `:host{display:inline-block;font-family:var(--sapFontFamily);font-size:var(--sapFontSize);color:var(--sapTextColor)}:host([hidden]){display:none}.ui5-exp-text-text{display:inline}.ui5-exp-text-text,.ui5-exp-text-toggle{font-family:inherit;font-size:inherit}.ui5-exp-text-text,.ui5-exp-text-ellipsis{color:inherit}.ui5-exp-text-popover::part(content){padding-inline:1rem}.ui5-exp-text-footer{width:100%;display:flex;align-items:center;justify-content:flex-end}\n`"], "names": ["$parcel$export", "e", "n", "v", "s", "Object", "defineProperty", "get", "set", "enumerable", "configurable", "parcelRequire", "$parcel$global", "globalThis", "parcelRegister", "register", "module", "exports", "$d3734fa6fadfc02c$export$2e2bcd8739ae039", "$lyEkX", "$1AVCi", "injectedProps", "className", "text", "maxCharacters", "part", "jsx", "default", "class", "$1295d8b36d5cb5a0$export$2e2bcd8739ae039", "$1295d8b36d5cb5a0$var$ExpandableText_1", "$hAJdp", "$c6CCt", "$bHGHH", "$hzmJ2", "$i1AiN", "$24kPe", "$d0KFw", "$TUBXI", "$lHZ6T", "$IaA5D", "$1295d8b36d5cb5a0$var$__decorate", "decorators", "target", "key", "desc", "d", "c", "arguments", "length", "r", "getOwnPropertyDescriptor", "Reflect", "decorate", "i", "$1295d8b36d5cb5a0$var$ExpandableText", "overflowMode", "emptyIndicatorMode", "_expanded", "getFocusDomRef", "_usePopover", "shadowRoot", "querySelector", "_displayedText", "substring", "_maxCharactersExceeded", "Popover", "_ellipsisText", "_textFor<PERSON><PERSON>gle", "i18nBundle", "getText", "EXPANDABLE_TEXT_SHOW_LESS", "EXPANDABLE_TEXT_SHOW_MORE", "_closeButtonText", "EXPANDABLE_TEXT_CLOSE", "_accessibilityAttributesForToggle", "expanded", "<PERSON><PERSON><PERSON><PERSON>", "_accessibleNameForToggle", "EXPANDABLE_TEXT_SHOW_LESS_POPOVER_ARIA_LABEL", "EXPANDABLE_TEXT_SHOW_MORE_POPOVER_ARIA_LABEL", "_handlePopoverClose", "isPhone", "_handleToggleClick", "_handleCloseButtonClick", "stopPropagation", "prototype", "type", "Number", "Boolean", "tag", "renderer", "styles", "template", "define", "$978f79e8630ab9e6$export$2e2bcd8739ae039", "ExpandableTextOverflowMode", "$978f79e8630ab9e6$var$ExpandableTextOverflowMode", "$fcdd0457d3b4ca5c$export$2e2bcd8739ae039", "$89OZf", "$jiNAP", "$iQEdW", "$e9Sj6", "jsxs", "children", "Fragment", "id", "accessibleRole", "accessibleName", "accessibilityAttributes", "onClick", "open", "opener", "accessibleNameRef", "contentOnlyOnDesktop", "_hideHeader", "onClose", "slot", "design", "$5f06b6e729921e17$export$2e2bcd8739ae039", "$5f06b6e729921e17$var$Text_1", "$aaoB5", "$jlowE", "$6T3KG", "$l7y0l", "$bCP4l", "$7vw7Y", "$5f06b6e729921e17$var$__decorate", "$5f06b6e729921e17$var$Text", "maxLines", "Infinity", "onBeforeRendering", "style", "setProperty", "getScopedVarName", "hasText", "_renderEmptyIndicator", "On", "_emptyIndicatorAriaLabel", "EMPTY_INDICATOR_ACCESSIBLE_TEXT", "_emptyIndicatorSymbol", "EMPTY_INDICATOR_SYMBOL", "Node", "$f604a10420b21ab3$export$2e2bcd8739ae039", "TextEmptyIndicatorMode", "$f604a10420b21ab3$var$TextEmptyIndicatorMode", "$876adb9214f850e8$export$2e2bcd8739ae039", "$577459857ec0804f$export$2e2bcd8739ae039", "$yeBrr", "$5ZfjE", "$fW3zH", "registerThemePropertiesLoader", "$084c515c39094a3a$export$2e2bcd8739ae039"], "version": 3, "file": "ListItemStandardExpandableTextTemplate.1c02d112.js.map"}