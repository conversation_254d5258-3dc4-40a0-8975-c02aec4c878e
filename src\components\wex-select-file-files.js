import { LitElement, html, css } from "lit";
import { render } from "lit/html.js";
import { storeInstance } from "store/index.js";
import * as util from "lib/util.ts";

import "@ui5/webcomponents/dist/Icon.js";
import "@ui5/webcomponents/dist/Tree.js";
import "@ui5/webcomponents/dist/TreeItem.js";

class WexSelectFileFiles extends LitElement {
  constructor() {
    super();
    this.state = {};
    this.string = {};
    this.subscription = null;

    this.folder = null;
    this.searchterm = null;
    this.subfolders = null;
  }

  connectedCallback() {
    super.connectedCallback();
    this.subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });
    this.state = storeInstance.state;
    this.string = this.state[this.state.langCode];
    // this.folder = null;
    // this.searchterm = null;
    // this.subfolders = null;
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this.subscription);
  }

  refreshData() {
    var searchQuery = [];
    if (this.state.select_file_dialog_search_term) {
      searchQuery.push(
        "VIEW := TextScoreView  SCORE Description,Content AnyWords('" +
          this.state.select_file_dialog_search_term +
          "') "
      );
    } else {
      searchQuery.push("VIEW := PropertyView ");
    }
    this.searchlang = this.state.select_file_dialog_lang_filter
      ? this.state.select_file_dialog_lang_filter.langCode
      : this.state.default_lang_object.langCode;
    this.searchbranch = this.state.select_file_dialog_branch_filter
      ? this.state.select_file_dialog_branch_filter.name
      : this.state.default_branch_object.name;

    this.asOfDate = "Now";
    //XPathValueDB('//title')
    this.fieldNames =
      " Title, Name, LockOwner,  CreateDate, ActiveProcesses, FileStatus, NumComments, Size, FileId, FolderPath ";

    var folderClause = "";
    var folder = "/";
    // console.log(
    //   "except here it is still set to",
    //   this.state.select_file_dialog_folderitem.collectionPathId
    // );
    if (this.state.select_file_dialog_folderitem) {
      // folder = this.state.select_file_dialog_folderitem.path;
      folder = this.state.select_file_dialog_folderitem.collectionPathId; // why was path broken here?
      if (!folder) {
        folder = this.state.select_file_dialog_folderitem.path || "/";
      }
    }

    if (this.state.select_file_dialog_search_subfolders) {
      folderClause =
        " WHERE InFolderDesc('" +
        folder +
        "') AND NOT (InFolderDesc('/Content/KbContent'))";
    } else {
      folderClause =
        " WHERE InFolder('" +
        folder +
        "') AND NOT (InFolderDesc('/Content/KbContent')) ";
    }

    searchQuery.push(
      "CONTEXT (LanguageCode='" +
        this.searchlang +
        "',BranchName='" +
        this.searchbranch +
        "',AsOfDate='" +
        this.asOfDate +
        "',HeadVersionOnly='true') FOR EACH " +
        folderClause +
        " RETURN " +
        this.fieldNames
    );
    // console.log("refreshing data in select file files", folder);

    storeInstance.dispatch("getFileSelectFiles", searchQuery.join(""));
  }

  myRender() {
    render(this.filesTemplate(), this.shadowRoot);
    this.contextMenu = this.shadowRoot.querySelector("#contextmenu");
  }

  stateChange(state) {
    this.state = state;
    this.string = this.state[this.state.langCode];

    var refreshRquired = false;

    // if (this.searchlang != this.state.select_file_dialog_lang_filter) {
    //   refreshRquired = true;
    //   this.searchlang = this.state.select_file_dialog_lang_filter;
    // }

    // if (this.searchbranch != this.state.select_file_dialog_branch_filter) {
    //   refreshRquired = true;
    //   this.searchbranch = this.state.select_file_dialog_branch_filter;
    // }

    if (this.folder != this.state.select_file_dialog_folderitem) {
      refreshRquired = true;
      this.folder = this.state.select_file_dialog_folderitem;
    }
    if (this.searchterm != this.state.select_file_dialog_search_term) {
      refreshRquired = true;
      this.searchterm = this.state.select_file_dialog_search_term;
    }
    if (this.subfolders != this.state.select_file_dialog_search_subfolders) {
      refreshRquired = true;
      this.subfolders = this.state.select_file_dialog_search_subfolders;
    }
    if (refreshRquired) {
      storeInstance.dispatch("setState", {
        property: "select_file_dialog_files",
        value: null,
      });
      this.refreshData();
    }

    if (this.state.select_file_dialog_files) {
      this._marshellColumns();
      this._marshellRows();
      this.myRender();
    }
  }

  _marshellColumns() {
    //filter for active col headings
    this.columns = this.state.selectfile_dialog_columns.filter(
      function (el) {
        return el.order > 0;
      }.bind(this)
    );
    //sort by column order
    this.columns.sort(function (a, b) {
      if (a.order < b.order) {
        return -1;
      }
      if (a.order > b.order) {
        return 1;
      }
      return 0;
    });
    //localize
    this.columns.map((col) => (col.heading = this.string[col.title]));
  }

  _marshellRows() {
    this.rows = [];
    if (this.state.select_file_dialog_files) {
      this.rows = JSON.parse(
        JSON.stringify(this.state.select_file_dialog_files)
      );
      for (var i = 0; i < this.rows.length; i++) {
        this.rows[i].selected = "";
        if (this.state.select_file_selected_file) {
          if (
            this.rows[i].resLblId ==
            this.state.select_file_selected_file.resLblId
          ) {
            this.rows[i].selected = " rowselected ";
          }
        }
        if (util.isDitamap(this.rows[i])) {
          this.rows[i].mapClass = " ditamap ";
        }
      }
      //apply claimed filter
    }

    this.rows.sort(
      util.columnSort(util.getSortColPropertyName(this.columns, "name"))
    );
  }

  /* Templates */
  filesTemplate() {
    return html`
      <style>
        .rowselected {
          --sapList_Background: var(--row-selected-background);
        }
        #notasks {
          font-style: oblique;
          opacity: 0.5;
          text-align: center;
          margin-top: 50px;
        }
        .datarow {
          cursor: pointer;
        }
        .ditamap {
          font-weight: bold;
        }

        .hide {
          visibility: hidden;
        }
        .sortbuttoncontainer {
          display: flex;
          flex-direction: column;
        }
        .sortbuttoncontainer iron-icon {
          margin-left: 0.25rem;
          --iron-icon-width: 0.9rem;
        }
        iron-icon.asc {
          margin-bottom: -0.6rem;
        }
        iron-icon.desc {
          margin-top: -0.6rem;
        }
        iron-icon.muted {
          color: #ccc;
        }

        .colhead {
          line-height: 1.4rem;
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .iconcontatiner {
          height: 1.4rem;
          overflow: hidden;
        }
        #filescontainer {
          overflow-y: scroll;
          height: 100%;
          max-height: 100%;
        }
      </style>
      <div id="filescontainer">
        <vaadin-context-menu
          id="contextmenu"
          selector=".has-menu"
          @item-selected="${this._contextmenuclicked.bind(this)}"
        >
          <ui5-table
            id="filestable"
            no-data-text="${this.string["_nofiles"]}"
            ?show-no-data="${this.rows.length == 0}"
            sticky-column-header
          >
            ${this.columns.map(
              (col) =>
                html` <ui5-table-column
                  class="foo"
                  slot="columns"
                  popin-text="${col.heading}"
                >
                  <span class="colhead">
                    <span>${col.heading}</span>
                    <span class="sortbuttoncontainer">
                      <span
                        class="iconcontatiner"
                        .colproperty="${col.fieldname}"
                        title="asc"
                        @click="${this._sortColumn.bind(this)}"
                        ><iron-icon
                          class="asc ${col.sort != "asc" ? "muted" : ""}"
                          icon="vaadin:caret-up"
                        ></iron-icon
                      ></span>
                      <span
                        class="iconcontatiner"
                        .colproperty="${col.fieldname}"
                        title="desc"
                        @click="${this._sortColumn.bind(this)}"
                        ><iron-icon
                          class="desc ${col.sort != "desc" ? "muted" : ""}"
                          icon="vaadin:caret-down"
                        ></iron-icon
                      ></span>
                    </span>
                  </span>
                </ui5-table-column>`
            )}
            ${this.rows.map((item) => {
              return html` <ui5-table-row
                class="datarow has-menu ${item.mapClass} ${item.selected}"
                .item="${item}"
                @click="${this._handleRowClicked.bind(this)}"
                @contextmenu="${this._handleRowRightClicked.bind(this)}"
              >
                ${this.columns.map(
                  (col) => html`
                    <ui5-table-cell align="left">
                      <div>&nbsp;${item[col.fieldname]}</div>
                    </ui5-table-cell>
                  `
                )}
              </ui5-table-row>`;
            })}
          </ui5-table>
        </vaadin-context-menu>
      </div>
    `;
  }
  _sortColumn(e) {
    var direction = e.currentTarget.title;
    if (e.currentTarget.colproperty) {
      var cols = util.changeColumnSort(
        e.currentTarget.colproperty,
        direction,
        structuredClone(this.state.selectfile_dialog_columns)
      );
      storeInstance.dispatch("setState", {
        property: "selectfile_dialog_columns",
        value: cols,
      });
    }
  }

  _contextmenuclicked(e) {
    switch (e.detail.value.name) {
      case "properties":
        storeInstance.dispatch(
          "filePropertiesRequest",
          this.state.select_file_selected_file
        );
        break;
      case "highlightfolder":
        storeInstance.dispatch("setState", {
          property: "select_file_highlight_folder",
          value: this.state.select_file_selected_file.folderPath,
        });
        break;
    }
  }

  _handleRowRightClicked(e) {
    //modify conditional context menu items
    var effectiveCap = this.state.wex_user.globalCapability;
    if (e.currentTarget.item) {
      var menuItems = util.buildFileMenu(
        e.currentTarget.item,
        this.state.menus.file_menu,
        "fileselect",
        effectiveCap
      );
      menuItems.map((item) => (item.text = this.string[item.label]));
      this.contextMenu.items = menuItems;

      if (this.state.select_file_selected_file) {
        if (
          this.state.select_file_selected_file.resLblId ==
          e.currentTarget.item.resLblId
        ) {
          return;
        }
      }
      storeInstance.dispatch("setState", {
        property: "select_file_selected_file",
        value: e.currentTarget.item,
      });
    }
  }

  _handleRowClicked(e) {
    console.log("row clicked", e.currentTarget.item);
    if (e.currentTarget.item) {
      if (this.state.select_file_selected_file) {
        if (
          this.state.select_file_selected_file.resLblId ==
          e.currentTarget.item.resLblId
        ) {
          storeInstance.dispatch("setState", {
            property: "select_file_selected_file",
            value: null,
          });
          return;
        }
      }
      storeInstance.dispatch("setState", {
        property: "select_file_selected_file",
        value: e.currentTarget.item,
      });
    }
  }
}

customElements.define("wex-select-file-files", WexSelectFileFiles);
