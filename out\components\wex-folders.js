var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { storeInstance as Store } from "store/index.js";
import "./wex-folder-item.ts";
import "@ui5/webcomponents/dist/Icon.js";
import { customElement, property } from "lit/decorators.js";
let WexFolders = class WexFolders extends LitElement {
    static get styles() {
        return css `
      #foldertree {
        list-style: none;
        padding: 0;
        margin: 0;
      }
    `;
    }
    constructor() {
        super();
        this.selectedfolder = null;
        this.isDraggable = false;
        this.state = null;
        this.string = {};
        this.subscription = null;
        this.isDraggable = false;
    }
    set contextmenu(val) {
        this.contextMenu = val;
        this._prepPaths(this.folders, null, this);
    }
    connectedCallback() {
        super.connectedCallback();
        this.state = Store.state;
        this.subscription = Store.subscribe((state) => {
            this.stateChange(state);
        });
        this.string = this.state[this.state.langCode];
    }
    disconnectedCallback() {
        Store.unsubscribe(this.subscription);
    }
    stateChange(state) {
        this.state = state;
        this.string = this.state[this.state.langCode];
    }
    updated(changedProperties) {
        if (changedProperties.has("selectedfolder")) {
            if (this.folders) {
                this._prepPaths(this.folders, null, this);
            }
        }
    }
    _prepPaths(item, path, c, parent) {
        if (path) {
            item.path = path + item.name;
        }
        else {
            item.path = "";
        }
        if (parent) {
            item.parent = parent;
        }
        item.selected = false;
        //item.open=false;
        if (this.selectedfolder) {
            if (item.id == this.selectedfolder.id) {
                item.selected = true;
                this._openParents(item, c);
            }
        }
        if (item.children) {
            item.children.map((itm) => c._prepPaths(itm, item.path + "/", c, item));
        }
    }
    _openParents(item, c) {
        if (item) {
            item.open = true;
        }
        if (item.parent) {
            c._openParents(item.parent, c);
        }
    }
    handleTreeChange() {
        this._prepPaths(this.folders, null, this);
    }
    render() {
        return html `<ul id="foldertree">
      <wex-folder-item
        id="treerootnode"
        .rootnode="true"
        .contextmenu="${this.contextMenu}"
        .item="${this.folders}"
        .origen="${this.id}"
        .isDraggable="${this.isDraggable}"
        @folderselected=${this.handleTreeChange}
        @folderopenstatus=${this.handleTreeChange}
      >
      </wex-folder-item>
    </ul>`;
    }
};
__decorate([
    property({ type: Object })
], WexFolders.prototype, "selectedfolder", void 0);
__decorate([
    property({ type: Boolean })
], WexFolders.prototype, "isDraggable", void 0);
__decorate([
    property({ type: Object })
], WexFolders.prototype, "folders", void 0);
WexFolders = __decorate([
    customElement("wex-folders")
], WexFolders);
//# sourceMappingURL=wex-folders.js.map