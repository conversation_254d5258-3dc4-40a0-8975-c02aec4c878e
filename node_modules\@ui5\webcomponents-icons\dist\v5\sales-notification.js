import { registerIcon } from "@ui5/webcomponents-base/dist/asset-registries/Icons.js";

const name = "sales-notification";
const pathData = "M422 0q38 0 64 26t26 64v396q0 11-7.5 18.5T486 512q-9 0-18-7l-95-95H90q-38 0-64-26.5T0 320V90q0-38 26-64T90 0h332zM51 320q0 16 11 27t28 11h64v-51h-32q-11 0-18.5-7T96 282t7.5-18.5T122 256h70q5 0 9-4t4-9-4-9-9-4h-26q-26 0-45-19t-19-45q0-23 15-40t37-22V51H90q-17 0-28 11T51 90v230zM461 90q0-17-11-28t-28-11H205v51h25q11 0 18.5 7.5T256 128t-7.5 18.5T230 154h-64q-5 0-8.5 3.5T154 166t3.5 9 8.5 4h26q26 0 45 19t19 45q0 23-14.5 40.5T205 306v52h179q10 0 18 8l59 59V90zm-71 6q11 0 18.5 7.5T416 122t-7.5 18-18.5 7h-44q-11 0-18.5-7t-7.5-18 7.5-18.5T346 96h44zm0 81q11 0 18.5 7t7.5 18-7.5 18.5T390 228h-44q-11 0-18.5-7.5T320 202t7.5-18 18.5-7h44zm0 64q11 0 18.5 7t7.5 18-7.5 18.5T390 292h-44q-11 0-18.5-7.5T320 266t7.5-18 18.5-7h44z";
const ltr = true;
const accData = null;
const collection = "SAP-icons-v5";
const packageName = "@ui5/webcomponents-icons";

registerIcon(name, { pathData, ltr, collection, packageName });

export default "SAP-icons-v5/sales-notification";
export { pathData, ltr, accData };