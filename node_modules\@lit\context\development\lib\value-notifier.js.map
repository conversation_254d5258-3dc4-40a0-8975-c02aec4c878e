{"version": 3, "file": "value-notifier.js", "sourceRoot": "", "sources": ["../../src/lib/value-notifier.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAcH;;;;;;;GAOG;AACH,MAAM,OAAO,aAAa;IAMxB,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IACD,IAAI,KAAK,CAAC,CAAI;QACZ,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAED,QAAQ,CAAC,CAAI,EAAE,KAAK,GAAG,KAAK;QAC1B,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAED,YAAY,YAAgB;QApBT,kBAAa,GAAG,IAAI,GAAG,EAGvC,CAAC;QAuBJ,oBAAe,GAAG,GAAS,EAAE;YAC3B,KAAK,MAAM,CAAC,QAAQ,EAAE,EAAC,QAAQ,EAAC,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxD,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAClC,CAAC;QACH,CAAC,CAAC;QATA,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;QAC5B,CAAC;IACH,CAAC;IAQD,WAAW,CACT,QAA4B,EAC5B,YAAqB,EACrB,SAAmB;QAEnB,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,6CAA6C;YAC7C,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC/B,QAAQ,EAAE,GAAG,EAAE;oBACb,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACtC,CAAC;gBACD,YAAY;aACb,CAAC,CAAC;QACL,CAAC;QACD,MAAM,EAAC,QAAQ,EAAC,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QACrD,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ContextCallback} from './context-request-event.js';\n\n/**\n * A disposer function\n */\ntype Disposer = () => void;\n\ninterface CallbackInfo {\n  disposer: Disposer;\n  consumerHost: Element;\n}\n\n/**\n * A simple class which stores a value, and triggers registered callbacks when\n * the value is changed via its setter.\n *\n * An implementor might use other observable patterns such as MobX or Redux to\n * get behavior like this. But this is a pretty minimal approach that will\n * likely work for a number of use cases.\n */\nexport class ValueNotifier<T> {\n  protected readonly subscriptions = new Map<\n    ContextCallback<T>,\n    CallbackInfo\n  >();\n  private _value!: T;\n  get value(): T {\n    return this._value;\n  }\n  set value(v: T) {\n    this.setValue(v);\n  }\n\n  setValue(v: T, force = false) {\n    const update = force || !Object.is(v, this._value);\n    this._value = v;\n    if (update) {\n      this.updateObservers();\n    }\n  }\n\n  constructor(defaultValue?: T) {\n    if (defaultValue !== undefined) {\n      this.value = defaultValue;\n    }\n  }\n\n  updateObservers = (): void => {\n    for (const [callback, {disposer}] of this.subscriptions) {\n      callback(this._value, disposer);\n    }\n  };\n\n  addCallback(\n    callback: ContextCallback<T>,\n    consumerHost: Element,\n    subscribe?: boolean\n  ): void {\n    if (!subscribe) {\n      // just call the callback once and we're done\n      callback(this.value);\n      return;\n    }\n    if (!this.subscriptions.has(callback)) {\n      this.subscriptions.set(callback, {\n        disposer: () => {\n          this.subscriptions.delete(callback);\n        },\n        consumerHost,\n      });\n    }\n    const {disposer} = this.subscriptions.get(callback)!;\n    callback(this.value, disposer);\n  }\n\n  clearCallbacks(): void {\n    this.subscriptions.clear();\n  }\n}\n"]}