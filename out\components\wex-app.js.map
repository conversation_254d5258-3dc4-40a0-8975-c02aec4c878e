{"version": 3, "file": "wex-app.js", "sourceRoot": "", "sources": ["../../src/components/wex-app.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AACvC,OAAO,EAAE,aAAa,IAAI,KAAK,EAAE,MAAM,gBAAgB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,sCAAsC,CAAC;AAG9D,OAAO,MAAM,MAAM,WAAW,CAAC;AAE/B,sDAAsD;AAEtD,OAAO,iBAAiB,CAAC;AACzB,OAAO,kBAAkB,CAAC;AAC1B,OAAO,qBAAqB,CAAC;AAE7B,OAAO,2BAA2B,CAAC;AACnC,OAAO,4BAA4B,CAAC;AACpC,OAAO,wBAAwB,CAAC;AAChC,OAAO,wBAAwB,CAAC;AAChC,OAAO,yBAAyB,CAAC;AACjC,OAAO,sBAAsB,CAAC;AAC9B,OAAO,oBAAoB,CAAC;AAC5B,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,wCAAwC,CAAC;AAChD,OAAO,kCAAkC,CAAC;AAC1C,8CAA8C;AAC9C,+CAA+C;AAE/C,4CAA4C;AAC5C,OAAO,6BAA6B,CAAC;AACrC,iCAAiC;AACjC,OAAO,kBAAkB,CAAC;AAC1B,6CAA6C;AAC7C,OAAO,8BAA8B,CAAC;AACtC,wCAAwC;AACxC,OAAO,yBAAyB,CAAC;AACjC,mCAAmC;AACnC,OAAO,oBAAoB,CAAC;AAC5B,wDAAwD;AACxD,OAAO,yCAAyC,CAAC;AAEjD,yBAAyB;AACzB,OAAO,8BAA8B,CAAC;AACtC,qCAAqC;AACrC,OAAO,iCAAiC,CAAC;AACzC,wCAAwC;AACxC,OAAO,mCAAmC,CAAC;AAC3C,0CAA0C;AAC1C,OAAO,0CAA0C,CAAC;AAClD,iDAAiD;AACjD,OAAO,iCAAiC,CAAC;AACzC,wCAAwC;AAExC,0CAA0C;AAC1C,OAAO,wBAAwB,CAAC;AAChC,+BAA+B;AAE/B,OAAO,+BAA+B,CAAC;AACvC,OAAO,yCAAyC,CAAC;AACjD,OAAO,kCAAkC,CAAC;AAC1C,OAAO,0CAA0C,CAAC;AAClD,OAAO,iCAAiC,CAAC;AACzC,OAAO,iCAAiC,CAAC;AACzC,OAAO,sBAAsB,CAAC;AAC9B,OAAO,uBAAuB,CAAC;AAC/B,OAAO,uBAAuB,CAAC;AAC/B,OAAO,iCAAiC,CAAC;AACzC,OAAO,oCAAoC,CAAC;AAC5C,OAAO,gCAAgC,CAAC;AACxC,OAAO,+BAA+B,CAAC;AACvC,OAAO,kCAAkC,CAAC;AAC1C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,6BAA6B,CAAC;AACrC,OAAO,6BAA6B,CAAC;AACrC,OAAO,8BAA8B,CAAC;AACtC,OAAO,iCAAiC,CAAC;AAEzC,OAAO,gBAAgB,CAAC;AAExB,8CAA8C;AAC9C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,iCAAiC,CAAC;AAEzC,OAAO,kCAAkC,CAAC;AAC1C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,gCAAgC,CAAC;AACxC,OAAO,gCAAgC,CAAC;AACxC,OAAO,iCAAiC,CAAC;AACzC,OAAO,gCAAgC,CAAC;AACxC,OAAO,qBAAqB,CAAC;AAC7B,OAAO,iCAAiC,CAAC;AACzC,OAAO,iCAAiC,CAAC;AACzC,OAAO,0BAA0B,CAAC;AAClC,OAAO,4BAA4B,CAAC;AACpC,OAAO,2BAA2B,CAAC,CAAC,uCAAuC;AAC3E,OAAO,0BAA0B,CAAC;AAClC,OAAO,wBAAwB,CAAC;AAChC,OAAO,qBAAqB,CAAC;AAC7B,OAAO,yBAAyB,CAAC;AAEjC,WAAW;AACX,OAAO,8BAA8B,CAAC;AAEtC,OAAO,wBAAwB,CAAC;AAChC,OAAO,kCAAkC,CAAC;AAC1C,OAAO,gCAAgC,CAAC;AAExC,OAAO,kBAAkB,CAAC;AAC1B,OAAO,cAAc,CAAC;AACtB,OAAO,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AAC3E,OAAO,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAC;AAChD,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AACrC,OAAO,EAAE,kBAAkB,EAAE,MAAM,wBAAwB,CAAC;AAE5D,MAAM,MAAO,SAAQ,UAAU;IAC7B;QACE,KAAK,EAAE,CAAC;QAIV,gBAAW,GAAG,KAAK,CAAC;IAHpB,CAAC;IAKD,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5C,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QACjC,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IACrC,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED,WAAW,CAAC,UAAU;QACpB,KAAK,CAAC,cAAc,GAAG,UAAU,CAAC;aAC/B,IAAI,CACH,UAAU,QAAQ;YAChB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,KAAK,EAAE,CAAC,CAAC,8BAA8B;YAC/C,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb;aACA,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjB,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YACb,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG;gBACrC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACzB,QAAQ,EAAE,GAAG;oBACb,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC;iBACjB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;aACD,KAAK,CAAC,UAAU,KAAK,IAAG,CAAC,CAAC;aAC1B,OAAO,CAAC,cAAa,CAAC,CAAC,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACjD,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,kBAAkB,EAAE,CAAC;gBACrB,MAAM,KAAK,EAAE,CAAC;YAChB,CAAC;YACD,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAChC,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;oBAC/D,kBAAkB,EAAE,CAAC;gBACvB,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,QAAQ,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;oBACjD,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,CAAC;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,gBAAgB;QACd,KAAK,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;QAC1C,KAAK,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;QACtC,KAAK,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QACrC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC9B,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC9B,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IACtC,CAAC;IAED,YAAY;QACV,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,oBAAoB;YAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;SACtC,CAAC,CAAC;QACH,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB;SACxC,CAAC,CAAC;QACH,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,oBAAoB;YAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;SAC/B,CAAC,CAAC;QACH,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,oBAAoB;YAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;SACtC,CAAC,CAAC;QACH,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,sBAAsB;YAChC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB;SACxC,CAAC,CAAC;QACH,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,oBAAoB;YAC9B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;SAC/B,CAAC,CAAC;QACH,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QACnD,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;YACzB,QAAQ,EAAE,cAAc;YACxB,KAAK,EAAE,IAAI,CAAC,IAAI;SACjB,CAAC,CAAC;IACL,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,CAAC;QAClB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;IACH,CAAC;IAED,WAAW,CAAC,KAAK;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QAEnC,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAC/D,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAC7C,UAAU,IAAI;gBACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;YACxD,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;YACF,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClB,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACzB,QAAQ,EAAE,qBAAqB;oBAC/B,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;iBACrB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;YACpE,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAClD,UAAU,IAAI;gBACZ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;YACtD,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;YACF,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpB,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACzB,QAAQ,EAAE,uBAAuB;oBACjC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;iBACvB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IACE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YAC9B,IAAI,CAAC,KAAK,CAAC,qBAAqB;YAChC,IAAI,CAAC,KAAK,CAAC,cAAc;YACzB,IAAI,CAAC,KAAK,CAAC,eAAe,EAC1B,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IACE,IAAI,CAAC,KAAK,CAAC,kBAAkB;gBAC7B,IAAI,CAAC,KAAK,CAAC,oBAAoB;gBAC/B,IAAI,CAAC,KAAK,CAAC,kBAAkB;gBAC7B,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAC/B,CAAC;gBACD,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAED,WAAW;QACT,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;QAC5C,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,OAAO,GAAG,QAAQ,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/C,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACrB,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED,WAAW;QACT,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAClE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG;gBACd,6BAA6B;gBAC7B,6BAA6B;gBAC7B,qCAAqC;gBACrC,4BAA4B;gBAC5B,4BAA4B;gBAC5B,wBAAwB;gBACxB,mBAAmB;gBACnB,mBAAmB;gBACnB,oCAAoC;gBACpC,iBAAiB;gBACjB,0BAA0B;gBAC1B,+BAA+B;gBAC/B,2BAA2B;gBAC3B,0BAA0B;gBAC1B,6BAA6B;gBAC7B,6BAA6B;gBAC7B,wBAAwB;gBACxB,yBAAyB;gBACzB,wBAAwB;gBACxB,kBAAkB;gBAClB,yBAAyB;gBACzB,4BAA4B;gBAC5B,4BAA4B;gBAC5B,4BAA4B;gBAC5B,8BAA8B;gBAC9B,wCAAwC;gBACxC,iCAAiC;gBACjC,iCAAiC;gBACjC,iCAAiC;gBACjC,+BAA+B;gBAC/B,+BAA+B;gBAC/B,gCAAgC;gBAChC,+BAA+B;gBAC/B,mBAAmB;gBACnB,oBAAoB;gBACpB,gCAAgC;gBAChC,gCAAgC;gBAChC,2BAA2B;gBAC3B,yBAAyB;gBACzB,yBAAyB;gBACzB,uBAAuB;gBACvB,oBAAoB;gBACpB,aAAa;aACd,CAAC;YACF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC1D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CACzB,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAC1D,CAAC;YACJ,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,4FAA4F;IAC5F,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ;QACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC;QACzC,MAAM,kBAAkB,GAAG,MAAM,qBAAqB,EAAE,CAAC;QACzD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,kBAAkB,CAAC;IAC5C,CAAC;IAED,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,uBAAuB;YAChF,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;gBACpB;oBACE,IAAI,EAAE,gBAAgB;oBACtB,oCAAoC;oBACpC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,CAClC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC;oBAC1C,QAAQ,EAAE;wBACR,sEAAsE;wBACtE,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,+BAA+B,EAAE;wBACxD,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,sBAAsB,EAAE;wBACzD,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE;wBACrC,EAAE,IAAI,EAAE,iBAAiB,EAAE,SAAS,EAAE,WAAW,EAAE;wBACnD,EAAE,IAAI,EAAE,yBAAyB,EAAE,SAAS,EAAE,WAAW,EAAE;wBAC3D,EAAE,IAAI,EAAE,qBAAqB,EAAE,SAAS,EAAE,eAAe,EAAE;wBAC3D;4BACE,IAAI,EAAE,oCAAoC;4BAC1C,SAAS,EAAE,eAAe;yBAC3B;wBACD,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,mBAAmB,EAAE;wBACnD,EAAE,IAAI,EAAE,uBAAuB,EAAE,SAAS,EAAE,mBAAmB,EAAE;wBACjE,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,mBAAmB,EAAE;wBACnD,EAAE,IAAI,EAAE,qBAAqB,EAAE,SAAS,EAAE,mBAAmB,EAAE;wBAE/D,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,oBAAoB,EAAE;wBACrD,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,6BAA6B,EAAE;wBAE3D,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,8BAA8B,EAAE;wBAC9D,uDAAuD;wBACvD,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,EAAE,wBAAwB,EAAE;wBAC/D,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,EAAE,wBAAwB,EAAE;wBAC/D;4BACE,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE,qCAAqC;yBAChD;wBACD;4BACE,IAAI,EAAE,uBAAuB;4BAC7B,SAAS,EAAE,uBAAuB;yBACnC;wBACD;4BACE,IAAI,EAAE,sBAAsB;4BAC5B,SAAS,EAAE,uBAAuB;yBACnC;wBACD,IAAI;wBACJ,0BAA0B;wBAC1B,wDAAwD;wBACxD,KAAK;wBACL,IAAI;wBACJ,sCAAsC;wBACtC,4CAA4C;wBAC5C,KAAK;wBACL,IAAI;wBACJ,gCAAgC;wBAChC,6CAA6C;wBAC7C,KAAK;wBACL,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,uBAAuB,EAAE;wBAC7D;4BACE,IAAI,EAAE,qBAAqB;4BAC3B,SAAS,EAAE,6BAA6B;yBACzC;qBACF;iBACF;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,eAAe;IACf,WAAW;QACT,yBAAyB;QACzB,mBAAmB;QACnB,iCAAiC;QACjC,uCAAuC;QACvC,mBAAmB;QAEnB,0CAA0C;QAC1C,OAAO,IAAI,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BV,CAAC;IACJ,CAAC;IAED,eAAe;QACb,OAAO,IAAI,CAAA,8DAA8D,CAAC;IAC5E,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAA,IAAI,KAAK,CAClB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EACtD,IAAI,CAAC,eAAe,EAAE,CACvB,EAAE,CAAC;IACN,CAAC;CACF;AA9XC;IADC,OAAO,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,CAAC;2CACpB;AAgYtB,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["import { LitElement, html } from \"lit\";\r\nimport { storeInstance as Store } from \"store/index.js\";\r\nimport { Router } from \"@vaadin/router/dist/vaadin-router.js\";\r\nimport * as util from \"lib/util.ts\";\r\nimport { httpError } from \"store/httpError.js\";\r\nimport pubsub from \"pubsub-js\";\r\n\r\n// import \"@ui5/webcomponents-icons/dist/AllIcons.js\";\r\n\r\nimport \"./wex-header.js\";\r\nimport \"./wex-editors.js\";\r\nimport \"./wex-navigators.js\";\r\n\r\nimport \"./wex-layout-loggedout.js\";\r\nimport \"../pages/workflow/tasks.js\";\r\nimport \"./wex-layout-search.js\";\r\nimport \"./wex-layout-browse.js\";\r\nimport \"./wex-layout-preview.js\";\r\nimport \"./wex-layout-edit.js\";\r\nimport \"./pages/reports.js\";\r\nimport \"../pages/reports/reports-locks.js\";\r\nimport \"../pages/reports/reports-rooms.js\";\r\nimport \"../pages/publish/publish-defs.js\";\r\nimport \"../pages/publish/publish-jobs.js\";\r\nimport \"../pages/publish/publish-adhoc-jobs.js\";\r\nimport \"../pages/publish/publish-pubs.js\";\r\n// import \"../pages/publish/publish-run-defs\";\r\n// import \"../pages/publish/publish-run-adhoc\";\r\n\r\n// import \"./wex-dialog-file-properties.js\";\r\nimport \"./dialog/file-properties.js\";\r\n// import \"./wex-dialog-asof.js\";\r\nimport \"./dialog/asof.js\";\r\n// import \"./wex-dialog-project-selector.js\";\r\nimport \"./dialog/project-selector.js\";\r\n// import \"./wex-dialog-select-file.js\";\r\nimport \"./dialog/select-file.js\";\r\n// import \"./wex-dialog-import.js\";\r\nimport \"./dialog/import.js\";\r\n// import \"./wex-dialog-extended-folder-create-file.js\";\r\nimport \"./dialog/extended-folder-create-file.js\";\r\n\r\n// converted but untested\r\nimport \"./wex-dialog-dirtyeditors.js\";\r\n// import \"./dialog/dirtyeditors.js\";\r\nimport \"./wex-dialog-close-reachable.js\";\r\n// import \"./dialog/close-reachable.js\";\r\nimport \"./wex-dialog-ditamap-lock-fail.js\";\r\n// import \"./dialog/ditamap-lock-fail.js\";\r\nimport \"./wex-dialog-task-history-item-detail.js\";\r\n// import \"./dialog/task-history-item-detail.js\";\r\nimport \"./wex-dialog-task-properties.js\";\r\n// import \"./dialog/task-properties.js\";\r\n\r\n// WIP - TODO EJS - work in progress below\r\nimport \"./wex-dialog-create.js\";\r\n// import \"./dialog/create.js\";\r\n\r\nimport \"./wex-dialog-folder-import.js\";\r\nimport \"./wex-dialog-ditamapnav-create-topic.js\";\r\nimport \"./wex-dialog-project-selector.js\";\r\nimport \"./wex-dialog-task-history-item-detail.js\";\r\nimport \"./wex-dialog-file-properties.js\";\r\nimport \"./wex-dialog-task-properties.js\";\r\nimport \"./wex-dialog-asof.js\";\r\nimport \"./wex-dialog-login.js\";\r\nimport \"./wex-dialog-error.js\";\r\nimport \"./wex-dialog-ditabase-nolock.js\";\r\nimport \"./wex-dialog-folder-create-file.js\";\r\nimport \"./wex-dialog-search-filters.js\";\r\nimport \"./wex-dialog-select-folder.js\";\r\nimport \"./wex-dialog-rootmap-selector.js\";\r\nimport \"./wex-dialog-profile-selector.js\";\r\nimport \"./wex-dialog-finish-form.js\";\r\nimport \"./wex-dialog-savecomment.js\";\r\nimport \"./wex-dialog-save-or-lose.js\";\r\nimport \"./wex-dialog-generic-message.js\";\r\n\r\nimport \"./wex-toast.js\";\r\n\r\n// import \"@ui5/webcomponents/dist/assets.js\";\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\nimport \"@ui5/webcomponents/dist/Dialog.js\";\r\nimport \"@ui5/webcomponents/dist/Label.js\";\r\nimport \"@ui5/webcomponents/dist/Input.js\";\r\nimport \"@ui5/webcomponents/dist/Icon.js\";\r\n\r\nimport \"./dialog/browse-create-folder.js\";\r\nimport \"./dialog/browse-rename-folder.js\";\r\nimport \"./dialog/browse-delete-folder.js\";\r\nimport \"./dialog/browse-delete-file.js\";\r\nimport \"./dialog/browse-rename-file.js\";\r\nimport \"./dialog/editor-create-image.js\";\r\nimport \"./dialog/select-editor-mode.js\";\r\nimport \"./dialog/message.js\";\r\nimport \"./dialog/publish-defs-create.js\";\r\nimport \"./dialog/publish-run-confirm.js\";\r\nimport \"./dialog/export-files.js\";\r\nimport \"./dialog/browse-publish.js\";\r\nimport \"./common/dialog-button.js\"; // import should be in individual file?\r\nimport \"./dialog/import-files.js\";\r\nimport \"./dialog/find-links.js\";\r\nimport \"./dialog/process.js\";\r\nimport \"./common/file-picker.js\";\r\n\r\n// popovers\r\nimport \"./popover/output-job-info.js\";\r\n\r\nimport \"./dialog/wex-select.js\";\r\nimport \"./wex-layout-edit-placeholder.js\";\r\nimport \"../pages/workflow/processes.js\";\r\n\r\nimport \"./wex-csh-nav.js\";\r\nimport \"./wex-csh.js\";\r\nimport { getAvailableLanguages, redirectToSSOLogin } from \"../lib/util.js\";\r\nimport { until } from \"lit/directives/until.js\";\r\nimport {provide} from \"@lit/context\";\r\nimport { globalStoreContext } from \"store/context/index.ts\";\r\n\r\nclass WexApp extends LitElement {\r\n  constructor() {\r\n    super();\r\n  }\r\n\r\n  @provide({context: globalStoreContext })\r\n  globalStore = Store;\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this._loadConfig(\"company_config.json\");\r\n    this._loadConfig(\"user_config.json\");\r\n    this.subscription = Store.subscribe((state) => {\r\n      this.stateChange(state);\r\n    });\r\n    this.state = Store.state;\r\n    this.string = this.state.strings;\r\n    Store.dispatch(\"setLanguage\", this.getPathLang());\r\n    this.getAuthInfoPromise = this.getAuthInfo();\r\n    this.wexUser = this.state.wex_user;\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    Store.unsubscribe(this.subscription);\r\n  }\r\n\r\n  _loadConfig(configFile) {\r\n    fetch(\"/wex/config/\" + configFile)\r\n      .then(\r\n        function (response) {\r\n          if (!response.ok) {\r\n            throw Error(); //to bail out of promise chain\r\n          }\r\n          return response;\r\n        }.bind(this)\r\n      )\r\n      .then((response) => {\r\n        return response.json();\r\n      })\r\n      .then((data) => {\r\n        Object.keys(data).forEach(function (key) {\r\n          Store.dispatch(\"setState\", {\r\n            property: key,\r\n            value: data[key],\r\n          });\r\n        });\r\n      })\r\n      .catch(function (error) {})\r\n      .finally(function () {});\r\n  }\r\n\r\n  async getAuthInfo() {\r\n    pubsub.publish(\"requestout\");\r\n    try {\r\n      const response = await fetch(\"/lwa/jrest/Logon\");\r\n      if (!response.ok) {\r\n        redirectToSSOLogin();\r\n        throw Error();\r\n      }\r\n      response.json().then((userData) => {\r\n        if (userData.userName === undefined || userData.userName == \"\") {\r\n          redirectToSSOLogin();\r\n        } else {\r\n          Store.dispatch(\"legacyHandleAuthData\", userData);\r\n          this.postLoginActions();\r\n        }\r\n        return userData;\r\n      });\r\n    } catch (e) {\r\n      console.log(e);\r\n    } finally {\r\n      pubsub.publish(\"requestin\");\r\n    }\r\n  }\r\n\r\n  postLoginActions() {\r\n    Store.dispatch(\"getEmptyRootmapResLblId\");\r\n    Store.dispatch(\"getGlobalProperties\");\r\n    Store.dispatch(\"getConfiguredLangs\");\r\n    Store.dispatch(\"getBranches\");\r\n    Store.dispatch(\"getProjects\");\r\n    Store.dispatch(\"getCollectionTree\");\r\n  }\r\n\r\n  initDefaults() {\r\n    Store.dispatch(\"setState\", {\r\n      property: \"browse_lang_filter\",\r\n      value: this.state.default_lang_object,\r\n    });\r\n    Store.dispatch(\"setState\", {\r\n      property: \"browse_branch_filter\",\r\n      value: this.state.default_branch_object,\r\n    });\r\n    Store.dispatch(\"setState\", {\r\n      property: \"browse_asof_filter\",\r\n      value: this.state.default_asof,\r\n    });\r\n    Store.dispatch(\"setState\", {\r\n      property: \"search_lang_filter\",\r\n      value: this.state.default_lang_object,\r\n    });\r\n    Store.dispatch(\"setState\", {\r\n      property: \"search_branch_filter\",\r\n      value: this.state.default_branch_object,\r\n    });\r\n    Store.dispatch(\"setState\", {\r\n      property: \"search_asof_filter\",\r\n      value: this.state.default_asof,\r\n    });\r\n    this.flat = [];\r\n    this._treeToArray(this.state.global_folders, this);\r\n    Store.dispatch(\"setState\", {\r\n      property: \"flat_folders\",\r\n      value: this.flat,\r\n    });\r\n  }\r\n\r\n  _treeToArray(node, c) {\r\n    c.flat.push({ id: node.id, name: node.name });\r\n    if (node.children) {\r\n      for (var i = 0; i < node.children.length; i++) {\r\n        c._treeToArray(node.children[i], c);\r\n      }\r\n    }\r\n  }\r\n\r\n  stateChange(state) {\r\n    this.state = state;\r\n    this.string = this.state.strings;\r\n    this.wexUser = this.state.wex_user;\r\n\r\n    if (this.state.global_langs && !this.state.default_lang_object) {\r\n      var defLangObj = this.state.global_langs.filter(\r\n        function (item) {\r\n          return item.langCode === this.state.default_lang_code;\r\n        }.bind(this)\r\n      );\r\n      if (defLangObj[0]) {\r\n        Store.dispatch(\"setState\", {\r\n          property: \"default_lang_object\",\r\n          value: defLangObj[0],\r\n        });\r\n      }\r\n    }\r\n\r\n    if (this.state.global_branches && !this.state.default_branch_object) {\r\n      var defBranchObj = this.state.global_branches.filter(\r\n        function (item) {\r\n          return item.name === this.state.default_branch_name;\r\n        }.bind(this)\r\n      );\r\n      if (defBranchObj[0]) {\r\n        Store.dispatch(\"setState\", {\r\n          property: \"default_branch_object\",\r\n          value: defBranchObj[0],\r\n        });\r\n      }\r\n    }\r\n\r\n    if (\r\n      this.state.default_lang_object &&\r\n      this.state.default_branch_object &&\r\n      this.state.global_folders &&\r\n      this.state.global_projects\r\n    ) {\r\n      if (!this.defaultInit) {\r\n        this.defaultInit = true;\r\n        this.initDefaults();\r\n        this.initRouter();\r\n      }\r\n    }\r\n\r\n    if (!this.dialogsCreated) {\r\n      if (\r\n        this.state.browse_lang_filter &&\r\n        this.state.browse_branch_filter &&\r\n        this.state.search_lang_filter &&\r\n        this.state.search_branch_filter\r\n      ) {\r\n        this.initDialogs();\r\n      }\r\n    }\r\n  }\r\n\r\n  getPathLang() {\r\n    var langCode = this.state.default_lang_code;\r\n    if (window.location.pathname.toLowerCase() == \"/wex/\") {\r\n      window.location.href = \"/wex/\" + langCode;\r\n    } else {\r\n      var path = window.location.pathname.split(\"/\");\r\n      if (path.length >= 2) {\r\n        langCode = window.location.pathname.split(\"/\")[2];\r\n      }\r\n    }\r\n    return langCode;\r\n  }\r\n\r\n  local(inVal) {\r\n    return localize(inVal, this.state.langCode);\r\n  }\r\n\r\n  initDialogs() {\r\n    this.dialogsContainer = this.shadowRoot.querySelector(\"#dialogs\");\r\n    if (this.dialogsContainer) {\r\n      const dialogs = [\r\n        \"wex-popover-output-job-info\",\r\n        \"wex-dialog-project-selector\",\r\n        \"wex-dialog-task-history-item-detail\",\r\n        \"wex-dialog-file-properties\",\r\n        \"wex-dialog-task-properties\",\r\n        \"wex-dialog-select-file\",\r\n        \"wex-dialog-import\",\r\n        \"wex-dialog-create\",\r\n        \"wex-dialog-ditamapnav-create-topic\",\r\n        \"wex-dialog-asof\",\r\n        \"wex-dialog-folder-import\",\r\n        \"wex-dialog-folder-create-file\",\r\n        \"wex-dialog-search-filters\",\r\n        \"wex-dialog-select-folder\",\r\n        \"wex-dialog-rootmap-selector\",\r\n        \"wex-dialog-profile-selector\",\r\n        \"wex-dialog-finish-form\",\r\n        \"wex-dialog-dirtyeditors\",\r\n        \"wex-dialog-savecomment\",\r\n        \"wex-dialog-error\",\r\n        \"wex-dialog-save-or-lose\",\r\n        \"wex-dialog-close-reachable\",\r\n        \"wex-dialog-ditabase-nolock\",\r\n        \"wex-dialog-generic-message\",\r\n        \"wex-dialog-ditamap-lock-fail\",\r\n        \"wex-dialog-extended-folder-create-file\",\r\n        \"wex-dialog-browse-create-folder\",\r\n        \"wex-dialog-browse-rename-folder\",\r\n        \"wex-dialog-browse-delete-folder\",\r\n        \"wex-dialog-browse-rename-file\",\r\n        \"wex-dialog-browse-delete-file\",\r\n        \"wex-dialog-editor-create-image\",\r\n        \"wex-dialog-select-editor-mode\",\r\n        \"wex-dialog-select\",\r\n        \"wex-dialog-message\",\r\n        \"wex-dialog-publish-defs-create\",\r\n        \"wex-dialog-publish-run-confirm\",\r\n        \"wex-dialog-browse-publish\",\r\n        \"wex-dialog-export-files\",\r\n        \"wex-dialog-import-files\",\r\n        \"wex-dialog-find-links\",\r\n        \"wex-dialog-process\",\r\n        \"file-picker\",\r\n      ];\r\n      this._dialogs = this.shadowRoot.querySelector(\"#dialogs\");\r\n      if (this._dialogs) {\r\n        dialogs.forEach((dialog) =>\r\n          this._dialogs.appendChild(document.createElement(dialog))\r\n        );\r\n      }\r\n      this.dialogsCreated = true;\r\n    }\r\n  }\r\n\r\n  // Validates that there is a language table for url parameter other wise redirect to english\r\n  async validateLangcode(context, commands) {\r\n    const langcode = context.params.langcode;\r\n    const availableLanguages = await getAvailableLanguages();\r\n    if (availableLanguages.includes(langcode)) {\r\n      return true;\r\n    }\r\n    window.location.href = \"/wex/en-US/tasks\";\r\n  }\r\n\r\n  initRouter() {\r\n    if (!this.router) {\r\n      const outlet = this.shadowRoot.querySelector(\"#outlet\"); // previously was \"DIV\"\r\n      this.router = new Router(outlet);\r\n      this.router.setRoutes([\r\n        {\r\n          path: \"/wex/:langcode\",\r\n          // redirect: \"/wex/:langcode/tasks\",\r\n          action: async (context, commands) =>\r\n            this.validateLangcode(context, commands),\r\n          children: [\r\n            // redirect still needs the entire path; does not respect child status\r\n            { path: \"/\", redirect: \"/wex/:langcode/workflow/tasks\" },\r\n            { path: \"/loggedout\", component: \"wex-layout-loggedout\" },\r\n            { path: \"/home\", redirect: \"/tasks\" },\r\n            { path: \"/workflow/tasks\", component: \"wex-tasks\" },\r\n            { path: \"/workflow/tasks/:taskId\", component: \"wex-tasks\" },\r\n            { path: \"/workflow/processes\", component: \"wex-processes\" },\r\n            {\r\n              path: \"/workflow/processes/:processInstId\",\r\n              component: \"wex-processes\",\r\n            },\r\n            { path: \"/browse\", component: \"wex-layout-browse\" },\r\n            { path: \"/browse/:collectionId\", component: \"wex-layout-browse\" },\r\n            { path: \"/search\", component: \"wex-layout-search\" },\r\n            { path: \"/search/:searchTerm\", component: \"wex-layout-search\" },\r\n\r\n            { path: \"/preview\", component: \"wex-layout-preview\" },\r\n            { path: \"/edit\", component: \"wex-layout-edit-placeholder\" },\r\n\r\n            { path: \"/reports\", redirect: \"/wex/:langcode/reports/locks\" },\r\n            // { path: \"/reports\", component: \"wex-page-reports\" },\r\n            { path: \"/reports/locks\", component: \"wex-page-reports-locks\" },\r\n            { path: \"/reports/rooms\", component: \"wex-page-reports-rooms\" },\r\n            {\r\n              path: \"/publish\",\r\n              redirect: \"/wex/:langcode/publish/publications\",\r\n            },\r\n            {\r\n              path: \"/publish/publications\",\r\n              component: \"wex-page-publish-pubs\",\r\n            },\r\n            {\r\n              path: \"/publish/definitions\",\r\n              component: \"wex-page-publish-defs\",\r\n            },\r\n            // {\r\n            //   path: \"/publish/run\",\r\n            //   redirect: \"/wex/:langcode/publish/run/definitions\",\r\n            // },\r\n            // {\r\n            //   path: \"/publish/run/definitions\",\r\n            //   component: \"wex-page-publish-run-defs\",\r\n            // },\r\n            // {\r\n            //   path: \"/publish/run/adhoc\",\r\n            //   component: \"wex-page-publish-run-adhoc\",\r\n            // },\r\n            { path: \"/publish/jobs\", component: \"wex-page-publish-jobs\" },\r\n            {\r\n              path: \"/publish/adhoc-jobs\",\r\n              component: \"wex-page-publish-adhoc-jobs\",\r\n            },\r\n          ],\r\n        },\r\n      ]);\r\n    }\r\n  }\r\n\r\n  /* Templates */\r\n  appTemplate() {\r\n    // changes from 55 merge:\r\n    // added #container\r\n    // added flex settings to #outlet\r\n    // added styling to children of #outlet\r\n    // version 1.31.2.3\r\n\r\n    // wrapping the whole app in div#container\r\n    return html`\r\n      <style>\r\n        #container {\r\n          /* width: 100vw; */\r\n          height: 100vh;\r\n          display: flex;\r\n          flex-direction: column;\r\n        }\r\n        #outlet {\r\n          display: flex;\r\n          flex-direction: column;\r\n          flex-grow: 1;\r\n          background-color: var(--clr-bg);\r\n        }\r\n        #outlet > * {\r\n          flex-grow: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n        }\r\n      </style>\r\n\r\n      <div id=\"container\">\r\n        <wex-header id=\"appheader\"></wex-header>\r\n        <div id=\"outlet\"></div>\r\n        <span id=\"dialogs\"></span>\r\n        <wex-toast></wex-toast>\r\n        <wex-layout-edit></wex-layout-edit>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  loadingTemplate() {\r\n    return html`<div id=\"loading\" aria-live=\"polite\" role=\"status\">...</div>`;\r\n  }\r\n\r\n  render() {\r\n    return html` ${until(\r\n      this.getAuthInfoPromise.then(() => this.appTemplate()),\r\n      this.loadingTemplate()\r\n    )}`;\r\n  }\r\n}\r\n\r\ncustomElements.define(\"wex-app\", WexApp);\r\n"]}