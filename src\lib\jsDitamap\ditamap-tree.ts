import { DitamapNode } from "./ditamap-node";

export class DitamapTree {
  cache: Map<string, HTMLElement>;
  root: DitamapNode | null = null;

  constructor(rootMapName: string, files?: Map<string, HTMLElement>) {
    this.cache = files ?? new Map();
    this.root = this.buildTree(rootMapName);
  }

  buildTree(mapName: string): DitamapNode | null {
    let mapEl = this.cache.get(mapName);
    if (!mapEl) return null;
  }
}
