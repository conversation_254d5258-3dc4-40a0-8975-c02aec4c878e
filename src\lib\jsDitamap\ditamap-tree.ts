import { DitamapNode } from "./ditamap-node";

export class DitamapTree {
  /**
   * Map of all the stored ditamap xml
   */
  workspace: Map<string, HTMLElement>;
  /**
   * Underlying tree data structure
   */
  root: DitamapNode | null = null;
  /**
   * Name of the root map
   */
  rootMapName: string | null = null;

  constructor(rootMapName: string, files?: Map<string, HTMLElement>) {
    this.workspace = files ?? new Map();
    this.root = this.buildTree(rootMapName)
    this.rootMapName = rootMapName;
  }

  buildTree(mapName: string, parent?: DitamapNode | null): DitamapNode | null {
    if (this.workspace.has(mapName)) {
      return this._treeBfs(this.root);
    }
    return null;
  }

  _fetchMap(mapName: string): Promise<HTMLElement> {}

  _attachNodeToParent(node: DitamapNode, parent: DitamapNode | null) {}

  /**
   * Builds json tree from xml
   * @param rootHtml - parsed xml element
   */
  _treeBfs(rootHtml: HTMLElement): DitamapNode {
    let tree = new DitamapNode();
    tree.xmlElement = rootHtml;

    let Q: QueueItem[] = [[rootHtml, tree]];
    while (Array.isArray(Q) && Q.length > 0) {
      let [element, parent, mapName] = Q.shift() as QueueItem;

      for (let child of element.children as HTMLCollectionOf<HTMLElement>) {
        let newNode = new DitamapNode();

        if (DitamapTree.isMap(child)) {
          this._decorateMapNode(child, newNode);
          let newMapName = DitamapTree.getMapName(child, this.rootMapName);
          let mapNameToUse = newMapName ?? mapName;
          if (!newMapName) {
            console.warn("No map name found for", child);
          }
          Q.push([child, newNode, mapNameToUse]);
          parent.children.push(newNode);
        }

        if (DitamapTree.isContent(child)) {
          Q.push([child, newNode, mapName]);
          parent.children.push(newNode);
        }
      }
    }

    return tree;
  }

  //---- Helpers ----
  /**
   * Checks if an html element is a ditamap
   * @param element
   */
  static isMap(element: HTMLElement): boolean {
    const isBookmap = element.tagName === "bookmap";
    const isDitamap = element.tagName === "map";
    const hasDitamapFormat = element.getAttribute("format") == "ditamap";
    const hasHref = element.getAttribute("href");
    return isDitamap || isBookmap || Boolean(hasDitamapFormat && hasHref);
  }

  /**
   * Checks if an html element is a content reference (MORE ATTENTION NEEDED HERE)
   * @param element
   */
  static isContent(element: HTMLElement): boolean {
    return element.tagName === "topicref" || element.tagName === "chapter";
  }

  /**
   * Decorates a tree map node with information from the xml
   * @param element - xml element
   */
  _decorateMapNode(element: HTMLElement, containingMap: string | null) {
    let node = new DitamapNode();
    node.href = element.getAttribute("href");
    node.containingMap = containingMap;
    node.title = this._removeXmlNoise(DitamapTree.getMapTitle(element));
    node.xmlElement = element; 
    node.embeddedElement = element;
    return node;

  }

  static getMapName(element: HTMLElement, rootMapName?: string) : string | undefined {
    if (element.getAttribute("href")) {
      return element.getAttribute("href") as string;
    }

    if (
      rootMapName &&
      (element.tagName.toLowerCase() === "bookmap" ||
        element.tagName.toLowerCase() === "map")
    ) {
      return rootMapName;
    }

    return undefined;
  }

  static getMapTitle(element: HTMLElement) {
    let mainbooktitleEl = element.querySelector("mainbooktitle");
    if (mainbooktitleEl) return mainbooktitleEl.innerHTML;

    let titleEl = element.querySelector("title");
    if (titleEl) return titleEl.innerText || titleEl.innerHTML;

    let titleAtt = element.getAttribute("title");
    if (titleAtt) return titleAtt;

    return element.tagName;
  }

  _removeXmlNoise(str: string) {
    if (str) return str.replace("<?xm-replace_text Main Book Title ?>", "");
    return str;
  }
}

type QueueItem = [HTMLElement, DitamapNode, (string | undefined)?];
