
import { assert, fixture, html } from "@open-wc/testing";
import "./store-wrapper";
import Store from 'store/beedle.js';



describe("Page", () => {
    it.skip("shows no tasks found if no tasks and fetch is complete", async () => {
        /**
         * 1. Fetch the call via an action and mock the response
         * 
         */
        const store = new Store(actions, mutations, initialState);
        const el = await fixture(html`<mock-store .store=${store}><wex-task-page></wex-task-page></mock-store>`);
    })
    it.skip("shows error if fetch fails")
    it.skip("shows one task")
    it.skip("shows multiple tasks")
    it.skip("shows claimed tasks before unclaimed tasks")
    it.skip("shows loading spinner if fetch is pending and there are no tasks in cache")
    it.skip("shows updating message if fetch is pending and there are tasks in cache")
})

describe("Filter", () => {
    it.skip("requests tasks by project");
    it.skip("requests tasks by claimed status");
    it.skip("requests tasks by project and claimed status");
    it.skip("clears filters");
})

describe("Tile", () => {
    it("is defined", () => {
        assert.isDefined(document.createElement("wex-task-tile"));
    });

    it.skip("displays task information");
    it.skip("requests task unclaimed when claimed");
    it.skip("requests task claimed when unclaimed");
    it.skip("finish task launches dialog");
    it.skip("shows primary files")
    it.skip("shows reference files")
    // Loading
    describe("Loading", () => {
        it.skip("shows skeleton when no files and fetch pending");
        it.skip("shows skeleton when error and fetch pending"); 
        it.skip("shows files with updating spinner when files cached and fetch pending");
        it.skip("shows no files message when no files and fetch complete");
        it.skip("shows error message when error and fetch complete");
    });
})

describe("Tile Menu", () => {
    it.skip("details");
    it.skip("diagram");
    it.skip("history");
});

describe("Filepackage", () => {
    it.skip("displays file name");
    it.skip("displays file status (reviewed/unreviewed)");
    it.skip("double click launches editor if task claimed");
    it.skip("shows context menu on right click");
    it.skip("toggle file reviewed status");
    it.skip("preview file");
});