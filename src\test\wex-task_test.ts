
import { fixture, assert, html, expect } from "@open-wc/testing";
import "./store-wrapper";


suite("Page", () => {
    test.skip("shows no tasks found if no tasks and fetch is complete")
    test.skip("shows error if fetch fails")
    test.skip("shows one task")
    test.skip("shows multiple tasks")
    test.skip("shows claimed tasks before unclaimed tasks")
    test.skip("shows loading spinner if fetch is pending and there are no tasks in cache")
    test.skip("shows updating message if fetch is pending and there are tasks in cache")
})

suite("Filter", () => {
    test.skip("requests tasks by project");
    test.skip("requests tasks by claimed status");
    test.skip("requests tasks by project and claimed status");
    test.skip("clears filters");
})

suite("Tile", () => {
    test("is defined", () => {
        assert.isDefined(document.createElement("wex-task-tile"));
    });

    test.skip("displays task information");
    test.skip("requests task unclaimed when claimed");
    test.skip("requests task claimed when unclaimed");
    test.skip("finish task launches dialog");
    test.skip("shows primary files")
    test.skip("shows reference files")
})

suite("Tile Menu", () => {
    test.skip("details");
    test.skip("diagram");
    test.skip("history");
});

suite("Filepackage", () => {
    test.skip("displays file name");
    test.skip("double click launches editor if task claimed");
});