{"version": 3, "file": "consume.js", "sourceRoot": "", "sources": ["../../../src/lib/decorators/consume.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAC,eAAe,EAAC,MAAM,oCAAoC,CAAC;AAGnE;;;;;GAKG;AAEH;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,UAAU,OAAO,CAAY,EACjC,OAAO,EACP,SAAS,GAIV;IACC,OAAO,CAAC,CACN,aAAuE,EACvE,aAE6D,EAC7D,EAAE;QACF,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YACtC,6BAA6B;YAC7B,aAAa,CAAC,cAAc,CAAC;gBAC3B,IAAI,eAAe,CAAC,IAAI,EAAE;oBACxB,OAAO;oBACP,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;wBAClB,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBACtC,CAAC;oBACD,SAAS;iBACV,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,iCAAiC;YAChC,aAAa,CAAC,WAAsC,CAAC,cAAc,CAClE,CAAC,OAAwB,EAAQ,EAAE;gBACjC,IAAI,eAAe,CAAC,OAAO,EAAE;oBAC3B,OAAO;oBACP,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;wBAClB,8DAA8D;wBAC7D,OAAe,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC;oBAC1C,CAAC;oBACD,SAAS;iBACV,CAAC,CAAC;YACL,CAAC,CACF,CAAC;QACJ,CAAC;IACH,CAAC,CAAgC,CAAC;AACpC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2022 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ReactiveElement} from '@lit/reactive-element';\nimport {ContextConsumer} from '../controllers/context-consumer.js';\nimport {Context} from '../create-context.js';\n\n/*\n * IMPORTANT: For compatibility with t<PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\n/**\n * A property decorator that adds a ContextConsumer controller to the component\n * which will try and retrieve a value for the property via the Context API.\n *\n * @param context A Context identifier value created via `createContext`\n * @param subscribe An optional boolean which when true allows the value to be updated\n *   multiple times.\n *\n * @example\n *\n * ```ts\n * import {consume} from '@lit/context';\n * import {loggerContext, Logger} from 'community-protocols/logger';\n *\n * class MyElement {\n *   @consume({context: loggerContext})\n *   logger?: Logger;\n *\n *   doThing() {\n *     this.logger!.log('thing was done');\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function consume<ValueType>({\n  context,\n  subscribe,\n}: {\n  context: Context<unknown, ValueType>;\n  subscribe?: boolean;\n}): ConsumeDecorator<ValueType> {\n  return ((\n    protoOrTarget: ClassAccessorDecoratorTarget<ReactiveElement, ValueType>,\n    nameOrContext:\n      | PropertyKey\n      | ClassAccessorDecoratorContext<ReactiveElement, ValueType>\n  ) => {\n    if (typeof nameOrContext === 'object') {\n      // Standard decorators branch\n      nameOrContext.addInitializer(function () {\n        new ContextConsumer(this, {\n          context,\n          callback: (value) => {\n            protoOrTarget.set.call(this, value);\n          },\n          subscribe,\n        });\n      });\n    } else {\n      // Experimental decorators branch\n      (protoOrTarget.constructor as typeof ReactiveElement).addInitializer(\n        (element: ReactiveElement): void => {\n          new ContextConsumer(element, {\n            context,\n            callback: (value) => {\n              // eslint-disable-next-line @typescript-eslint/no-explicit-any\n              (element as any)[nameOrContext] = value;\n            },\n            subscribe,\n          });\n        }\n      );\n    }\n  }) as ConsumeDecorator<ValueType>;\n}\n\n/**\n * Generates a public interface type that removes private and protected fields.\n * This allows accepting otherwise incompatible versions of the type (e.g. from\n * multiple copies of the same package in `node_modules`).\n */\ntype Interface<T> = {\n  [K in keyof T]: T[K];\n};\n\ntype ConsumeDecorator<ValueType> = {\n  // legacy\n  <\n    K extends PropertyKey,\n    Proto extends Interface<Omit<ReactiveElement, 'renderRoot'>>,\n  >(\n    protoOrDescriptor: Proto,\n    name?: K\n  ): FieldMustMatchProvidedType<Proto, K, ValueType>;\n\n  // standard\n  <\n    C extends Interface<Omit<ReactiveElement, 'renderRoot'>>,\n    V extends ValueType,\n  >(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): void;\n};\n\n// Note TypeScript requires the return type of a decorator to be `void | any`\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype DecoratorReturn = void | any;\n\ntype FieldMustMatchProvidedType<Obj, Key extends PropertyKey, ProvidedType> =\n  // First we check whether the object has the property as a required field\n  Obj extends Record<Key, infer ConsumingType>\n    ? // Ok, it does, just check whether it's ok to assign the\n      // provided type to the consuming field\n      [ProvidedType] extends [ConsumingType]\n      ? DecoratorReturn\n      : {\n          message: 'provided type not assignable to consuming field';\n          provided: ProvidedType;\n          consuming: ConsumingType;\n        }\n    : // Next we check whether the object has the property as an optional field\n      Obj extends Partial<Record<Key, infer ConsumingType>>\n      ? // Check assignability again. Note that we have to include undefined\n        // here on the consuming type because it's optional.\n        [ProvidedType] extends [ConsumingType | undefined]\n        ? DecoratorReturn\n        : {\n            message: 'provided type not assignable to consuming field';\n            provided: ProvidedType;\n            consuming: ConsumingType | undefined;\n          }\n      : // Ok, the field isn't present, so either someone's using consume\n        // manually, i.e. not as a decorator (maybe don't do that! but if you do,\n        // you're on your own for your type checking, sorry), or the field is\n        // private, in which case we can't check it.\n        DecoratorReturn;\n"]}