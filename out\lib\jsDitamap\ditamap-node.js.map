{"version": 3, "file": "ditamap-node.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-node.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,WAAW;IA+BtB;QACE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;CACF", "sourcesContent": ["export class DitamapNode {\r\n  /** Unique identifier for the node */\r\n  id: string | null;\r\n\r\n  /** Type of DITA element (topicref, mapref, etc.) */\r\n  type: string | null;\r\n\r\n  /** Reference to the target resource */\r\n  href?: string | null;\r\n\r\n  /** Display title for the node */\r\n  title: string | null;\r\n\r\n  /** Name of the containing map */\r\n  mapName: string | null;\r\n\r\n  /** Path hierarchy to this node */\r\n  mapPath: string[];\r\n\r\n  /** Children of this node */\r\n  children: DitamapNode[];\r\n\r\n  /** Reference to the actual DOM element in XML tree */\r\n  domElement: HTMLElement | null;\r\n\r\n  /** Reference to element in parent map (for nested maps) */\r\n  embeddedElement?: HTMLElement | null;\r\n\r\n  /** Tag name at the root of the XML element */\r\n  rootElementName: string;\r\n\r\n  constructor() {\r\n    this.id = null;\r\n    this.type = \"root\";\r\n    this.href = null;\r\n    this.title = null;\r\n    this.mapName = null;\r\n    this.mapPath = [];\r\n    this.domElement = null;\r\n    this.embeddedElement = null;\r\n    this.rootElementName = \"\";\r\n    this.children = [];\r\n  }\r\n}\r\n\r\nexport interface DitamapNodeStructure {\r\n  type: string;\r\n  title: string;\r\n  mapName?: string;\r\n  mapPath: string[];\r\n  href: string;\r\n  rootElementName: string;\r\n  children: DitamapNodeStructure[];\r\n}\r\n"]}