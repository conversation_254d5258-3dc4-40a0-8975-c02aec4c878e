{"version": 3, "file": "ditamap-node.js", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-node.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,WAAW;IA4BtB;QACE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,8CAA8C;IAC9C,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACjC,CAAC;CAGF", "sourcesContent": ["export class DitamapNode {\r\n  /** Unique identifier for the node */\r\n  id: string | null;\r\n\r\n  /** Type of DITA element (topicref, mapref, etc.) */\r\n  type: string | null;\r\n\r\n  /** Reference to the target resource */\r\n  href?: string | null;\r\n\r\n  /** Display title for the node */\r\n  title: string | null;\r\n\r\n  /** Name of the containing map */\r\n  containingMap: string | null;\r\n\r\n  /** Path hierarchy to this node */\r\n  mapPath: string[];\r\n\r\n  /** Children of this node */\r\n  children: DitamapNode[];\r\n\r\n  /** Reference to the actual DOM element in XML tree */\r\n  domElement!: HTMLElement;\r\n\r\n  /** Reference to element in parent map (for nested maps) */\r\n  embeddedElement?: HTMLElement | null;\r\n\r\n  constructor() {\r\n    this.id = null;\r\n    this.type = \"root\";\r\n    this.href = null;\r\n    this.title = null;\r\n    this.containingMap = null;\r\n    this.mapPath = [];\r\n    this.embeddedElement = null;\r\n    this.children = [];\r\n  }\r\n\r\n  /** Tag name at the root of the XML element */\r\n  get tagName() {\r\n    return this.domElement.tagName;\r\n  }\r\n\r\n\r\n}\r\n\r\nexport interface DitamapNodeStructure {\r\n  type: string;\r\n  title: string;\r\n  mapName?: string;\r\n  mapPath: string[];\r\n  href: string;\r\n  tagName: string;\r\n  children: DitamapNodeStructure[];\r\n}\r\n"]}