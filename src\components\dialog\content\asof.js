import { LitElement, html, css } from "lit-element";

import "@ui5/webcomponents/dist/RadioButton.js";
import "@ui5/webcomponents/dist/Select";
import "@ui5/webcomponents/dist/Option";
import "@ui5/webcomponents/dist/DateTimePicker";
// import "@ui5/webcomponents/dist/Label.js";
// import "@ui5/webcomponents/dist/Input.js";
// import "@ui5/webcomponents/dist/DatePicker";

// import "@vaadin/vaadin-date-picker/vaadin-date-picker.js";

import "../../base/row-item";
import "../../base/col-item";
import "../../common/select.js";

class WexDialogAsofContent extends LitElement {
  static get properties() {
    return {
      asofNowLabel: { type: String },
      asofLabelLabel: { type: String },
      asofDateLabel: { type: String },
      labels: { type: Array },
      selectedOption: { type: String },
    };
  }

  /*
    #asof-dialog {
      width: 50%;
    }
  
  
      .fileform > div {
        display: grid;
        width: 15rem;
        margin-bottom: 0.5rem;
      }
  
      [inner-input] {
        color: inherit;
        font-style: normal;
        -webkit-appearance: none;
        line-height: normal;
        box-sizing: border-box;
        min-width: 3rem;
        text-overflow: ellipsis;
        font-size: inherit;
        font-family: inherit;
        background: transparent;
        border-width: initial;
        border-style: none;
        border-color: initial;
        border-image: initial;
        flex: 1 1 0%;
        outline: none;
      }
      .hide {
        display: none !important;
      }
            #message {
        margin: 5px;
        width: 90%;
      }
      ui5-label {
        text-align: left;
      }
            .dialogbtns {
        margin: 1rem;
      }
      #filename {
        margin-top: -12px;
      }
      #labelselector {
        height: 200px;
        margin-bottom: 20px;
      }
      #message {
        height: 15px;
      }
  
      .optionrow {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
      }

      #datepicker {
        vertical-align: text-top;
        margin-top: 6px;
      }
      #labelselectlist {
        vertical-align: text-top;
        margin-top: 6px;
      }
      #utclabel {
        font-size: 60%;
        font-variant: small-caps;
        margin-bottom: -12px;
      }
  
  
  
      */
  // .test {
  //   max-width: 3rem;
  // }
  // ui5-radiobutton {
  //   vertical-align: text-top;
  //   border: 1px solid #00f;
  // }
  static get styles() {
    return css`
      .radiobutton-container {
        max-width: 10rem;
      }
      ui5-select,
      ui5-datetime-picker {
        width: 100%;
      }
    `;
  }

  constructor() {
    super();

    this.selectedDate = null;
    this.selectedLabel = null;
    this.selectedOption = null;
  }

  // updated(changedProperties) {
  //   console.log("updated", this.labels);
  // }

  _optionChange = (e) => {
    this.selectedOption = e.currentTarget.id;
    const event = new CustomEvent("selected-option-changed", {
      detail: { selectedOption: this.selectedOption },
      bubbles: true,
      composed: true,
    });
    this.dispatchEvent(event);
  };

  _labelChange = (e) => {
    this.selectedLabel = e.detail.selectedOption.item;
    const event = new CustomEvent("selected-label-changed", {
      detail: { selectedLabel: this.selectedLabel },
      bubbles: true,
      composed: true,
    });
    this.dispatchEvent(event);
  };

  _dateChange = (e) => {
    this.selectedDate = e.detail.value;
    const event = new CustomEvent("selected-date-changed", {
      detail: { selectedDate: this.selectedDate },
      bubbles: true,
      composed: true,
    });
    this.dispatchEvent(event);
  };

  render() {
    return html`
      <wex-row-item justifyContent="flex-start">
        <wex-col-item class="radiobutton-container">
          <ui5-radio-button
            id="asofnow"
            text="${this.asofNowLabel}"
            @change="${this._optionChange}"
            ?checked="${this.selectedOption == "asofnow"}"
            name="asof-group"
          ></ui5-radio-button>
        </wex-col-item>
      </wex-row-item>
      <wex-row-item>
        <wex-col-item class="radiobutton-container">
          <ui5-radio-button
            id="asoflabel"
            text="${this.asofLabelLabel}"
            @change="${this._optionChange}"
            ?checked="${this.selectedOption == "asoflabel"}"
            name="asof-group"
          ></ui5-radio-button>
        </wex-col-item>
        <wex-col-item class="test">
          <ui5-select
            id="labelselectlist"
            ?disabled="${this.selectedOption != "asoflabel"}"
            @change="${this._labelChange}"
          >
            ${this.labels.map(
              (label) => html`
                <ui5-option icon="meal" .item="${label}"
                  >${label.name} - ${label.gmtLabelDate}
                </ui5-option>
              `
            )}
          </ui5-select>
        </wex-col-item>
      </wex-row-item>
      <wex-row-item>
        <wex-col-item class="radiobutton-container">
          <ui5-radio-button
            id="asofdate"
            text="${this.asofDateLabel}"
            @change="${this._optionChange}"
            ?checked="${this.selectedOption == "asofdate"}"
            name="asof-group"
          ></ui5-radio-button>
        </wex-col-item>
        <wex-col-item>
          <ui5-datetime-picker
            ?disabled="${this.selectedOption != "asofdate"}"
            @change="${this._dateChange}"
          ></ui5-datetime-picker>
        </wex-col-item>
      </wex-row-item>
    `;
  }
}

customElements.define("wex-dialog-asof-content", WexDialogAsofContent);
