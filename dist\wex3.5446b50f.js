var e=globalThis,a={},n=function(e){var a=/\blang(?:uage)?-([\w-]+)\b/i,n=0,t={manual:e.Prism&&e.Prism.manual,disableWorkerMessageHandler:e.Prism&&e.Prism.disableWorkerMessageHandler,util:{encode:function e(a){return a instanceof r?new r(a.type,e(a.content),a.alias):Array.isArray(a)?a.map(e):a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++n}),e.__id},clone:function e(a,n){var r,i;switch(n=n||{},t.util.type(a)){case"Object":if(n[i=t.util.objId(a)])return n[i];for(var l in r={},n[i]=r,a)a.hasOwnProperty(l)&&(r[l]=e(a[l],n));return r;case"Array":return n[i=t.util.objId(a)]?n[i]:(r=[],n[i]=r,a.forEach(function(a,t){r[t]=e(a,n)}),r);default:return a}},getLanguage:function(e){for(;e&&!a.test(e.className);)e=e.parentElement;return e?(e.className.match(a)||[,"none"])[1].toLowerCase():"none"},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw Error()}catch(t){var e=(/at [^(\r\n]*\((.*):.+:.+\)$/i.exec(t.stack)||[])[1];if(e){var a=document.getElementsByTagName("script");for(var n in a)if(a[n].src==e)return a[n]}return null}},isActive:function(e,a,n){for(var t="no-"+a;e;){var r=e.classList;if(r.contains(a))return!0;if(r.contains(t))return!1;e=e.parentElement}return!!n}},languages:{extend:function(e,a){var n=t.util.clone(t.languages[e]);for(var r in a)n[r]=a[r];return n},insertBefore:function(e,a,n,r){var i=(r=r||t.languages)[e],l={};for(var s in i)if(i.hasOwnProperty(s)){if(s==a)for(var o in n)n.hasOwnProperty(o)&&(l[o]=n[o]);n.hasOwnProperty(s)||(l[s]=i[s])}var u=r[e];return r[e]=l,t.languages.DFS(t.languages,function(a,n){n===u&&a!=e&&(this[a]=l)}),l},DFS:function e(a,n,r,i){i=i||{};var l=t.util.objId;for(var s in a)if(a.hasOwnProperty(s)){n.call(a,s,a[s],r||s);var o=a[s],u=t.util.type(o);"Object"!==u||i[l(o)]?"Array"!==u||i[l(o)]||(i[l(o)]=!0,e(o,n,s,i)):(i[l(o)]=!0,e(o,n,null,i))}}},plugins:{},highlightAll:function(e,a){t.highlightAllUnder(document,e,a)},highlightAllUnder:function(e,a,n){var r={callback:n,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};t.hooks.run("before-highlightall",r),r.elements=Array.prototype.slice.apply(r.container.querySelectorAll(r.selector)),t.hooks.run("before-all-elements-highlight",r);for(var i,l=0;i=r.elements[l++];)t.highlightElement(i,!0===a,r.callback)},highlightElement:function(n,r,i){var l=t.util.getLanguage(n),s=t.languages[l];n.className=n.className.replace(a,"").replace(/\s+/g," ")+" language-"+l;var o=n.parentElement;o&&"pre"===o.nodeName.toLowerCase()&&(o.className=o.className.replace(a,"").replace(/\s+/g," ")+" language-"+l);var u={element:n,language:l,grammar:s,code:n.textContent};function g(e){u.highlightedCode=e,t.hooks.run("before-insert",u),u.element.innerHTML=u.highlightedCode,t.hooks.run("after-highlight",u),t.hooks.run("complete",u),i&&i.call(u.element)}if(t.hooks.run("before-sanity-check",u),!u.code)return t.hooks.run("complete",u),void(i&&i.call(u.element));if(t.hooks.run("before-highlight",u),u.grammar)if(r&&e.Worker){var c=new Worker(t.filename);c.onmessage=function(e){g(e.data)},c.postMessage(JSON.stringify({language:u.language,code:u.code,immediateClose:!0}))}else g(t.highlight(u.code,u.grammar,u.language));else g(t.util.encode(u.code))},highlight:function(e,a,n){var i={code:e,grammar:a,language:n};return t.hooks.run("before-tokenize",i),i.tokens=t.tokenize(i.code,i.grammar),t.hooks.run("after-tokenize",i),r.stringify(t.util.encode(i.tokens),i.language)},tokenize:function(e,a){var n=a.rest;if(n){for(var s in n)a[s]=n[s];delete a.rest}var o=new i;return l(o,o.head,e),function e(a,n,i,s,o,u){for(var g in i)if(i.hasOwnProperty(g)&&i[g]){var c=i[g];c=Array.isArray(c)?c:[c];for(var d=0;d<c.length;++d){if(u&&u.cause==g+","+d)return;var h=c[d],p=h.inside,f=!!h.lookbehind,m=!!h.greedy,v=0,y=h.alias;if(m&&!h.pattern.global){var k=h.pattern.toString().match(/[imsuy]*$/)[0];h.pattern=RegExp(h.pattern.source,k+"g")}for(var b=h.pattern||h,x=s.next,A=o;x!==n.tail&&!(u&&A>=u.reach);A+=x.value.length,x=x.next){var w=x.value;if(n.length>a.length)return;if(!(w instanceof r)){var S=1;if(m&&x!=n.tail.prev){b.lastIndex=A;var C=b.exec(a);if(!C)break;var E=C.index+(f&&C[1]?C[1].length:0),O=C.index+C[0].length,P=A;for(P+=x.value.length;P<=E;)P+=(x=x.next).value.length;if(P-=x.value.length,A=P,x.value instanceof r)continue;for(var T=x;T!==n.tail&&(P<O||"string"==typeof T.value);T=T.next)S++,P+=T.value.length;S--,w=a.slice(A,P),C.index-=A}else{b.lastIndex=0;var C=b.exec(w)}if(C){f&&(v=C[1]?C[1].length:0);var E=C.index+v,_=C[0].slice(v),O=E+_.length,D=w.slice(0,E),j=w.slice(O),L=A+w.length;u&&L>u.reach&&(u.reach=L);var N=x.prev;D&&(N=l(n,N,D),A+=D.length),function(e,a,n){for(var t=a.next,r=0;r<n&&t!==e.tail;r++)t=t.next;(a.next=t).prev=a,e.length-=r}(n,N,S),x=l(n,N,new r(g,p?t.tokenize(_,p):_,y,_)),j&&l(n,x,j),1<S&&e(a,n,i,x.prev,A,{cause:g+","+d,reach:L})}}}}}}(e,o,a,o.head,0),function(e){for(var a=[],n=e.head.next;n!==e.tail;)a.push(n.value),n=n.next;return a}(o)},hooks:{all:{},add:function(e,a){var n=t.hooks.all;n[e]=n[e]||[],n[e].push(a)},run:function(e,a){var n=t.hooks.all[e];if(n&&n.length)for(var r,i=0;r=n[i++];)r(a)}},Token:r};function r(e,a,n,t){this.type=e,this.content=a,this.alias=n,this.length=0|(t||"").length}function i(){var e={value:null,prev:null,next:null},a={value:null,prev:e,next:null};e.next=a,this.head=e,this.tail=a,this.length=0}function l(e,a,n){var t=a.next,r={value:n,prev:a,next:t};return a.next=r,t.prev=r,e.length++,r}if(e.Prism=t,r.stringify=function e(a,n){if("string"==typeof a)return a;if(Array.isArray(a)){var r="";return a.forEach(function(a){r+=e(a,n)}),r}var i={type:a.type,content:e(a.content,n),tag:"span",classes:["token",a.type],attributes:{},language:n},l=a.alias;l&&(Array.isArray(l)?Array.prototype.push.apply(i.classes,l):i.classes.push(l)),t.hooks.run("wrap",i);var s="";for(var o in i.attributes)s+=" "+o+'="'+(i.attributes[o]||"").replace(/"/g,"&quot;")+'"';return"<"+i.tag+' class="'+i.classes.join(" ")+'"'+s+">"+i.content+"</"+i.tag+">"},!e.document)return e.addEventListener&&(t.disableWorkerMessageHandler||e.addEventListener("message",function(a){var n=JSON.parse(a.data),r=n.language,i=n.code,l=n.immediateClose;e.postMessage(t.highlight(i,t.languages[r],r)),l&&e.close()},!1)),t;var s=t.util.currentScript();function o(){t.manual||t.highlightAll()}if(s&&(t.filename=s.src,s.hasAttribute("data-manual")&&(t.manual=!0)),!t.manual){var u=document.readyState;"loading"===u||"interactive"===u&&s&&s.defer?document.addEventListener("DOMContentLoaded",o):window.requestAnimationFrame?window.requestAnimationFrame(o):window.setTimeout(o,16)}return t}("undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{});a&&(a=n),void 0!==e&&(e.Prism=n),n.languages.markup={comment:/<!--[\s\S]*?-->/,prolog:/<\?[\s\S]+?\?>/,doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/,name:/[^\s<>'"]+/}},cdata:/<!\[CDATA\[[\s\S]*?]]>/i,tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},n.languages.markup.tag.inside["attr-value"].inside.entity=n.languages.markup.entity,n.languages.markup.doctype.inside["internal-subset"].inside=n.languages.markup,n.hooks.add("wrap",function(e){"entity"===e.type&&(e.attributes.title=e.content.replace(/&amp;/,"&"))}),Object.defineProperty(n.languages.markup.tag,"addInlined",{value:function(e,a){var t={};t["language-"+a]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:n.languages[a]},t.cdata=/^<!\[CDATA\[|\]\]>$/i;var r={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:t}};r["language-"+a]={pattern:/[\s\S]+/,inside:n.languages[a]};var i={};i[e]={pattern:RegExp("(<__[^]*?>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[^])*?(?=</__>)".replace(/__/g,function(){return e}),"i"),lookbehind:!0,greedy:!0,inside:r},n.languages.insertBefore("markup","cdata",i)}}),n.languages.html=n.languages.markup,n.languages.mathml=n.languages.markup,n.languages.svg=n.languages.markup,n.languages.xml=n.languages.extend("markup",{}),n.languages.ssml=n.languages.xml,n.languages.atom=n.languages.xml,n.languages.rss=n.languages.xml;function t(e,a){n.languages[e]&&n.languages.insertBefore(e,"comment",{"doc-comment":a})}var r=n.languages.markup.tag,i={pattern:/\/\/\/.*/,greedy:!0,alias:"comment",inside:{tag:r}};t("csharp",i),t("fsharp",i),t("vbnet",{pattern:/'''.*/,greedy:!0,alias:"comment",inside:{tag:r}});
//# sourceMappingURL=wex3.5446b50f.js.map
