{"version": 3, "file": "context-consumer.js", "sourceRoot": "", "sources": ["../../../src/lib/controllers/context-consumer.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAEL,mBAAmB,GACpB,MAAM,6BAA6B,CAAC;AAarC;;;;;;;;;;;GAWG;AACH,MAAM,OAAO,eAAe;IAsB1B,YACE,IAAiB,EACjB,gBAAgC,EAChC,QAAgE,EAChE,SAAmB;QAlBb,cAAS,GAAG,KAAK,CAAC;QAElB,aAAQ,GAAG,KAAK,CAAC;QAEzB,UAAK,GAAoB,SAAS,CAAC;QAwDnC,4EAA4E;QAC5E,2CAA2C;QACnC,cAAS,GAAoC,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;YAC1E,6FAA6F;YAC7F,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,4EAA4E;gBAC5E,IAAI,IAAI,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;oBACrC,2BAA2B;oBAC3B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACtB,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,CAAC;gBACD,4DAA4D;gBAC5D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;oBACpB,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,kEAAkE;YAClE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACnB,8DAA8D;YAC9D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAE1B,0EAA0E;YAC1E,wBAAwB;YACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;YAED,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QACjC,CAAC,CAAC;QAxEA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,2EAA2E;QAC3E,wEAAwE;QACxE,IAAK,gBAA+B,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC3D,MAAM,OAAO,GAAG,gBAA8B,CAAC;YAC/C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAC/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YACjC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,GAAG,gBAAqB,CAAC;YACrC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,KAAK,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAID,aAAa;QACX,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,gBAAgB;QACd,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,IAAI,CAAC,aAAa,CACrB,IAAI,mBAAmB,CACrB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,CACf,CACF,CAAC;IACJ,CAAC;CAmCF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {\n  ContextCallback,\n  ContextRequestEvent,\n} from '../context-request-event.js';\nimport type {Context, ContextType} from '../create-context.js';\nimport type {\n  Reactive<PERSON>ontroller,\n  ReactiveControllerHost,\n} from '@lit/reactive-element';\n\nexport interface Options<C extends Context<unknown, unknown>> {\n  context: C;\n  callback?: (value: ContextType<C>, dispose?: () => void) => void;\n  subscribe?: boolean;\n}\n\n/**\n * A ReactiveController which adds context consuming behavior to a custom\n * element by dispatching `context-request` events.\n *\n * When the host element is connected to the document it will emit a\n * `context-request` event with its context key. When the context request\n * is satisfied the controller will invoke the callback, if present, and\n * trigger a host update so it can respond to the new value.\n *\n * It will also call the dispose method given by the provider when the\n * host element is disconnected.\n */\nexport class ContextConsumer<\n  C extends Context<unknown, unknown>,\n  HostElement extends ReactiveC<PERSON>rollerHost & HTMLElement,\n> implements ReactiveController\n{\n  protected host: HostElement;\n  private context: C;\n  private callback?: (value: ContextType<C>, dispose?: () => void) => void;\n  private subscribe = false;\n\n  private provided = false;\n\n  value?: ContextType<C> = undefined;\n\n  constructor(host: HostElement, options: Options<C>);\n  /** @deprecated Use new ContextConsumer(host, options) */\n  constructor(\n    host: HostElement,\n    context: C,\n    callback?: (value: ContextType<C>, dispose?: () => void) => void,\n    subscribe?: boolean\n  );\n  constructor(\n    host: HostElement,\n    contextOrOptions: C | Options<C>,\n    callback?: (value: ContextType<C>, dispose?: () => void) => void,\n    subscribe?: boolean\n  ) {\n    this.host = host;\n    // This is a potentially fragile duck-type. It means a context object can't\n    // have a property name context and be used in positional argument form.\n    if ((contextOrOptions as Options<C>).context !== undefined) {\n      const options = contextOrOptions as Options<C>;\n      this.context = options.context;\n      this.callback = options.callback;\n      this.subscribe = options.subscribe ?? false;\n    } else {\n      this.context = contextOrOptions as C;\n      this.callback = callback;\n      this.subscribe = subscribe ?? false;\n    }\n    this.host.addController(this);\n  }\n\n  private unsubscribe?: () => void;\n\n  hostConnected(): void {\n    this.dispatchRequest();\n  }\n\n  hostDisconnected(): void {\n    if (this.unsubscribe) {\n      this.unsubscribe();\n      this.unsubscribe = undefined;\n    }\n  }\n\n  private dispatchRequest() {\n    this.host.dispatchEvent(\n      new ContextRequestEvent(\n        this.context,\n        this.host,\n        this._callback,\n        this.subscribe\n      )\n    );\n  }\n\n  // This function must have stable identity to properly dedupe in ContextRoot\n  // if this element connects multiple times.\n  private _callback: ContextCallback<ContextType<C>> = (value, unsubscribe) => {\n    // some providers will pass an unsubscribe function indicating they may provide future values\n    if (this.unsubscribe) {\n      // if the unsubscribe function changes this implies we have changed provider\n      if (this.unsubscribe !== unsubscribe) {\n        // cleanup the old provider\n        this.provided = false;\n        this.unsubscribe();\n      }\n      // if we don't support subscription, immediately unsubscribe\n      if (!this.subscribe) {\n        this.unsubscribe();\n      }\n    }\n\n    // store the value so that it can be retrieved from the controller\n    this.value = value;\n    // schedule an update in case this value is used in a template\n    this.host.requestUpdate();\n\n    // only invoke callback if we are either expecting updates or have not yet\n    // been provided a value\n    if (!this.provided || this.subscribe) {\n      this.provided = true;\n      if (this.callback) {\n        this.callback(value, unsubscribe);\n      }\n    }\n\n    this.unsubscribe = unsubscribe;\n  };\n}\n"]}