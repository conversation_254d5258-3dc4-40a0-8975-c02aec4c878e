{"version": 3, "file": "consume.d.ts", "sourceRoot": "", "sources": ["../../../src/lib/decorators/consume.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,eAAe,EAAC,MAAM,uBAAuB,CAAC;AAEtD,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAS7C;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,wBAAgB,OAAO,CAAC,SAAS,EAAE,EACjC,OAAO,EACP,SAAS,GACV,EAAE;IACD,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACrC,SAAS,CAAC,EAAE,OAAO,CAAC;CACrB,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAkC9B;AAED;;;;GAIG;AACH,KAAK,SAAS,CAAC,CAAC,IAAI;KACjB,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACrB,CAAC;AAEF,KAAK,gBAAgB,CAAC,SAAS,IAAI;IAEjC,CACE,CAAC,SAAS,WAAW,EACrB,KAAK,SAAS,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,EAE5D,iBAAiB,EAAE,KAAK,EACxB,IAAI,CAAC,EAAE,CAAC,GACP,0BAA0B,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;IAGnD,CACE,CAAC,SAAS,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,EACxD,CAAC,SAAS,SAAS,EAEnB,KAAK,EAAE,4BAA4B,CAAC,CAAC,EAAE,CAAC,CAAC,EACzC,OAAO,EAAE,6BAA6B,CAAC,CAAC,EAAE,CAAC,CAAC,GAC3C,IAAI,CAAC;CACT,CAAC;AAIF,KAAK,eAAe,GAAG,IAAI,GAAG,GAAG,CAAC;AAElC,KAAK,0BAA0B,CAAC,GAAG,EAAE,GAAG,SAAS,WAAW,EAAE,YAAY,IAExE,GAAG,SAAS,MAAM,CAAC,GAAG,EAAE,MAAM,aAAa,CAAC,GAGxC;IAAC,YAAY;CAAC,SAAS,CAAC,aAAa,CAAC,GACpC,eAAe,GACf;IACE,OAAO,EAAE,iDAAiD,CAAC;IAC3D,QAAQ,EAAE,YAAY,CAAC;IACvB,SAAS,EAAE,aAAa,CAAC;CAC1B,GAEH,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,aAAa,CAAC,CAAC,GAGnD;IAAC,YAAY;CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,GAChD,eAAe,GACf;IACE,OAAO,EAAE,iDAAiD,CAAC;IAC3D,QAAQ,EAAE,YAAY,CAAC;IACvB,SAAS,EAAE,aAAa,GAAG,SAAS,CAAC;CACtC,GAKH,eAAe,CAAC"}