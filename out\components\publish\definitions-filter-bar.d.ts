import { LitElement } from "lit";
import "../base/col-item";
import "../base/row-item";
import "../base/fixed-item";
import "../base/icon";
import "../base/multi-combobox";
export declare class WexPublishDefinitionsFilterBar extends LitElement {
    pubDefs: any[] | null;
    projects: any[] | null;
    categories: any[] | null;
    string: any | null;
    private filterOptions;
    private activeFilters;
    private subscription;
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    stateChange(state: any): void;
    _init(): Promise<void>;
    _generateFilterOptions(): void;
    _createPubDef(): void;
    _handleFilterChange(e: CustomEvent): void;
    _handleCategorySelect: (e: any) => void;
    render(): import("lit").TemplateResult<1>;
}
//# sourceMappingURL=definitions-filter-bar.d.ts.map