import { registerIcon } from "@ui5/webcomponents-base/dist/asset-registries/Icons.js";

const name = "share-2";
const pathData = "M416 320q41 0 68.5 27.5T512 416t-27.5 68.5T416 512t-68.5-28-27.5-68l1-10-159-79q-28 25-65 25-18 0-35.5-6.5t-31-19-22-30.5T0 256t8.5-40 22-30.5 31-19T97 160q36 0 64 25l159-80v-9q0-40 27-68t69-28q41 0 68.5 27.5T512 96t-27.5 68.5T416 192q-48 0-78-39l-150 76q4 11 4 27 0 15-4 27l151 75q29-38 77-38zm0-269q-19 0-32 12.5T371 96q0 19 13 32t32 13q20 0 32.5-13T461 96q0-20-12.5-32.5T416 51zM96 301q20 0 32.5-12.5T141 256t-12.5-32.5T96 211t-32.5 12.5T51 256t12.5 32.5T96 301zm320 160q20 0 32.5-13t12.5-32q0-20-12.5-32.5T416 371q-19 0-32 12.5T371 416q0 19 13 32t32 13z";
const ltr = false;
const accData = null;
const collection = "SAP-icons-v5";
const packageName = "@ui5/webcomponents-icons";

registerIcon(name, { pathData, ltr, collection, packageName });

export default "SAP-icons-v5/share-2";
export { pathData, ltr, accData };