{"mappings": "A,S,E,C,C,C,C,C,C,C,E,O,c,C,E,E,C,I,E,I,E,W,C,E,a,C,C,E,C,I,E,A,W,iB,C,E,E,Q,C,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,S,E,S,E,S,I,E,E,S,E,E,S,E,E,QE+CA,OAAM,EAcL,IAAI,UAAJ,CACC,OAAO,EAAP,OAAA,AACD,CAEA,YAAY,CAA8B,CAAE,CAAgB,CAAE,CAAkB,CAAE,CAAoB,CAAtG,CAEC,IAAI,CAAC,SAAS,CAAG,EAGjB,IAAI,CAAC,QAAQ,CAAG,EAGhB,IAAI,CAAC,WAAW,CAAG,EAGnB,IAAI,CAAC,SAAS,CAAG,EAIjB,IAAI,CAAC,iBAAiB,CAAG,EAC1B,CAEA,KAAK,CAAgB,CAAE,CAAmB,CAA1C,CACC,EAAE,cAAc,GAChB,IAAM,EAAQ,CAAC,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,cAAc,EAAI,AAAgB,KAAhB,EAAqB,EAAI,EAEhF,OADA,IAAI,CAAC,qBAAqB,CAAC,CAAA,EAAqB,GACzC,CAAA,CACR,CAEA,OAAO,CAAgB,CAAE,CAAmB,CAA5C,CACC,EAAE,cAAc,GAChB,IAAM,EAAQ,CAAC,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,cAAc,EAAI,AAAgB,KAAhB,EAAqB,EAAI,EAEhF,OADA,IAAI,CAAC,qBAAqB,CAAC,CAAA,EAAoB,GACxC,CAAA,CACR,CAEA,QAAQ,CAAgB,CAAxB,OACC,EAAI,IAAI,CAAC,eAAe,KACvB,EAAE,cAAc,GAChB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAE,CAAA,GACjC,CAAA,EAGT,CAEA,QAAQ,CAAgB,CAAxB,QACC,AAAI,IAAI,CAAC,YAAY,EACpB,EAAE,cAAc,GACT,CAAA,KAGJ,IAAI,CAAC,eAAe,KACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAE,CAAA,GACjC,CAAA,EAIT,CAEA,SAAS,CAAgB,CAAzB,CACC,EAAE,cAAc,GAEhB,IAAM,EAAmB,IAAI,CAAC,iBAAiB,CAAG,GAAK,UAEnD,IAAI,CAAC,cAAc,EAAI,CAAC,EAC3B,IAAI,CAAC,gBAAgB,GAItB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAC7C,EAAmB,IAAI,CAAC,iBAAiB,EAAI,GAAK,IAAI,CAAC,iBAAiB,CAAG,GACrE,CAAA,CACR,CAEA,WAAW,CAAgB,CAA3B,CACC,EAAE,cAAc,GAEhB,IAAM,EAAQ,IAAI,CAAC,SAAS,GACtB,EAAgB,EAAM,MAAM,CAAG,EAC/B,EAAmB,IAAI,CAAC,iBAAiB,CAAG,IAAM,SAEpD,IAAI,CAAC,cAAc,EAAI,CAAC,EAC3B,IAAI,CAAC,gBAAgB,GAItB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAC7C,EAAmB,IAAI,CAAC,iBAAiB,EAAI,GAAK,IAAI,CAAC,iBAAiB,CAAG,GACrE,CAAA,CACR,CAEA,OAAO,CAAgB,CAAvB,QACC,EAAE,cAAc,GAEZ,IAAI,CAAC,cAAc,EACtB,IAAI,CAAC,gBAAgB,GAItB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAE,IAAI,CAAC,iBAAiB,CAAG,GAClE,CAAA,CACR,CAEA,MAAM,CAAgB,CAAtB,CACC,EAAE,cAAc,GAEhB,IAAM,EAAgB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAG,SAE5C,IAAI,CAAC,cAAc,EAAI,CAAC,EAC3B,IAAI,CAAC,gBAAgB,GAItB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAE,IAAI,CAAC,iBAAiB,CAAG,GAClE,CAAA,CACR,CAEA,OAAA,OACC,EAAI,IAAI,CAAC,eAAe,KACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAE,CAAA,GACjC,CAAA,EAGT,CAEA,OAAO,CAAgB,CAAE,CAAyC,CAAlE,CACgB,CAAA,AAAY,KAAA,IAAZ,EAAwB,EAAU,CAAC,IAAI,CAAC,QAAQ,EAA/D,EAGC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAG,CAAA,EAE5B,IAAI,CAAC,KAAK,CAAC,EAAQ,mBAAmB,CAExC,CAEA,IAAI,eAAJ,CACC,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,AAAA,GAAQ,EAAK,QAAQ,CAC3D,CAEA,eAAA,CACC,IAAM,EAAK,IAAI,CAAC,mBAAmB,GACnC,OAAO,EAAG,YAAY,CAAG,EAAG,YAAY,AACzC,CAEA,MAAM,EAAsB,CAAA,CAAK,CAAjC,CACC,IAAM,EAAe,IAAI,CAAC,SAAS,IAAM,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,AAEjF,CAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAG,CAAA,EAC5B,IAAM,EAAS,IAAI,CAAC,UAAU,EAC9B,CAAA,EAAO,mBAAmB,CAAG,EAC7B,EAAO,IAAI,CAAG,CAAA,EAEV,GAAgB,EAAa,OAAO,EACvC,CAAA,EAAa,OAAO,CAAG,CAAA,CADxB,CAGD,CAEA,2BAA2B,CAAW,CAAtC,CACC,IAAI,CAAC,iBAAiB,CAAG,CAC1B,CAEA,eAAe,CAAmD,CAAE,CAAqB,CAAzF,CAEC,IAAM,EAAgB,IAAI,CAAC,iBAAiB,GAD/B,IAOb,IAAI,CAAC,OAAO,CAAG,CACd,QAAS,AARG,EAQE,YAAY,CAAC,6BAC3B,WAAY,EAAc,OAAO,CATrB,GAS8B,EAC1C,SAAU,EAAc,MAAM,CAC9B,SAAU,AAXE,EAWG,IAAI,EAAI,GACvB,eAAgB,AAZJ,EAYS,cAAc,AACnC,EAED,IAAI,CAAC,aAAa,GAAG,cAAc,CAftB,EAe6B,GAC1C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAG,CAAA,EAC7B,CAEA,aAAa,CAA0B,CAAvC,CACC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,EACnC,CAIA,YAAY,CAAyE,CAArF,CAEC,IADI,EACE,EAAe,AAAW,mBAAX,EAAE,IAAI,CAItB,CAAA,CAAA,GAAkB,EAAE,MAAmC,CAAC,IAAI,CAAC,QAAQ,AAAR,GAAc,CAAA,CAAA,IAAI,CAAC,aAAa,EAAK,CAAA,IAInG,GAAiB,EAAE,MAAmC,CAAC,IAAI,CAAC,QAAQ,EACvE,EAAe,EAAE,MAAmC,CAAC,IAAI,CACzD,IAAI,CAAC,aAAa,CAAG,CAAA,GAErB,EAAe,EAAE,MAAyC,CAAC,aAAa,CAAC,EAAE,CAG5E,IAAI,CAAC,cAAc,CAAC,EAA+B,CAAA,GACpD,CAEA,UAAA,CACC,IAAI,CAAC,aAAa,CAAG,CAAA,CACtB,CAEA,iBAAA,CACC,OAAO,IAAI,CAAC,QAAQ,IAAM,AAA2B,OAA3B,IAAI,CAAC,iBAAiB,EAAa,AAA2B,KAA3B,IAAI,CAAC,iBAAiB,EAAW,CAAC,IAAI,CAAC,YAAY,AACjH,CAEA,IAAI,cAAJ,CACC,IAAM,EAAQ,IAAI,CAAC,SAAS,SAE5B,CAAI,CAAC,IAAS,CAAC,CAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAIrC,CAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,4BACnD,CAEA,UAAA,CACC,MAAO,CAAC,CAAE,IAAI,CAAC,UAAU,IAAI,IAC9B,CAEA,sBAAsB,CAAgB,CAAE,CAAa,CAArD,CACC,IAAI,CAAC,iBAAiB,CAAG,EAEpB,IAAI,CAAC,SAAS,GAAG,MAAM,GAGxB,EACH,IAAI,CAAC,eAAe,GAEpB,IAAI,CAAC,mBAAmB,GAE1B,CAEA,iBAAA,CACC,IAAM,EAAa,IAAI,CAAC,SAAS,GAAG,MAAM,CAEpC,EAAsB,IAAI,CAAC,iBAAiB,CAElD,GAAI,IAAI,CAAC,cAAc,EAAI,AAAwB,KAAxB,GAA8B,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAE,YAC9F,IAAI,CAAC,gBAAgB,GAIjB,CAAA,AAAwB,KAAxB,GAA8B,CAAC,IAAI,CAAC,cAAc,EAAK,IAAI,CAAC,SAAS,CAAC,oBAAoB,AAApB,IAC1E,IAAI,CAAC,qBAAqB,GAC1B,IAAI,CAAC,iBAAiB,CAAG,IAGE,KAAxB,GAA8B,EAAsB,EAAI,EAAa,GAIzE,IAAI,CAAC,kBAAkB,CAAC,EAAqB,EAAE,IAAI,CAAC,iBAAiB,CACtE,CAEA,qBAAA,CACC,IAAM,EAAQ,IAAI,CAAC,SAAS,GACtB,EAAsB,IAAI,CAAC,iBAAiB,CAElD,GAAI,IAAI,CAAC,cAAc,EAAI,AAAwB,IAAxB,GAA6B,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAE,CAC7F,IAAI,CAAC,SAAS,CAAC,yBAAyB,CAAG,CAAA,EAC3C,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAG,CAAA,EACtC,IAAI,CAAC,iBAAiB,CAAG,EAEzB,CAAK,CAAC,EAAE,CAAC,OAAO,CAAG,CAAA,EAEf,CAAK,CAAC,EAAE,CAAC,YAAY,CAAC,wBACxB,CAAA,CAAK,CAAC,EAAqB,CAAC,QAAQ,CAAG,CAAA,CADzC,EAIA,MACD,CAEA,GAAI,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAE,CACxC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAG,CAAA,EACzB,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAG,CAAA,EACtC,IAAI,CAAC,iBAAiB,CAAG,EAEzB,MACD,CAEA,GAAI,AAAwB,KAAxB,GAA8B,AAAwB,OAAxB,GAIlC,GAAI,EAAsB,EAAI,EAAG,CAC5B,CAAA,CAAK,CAAC,EAAoB,CAAC,YAAY,CAAC,wBAA0B,CAAK,CAAC,EAAoB,CAAC,YAAY,CAAC,6BAAA,GAC5G,CAAA,CAAK,CAAC,EAAuC,CAAC,QAAQ,CAAG,CAAA,CAD3D,EAIA,CAAK,CAAC,EAAoB,CAAC,OAAO,CAAG,CAAA,EAErC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAG,CAAA,EACzB,IAAI,CAAC,SAAS,CAAC,yBAAyB,CAAG,CAAA,EAC3C,IAAI,CAAC,iBAAiB,EAAI,EAC1B,MACD,CAEA,IAAI,CAAC,kBAAkB,CAAC,EAAqB,EAAE,IAAI,CAAC,iBAAiB,EACtE,CAEA,mBAAmB,CAAmB,CAAE,CAAe,CAAvD,CACC,IAAM,EAAQ,IAAI,CAAC,SAAS,GACtB,EAAc,CAAK,CAAC,EAAQ,CAC5B,EAAe,CAAK,CAAC,EAAY,CACjC,EAAgB,IAAI,CAAC,iBAAiB,GACtC,EAAc,EAAY,YAAY,CAAC,6BAE7C,GAAI,CAAC,EACJ,MAGD,CAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAG,CAAA,EACzB,IAAI,CAAC,qBAAqB,GAE1B,IAAM,EAAe,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAoC7D,GAlCA,IAAI,CAAC,OAAO,CAAG,CACd,QAAS,EACT,WAAY,EAAM,OAAO,CAAC,GAAe,EACzC,SAAU,AAAC,CAAA,EAAe,EAAqC,UAAU,CAAI,EAAgD,IAAI,AAAJ,GAAS,EACtI,EAEG,CAAA,EAAY,YAAY,CAAC,wBAA0B,EAAY,YAAY,CAAC,6BAAA,IAC/E,IAAI,CAAC,OAAO,CAAC,cAAc,CAAI,EAA+C,cAAc,EAAI,GAChG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAG,EAAc,OAAO,CAAC,GAAiD,EACjG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAG,EAAc,MAAM,EAGzC,GACH,CAAA,EAAa,OAAO,CAAG,CAAA,CADxB,EAGI,CAAA,GAAc,aAAa,wBAA0B,GAAc,aAAa,6BAAA,GAClF,CAAA,EAAgD,QAAQ,CAAG,CAAA,CAD7D,EAII,IACH,EAAY,OAAO,CAAG,CAAA,EAElB,AAAC,GACH,CAAA,EAA+C,QAAQ,CAAG,CAAA,CAD5D,EAII,IAAI,CAAC,WAAW,EACnB,EAAY,KAAK,IAInB,IAAI,CAAC,SAAS,CAAC,yBAAyB,CAAG,CAAA,EAC3C,IAAI,CAAC,YAAY,CAAC,GAEd,CAAC,IAAI,CAAC,eAAe,CAAC,GAAc,CACvC,IAAM,EAAU,IAAI,CAAC,YAAY,CAAI,EAAY,UAAW,CAAC,aAAa,CAAC,yBAAoD,EAC/H,IAAI,CAAC,mBAAmB,CAAC,EAC1B,CACD,CAEA,gBAAA,CAEC,AADc,IAAI,CAAC,SAAS,GACtB,OAAO,CAAC,AAAA,IACT,EAAK,YAAY,CAAC,wBACpB,CAAA,EAAwB,QAAQ,CAAG,CAAA,CADrC,EAIA,EAAK,OAAO,CAAG,CAAA,CAChB,EACD,CAEA,iBAAA,CACC,IAAM,EAAc,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,AAAA,GAAQ,EAAK,OAAO,CAE1D,CAAA,GACH,CAAA,EAAY,OAAO,CAAG,CAAA,CADvB,CAGD,CAEA,gBAAgB,CAA0B,CAA1C,CACC,IAAM,EAAW,EAAK,SAAS,GAAI,qBAAqB,GAClD,EAAY,IAAI,CAAC,aAAa,GAAG,SAAS,GAAI,qBAAqB,GACnE,EAAgB,OAAO,WAAW,EAAI,SAAS,eAAe,CAAC,YAAY,CAC7E,EAAe,EAOnB,OALI,IAAI,CAAC,cAAc,EAEtB,CAAA,EAAe,AADU,IAAI,CAAC,UAAU,GAAG,aAAa,CAAC,iBACzB,qBAAqB,GAAG,MAAM,AAAN,EAGjD,EAAS,GAAG,CAAG,EAAY,WAAW,EAAI,GAAkB,EAAS,GAAG,EAAI,EAAU,GAAG,CAAG,CACrG,CAEA,oBAAoB,CAA0B,CAA9C,CACC,EAAK,cAAc,CAAC,CACnB,SAAU,OACV,MAAO,UACP,OAAQ,SACR,EACF,CAEA,qBAAA,CAKC,OAJI,AAAC,IAAI,CAAC,gBAAgB,EACzB,CAAA,IAAI,CAAC,gBAAgB,CAAG,IAAI,CAAC,UAAU,GAAI,UAAW,CAAC,aAAa,CAAC,qBADtE,EAIO,IAAI,CAAC,gBAAgB,AAC7B,CAMA,WAAA,CAGC,OAAO,AAFqB,IAAI,CAAC,aAAa,GAEnB,eAAe,CAAuB,mBAAmB,OAAO,CAAC,AAAA,GACpF,EAAK,YAAY,CAAC,6BAA+B,CAAC,KAAS,EAAK,KAAM,CAAC,CAAG,CAAC,EAAK,CAEzF,CAEA,mBAAA,CACC,OAAO,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,AAAA,GAAQ,CAAC,EAAK,YAAY,CAAC,6BAC3D,CAEA,eAAA,CACC,OAAO,IAAI,CAAC,SAAS,AACtB,CAEA,UAAA,CACC,OAAO,IAAI,CAAC,UAAU,GAAG,aAAa,CAAO,aAC9C,CAEA,eAAA,CACC,OAAO,IAAI,CAAC,QAAQ,IAAI,WACzB,CAEA,YAAA,CACC,OAAO,IAAI,CAAC,aAAa,GAAG,UAAW,CAAC,aAAa,CAAoB,2BAC1E,CAEA,IAAI,uBAAJ,CACC,GAAI,CAAC,IAAI,CAAC,OAAO,CAChB,MAAO,GAGR,GAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CACvB,MAAO,CAAA,EAAG,EAAY,UAAU,CAAC,OAAO,CAAC,EAAA,sBAAA,EAAuB,CAAA,EAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA,CAAE,CAG5F,IAAM,EAAmB,EAAY,UAAU,CAAC,OAAO,CAAC,EAAA,kBAAA,CAAoB,IAAI,CAAC,OAAO,CAAC,UAAU,EAAI,EAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAI,GAEnI,MAAO,CAAA,EAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAA,CAAA,EAAI,EAAA,CAAkB,AAC5D,CAEA,gBAAgB,CAAY,CAAE,CAAa,CAA3C,CACC,MAAO,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAA0B,EAAM,EACxC,CAEA,IAAI,gBAAJ,CACC,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,AAC3C,CAEA,kBAAA,CACC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAG,CAAA,EACtC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAG,CAAA,EACzB,IAAI,CAAC,SAAS,CAAC,yBAAyB,CAAG,CAAA,EAC3C,IAAI,CAAC,iBAAiB,CAAG,EACzB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAElD,IAAI,CAAC,cAAc,EACpB,CAEA,uBAAA,CACC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAG,CAAA,CACvC,CAEA,oCAAA,CACC,IAAI,CAAC,OAAO,CAAG,KAAA,EACf,IAAI,CAAC,iBAAiB,CAAG,CAC1B,C,CAxeO,EAAA,WAAW,CAAG,GA2etB,AAAA,EAAA,OAAA,CAAM,gBAAgB,CAAG,EAEzB,IAAA,EAAe,C,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,S,E,E,SEniBf,SAAS,EAAW,CAAY,CAAE,CAAY,CAAE,CAAiE,CAAE,CAAwB,EAC1I,OAAO,EAAK,UAAU,CAAC,AAAI,OAAO,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAY,GAAO,CAAA,EAAG,EAAkB,IAAM,GAAE,CAAA,CAAG,EAAG,EACzF,CAmCA,IAAA,EA1BA,SAAmC,CAAY,CAAE,CAAuB,EACvE,GAAI,CAAC,GAAQ,CAAC,EACb,OAAO,EAIR,IAAM,EAAY,AAAC,IAClB,GAAM,CAAC,EAAG,EAAE,CAAG,EAAE,KAAK,CAAC,IACvB,KAAO,EAAK,OAAO,CAAC,IAAM,GAAK,EAAgB,OAAO,CAAC,IAAM,GAC5D,EAAI,CAAA,EAAG,EAAA,EAAI,EAAA,EAAI,EAAA,CAAG,CAEnB,OAAO,CACR,EAGM,EAAY,EAAU,MACtB,EAAa,EAAU,MAEzB,EAAS,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAU,EAAW,EAAM,EAAiB,AAAC,GAAkB,CAAA,EAAG,EAAA,EAAY,EAAA,EAAQ,EAAA,CAAY,CAAE,CAAA,IAKjH,MAHA,CAAC,CAAC,EAAW,MAAM,CAAE,CAAC,EAAY,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAM,EAAQ,IAClE,EAAS,EAAW,EAAQ,EAAM,EAAS,CAAA,EAC5C,GACO,CACR,C,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,GE9BA,IAAA,EAJoB,AAAC,GACb,EAAK,OAAO,CAAC,sBAAuB,O,G,E,Q,S,C,C,C,E,I,E,E,S,E,E,S,E,E,S,E,E,S,E,S,C,C,C,C,C,C,C,E,I,E,E,U,M,C,E,E,E,E,A,O,E,E,O,wB,C,E,G,E,G,A,U,O,S,A,Y,O,Q,Q,C,E,Q,Q,C,E,E,E,Q,I,I,E,E,M,C,E,G,E,I,C,E,C,C,E,A,G,C,E,A,C,E,E,E,G,E,E,E,E,E,G,E,E,E,G,C,E,O,E,G,G,O,c,C,E,E,G,C,EEY5C,IAAM,EAAN,cAAkC,EAAA,OAAA,CAWjC,EADA,EAAA,CALC,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAK,CACL,QAAW,CAAA,EACX,wBAAyB,CAAA,EACzB,KAAM,WACN,GACiC,CAAA,EAAA,SAAA,CAAA,QAAA,KAAA,GAGnC,AAbM,CAAA,EAAA,EAAA,CAJL,AAAA,CAAA,EAAA,EAAA,OAAA,AAAA,EAAc,CACd,IAAK,4BACL,SAAU,EAAA,OAAA,AACV,GACK,CAAA,EAAA,EAac,MAAM,E,G,E,Q,S,C,C,C,E,E,E,O,C,U,I,G,I,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,S,E,E,SEtBZ,SAAA,EAAgD,CAA4K,EACzO,IAAM,EAAkB,GAAO,iBAAmB,EAC5C,EAAoB,GAAO,kBAC3B,EAA6B,GAAO,2BAE1C,MACC,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAC,EAAA,OAAA,CAAiB,CACjB,MAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAC3B,UAAW,CAAA,EACX,oBAAqB,CAAA,EACrB,oBAAqB,CAAA,EACrB,UAAU,SACV,gBAAgB,QAChB,SAAU,GACV,MAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CACrC,OAAQ,IAAI,CAAC,gBAAgB,CAC7B,QAAS,IAAI,CAAC,iBAAiB,CAC/B,SAAU,IAAI,CAAC,OAAO,CACtB,KAAM,IAAI,CAAC,IAAI,CACf,OAAQ,IAAI,CACZ,eAAgB,IAAI,CAAC,WAAW,CAAA,SAAA,CAE/B,IAAI,CAAC,QAAQ,EACb,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAA,EAAA,QAAA,CAAA,CAAA,SAAA,CACC,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAA,MAAA,CAAK,KAAK,SAAS,MAAM,gCAA+B,SAAA,CACvD,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAA,MAAA,CAAK,MAAM,MAAK,SAAA,CACf,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,OAAA,CAAA,SAAO,IAAI,CAAC,gBAAgB,AAAA,GAC5B,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAM,CACN,MAAM,mCACN,KAAM,EAAA,OAAA,CACN,OAAO,cACP,QAAS,IAAI,CAAC,YAAY,AAAA,GAElB,AAAA,GAEV,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,MAAA,CAAK,MAAM,MAAK,SACf,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,MAAA,CAAK,MAAM,wCAAuC,SACjD,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAK,CACL,MAAM,wBACN,KAAM,IAAI,CAAC,SAAS,CACpB,MAAO,IAAI,CAAC,KAAK,CACjB,cAAe,IAAI,CAAC,aAAa,CACjC,YAAa,IAAI,CAAC,WAAW,CAC7B,QAAS,IAAI,CAAC,YAAY,CAC1B,SAAU,IAAI,CAAC,aAAa,AAAA,EAC3B,EACG,GACD,AAAA,GAGN,IAAI,CAAC,oBAAoB,EAC1B,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAA,MAAA,CAAK,MAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAE,MAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAA,SAAA,CACrF,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAI,CAAC,MAAM,qCAAqC,KAAM,GAA4B,KAAK,IAAI,CAAC,GAC3F,IAAI,CAAC,IAAI,EAAI,GAAmB,KAAK,IAAI,EAAC,AAAA,GACvC,AAAA,GAKP,CAAC,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,oBAAoB,EAC1C,AAAA,CAAA,EAAA,EAAA,IAAA,AAAA,EAAA,MAAA,CACC,KAAK,SACL,MAAO,CACN,gCAAiC,CAAA,EACjC,yCAA0C,IAAI,CAAC,oBAAoB,CACnE,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,AACjC,EACD,MAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAA,SAAA,CAE1C,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAI,CAAC,MAAM,qCAAqC,KAAM,GAA4B,KAAK,IAAI,CAAC,GAC3F,IAAI,CAAC,IAAI,EAAI,GAAmB,KAAK,IAAI,EAAC,AAAA,GAI7C,EAAgB,IAAI,CAAC,IAAI,EAE1B,IAAI,CAAC,QAAQ,EACb,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,MAAA,CAAK,KAAK,SAAS,MAAM,gCAA+B,SACvD,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAM,CACN,OAAO,cACP,QAAS,IAAI,CAAC,YAAY,CAAA,SAEzB,IAAI,CAAC,wBAAwB,AAAA,EACtB,GACJ,AAAA,EAIV,CAEA,SAAS,IACR,MACC,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAC,EAAA,OAAA,CAAI,CACJ,eAAgB,AAAA,EAAA,OAAA,CAAmB,OAAO,CAC1C,WAAY,IAAI,CAAC,oBAAoB,CACrC,cAAc,SACd,YAAa,IAAI,CAAC,eAAe,CACjC,YAAa,IAAI,CAAC,0BAA0B,CAC5C,kBAAmB,IAAI,CAAC,sBAAsB,CAAA,SAE9C,AAAA,CAAA,EAAA,EAAA,GAAA,AAAA,EAAA,OAAA,CAAA,EAAa,EAGhB,C", "sources": ["<anon>", "node_modules/@ui5/webcomponents/dist/features/InputSuggestions.js", "node_modules/@ui5/webcomponents/src/features/InputSuggestions.ts", "node_modules/@ui5/webcomponents-base/dist/util/generateHighlightedMarkup.js", "node_modules/@ui5/webcomponents-base/src/util/generateHighlightedMarkup.ts", "node_modules/@ui5/webcomponents-base/dist/util/escapeRegex.js", "node_modules/@ui5/webcomponents-base/src/util/escapeRegex.ts", "node_modules/@ui5/webcomponents/dist/SuggestionItemGroup.js", "node_modules/@ui5/webcomponents/src/SuggestionItemGroup.ts", "node_modules/@ui5/webcomponents/dist/features/InputSuggestionsTemplate.js", "node_modules/@ui5/webcomponents/src/features/InputSuggestionsTemplate.tsx"], "sourcesContent": ["\nfunction $parcel$export(e, n, v, s) {\n  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});\n}\n\n      var $parcel$global = globalThis;\n    var parcelRequire = $parcel$global[\"parcelRequire94c2\"];\nvar parcelRegister = parcelRequire.register;\nparcelRegister(\"epwvW\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $a7dc9f2231b7cc7d$export$2e2bcd8739ae039);\n\nvar $3e6om = parcelRequire(\"3e6om\");\nparcelRequire(\"lbnaY\");\nparcelRequire(\"kOmhP\");\n\nvar $5Fd8G = parcelRequire(\"5Fd8G\");\n\nvar $65idz = parcelRequire(\"65idz\");\n\nvar $TUBXI = parcelRequire(\"TUBXI\");\n/**\n * A class to manage the `Input` suggestion items.\n * @class\n * @private\n */ class $a7dc9f2231b7cc7d$var$Suggestions {\n    get template() {\n        return 0, $5Fd8G.default;\n    }\n    constructor(component, slotName, highlight, handleFocus){\n        // The component, that the suggestion would plug into.\n        this.component = component;\n        // Defines the items` slot name.\n        this.slotName = slotName;\n        // Defines, if the focus will be moved via the arrow keys.\n        this.handleFocus = handleFocus;\n        // Defines, if the suggestions should highlight.\n        this.highlight = highlight;\n        // An integer value to store the currently selected item position,\n        // that changes due to user interaction.\n        this.selectedItemIndex = -1;\n    }\n    onUp(e, indexOfItem) {\n        e.preventDefault();\n        const index = !this.isOpened && this._hasValueState && indexOfItem === -1 ? 0 : indexOfItem;\n        this._handleItemNavigation(false, index);\n        return true;\n    }\n    onDown(e, indexOfItem) {\n        e.preventDefault();\n        const index = !this.isOpened && this._hasValueState && indexOfItem === -1 ? 0 : indexOfItem;\n        this._handleItemNavigation(true, index);\n        return true;\n    }\n    onSpace(e) {\n        if (this._isItemOnTarget()) {\n            e.preventDefault();\n            this.onItemSelected(this._selectedItem, true);\n            return true;\n        }\n        return false;\n    }\n    onEnter(e) {\n        if (this._isGroupItem) {\n            e.preventDefault();\n            return false;\n        }\n        if (this._isItemOnTarget()) {\n            this.onItemSelected(this._selectedItem, true);\n            return true;\n        }\n        return false;\n    }\n    onPageUp(e) {\n        e.preventDefault();\n        const isItemIndexValid = this.selectedItemIndex - 10 > -1;\n        if (this._hasValueState && !isItemIndexValid) {\n            this._focusValueState();\n            return true;\n        }\n        this._moveItemSelection(this.selectedItemIndex, isItemIndexValid ? this.selectedItemIndex -= 10 : this.selectedItemIndex = 0);\n        return true;\n    }\n    onPageDown(e) {\n        e.preventDefault();\n        const items = this._getItems();\n        const lastItemIndex = items.length - 1;\n        const isItemIndexValid = this.selectedItemIndex + 10 <= lastItemIndex;\n        if (this._hasValueState && !items) {\n            this._focusValueState();\n            return true;\n        }\n        this._moveItemSelection(this.selectedItemIndex, isItemIndexValid ? this.selectedItemIndex += 10 : this.selectedItemIndex = lastItemIndex);\n        return true;\n    }\n    onHome(e) {\n        e.preventDefault();\n        if (this._hasValueState) {\n            this._focusValueState();\n            return true;\n        }\n        this._moveItemSelection(this.selectedItemIndex, this.selectedItemIndex = 0);\n        return true;\n    }\n    onEnd(e) {\n        e.preventDefault();\n        const lastItemIndex = this._getItems().length - 1;\n        if (this._hasValueState && !lastItemIndex) {\n            this._focusValueState();\n            return true;\n        }\n        this._moveItemSelection(this.selectedItemIndex, this.selectedItemIndex = lastItemIndex);\n        return true;\n    }\n    onTab() {\n        if (this._isItemOnTarget()) {\n            this.onItemSelected(this._selectedItem, true);\n            return true;\n        }\n        return false;\n    }\n    toggle(bToggle, options) {\n        const toggle = bToggle !== undefined ? bToggle : !this.isOpened();\n        if (toggle) this._getComponent().open = true;\n        else this.close(options.preventFocusRestore);\n    }\n    get _selectedItem() {\n        return this._getNonGroupItems().find((item)=>item.selected);\n    }\n    _isScrollable() {\n        const sc = this._getScrollContainer();\n        return sc.offsetHeight < sc.scrollHeight;\n    }\n    close(preventFocusRestore = false) {\n        const selectedItem = this._getItems() && this._getItems()[this.selectedItemIndex];\n        this._getComponent().open = false;\n        const picker = this._getPicker();\n        picker.preventFocusRestore = preventFocusRestore;\n        picker.open = false;\n        if (selectedItem && selectedItem.focused) selectedItem.focused = false;\n    }\n    updateSelectedItemPosition(pos) {\n        this.selectedItemIndex = pos;\n    }\n    onItemSelected(selectedItem, keyboardUsed) {\n        const item = selectedItem;\n        const nonGroupItems = this._getNonGroupItems();\n        if (!item) return;\n        this.accInfo = {\n            isGroup: item.hasAttribute(\"ui5-suggestion-item-group\"),\n            currentPos: nonGroupItems.indexOf(item) + 1,\n            listSize: nonGroupItems.length,\n            itemText: item.text || \"\",\n            additionalText: item.additionalText\n        };\n        this._getComponent().onItemSelected(item, keyboardUsed);\n        this._getComponent().open = false;\n    }\n    onItemSelect(item) {\n        this._getComponent().onItemSelect(item);\n    }\n    /* Private methods */ // Note: Split into two separate handlers\n    onItemPress(e) {\n        let pressedItem; // SuggestionListItem\n        const isPressEvent = e.type === \"ui5-item-click\";\n        // Only use the press e if the item is already selected, in all other cases we are listening for 'ui5-selection-change' from the list\n        // Also we have to check if the selection-change is fired by the list's 'item-click' event handling, to avoid double handling on our side\n        if (isPressEvent && !e.detail.item.selected || this._handledPress && !isPressEvent) return;\n        if (isPressEvent && e.detail.item.selected) {\n            pressedItem = e.detail.item;\n            this._handledPress = true;\n        } else pressedItem = e.detail.selectedItems[0];\n        this.onItemSelected(pressedItem, false);\n    }\n    _onClose() {\n        this._handledPress = false;\n    }\n    _isItemOnTarget() {\n        return this.isOpened() && this.selectedItemIndex !== null && this.selectedItemIndex !== -1 && !this._isGroupItem;\n    }\n    get _isGroupItem() {\n        const items = this._getItems();\n        if (!items || !items[this.selectedItemIndex]) return false;\n        return items[this.selectedItemIndex].hasAttribute(\"ui5-suggestion-item-group\");\n    }\n    isOpened() {\n        return !!this._getPicker()?.open;\n    }\n    _handleItemNavigation(forward, index) {\n        this.selectedItemIndex = index;\n        if (!this._getItems().length) return;\n        if (forward) this._selectNextItem();\n        else this._selectPreviousItem();\n    }\n    _selectNextItem() {\n        const itemsCount = this._getItems().length;\n        const previousSelectedIdx = this.selectedItemIndex;\n        if (this._hasValueState && previousSelectedIdx === -1 && !this.component._isValueStateFocused) {\n            this._focusValueState();\n            return;\n        }\n        if (previousSelectedIdx === -1 && !this._hasValueState || this.component._isValueStateFocused) {\n            this._clearValueStateFocus();\n            this.selectedItemIndex = -1;\n        }\n        if (previousSelectedIdx !== -1 && previousSelectedIdx + 1 > itemsCount - 1) return;\n        this._moveItemSelection(previousSelectedIdx, ++this.selectedItemIndex);\n    }\n    _selectPreviousItem() {\n        const items = this._getItems();\n        const previousSelectedIdx = this.selectedItemIndex;\n        if (this._hasValueState && previousSelectedIdx === 0 && !this.component._isValueStateFocused) {\n            this.component.hasSuggestionItemSelected = false;\n            this.component._isValueStateFocused = true;\n            this.selectedItemIndex = 0;\n            items[0].focused = false;\n            if (items[0].hasAttribute(\"ui5-suggestion-item\")) items[0].selected = false;\n            return;\n        }\n        if (this.component._isValueStateFocused) {\n            this.component.focused = true;\n            this.component._isValueStateFocused = false;\n            this.selectedItemIndex = 0;\n            return;\n        }\n        if (previousSelectedIdx === -1 || previousSelectedIdx === null) return;\n        if (previousSelectedIdx - 1 < 0) {\n            if (items[previousSelectedIdx].hasAttribute(\"ui5-suggestion-item\") || items[previousSelectedIdx].hasAttribute(\"ui5-suggestion-item-custom\")) items[previousSelectedIdx].selected = false;\n            items[previousSelectedIdx].focused = false;\n            this.component.focused = true;\n            this.component.hasSuggestionItemSelected = false;\n            this.selectedItemIndex -= 1;\n            return;\n        }\n        this._moveItemSelection(previousSelectedIdx, --this.selectedItemIndex);\n    }\n    _moveItemSelection(previousIdx, nextIdx) {\n        const items = this._getItems();\n        const currentItem = items[nextIdx];\n        const previousItem = items[previousIdx];\n        const nonGroupItems = this._getNonGroupItems();\n        const isGroupItem = currentItem.hasAttribute(\"ui5-suggestion-item-group\");\n        if (!currentItem) return;\n        this.component.focused = false;\n        this._clearValueStateFocus();\n        const selectedItem = this._getItems()[this.selectedItemIndex];\n        this.accInfo = {\n            isGroup: isGroupItem,\n            currentPos: items.indexOf(currentItem) + 1,\n            itemText: (isGroupItem ? selectedItem.headerText : selectedItem.text) || \"\"\n        };\n        if (currentItem.hasAttribute(\"ui5-suggestion-item\") || currentItem.hasAttribute(\"ui5-suggestion-item-custom\")) {\n            this.accInfo.additionalText = currentItem.additionalText || \"\";\n            this.accInfo.currentPos = nonGroupItems.indexOf(currentItem) + 1;\n            this.accInfo.listSize = nonGroupItems.length;\n        }\n        if (previousItem) previousItem.focused = false;\n        if (previousItem?.hasAttribute(\"ui5-suggestion-item\") || previousItem?.hasAttribute(\"ui5-suggestion-item-custom\")) previousItem.selected = false;\n        if (currentItem) {\n            currentItem.focused = true;\n            if (!isGroupItem) currentItem.selected = true;\n            if (this.handleFocus) currentItem.focus();\n        }\n        this.component.hasSuggestionItemSelected = true;\n        this.onItemSelect(currentItem);\n        if (!this._isItemIntoView(currentItem)) {\n            const itemRef = this._isGroupItem ? currentItem.shadowRoot.querySelector(\"[ui5-li-group-header]\") : currentItem;\n            this._scrollItemIntoView(itemRef);\n        }\n    }\n    _deselectItems() {\n        const items = this._getItems();\n        items.forEach((item)=>{\n            if (item.hasAttribute(\"ui5-suggestion-item\")) item.selected = false;\n            item.focused = false;\n        });\n    }\n    _clearItemFocus() {\n        const focusedItem = this._getItems().find((item)=>item.focused);\n        if (focusedItem) focusedItem.focused = false;\n    }\n    _isItemIntoView(item) {\n        const rectItem = item.getDomRef().getBoundingClientRect();\n        const rectInput = this._getComponent().getDomRef().getBoundingClientRect();\n        const windowHeight = window.innerHeight || document.documentElement.clientHeight;\n        let headerHeight = 0;\n        if (this._hasValueState) {\n            const valueStateHeader = this._getPicker().querySelector(\"[slot=header]\");\n            headerHeight = valueStateHeader.getBoundingClientRect().height;\n        }\n        return rectItem.top + $a7dc9f2231b7cc7d$var$Suggestions.SCROLL_STEP <= windowHeight && rectItem.top >= rectInput.top + headerHeight;\n    }\n    _scrollItemIntoView(item) {\n        item.scrollIntoView({\n            behavior: \"auto\",\n            block: \"nearest\",\n            inline: \"nearest\"\n        });\n    }\n    _getScrollContainer() {\n        if (!this._scrollContainer) this._scrollContainer = this._getPicker().shadowRoot.querySelector(\".ui5-popup-content\");\n        return this._scrollContainer;\n    }\n    /**\n     * Returns the items in 1D array.\n     *\n     */ _getItems() {\n        const suggestionComponent = this._getComponent();\n        return suggestionComponent.getSlottedNodes(\"suggestionItems\").flatMap((item)=>{\n            return item.hasAttribute(\"ui5-suggestion-item-group\") ? [\n                item,\n                ...item.items\n            ] : [\n                item\n            ];\n        });\n    }\n    _getNonGroupItems() {\n        return this._getItems().filter((item)=>!item.hasAttribute(\"ui5-suggestion-item-group\"));\n    }\n    _getComponent() {\n        return this.component;\n    }\n    _getList() {\n        return this._getPicker().querySelector(\"[ui5-list]\");\n    }\n    _getListWidth() {\n        return this._getList()?.offsetWidth;\n    }\n    _getPicker() {\n        return this._getComponent().shadowRoot.querySelector(\"[ui5-responsive-popover]\");\n    }\n    get itemSelectionAnnounce() {\n        if (!this.accInfo) return \"\";\n        if (this.accInfo.isGroup) return `${$a7dc9f2231b7cc7d$var$Suggestions.i18nBundle.getText((0, $TUBXI.LIST_ITEM_GROUP_HEADER))} ${this.accInfo.itemText}`;\n        const itemPositionText = $a7dc9f2231b7cc7d$var$Suggestions.i18nBundle.getText((0, $TUBXI.LIST_ITEM_POSITION), this.accInfo.currentPos || 0, this.accInfo.listSize || 0);\n        return `${this.accInfo.additionalText} ${itemPositionText}`;\n    }\n    hightlightInput(text, input) {\n        return (0, $3e6om.default)(text, input);\n    }\n    get _hasValueState() {\n        return this.component.hasValueStateMessage;\n    }\n    _focusValueState() {\n        this.component._isValueStateFocused = true;\n        this.component.focused = false;\n        this.component.hasSuggestionItemSelected = false;\n        this.selectedItemIndex = 0;\n        this.component.value = this.component.typedInValue;\n        this._deselectItems();\n    }\n    _clearValueStateFocus() {\n        this.component._isValueStateFocused = false;\n    }\n    _clearSelectedSuggestionAndaccInfo() {\n        this.accInfo = undefined;\n        this.selectedItemIndex = 0;\n    }\n}\n$a7dc9f2231b7cc7d$var$Suggestions.SCROLL_STEP = 60;\n(0, $65idz.default).SuggestionsClass = $a7dc9f2231b7cc7d$var$Suggestions;\nvar $a7dc9f2231b7cc7d$export$2e2bcd8739ae039 = $a7dc9f2231b7cc7d$var$Suggestions;\n\n});\nparcelRegister(\"3e6om\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $2597a33886fa5427$export$2e2bcd8739ae039);\n\nvar $hNhGB = parcelRequire(\"hNhGB\");\n\nvar $2LNch = parcelRequire(\"2LNch\");\n// utility to replace all occurances of a string\nfunction $2597a33886fa5427$var$replaceAll(text, find, replace, caseInsensitive) {\n    return text.replaceAll(new RegExp((0, $hNhGB.default)(find), `${caseInsensitive ? \"i\" : \"\"}g`), replace);\n}\n/**\n * Generate markup for a raw string where a particular text is wrapped with some tag, by default `<b>` (bold) tag.\n * All inputs to this function are considered literal text, and special characters will always be escaped.\n * @param {string} text The text to add highlighting to\n * @param {string} textToHighlight The text which should be highlighted\n * @return {string} the markup HTML which contains all occurrances of the input text surrounded with a `<b>` tag.\n */ function $2597a33886fa5427$var$generateHighlightedMarkup(text, textToHighlight) {\n    if (!text || !textToHighlight) return text;\n    // a token is some string that does not appear in either of the input strings\n    // repeat the token until it does not appear in the string\n    const makeToken = (t)=>{\n        const [s, e] = t.split(\"\");\n        while(text.indexOf(t) >= 0 || textToHighlight.indexOf(t) >= 0)t = `${s}${t}${e}`;\n        return t;\n    };\n    // It doesn't matter what characters are used as long as all 4 of them are unique\n    // And also that encodeXML will not change these characters\n    const openToken = makeToken(\"12\");\n    const closeToken = makeToken(\"34\");\n    // wrap every occurance of the textToHighlight using the open/close tokens (instead of markup at this point)\n    let result = (0, $2LNch.default)($2597a33886fa5427$var$replaceAll(text, textToHighlight, (match)=>`${openToken}${match}${closeToken}`, true));\n    // now replace the open and close tokens with the markup that we expect\n    [\n        [\n            openToken,\n            \"<b>\"\n        ],\n        [\n            closeToken,\n            \"</b>\"\n        ]\n    ].forEach(([find, replace])=>{\n        result = $2597a33886fa5427$var$replaceAll(result, find, replace, false);\n    });\n    return result;\n}\nvar $2597a33886fa5427$export$2e2bcd8739ae039 = $2597a33886fa5427$var$generateHighlightedMarkup;\n\n});\nparcelRegister(\"hNhGB\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $cf44bb636867246b$export$2e2bcd8739ae039);\n/**\n * Escapes a regular expression text so that it can be used in a regular expression.\n *\n * @param { string } text the string to be interpreted literally\n * @returns { string } the escaped string\n */ const $cf44bb636867246b$var$escapeRegex = (text)=>{\n    return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\nvar $cf44bb636867246b$export$2e2bcd8739ae039 = $cf44bb636867246b$var$escapeRegex;\n\n});\n\n\nparcelRegister(\"kOmhP\", function(module, exports) {\n\nvar $c6CCt = parcelRequire(\"c6CCt\");\n\nvar $aaoB5 = parcelRequire(\"aaoB5\");\n\nvar $809gB = parcelRequire(\"809gB\");\n\nvar $c9ojM = parcelRequire(\"c9ojM\");\nvar $f269c3c8be5a1789$var$__decorate = undefined && undefined.__decorate || function(decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n/**\n * @class\n * The `ui5-suggestion-item-group` is type of suggestion item,\n * that can be used to split the `ui5-input` suggestions into groups.\n * @constructor\n * @extends ListItemGroup\n * @public\n * @since 2.0.0\n */ let $f269c3c8be5a1789$var$SuggestionItemGroup = class SuggestionItemGroup extends (0, $809gB.default) {\n};\n$f269c3c8be5a1789$var$__decorate([\n    (0, $aaoB5.default)({\n        \"default\": true,\n        invalidateOnChildChange: true,\n        type: HTMLElement\n    })\n], $f269c3c8be5a1789$var$SuggestionItemGroup.prototype, \"items\", void 0);\n$f269c3c8be5a1789$var$SuggestionItemGroup = $f269c3c8be5a1789$var$__decorate([\n    (0, $c6CCt.default)({\n        tag: \"ui5-suggestion-item-group\",\n        template: (0, $c9ojM.default)\n    })\n], $f269c3c8be5a1789$var$SuggestionItemGroup);\n$f269c3c8be5a1789$var$SuggestionItemGroup.define();\nvar $f269c3c8be5a1789$export$2e2bcd8739ae039 = $f269c3c8be5a1789$var$SuggestionItemGroup;\n\n});\n\nparcelRegister(\"5Fd8G\", function(module, exports) {\n\n$parcel$export(module.exports, \"default\", () => $41faf93a088b8b10$export$2e2bcd8739ae039);\n\nvar $lyEkX = parcelRequire(\"lyEkX\");\n\nvar $65idz = parcelRequire(\"65idz\");\n\nvar $6XEKD = parcelRequire(\"6XEKD\");\n\nvar $kmcVJ = parcelRequire(\"kmcVJ\");\n\nvar $aDnpv = parcelRequire(\"aDnpv\");\n\nvar $e9Sj6 = parcelRequire(\"e9Sj6\");\n\nvar $iQEdW = parcelRequire(\"iQEdW\");\n\nvar $adKC4 = parcelRequire(\"adKC4\");\nfunction $41faf93a088b8b10$export$2e2bcd8739ae039(hooks) {\n    const suggestionsList = hooks?.suggestionsList || $41faf93a088b8b10$var$defaultSuggestionsList;\n    const valueStateMessage = hooks?.valueStateMessage;\n    const valueStateMessageInputIcon = hooks?.valueStateMessageInputIcon;\n    return (0, $lyEkX.jsxs)((0, $e9Sj6.default), {\n        class: this.classes.popover,\n        hideArrow: true,\n        preventFocusRestore: true,\n        preventInitialFocus: true,\n        placement: \"Bottom\",\n        horizontalAlign: \"Start\",\n        tabindex: -1,\n        style: this.styles.suggestionsPopover,\n        onOpen: this._afterOpenPicker,\n        onClose: this._afterClosePicker,\n        onScroll: this._scroll,\n        open: this.open,\n        opener: this,\n        accessibleName: this._popupLabel,\n        children: [\n            this._isPhone && (0, $lyEkX.jsxs)((0, $lyEkX.Fragment), {\n                children: [\n                    (0, $lyEkX.jsxs)(\"div\", {\n                        slot: \"header\",\n                        class: \"ui5-responsive-popover-header\",\n                        children: [\n                            (0, $lyEkX.jsxs)(\"div\", {\n                                class: \"row\",\n                                children: [\n                                    (0, $lyEkX.jsx)(\"span\", {\n                                        children: this._headerTitleText\n                                    }),\n                                    (0, $lyEkX.jsx)((0, $iQEdW.default), {\n                                        class: \"ui5-responsive-popover-close-btn\",\n                                        icon: (0, $kmcVJ.default),\n                                        design: \"Transparent\",\n                                        onClick: this._closePicker\n                                    })\n                                ]\n                            }),\n                            (0, $lyEkX.jsx)(\"div\", {\n                                class: \"row\",\n                                children: (0, $lyEkX.jsx)(\"div\", {\n                                    class: \"input-root-phone native-input-wrapper\",\n                                    children: (0, $lyEkX.jsx)((0, $65idz.default), {\n                                        class: \"ui5-input-inner-phone\",\n                                        type: this.inputType,\n                                        value: this.value,\n                                        showClearIcon: this.showClearIcon,\n                                        placeholder: this.placeholder,\n                                        onInput: this._handleInput,\n                                        onChange: this._handleChange\n                                    })\n                                })\n                            })\n                        ]\n                    }),\n                    this.hasValueStateMessage && (0, $lyEkX.jsxs)(\"div\", {\n                        class: this.classes.popoverValueState,\n                        style: this.styles.suggestionPopoverHeader,\n                        children: [\n                            (0, $lyEkX.jsx)((0, $6XEKD.default), {\n                                class: \"ui5-input-value-state-message-icon\",\n                                name: valueStateMessageInputIcon?.call(this)\n                            }),\n                            this.open && valueStateMessage?.call(this)\n                        ]\n                    })\n                ]\n            }),\n            !this._isPhone && this.hasValueStateMessage && (0, $lyEkX.jsxs)(\"div\", {\n                slot: \"header\",\n                class: {\n                    \"ui5-responsive-popover-header\": true,\n                    \"ui5-responsive-popover-header--focused\": this._isValueStateFocused,\n                    ...this.classes.popoverValueState\n                },\n                style: this.styles.suggestionPopoverHeader,\n                children: [\n                    (0, $lyEkX.jsx)((0, $6XEKD.default), {\n                        class: \"ui5-input-value-state-message-icon\",\n                        name: valueStateMessageInputIcon?.call(this)\n                    }),\n                    this.open && valueStateMessage?.call(this)\n                ]\n            }),\n            suggestionsList.call(this),\n            this._isPhone && (0, $lyEkX.jsx)(\"div\", {\n                slot: \"footer\",\n                class: \"ui5-responsive-popover-footer\",\n                children: (0, $lyEkX.jsx)((0, $iQEdW.default), {\n                    design: \"Transparent\",\n                    onClick: this._closePicker,\n                    children: this._suggestionsOkButtonText\n                })\n            })\n        ]\n    });\n}\nfunction $41faf93a088b8b10$var$defaultSuggestionsList() {\n    return (0, $lyEkX.jsx)((0, $aDnpv.default), {\n        accessibleRole: (0, $adKC4.default).ListBox,\n        separators: this.suggestionSeparators,\n        selectionMode: \"Single\",\n        onMouseDown: this.onItemMouseDown,\n        onItemClick: this._handleSuggestionItemPress,\n        onSelectionChange: this._handleSelectionChange,\n        children: (0, $lyEkX.jsx)(\"slot\", {})\n    });\n}\n\n});\n\n\n\n//# sourceMappingURL=InputSuggestions.a8a3b8f2.js.map\n", "import generateHighlightedMarkup from \"@ui5/webcomponents-base/dist/util/generateHighlightedMarkup.js\";\nimport \"../SuggestionItem.js\";\nimport \"../SuggestionItemGroup.js\";\nimport InputSuggestionsTemplate from \"./InputSuggestionsTemplate.js\";\nimport Input from \"../Input.js\";\nimport { LIST_ITEM_POSITION, LIST_ITEM_GROUP_HEADER, } from \"../generated/i18n/i18n-defaults.js\";\n/**\n * A class to manage the `Input` suggestion items.\n * @class\n * @private\n */\nclass Suggestions {\n    get template() {\n        return InputSuggestionsTemplate;\n    }\n    constructor(component, slotName, highlight, handleFocus) {\n        // The component, that the suggestion would plug into.\n        this.component = component;\n        // Defines the items` slot name.\n        this.slotName = slotName;\n        // Defines, if the focus will be moved via the arrow keys.\n        this.handleFocus = handleFocus;\n        // Defines, if the suggestions should highlight.\n        this.highlight = highlight;\n        // An integer value to store the currently selected item position,\n        // that changes due to user interaction.\n        this.selectedItemIndex = -1;\n    }\n    onUp(e, indexOfItem) {\n        e.preventDefault();\n        const index = !this.isOpened && this._hasValueState && indexOfItem === -1 ? 0 : indexOfItem;\n        this._handleItemNavigation(false /* forward */, index);\n        return true;\n    }\n    onDown(e, indexOfItem) {\n        e.preventDefault();\n        const index = !this.isOpened && this._hasValueState && indexOfItem === -1 ? 0 : indexOfItem;\n        this._handleItemNavigation(true /* forward */, index);\n        return true;\n    }\n    onSpace(e) {\n        if (this._isItemOnTarget()) {\n            e.preventDefault();\n            this.onItemSelected(this._selectedItem, true /* keyboardUsed */);\n            return true;\n        }\n        return false;\n    }\n    onEnter(e) {\n        if (this._isGroupItem) {\n            e.preventDefault();\n            return false;\n        }\n        if (this._isItemOnTarget()) {\n            this.onItemSelected(this._selectedItem, true /* keyboardUsed */);\n            return true;\n        }\n        return false;\n    }\n    onPageUp(e) {\n        e.preventDefault();\n        const isItemIndexValid = this.selectedItemIndex - 10 > -1;\n        if (this._hasValueState && !isItemIndexValid) {\n            this._focusValueState();\n            return true;\n        }\n        this._moveItemSelection(this.selectedItemIndex, isItemIndexValid ? this.selectedItemIndex -= 10 : this.selectedItemIndex = 0);\n        return true;\n    }\n    onPageDown(e) {\n        e.preventDefault();\n        const items = this._getItems();\n        const lastItemIndex = items.length - 1;\n        const isItemIndexValid = this.selectedItemIndex + 10 <= lastItemIndex;\n        if (this._hasValueState && !items) {\n            this._focusValueState();\n            return true;\n        }\n        this._moveItemSelection(this.selectedItemIndex, isItemIndexValid ? this.selectedItemIndex += 10 : this.selectedItemIndex = lastItemIndex);\n        return true;\n    }\n    onHome(e) {\n        e.preventDefault();\n        if (this._hasValueState) {\n            this._focusValueState();\n            return true;\n        }\n        this._moveItemSelection(this.selectedItemIndex, this.selectedItemIndex = 0);\n        return true;\n    }\n    onEnd(e) {\n        e.preventDefault();\n        const lastItemIndex = this._getItems().length - 1;\n        if (this._hasValueState && !lastItemIndex) {\n            this._focusValueState();\n            return true;\n        }\n        this._moveItemSelection(this.selectedItemIndex, this.selectedItemIndex = lastItemIndex);\n        return true;\n    }\n    onTab() {\n        if (this._isItemOnTarget()) {\n            this.onItemSelected(this._selectedItem, true);\n            return true;\n        }\n        return false;\n    }\n    toggle(bToggle, options) {\n        const toggle = bToggle !== undefined ? bToggle : !this.isOpened();\n        if (toggle) {\n            this._getComponent().open = true;\n        }\n        else {\n            this.close(options.preventFocusRestore);\n        }\n    }\n    get _selectedItem() {\n        return this._getNonGroupItems().find(item => item.selected);\n    }\n    _isScrollable() {\n        const sc = this._getScrollContainer();\n        return sc.offsetHeight < sc.scrollHeight;\n    }\n    close(preventFocusRestore = false) {\n        const selectedItem = this._getItems() && this._getItems()[this.selectedItemIndex];\n        this._getComponent().open = false;\n        const picker = this._getPicker();\n        picker.preventFocusRestore = preventFocusRestore;\n        picker.open = false;\n        if (selectedItem && selectedItem.focused) {\n            selectedItem.focused = false;\n        }\n    }\n    updateSelectedItemPosition(pos) {\n        this.selectedItemIndex = pos;\n    }\n    onItemSelected(selectedItem, keyboardUsed) {\n        const item = selectedItem;\n        const nonGroupItems = this._getNonGroupItems();\n        if (!item) {\n            return;\n        }\n        this.accInfo = {\n            isGroup: item.hasAttribute(\"ui5-suggestion-item-group\"),\n            currentPos: nonGroupItems.indexOf(item) + 1,\n            listSize: nonGroupItems.length,\n            itemText: item.text || \"\",\n            additionalText: item.additionalText,\n        };\n        this._getComponent().onItemSelected(item, keyboardUsed);\n        this._getComponent().open = false;\n    }\n    onItemSelect(item) {\n        this._getComponent().onItemSelect(item);\n    }\n    /* Private methods */\n    // Note: Split into two separate handlers\n    onItemPress(e) {\n        let pressedItem; // SuggestionListItem\n        const isPressEvent = e.type === \"ui5-item-click\";\n        // Only use the press e if the item is already selected, in all other cases we are listening for 'ui5-selection-change' from the list\n        // Also we have to check if the selection-change is fired by the list's 'item-click' event handling, to avoid double handling on our side\n        if ((isPressEvent && !e.detail.item.selected) || (this._handledPress && !isPressEvent)) {\n            return;\n        }\n        if (isPressEvent && e.detail.item.selected) {\n            pressedItem = e.detail.item;\n            this._handledPress = true;\n        }\n        else {\n            pressedItem = e.detail.selectedItems[0];\n        }\n        this.onItemSelected(pressedItem, false /* keyboardUsed */);\n    }\n    _onClose() {\n        this._handledPress = false;\n    }\n    _isItemOnTarget() {\n        return this.isOpened() && this.selectedItemIndex !== null && this.selectedItemIndex !== -1 && !this._isGroupItem;\n    }\n    get _isGroupItem() {\n        const items = this._getItems();\n        if (!items || !items[this.selectedItemIndex]) {\n            return false;\n        }\n        return items[this.selectedItemIndex].hasAttribute(\"ui5-suggestion-item-group\");\n    }\n    isOpened() {\n        return !!(this._getPicker()?.open);\n    }\n    _handleItemNavigation(forward, index) {\n        this.selectedItemIndex = index;\n        if (!this._getItems().length) {\n            return;\n        }\n        if (forward) {\n            this._selectNextItem();\n        }\n        else {\n            this._selectPreviousItem();\n        }\n    }\n    _selectNextItem() {\n        const itemsCount = this._getItems().length;\n        const previousSelectedIdx = this.selectedItemIndex;\n        if (this._hasValueState && previousSelectedIdx === -1 && !this.component._isValueStateFocused) {\n            this._focusValueState();\n            return;\n        }\n        if ((previousSelectedIdx === -1 && !this._hasValueState) || this.component._isValueStateFocused) {\n            this._clearValueStateFocus();\n            this.selectedItemIndex = -1;\n        }\n        if (previousSelectedIdx !== -1 && previousSelectedIdx + 1 > itemsCount - 1) {\n            return;\n        }\n        this._moveItemSelection(previousSelectedIdx, ++this.selectedItemIndex);\n    }\n    _selectPreviousItem() {\n        const items = this._getItems();\n        const previousSelectedIdx = this.selectedItemIndex;\n        if (this._hasValueState && previousSelectedIdx === 0 && !this.component._isValueStateFocused) {\n            this.component.hasSuggestionItemSelected = false;\n            this.component._isValueStateFocused = true;\n            this.selectedItemIndex = 0;\n            items[0].focused = false;\n            if (items[0].hasAttribute(\"ui5-suggestion-item\")) {\n                items[0].selected = false;\n            }\n            return;\n        }\n        if (this.component._isValueStateFocused) {\n            this.component.focused = true;\n            this.component._isValueStateFocused = false;\n            this.selectedItemIndex = 0;\n            return;\n        }\n        if (previousSelectedIdx === -1 || previousSelectedIdx === null) {\n            return;\n        }\n        if (previousSelectedIdx - 1 < 0) {\n            if (items[previousSelectedIdx].hasAttribute(\"ui5-suggestion-item\") || items[previousSelectedIdx].hasAttribute(\"ui5-suggestion-item-custom\")) {\n                items[previousSelectedIdx].selected = false;\n            }\n            items[previousSelectedIdx].focused = false;\n            this.component.focused = true;\n            this.component.hasSuggestionItemSelected = false;\n            this.selectedItemIndex -= 1;\n            return;\n        }\n        this._moveItemSelection(previousSelectedIdx, --this.selectedItemIndex);\n    }\n    _moveItemSelection(previousIdx, nextIdx) {\n        const items = this._getItems();\n        const currentItem = items[nextIdx];\n        const previousItem = items[previousIdx];\n        const nonGroupItems = this._getNonGroupItems();\n        const isGroupItem = currentItem.hasAttribute(\"ui5-suggestion-item-group\");\n        if (!currentItem) {\n            return;\n        }\n        this.component.focused = false;\n        this._clearValueStateFocus();\n        const selectedItem = this._getItems()[this.selectedItemIndex];\n        this.accInfo = {\n            isGroup: isGroupItem,\n            currentPos: items.indexOf(currentItem) + 1,\n            itemText: (isGroupItem ? selectedItem.headerText : selectedItem.text) || \"\",\n        };\n        if (currentItem.hasAttribute(\"ui5-suggestion-item\") || currentItem.hasAttribute(\"ui5-suggestion-item-custom\")) {\n            this.accInfo.additionalText = currentItem.additionalText || \"\";\n            this.accInfo.currentPos = nonGroupItems.indexOf(currentItem) + 1;\n            this.accInfo.listSize = nonGroupItems.length;\n        }\n        if (previousItem) {\n            previousItem.focused = false;\n        }\n        if (previousItem?.hasAttribute(\"ui5-suggestion-item\") || previousItem?.hasAttribute(\"ui5-suggestion-item-custom\")) {\n            previousItem.selected = false;\n        }\n        if (currentItem) {\n            currentItem.focused = true;\n            if (!isGroupItem) {\n                currentItem.selected = true;\n            }\n            if (this.handleFocus) {\n                currentItem.focus();\n            }\n        }\n        this.component.hasSuggestionItemSelected = true;\n        this.onItemSelect(currentItem);\n        if (!this._isItemIntoView(currentItem)) {\n            const itemRef = this._isGroupItem ? currentItem.shadowRoot.querySelector(\"[ui5-li-group-header]\") : currentItem;\n            this._scrollItemIntoView(itemRef);\n        }\n    }\n    _deselectItems() {\n        const items = this._getItems();\n        items.forEach(item => {\n            if (item.hasAttribute(\"ui5-suggestion-item\")) {\n                item.selected = false;\n            }\n            item.focused = false;\n        });\n    }\n    _clearItemFocus() {\n        const focusedItem = this._getItems().find(item => item.focused);\n        if (focusedItem) {\n            focusedItem.focused = false;\n        }\n    }\n    _isItemIntoView(item) {\n        const rectItem = item.getDomRef().getBoundingClientRect();\n        const rectInput = this._getComponent().getDomRef().getBoundingClientRect();\n        const windowHeight = (window.innerHeight || document.documentElement.clientHeight);\n        let headerHeight = 0;\n        if (this._hasValueState) {\n            const valueStateHeader = this._getPicker().querySelector(\"[slot=header]\");\n            headerHeight = valueStateHeader.getBoundingClientRect().height;\n        }\n        return (rectItem.top + Suggestions.SCROLL_STEP <= windowHeight) && (rectItem.top >= rectInput.top + headerHeight);\n    }\n    _scrollItemIntoView(item) {\n        item.scrollIntoView({\n            behavior: \"auto\",\n            block: \"nearest\",\n            inline: \"nearest\",\n        });\n    }\n    _getScrollContainer() {\n        if (!this._scrollContainer) {\n            this._scrollContainer = this._getPicker().shadowRoot.querySelector(\".ui5-popup-content\");\n        }\n        return this._scrollContainer;\n    }\n    /**\n     * Returns the items in 1D array.\n     *\n     */\n    _getItems() {\n        const suggestionComponent = this._getComponent();\n        return suggestionComponent.getSlottedNodes(\"suggestionItems\").flatMap(item => {\n            return item.hasAttribute(\"ui5-suggestion-item-group\") ? [item, ...item.items] : [item];\n        });\n    }\n    _getNonGroupItems() {\n        return this._getItems().filter(item => !item.hasAttribute(\"ui5-suggestion-item-group\"));\n    }\n    _getComponent() {\n        return this.component;\n    }\n    _getList() {\n        return this._getPicker().querySelector(\"[ui5-list]\");\n    }\n    _getListWidth() {\n        return this._getList()?.offsetWidth;\n    }\n    _getPicker() {\n        return this._getComponent().shadowRoot.querySelector(\"[ui5-responsive-popover]\");\n    }\n    get itemSelectionAnnounce() {\n        if (!this.accInfo) {\n            return \"\";\n        }\n        if (this.accInfo.isGroup) {\n            return `${Suggestions.i18nBundle.getText(LIST_ITEM_GROUP_HEADER)} ${this.accInfo.itemText}`;\n        }\n        const itemPositionText = Suggestions.i18nBundle.getText(LIST_ITEM_POSITION, this.accInfo.currentPos || 0, this.accInfo.listSize || 0);\n        return `${this.accInfo.additionalText} ${itemPositionText}`;\n    }\n    hightlightInput(text, input) {\n        return generateHighlightedMarkup(text, input);\n    }\n    get _hasValueState() {\n        return this.component.hasValueStateMessage;\n    }\n    _focusValueState() {\n        this.component._isValueStateFocused = true;\n        this.component.focused = false;\n        this.component.hasSuggestionItemSelected = false;\n        this.selectedItemIndex = 0;\n        this.component.value = this.component.typedInValue;\n        this._deselectItems();\n    }\n    _clearValueStateFocus() {\n        this.component._isValueStateFocused = false;\n    }\n    _clearSelectedSuggestionAndaccInfo() {\n        this.accInfo = undefined;\n        this.selectedItemIndex = 0;\n    }\n}\nSuggestions.SCROLL_STEP = 60;\nInput.SuggestionsClass = Suggestions;\nexport default Suggestions;\n//# sourceMappingURL=InputSuggestions.js.map", "import type UI5Element from \"@ui5/webcomponents-base/dist/UI5Element.js\";\nimport type I18nBundle from \"@ui5/webcomponents-base/dist/i18nBundle.js\";\nimport generateHighlightedMarkup from \"@ui5/webcomponents-base/dist/util/generateHighlightedMarkup.js\";\nimport type List from \"../List.js\";\nimport type { ListItemClickEventDetail, ListSelectionChangeEventDetail } from \"../List.js\";\nimport type ResponsivePopover from \"../ResponsivePopover.js\";\nimport \"../SuggestionItem.js\";\nimport \"../SuggestionItemGroup.js\";\nimport type SuggestionItem from \"../SuggestionItem.js\";\nimport type ListItemGroupHeader from \"../ListItemGroupHeader.js\";\nimport InputSuggestionsTemplate from \"./InputSuggestionsTemplate.js\";\nimport Input from \"../Input.js\";\n\nimport {\n\tLIST_ITEM_POSITION,\n\tLIST_ITEM_GROUP_HEADER,\n} from \"../generated/i18n/i18n-defaults.js\";\nimport type ListItemBase from \"../ListItemBase.js\";\nimport type SuggestionItemGroup from \"../SuggestionItemGroup.js\";\nimport type { IInputSuggestionItem, IInputSuggestionItemSelectable } from \"../Input.js\";\n\ninterface SuggestionComponent extends UI5Element {\n\t_isValueStateFocused: boolean;\n\tfocused: boolean;\n\thasSuggestionItemSelected: boolean;\n\tvalue: string;\n\ttypedInValue: string;\n\thasValueStateMessage: boolean;\n\tsuggestionItems: Array<IInputSuggestionItem>;\n\topen: boolean;\n\tonItemSelected: (pressedItem: IInputSuggestionItemSelectable, keyboardUsed: boolean) => void;\n\tonItemSelect: (item: IInputSuggestionItem) => void;\n}\n\ntype SuggestionsAccInfo = {\n\tisGroup: boolean;\n\tcurrentPos?: number;\n\tlistSize?: number;\n\titemText: string;\n\tadditionalText?: string;\n}\n\n/**\n * A class to manage the `Input` suggestion items.\n * @class\n * @private\n */\nclass Suggestions {\n\tcomponent: SuggestionComponent;\n\tslotName: string;\n\thandleFocus: boolean;\n\thighlight: boolean;\n\tselectedItemIndex: number;\n\taccInfo?: SuggestionsAccInfo;\n\t_scrollContainer?: HTMLElement;\n\t_handledPress?: boolean;\n\tattachedAfterOpened?: boolean;\n\tattachedAfterClose?: boolean;\n\tstatic i18nBundle: I18nBundle;\n\tstatic SCROLL_STEP = 60;\n\n\tget template() {\n\t\treturn InputSuggestionsTemplate;\n\t}\n\n\tconstructor(component: SuggestionComponent, slotName: string, highlight: boolean, handleFocus: boolean) {\n\t\t// The component, that the suggestion would plug into.\n\t\tthis.component = component;\n\n\t\t// Defines the items` slot name.\n\t\tthis.slotName = slotName;\n\n\t\t// Defines, if the focus will be moved via the arrow keys.\n\t\tthis.handleFocus = handleFocus;\n\n\t\t// Defines, if the suggestions should highlight.\n\t\tthis.highlight = highlight;\n\n\t\t// An integer value to store the currently selected item position,\n\t\t// that changes due to user interaction.\n\t\tthis.selectedItemIndex = -1;\n\t}\n\n\tonUp(e: KeyboardEvent, indexOfItem: number) {\n\t\te.preventDefault();\n\t\tconst index = !this.isOpened && this._hasValueState && indexOfItem === -1 ? 0 : indexOfItem;\n\t\tthis._handleItemNavigation(false /* forward */, index);\n\t\treturn true;\n\t}\n\n\tonDown(e: KeyboardEvent, indexOfItem: number) {\n\t\te.preventDefault();\n\t\tconst index = !this.isOpened && this._hasValueState && indexOfItem === -1 ? 0 : indexOfItem;\n\t\tthis._handleItemNavigation(true /* forward */, index);\n\t\treturn true;\n\t}\n\n\tonSpace(e: KeyboardEvent) {\n\t\tif (this._isItemOnTarget()) {\n\t\t\te.preventDefault();\n\t\t\tthis.onItemSelected(this._selectedItem, true /* keyboardUsed */);\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\tonEnter(e: KeyboardEvent) {\n\t\tif (this._isGroupItem) {\n\t\t\te.preventDefault();\n\t\t\treturn false;\n\t\t}\n\n\t\tif (this._isItemOnTarget()) {\n\t\t\tthis.onItemSelected(this._selectedItem, true /* keyboardUsed */);\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tonPageUp(e: KeyboardEvent) {\n\t\te.preventDefault();\n\n\t\tconst isItemIndexValid = this.selectedItemIndex - 10 > -1;\n\n\t\tif (this._hasValueState && !isItemIndexValid) {\n\t\t\tthis._focusValueState();\n\t\t\treturn true;\n\t\t}\n\n\t\tthis._moveItemSelection(this.selectedItemIndex,\n\t\t\tisItemIndexValid ? this.selectedItemIndex -= 10 : this.selectedItemIndex = 0);\n\t\treturn true;\n\t}\n\n\tonPageDown(e: KeyboardEvent) {\n\t\te.preventDefault();\n\n\t\tconst items = this._getItems();\n\t\tconst lastItemIndex = items.length - 1;\n\t\tconst isItemIndexValid = this.selectedItemIndex + 10 <= lastItemIndex;\n\n\t\tif (this._hasValueState && !items) {\n\t\t\tthis._focusValueState();\n\t\t\treturn true;\n\t\t}\n\n\t\tthis._moveItemSelection(this.selectedItemIndex,\n\t\t\tisItemIndexValid ? this.selectedItemIndex += 10 : this.selectedItemIndex = lastItemIndex);\n\t\treturn true;\n\t}\n\n\tonHome(e: KeyboardEvent) {\n\t\te.preventDefault();\n\n\t\tif (this._hasValueState) {\n\t\t\tthis._focusValueState();\n\t\t\treturn true;\n\t\t}\n\n\t\tthis._moveItemSelection(this.selectedItemIndex, this.selectedItemIndex = 0);\n\t\treturn true;\n\t}\n\n\tonEnd(e: KeyboardEvent) {\n\t\te.preventDefault();\n\n\t\tconst lastItemIndex = this._getItems().length - 1;\n\n\t\tif (this._hasValueState && !lastItemIndex) {\n\t\t\tthis._focusValueState();\n\t\t\treturn true;\n\t\t}\n\n\t\tthis._moveItemSelection(this.selectedItemIndex, this.selectedItemIndex = lastItemIndex);\n\t\treturn true;\n\t}\n\n\tonTab() {\n\t\tif (this._isItemOnTarget()) {\n\t\t\tthis.onItemSelected(this._selectedItem, true);\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\ttoggle(bToggle: boolean, options: { preventFocusRestore: boolean }) {\n\t\tconst toggle = bToggle !== undefined ? bToggle : !this.isOpened();\n\n\t\tif (toggle) {\n\t\t\tthis._getComponent().open = true;\n\t\t} else {\n\t\t\tthis.close(options.preventFocusRestore);\n\t\t}\n\t}\n\n\tget _selectedItem() {\n\t\treturn this._getNonGroupItems().find(item => item.selected) as SuggestionItem | null;\n\t}\n\n\t_isScrollable() {\n\t\tconst sc = this._getScrollContainer();\n\t\treturn sc.offsetHeight < sc.scrollHeight;\n\t}\n\n\tclose(preventFocusRestore = false) {\n\t\tconst selectedItem = this._getItems() && this._getItems()[this.selectedItemIndex];\n\n\t\tthis._getComponent().open = false;\n\t\tconst picker = this._getPicker();\n\t\tpicker.preventFocusRestore = preventFocusRestore;\n\t\tpicker.open = false;\n\n\t\tif (selectedItem && selectedItem.focused) {\n\t\t\tselectedItem.focused = false;\n\t\t}\n\t}\n\n\tupdateSelectedItemPosition(pos: number) {\n\t\tthis.selectedItemIndex = pos;\n\t}\n\n\tonItemSelected(selectedItem: IInputSuggestionItemSelectable | null, keyboardUsed: boolean) {\n\t\tconst item = selectedItem;\n\t\tconst nonGroupItems = this._getNonGroupItems();\n\n\t\tif (!item) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.accInfo = {\n\t\t\tisGroup: item.hasAttribute(\"ui5-suggestion-item-group\"),\n\t\t\tcurrentPos: nonGroupItems.indexOf(item) + 1,\n\t\t\tlistSize: nonGroupItems.length,\n\t\t\titemText: item.text || \"\",\n\t\t\tadditionalText: item.additionalText,\n\t\t};\n\n\t\tthis._getComponent().onItemSelected(item, keyboardUsed);\n\t\tthis._getComponent().open = false;\n\t}\n\n\tonItemSelect(item: IInputSuggestionItem) {\n\t\tthis._getComponent().onItemSelect(item);\n\t}\n\n\t/* Private methods */\n\t// Note: Split into two separate handlers\n\tonItemPress(e: CustomEvent<ListItemClickEventDetail | ListSelectionChangeEventDetail>) {\n\t\tlet pressedItem: ListItemBase; // SuggestionListItem\n\t\tconst isPressEvent = e.type === \"ui5-item-click\";\n\n\t\t// Only use the press e if the item is already selected, in all other cases we are listening for 'ui5-selection-change' from the list\n\t\t// Also we have to check if the selection-change is fired by the list's 'item-click' event handling, to avoid double handling on our side\n\t\tif ((isPressEvent && !(e.detail as ListItemClickEventDetail).item.selected) || (this._handledPress && !isPressEvent)) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (isPressEvent && (e.detail as ListItemClickEventDetail).item.selected) {\n\t\t\tpressedItem = (e.detail as ListItemClickEventDetail).item;\n\t\t\tthis._handledPress = true;\n\t\t} else {\n\t\t\tpressedItem = (e.detail as ListSelectionChangeEventDetail).selectedItems[0];\n\t\t}\n\n\t\tthis.onItemSelected(pressedItem as SuggestionItem, false /* keyboardUsed */);\n\t}\n\n\t_onClose() {\n\t\tthis._handledPress = false;\n\t}\n\n\t_isItemOnTarget() {\n\t\treturn this.isOpened() && this.selectedItemIndex !== null && this.selectedItemIndex !== -1 && !this._isGroupItem;\n\t}\n\n\tget _isGroupItem() {\n\t\tconst items = this._getItems();\n\n\t\tif (!items || !items[this.selectedItemIndex]) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn items[this.selectedItemIndex].hasAttribute(\"ui5-suggestion-item-group\");\n\t}\n\n\tisOpened() {\n\t\treturn !!(this._getPicker()?.open);\n\t}\n\n\t_handleItemNavigation(forward: boolean, index: number) {\n\t\tthis.selectedItemIndex = index;\n\n\t\tif (!this._getItems().length) {\n\t\t\treturn;\n\t\t}\n\t\tif (forward) {\n\t\t\tthis._selectNextItem();\n\t\t} else {\n\t\t\tthis._selectPreviousItem();\n\t\t}\n\t}\n\n\t_selectNextItem() {\n\t\tconst itemsCount = this._getItems().length;\n\n\t\tconst previousSelectedIdx = this.selectedItemIndex;\n\n\t\tif (this._hasValueState && previousSelectedIdx === -1 && !this.component._isValueStateFocused) {\n\t\t\tthis._focusValueState();\n\t\t\treturn;\n\t\t}\n\n\t\tif ((previousSelectedIdx === -1 && !this._hasValueState) || this.component._isValueStateFocused) {\n\t\t\tthis._clearValueStateFocus();\n\t\t\tthis.selectedItemIndex = -1;\n\t\t}\n\n\t\tif (previousSelectedIdx !== -1 && previousSelectedIdx + 1 > itemsCount - 1) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._moveItemSelection(previousSelectedIdx, ++this.selectedItemIndex);\n\t}\n\n\t_selectPreviousItem() {\n\t\tconst items = this._getItems();\n\t\tconst previousSelectedIdx = this.selectedItemIndex;\n\n\t\tif (this._hasValueState && previousSelectedIdx === 0 && !this.component._isValueStateFocused) {\n\t\t\tthis.component.hasSuggestionItemSelected = false;\n\t\t\tthis.component._isValueStateFocused = true;\n\t\t\tthis.selectedItemIndex = 0;\n\n\t\t\titems[0].focused = false;\n\n\t\t\tif (items[0].hasAttribute(\"ui5-suggestion-item\")) {\n\t\t\t\t(items[0] as SuggestionItem).selected = false;\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\tif (this.component._isValueStateFocused) {\n\t\t\tthis.component.focused = true;\n\t\t\tthis.component._isValueStateFocused = false;\n\t\t\tthis.selectedItemIndex = 0;\n\n\t\t\treturn;\n\t\t}\n\n\t\tif (previousSelectedIdx === -1 || previousSelectedIdx === null) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (previousSelectedIdx - 1 < 0) {\n\t\t\tif (items[previousSelectedIdx].hasAttribute(\"ui5-suggestion-item\") || items[previousSelectedIdx].hasAttribute(\"ui5-suggestion-item-custom\")) {\n\t\t\t\t(items[previousSelectedIdx] as SuggestionItem).selected = false;\n\t\t\t}\n\n\t\t\titems[previousSelectedIdx].focused = false;\n\n\t\t\tthis.component.focused = true;\n\t\t\tthis.component.hasSuggestionItemSelected = false;\n\t\t\tthis.selectedItemIndex -= 1;\n\t\t\treturn;\n\t\t}\n\n\t\tthis._moveItemSelection(previousSelectedIdx, --this.selectedItemIndex);\n\t}\n\n\t_moveItemSelection(previousIdx: number, nextIdx: number) {\n\t\tconst items = this._getItems();\n\t\tconst currentItem = items[nextIdx];\n\t\tconst previousItem = items[previousIdx];\n\t\tconst nonGroupItems = this._getNonGroupItems();\n\t\tconst isGroupItem = currentItem.hasAttribute(\"ui5-suggestion-item-group\");\n\n\t\tif (!currentItem) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.component.focused = false;\n\t\tthis._clearValueStateFocus();\n\n\t\tconst selectedItem = this._getItems()[this.selectedItemIndex];\n\n\t\tthis.accInfo = {\n\t\t\tisGroup: isGroupItem,\n\t\t\tcurrentPos: items.indexOf(currentItem) + 1,\n\t\t\titemText: (isGroupItem ? (selectedItem as SuggestionItemGroup).headerText : (selectedItem as IInputSuggestionItemSelectable).text) || \"\",\n\t\t};\n\n\t\tif (currentItem.hasAttribute(\"ui5-suggestion-item\") || currentItem.hasAttribute(\"ui5-suggestion-item-custom\")) {\n\t\t\tthis.accInfo.additionalText = (currentItem as IInputSuggestionItemSelectable).additionalText || \"\";\n\t\t\tthis.accInfo.currentPos = nonGroupItems.indexOf(currentItem as IInputSuggestionItemSelectable) + 1;\n\t\t\tthis.accInfo.listSize = nonGroupItems.length;\n\t\t}\n\n\t\tif (previousItem) {\n\t\t\tpreviousItem.focused = false;\n\t\t}\n\t\tif (previousItem?.hasAttribute(\"ui5-suggestion-item\") || previousItem?.hasAttribute(\"ui5-suggestion-item-custom\")) {\n\t\t\t(previousItem as IInputSuggestionItemSelectable).selected = false;\n\t\t}\n\n\t\tif (currentItem) {\n\t\t\tcurrentItem.focused = true;\n\n\t\t\tif (!isGroupItem) {\n\t\t\t\t(currentItem as IInputSuggestionItemSelectable).selected = true;\n\t\t\t}\n\n\t\t\tif (this.handleFocus) {\n\t\t\t\tcurrentItem.focus();\n\t\t\t}\n\t\t}\n\n\t\tthis.component.hasSuggestionItemSelected = true;\n\t\tthis.onItemSelect(currentItem);\n\n\t\tif (!this._isItemIntoView(currentItem)) {\n\t\t\tconst itemRef = this._isGroupItem ? (currentItem.shadowRoot!.querySelector(\"[ui5-li-group-header]\") as ListItemGroupHeader)! : currentItem;\n\t\t\tthis._scrollItemIntoView(itemRef);\n\t\t}\n\t}\n\n\t_deselectItems() {\n\t\tconst items = this._getItems();\n\t\titems.forEach(item => {\n\t\t\tif (item.hasAttribute(\"ui5-suggestion-item\")) {\n\t\t\t\t(item as SuggestionItem).selected = false;\n\t\t\t}\n\n\t\t\titem.focused = false;\n\t\t});\n\t}\n\n\t_clearItemFocus() {\n\t\tconst focusedItem = this._getItems().find(item => item.focused);\n\n\t\tif (focusedItem) {\n\t\t\tfocusedItem.focused = false;\n\t\t}\n\t}\n\n\t_isItemIntoView(item: IInputSuggestionItem) {\n\t\tconst rectItem = item.getDomRef()!.getBoundingClientRect();\n\t\tconst rectInput = this._getComponent().getDomRef()!.getBoundingClientRect();\n\t\tconst windowHeight = (window.innerHeight || document.documentElement.clientHeight);\n\t\tlet headerHeight = 0;\n\n\t\tif (this._hasValueState) {\n\t\t\tconst valueStateHeader = this._getPicker().querySelector(\"[slot=header]\")!;\n\t\t\theaderHeight = valueStateHeader.getBoundingClientRect().height;\n\t\t}\n\n\t\treturn (rectItem.top + Suggestions.SCROLL_STEP <= windowHeight) && (rectItem.top >= rectInput.top + headerHeight);\n\t}\n\n\t_scrollItemIntoView(item: IInputSuggestionItem) {\n\t\titem.scrollIntoView({\n\t\t\tbehavior: \"auto\",\n\t\t\tblock: \"nearest\",\n\t\t\tinline: \"nearest\",\n\t\t});\n\t}\n\n\t_getScrollContainer() {\n\t\tif (!this._scrollContainer) {\n\t\t\tthis._scrollContainer = this._getPicker()!.shadowRoot!.querySelector(\".ui5-popup-content\")!;\n\t\t}\n\n\t\treturn this._scrollContainer;\n\t}\n\n\t/**\n\t * Returns the items in 1D array.\n\t *\n\t */\n\t_getItems(): Array<IInputSuggestionItem> {\n\t\tconst suggestionComponent = this._getComponent();\n\n\t\treturn suggestionComponent.getSlottedNodes<IInputSuggestionItem>(\"suggestionItems\").flatMap(item => {\n\t\t\treturn item.hasAttribute(\"ui5-suggestion-item-group\") ? [item, ...item.items!] : [item];\n\t\t});\n\t}\n\n\t_getNonGroupItems(): Array<IInputSuggestionItemSelectable> {\n\t\treturn this._getItems().filter(item => !item.hasAttribute(\"ui5-suggestion-item-group\")) as Array<IInputSuggestionItemSelectable>;\n\t}\n\n\t_getComponent(): SuggestionComponent {\n\t\treturn this.component;\n\t}\n\n\t_getList() {\n\t\treturn this._getPicker().querySelector<List>(\"[ui5-list]\")!;\n\t}\n\n\t_getListWidth() {\n\t\treturn this._getList()?.offsetWidth;\n\t}\n\n\t_getPicker() {\n\t\treturn this._getComponent().shadowRoot!.querySelector<ResponsivePopover>(\"[ui5-responsive-popover]\")!;\n\t}\n\n\tget itemSelectionAnnounce() {\n\t\tif (!this.accInfo) {\n\t\t\treturn \"\";\n\t\t}\n\n\t\tif (this.accInfo.isGroup) {\n\t\t\treturn `${Suggestions.i18nBundle.getText(LIST_ITEM_GROUP_HEADER)} ${this.accInfo.itemText}`;\n\t\t}\n\n\t\tconst itemPositionText = Suggestions.i18nBundle.getText(LIST_ITEM_POSITION, this.accInfo.currentPos || 0, this.accInfo.listSize || 0);\n\n\t\treturn `${this.accInfo.additionalText} ${itemPositionText}`;\n\t}\n\n\thightlightInput(text: string, input: string) {\n\t\treturn generateHighlightedMarkup(text, input);\n\t}\n\n\tget _hasValueState() {\n\t\treturn this.component.hasValueStateMessage;\n\t}\n\n\t_focusValueState() {\n\t\tthis.component._isValueStateFocused = true;\n\t\tthis.component.focused = false;\n\t\tthis.component.hasSuggestionItemSelected = false;\n\t\tthis.selectedItemIndex = 0;\n\t\tthis.component.value = this.component.typedInValue;\n\n\t\tthis._deselectItems();\n\t}\n\n\t_clearValueStateFocus() {\n\t\tthis.component._isValueStateFocused = false;\n\t}\n\n\t_clearSelectedSuggestionAndaccInfo() {\n\t\tthis.accInfo = undefined;\n\t\tthis.selectedItemIndex = 0;\n\t}\n}\n\nInput.SuggestionsClass = Suggestions;\n\nexport default Suggestions;\n\nexport type {\n\tSuggestionComponent,\n};\n", "import escapeRegex from \"./escapeRegex.js\";\n// @ts-ignore\nimport encodeXML from \"../sap/base/security/encodeXML.js\";\n// utility to replace all occurances of a string\nfunction replaceAll(text, find, replace, caseInsensitive) {\n    return text.replaceAll(new RegExp(escapeRegex(find), `${caseInsensitive ? \"i\" : \"\"}g`), replace);\n}\n/**\n * Generate markup for a raw string where a particular text is wrapped with some tag, by default `<b>` (bold) tag.\n * All inputs to this function are considered literal text, and special characters will always be escaped.\n * @param {string} text The text to add highlighting to\n * @param {string} textToHighlight The text which should be highlighted\n * @return {string} the markup HTML which contains all occurrances of the input text surrounded with a `<b>` tag.\n */\nfunction generateHighlightedMarkup(text, textToHighlight) {\n    if (!text || !textToHighlight) {\n        return text;\n    }\n    // a token is some string that does not appear in either of the input strings\n    // repeat the token until it does not appear in the string\n    const makeToken = (t) => {\n        const [s, e] = t.split(\"\");\n        while (text.indexOf(t) >= 0 || textToHighlight.indexOf(t) >= 0) {\n            t = `${s}${t}${e}`;\n        }\n        return t;\n    };\n    // It doesn't matter what characters are used as long as all 4 of them are unique\n    // And also that encodeXML will not change these characters\n    const openToken = makeToken(\"12\");\n    const closeToken = makeToken(\"34\");\n    // wrap every occurance of the textToHighlight using the open/close tokens (instead of markup at this point)\n    let result = encodeXML(replaceAll(text, textToHighlight, (match) => `${openToken}${match}${closeToken}`, true));\n    // now replace the open and close tokens with the markup that we expect\n    [[openToken, \"<b>\"], [closeToken, \"</b>\"]].forEach(([find, replace]) => {\n        result = replaceAll(result, find, replace, false);\n    });\n    return result;\n}\nexport default generateHighlightedMarkup;\n//# sourceMappingURL=generateHighlightedMarkup.js.map", "import escapeRegex from \"./escapeRegex.js\";\n// @ts-ignore\nimport encodeXML from \"../sap/base/security/encodeXML.js\";\n\n// utility to replace all occurances of a string\nfunction replaceAll(text: string, find: string, replace: string | ((substring: string, ...args: any[]) => string), caseInsensitive: boolean) {\n\treturn text.replaceAll(new RegExp(escapeRegex(find), `${caseInsensitive ? \"i\" : \"\"}g`), replace as string);\n}\n\n/**\n * Generate markup for a raw string where a particular text is wrapped with some tag, by default `<b>` (bold) tag.\n * All inputs to this function are considered literal text, and special characters will always be escaped.\n * @param {string} text The text to add highlighting to\n * @param {string} textToHighlight The text which should be highlighted\n * @return {string} the markup HTML which contains all occurrances of the input text surrounded with a `<b>` tag.\n */\nfunction generateHighlightedMarkup(text: string, textToHighlight: string) {\n\tif (!text || !textToHighlight) {\n\t\treturn text;\n\t}\n\t// a token is some string that does not appear in either of the input strings\n\t// repeat the token until it does not appear in the string\n\tconst makeToken = (t: string) => {\n\t\tconst [s, e] = t.split(\"\");\n\t\twhile (text.indexOf(t) >= 0 || textToHighlight.indexOf(t) >= 0) {\n\t\t\tt = `${s}${t}${e}`;\n\t\t}\n\t\treturn t;\n\t};\n\t// It doesn't matter what characters are used as long as all 4 of them are unique\n\t// And also that encodeXML will not change these characters\n\tconst openToken = makeToken(\"12\");\n\tconst closeToken = makeToken(\"34\");\n\t// wrap every occurance of the textToHighlight using the open/close tokens (instead of markup at this point)\n\tlet result = encodeXML(replaceAll(text, textToHighlight, (match: string) => `${openToken}${match}${closeToken}`, true)) as string;\n\t// now replace the open and close tokens with the markup that we expect\n\t[[openToken, \"<b>\"], [closeToken, \"</b>\"]].forEach(([find, replace]) => {\n\t\tresult = replaceAll(result, find, replace, false);\n\t});\n\treturn result;\n}\n\nexport default generateHighlightedMarkup;\n", "/**\n * Escapes a regular expression text so that it can be used in a regular expression.\n *\n * @param { string } text the string to be interpreted literally\n * @returns { string } the escaped string\n */\nconst escapeRegex = (text) => {\n    return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\nexport default escapeRegex;\n//# sourceMappingURL=escapeRegex.js.map", "/**\n * Escapes a regular expression text so that it can be used in a regular expression.\n *\n * @param { string } text the string to be interpreted literally\n * @returns { string } the escaped string\n */\nconst escapeRegex = (text: string) => {\n\treturn text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\n\nexport default escapeRegex;\n", "var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nimport customElement from \"@ui5/webcomponents-base/dist/decorators/customElement.js\";\nimport slot from \"@ui5/webcomponents-base/dist/decorators/slot.js\";\nimport ListItemGroup from \"./ListItemGroup.js\";\nimport ListBoxItemGroupTemplate from \"./ListBoxItemGroupTemplate.js\";\n/**\n * @class\n * The `ui5-suggestion-item-group` is type of suggestion item,\n * that can be used to split the `ui5-input` suggestions into groups.\n * @constructor\n * @extends ListItemGroup\n * @public\n * @since 2.0.0\n */\nlet SuggestionItemGroup = class SuggestionItemGroup extends ListItemGroup {\n};\n__decorate([\n    slot({\n        \"default\": true,\n        invalidateOnChildChange: true,\n        type: HTMLElement,\n    })\n], SuggestionItemGroup.prototype, \"items\", void 0);\nSuggestionItemGroup = __decorate([\n    customElement({\n        tag: \"ui5-suggestion-item-group\",\n        template: ListBoxItemGroupTemplate,\n    })\n], SuggestionItemGroup);\nSuggestionItemGroup.define();\nexport default SuggestionItemGroup;\n//# sourceMappingURL=SuggestionItemGroup.js.map", "import customElement from \"@ui5/webcomponents-base/dist/decorators/customElement.js\";\nimport slot from \"@ui5/webcomponents-base/dist/decorators/slot.js\";\nimport type SuggestionListItem from \"./SuggestionListItem.js\";\nimport ListItemGroup from \"./ListItemGroup.js\";\nimport ListBoxItemGroupTemplate from \"./ListBoxItemGroupTemplate.js\";\n\n/**\n * @class\n * The `ui5-suggestion-item-group` is type of suggestion item,\n * that can be used to split the `ui5-input` suggestions into groups.\n * @constructor\n * @extends ListItemGroup\n * @public\n * @since 2.0.0\n */\n@customElement({\n\ttag: \"ui5-suggestion-item-group\",\n\ttemplate: ListBoxItemGroupTemplate,\n})\nclass SuggestionItemGroup extends ListItemGroup {\n\t/**\n\t * Defines the items of the <code>ui5-suggestion-item-group</code>.\n\t * @public\n\t */\n\t@slot({\n\t\t\"default\": true,\n\t\tinvalidateOnChildChange: true,\n\t\ttype: HTMLElement,\n\t})\n\titems!: Array<SuggestionListItem>;\n}\n\nSuggestionItemGroup.define();\n\nexport default SuggestionItemGroup;\n", "import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"@ui5/webcomponents-base/jsx-runtime\";\nimport Input from \"../Input.js\";\nimport Icon from \"../Icon.js\";\nimport decline from \"@ui5/webcomponents-icons/dist/decline.js\";\nimport List from \"../List.js\";\nimport ResponsivePopover from \"../ResponsivePopover.js\";\nimport Button from \"../Button.js\";\nimport ListAccessibleRole from \"../types/ListAccessibleRole.js\";\nexport default function InputSuggestionsTemplate(hooks) {\n    const suggestionsList = hooks?.suggestionsList || defaultSuggestionsList;\n    const valueStateMessage = hooks?.valueStateMessage;\n    const valueStateMessageInputIcon = hooks?.valueStateMessageInputIcon;\n    return (_jsxs(ResponsivePopover, { class: this.classes.popover, hideArrow: true, preventFocusRestore: true, preventInitialFocus: true, placement: \"Bottom\", horizontalAlign: \"Start\", tabindex: -1, style: this.styles.suggestionsPopover, onOpen: this._afterOpenPicker, onClose: this._afterClosePicker, onScroll: this._scroll, open: this.open, opener: this, accessibleName: this._popupLabel, children: [this._isPhone &&\n                _jsxs(_Fragment, { children: [_jsxs(\"div\", { slot: \"header\", class: \"ui5-responsive-popover-header\", children: [_jsxs(\"div\", { class: \"row\", children: [_jsx(\"span\", { children: this._headerTitleText }), _jsx(Button, { class: \"ui5-responsive-popover-close-btn\", icon: decline, design: \"Transparent\", onClick: this._closePicker })] }), _jsx(\"div\", { class: \"row\", children: _jsx(\"div\", { class: \"input-root-phone native-input-wrapper\", children: _jsx(Input, { class: \"ui5-input-inner-phone\", type: this.inputType, value: this.value, showClearIcon: this.showClearIcon, placeholder: this.placeholder, onInput: this._handleInput, onChange: this._handleChange }) }) })] }), this.hasValueStateMessage &&\n                            _jsxs(\"div\", { class: this.classes.popoverValueState, style: this.styles.suggestionPopoverHeader, children: [_jsx(Icon, { class: \"ui5-input-value-state-message-icon\", name: valueStateMessageInputIcon?.call(this) }), this.open && valueStateMessage?.call(this)] })] }), !this._isPhone && this.hasValueStateMessage &&\n                _jsxs(\"div\", { slot: \"header\", class: {\n                        \"ui5-responsive-popover-header\": true,\n                        \"ui5-responsive-popover-header--focused\": this._isValueStateFocused,\n                        ...this.classes.popoverValueState,\n                    }, style: this.styles.suggestionPopoverHeader, children: [_jsx(Icon, { class: \"ui5-input-value-state-message-icon\", name: valueStateMessageInputIcon?.call(this) }), this.open && valueStateMessage?.call(this)] }), suggestionsList.call(this), this._isPhone &&\n                _jsx(\"div\", { slot: \"footer\", class: \"ui5-responsive-popover-footer\", children: _jsx(Button, { design: \"Transparent\", onClick: this._closePicker, children: this._suggestionsOkButtonText }) })] }));\n}\nfunction defaultSuggestionsList() {\n    return (_jsx(List, { accessibleRole: ListAccessibleRole.ListBox, separators: this.suggestionSeparators, selectionMode: \"Single\", onMouseDown: this.onItemMouseDown, onItemClick: this._handleSuggestionItemPress, onSelectionChange: this._handleSelectionChange, children: _jsx(\"slot\", {}) }));\n}\n//# sourceMappingURL=InputSuggestionsTemplate.js.map", "import type { JsxTemplateResult } from \"@ui5/webcomponents-base/dist/index.js\";\nimport Input from \"../Input.js\";\nimport Icon from \"../Icon.js\";\nimport decline from \"@ui5/webcomponents-icons/dist/decline.js\";\n\nimport List from \"../List.js\";\nimport ResponsivePopover from \"../ResponsivePopover.js\";\nimport Button from \"../Button.js\";\nimport ListAccessibleRole from \"../types/ListAccessibleRole.js\";\n\nexport default function InputSuggestionsTemplate(this: Input, hooks?: { suggestionsList?: (this: Input) => JsxTemplateResult, valueStateMessage: (this: Input) => JsxTemplateResult, valueStateMessageInputIcon: (this: Input) => string }) {\n\tconst suggestionsList = hooks?.suggestionsList || defaultSuggestionsList;\n\tconst valueStateMessage = hooks?.valueStateMessage;\n\tconst valueStateMessageInputIcon = hooks?.valueStateMessageInputIcon;\n\n\treturn (\n\t\t<ResponsivePopover\n\t\t\tclass={this.classes.popover}\n\t\t\thideArrow={true}\n\t\t\tpreventFocusRestore={true}\n\t\t\tpreventInitialFocus={true}\n\t\t\tplacement=\"Bottom\"\n\t\t\thorizontalAlign=\"Start\"\n\t\t\ttabindex={-1}\n\t\t\tstyle={this.styles.suggestionsPopover}\n\t\t\tonOpen={this._afterOpenPicker}\n\t\t\tonClose={this._afterClosePicker}\n\t\t\tonScroll={this._scroll}\n\t\t\topen={this.open}\n\t\t\topener={this}\n\t\t\taccessibleName={this._popupLabel}\n\t\t>\n\t\t\t{this._isPhone &&\n\t\t\t\t<>\n\t\t\t\t\t<div slot=\"header\" class=\"ui5-responsive-popover-header\">\n\t\t\t\t\t\t<div class=\"row\">\n\t\t\t\t\t\t\t<span>{this._headerTitleText}</span>\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tclass=\"ui5-responsive-popover-close-btn\"\n\t\t\t\t\t\t\t\ticon={decline}\n\t\t\t\t\t\t\t\tdesign=\"Transparent\"\n\t\t\t\t\t\t\t\tonClick={this._closePicker}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"row\">\n\t\t\t\t\t\t\t<div class=\"input-root-phone native-input-wrapper\">\n\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\tclass=\"ui5-input-inner-phone\"\n\t\t\t\t\t\t\t\t\ttype={this.inputType}\n\t\t\t\t\t\t\t\t\tvalue={this.value}\n\t\t\t\t\t\t\t\t\tshowClearIcon={this.showClearIcon}\n\t\t\t\t\t\t\t\t\tplaceholder={this.placeholder}\n\t\t\t\t\t\t\t\t\tonInput={this._handleInput}\n\t\t\t\t\t\t\t\t\tonChange={this._handleChange}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{this.hasValueStateMessage &&\n\t\t\t\t\t<div class={this.classes.popoverValueState} style={this.styles.suggestionPopoverHeader}>\n\t\t\t\t\t\t<Icon class=\"ui5-input-value-state-message-icon\" name={valueStateMessageInputIcon?.call(this)} />\n\t\t\t\t\t\t{ this.open && valueStateMessage?.call(this) }\n\t\t\t\t\t</div>\n\t\t\t\t\t}\n\t\t\t\t</>\n\t\t\t}\n\n\t\t\t{!this._isPhone && this.hasValueStateMessage &&\n\t\t\t\t\t<div\n\t\t\t\t\t\tslot=\"header\"\n\t\t\t\t\t\tclass={{\n\t\t\t\t\t\t\t\"ui5-responsive-popover-header\": true,\n\t\t\t\t\t\t\t\"ui5-responsive-popover-header--focused\": this._isValueStateFocused,\n\t\t\t\t\t\t\t...this.classes.popoverValueState,\n\t\t\t\t\t\t}}\n\t\t\t\t\t\tstyle={this.styles.suggestionPopoverHeader}\n\t\t\t\t\t>\n\t\t\t\t\t\t<Icon class=\"ui5-input-value-state-message-icon\" name={valueStateMessageInputIcon?.call(this)} />\n\t\t\t\t\t\t{ this.open && valueStateMessage?.call(this) }\n\t\t\t\t\t</div>\n\t\t\t}\n\n\t\t\t{ suggestionsList.call(this) }\n\n\t\t\t{this._isPhone &&\n\t\t\t\t<div slot=\"footer\" class=\"ui5-responsive-popover-footer\">\n\t\t\t\t\t<Button\n\t\t\t\t\t\tdesign=\"Transparent\"\n\t\t\t\t\t\tonClick={this._closePicker}\n\t\t\t\t\t>\n\t\t\t\t\t\t{this._suggestionsOkButtonText}\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t}\n\t\t</ResponsivePopover>\n\t);\n}\n\nfunction defaultSuggestionsList(this: Input) {\n\treturn (\n\t\t<List\n\t\t\taccessibleRole={ListAccessibleRole.ListBox}\n\t\t\tseparators={this.suggestionSeparators}\n\t\t\tselectionMode=\"Single\"\n\t\t\tonMouseDown={this.onItemMouseDown}\n\t\t\tonItemClick={this._handleSuggestionItemPress}\n\t\t\tonSelectionChange={this._handleSelectionChange}\n\t\t>\n\t\t\t<slot></slot>\n\t\t</List>\n\t);\n}\n"], "names": ["$parcel$export", "e", "n", "v", "s", "Object", "defineProperty", "get", "set", "enumerable", "configurable", "parcelRequire", "$parcel$global", "globalThis", "parcelRegister", "register", "module", "exports", "$a7dc9f2231b7cc7d$export$2e2bcd8739ae039", "$3e6om", "$5Fd8G", "$65idz", "$TUBXI", "$a7dc9f2231b7cc7d$var$Suggestions", "template", "default", "component", "slotName", "highlight", "handleFocus", "selectedItemIndex", "onUp", "indexOfItem", "preventDefault", "index", "isOpened", "_hasValueState", "_handleItemNavigation", "onDown", "onSpace", "_isItemOnTarget", "onItemSelected", "_selectedItem", "onEnter", "_isGroupItem", "onPageUp", "isItemIndexValid", "_focusValueState", "_moveItemSelection", "onPageDown", "items", "_getItems", "lastItemIndex", "length", "onHome", "onEnd", "onTab", "toggle", "bToggle", "options", "undefined", "_getComponent", "open", "close", "preventFocusRestore", "_getNonGroupItems", "find", "item", "selected", "_isScrollable", "sc", "_getScrollContainer", "offsetHeight", "scrollHeight", "selectedItem", "picker", "_getPicker", "focused", "updateSelectedItemPosition", "pos", "keyboardUsed", "nonGroupItems", "accInfo", "isGroup", "hasAttribute", "currentPos", "indexOf", "listSize", "itemText", "text", "additionalText", "onItemSelect", "onItemPress", "pressedItem", "isPressEvent", "type", "detail", "_handledPress", "selectedItems", "_onClose", "forward", "_selectNextItem", "_selectPreviousItem", "itemsCount", "previousSelectedIdx", "_isValueStateFocused", "_clearValueStateFocus", "hasSuggestionItemSelected", "previousIdx", "nextIdx", "currentItem", "previousItem", "isGroupItem", "headerText", "focus", "_isItemIntoView", "itemRef", "shadowRoot", "querySelector", "_scrollItemIntoView", "_deselectItems", "for<PERSON>ach", "_clearItemFocus", "focusedItem", "rectItem", "getDomRef", "getBoundingClientRect", "rectInput", "windowHeight", "window", "innerHeight", "document", "documentElement", "clientHeight", "headerHeight", "valueStateHeader", "height", "top", "SCROLL_STEP", "scrollIntoView", "behavior", "block", "inline", "_scrollContainer", "suggestionComponent", "getSlottedNodes", "flatMap", "filter", "_getList", "_getListWidth", "offsetWidth", "itemSelectionAnnounce", "i18nBundle", "getText", "LIST_ITEM_GROUP_HEADER", "itemPositionText", "LIST_ITEM_POSITION", "hightlightInput", "input", "hasValueStateMessage", "value", "typedInValue", "_clearSelectedSuggestionAndaccInfo", "SuggestionsClass", "$2597a33886fa5427$export$2e2bcd8739ae039", "$hNhGB", "$2LNch", "$2597a33886fa5427$var$replaceAll", "replace", "caseInsensitive", "replaceAll", "RegExp", "textToHighlight", "makeToken", "t", "split", "openToken", "closeToken", "result", "match", "$cf44bb636867246b$export$2e2bcd8739ae039", "$c6CCt", "$aaoB5", "$809gB", "$c9ojM", "$f269c3c8be5a1789$var$__decorate", "decorators", "target", "key", "desc", "d", "c", "arguments", "r", "getOwnPropertyDescriptor", "Reflect", "decorate", "i", "$f269c3c8be5a1789$var$SuggestionItemGroup", "invalidate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HTMLElement", "prototype", "tag", "define", "$41faf93a088b8b10$export$2e2bcd8739ae039", "$lyEkX", "$6XEKD", "$kmcVJ", "$aDnpv", "$e9Sj6", "$iQEdW", "$adKC4", "hooks", "suggestionsList", "$41faf93a088b8b10$var$defaultSuggestionsList", "valueStateMessage", "valueStateMessageInputIcon", "jsxs", "class", "classes", "popover", "hideArrow", "preventInitialFocus", "placement", "horizontalAlign", "tabindex", "style", "styles", "suggestionsPopover", "onOpen", "_afterOpenPicker", "onClose", "_afterClosePicker", "onScroll", "_scroll", "opener", "accessibleName", "_popupLabel", "children", "_isPhone", "Fragment", "slot", "jsx", "_headerTitleText", "icon", "design", "onClick", "_closePicker", "inputType", "showClearIcon", "placeholder", "onInput", "_handleInput", "onChange", "_handleChange", "popoverValueState", "suggestionPopoverHeader", "name", "call", "_suggestionsOkButtonText", "accessibleRole", "ListBox", "separators", "suggestionSeparators", "selectionMode", "onMouseDown", "onItemMouseDown", "onItemClick", "_handleSuggestionItemPress", "onSelectionChange", "_handleSelectionChange"], "version": 3, "file": "InputSuggestions.a8a3b8f2.js.map"}