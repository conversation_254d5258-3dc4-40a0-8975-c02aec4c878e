{"version": 3, "file": "icon.js", "sourceRoot": "", "sources": ["../../../src/components/base/icon.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAGrD,IAAM,OAAO,GAAb,MAAM,OAAQ,SAAQ,UAAU;IAAhC;;QACuB,SAAI,GAAG,EAAE,CAAC;QACT,YAAO,GAAG,IAAI,CAAC;IA0B9C,CAAC;IARC,MAAM;QACJ,OAAO,IAAI,CAAA;;gBAEC,IAAI,CAAC,IAAI,IAAI,YAAY;iBACxB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;KAEzC,CAAC;IACJ,CAAC;;AAvBM,cAAM,GAAG,GAAG,CAAA;;;;;;;;;;;;;;GAclB,AAdY,CAcX;AAjB0B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;qCAAW;AACT;IAA5B,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;wCAAgB;AAFjC,OAAO;IADnB,aAAa,CAAC,UAAU,CAAC;GACb,OAAO,CA4BnB", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\n\r\n@customElement(\"wex-icon\")\r\nexport class WexIcon extends LitElement {\r\n  @property({ type: String }) icon = \"\";\r\n  @property({ type: Boolean }) pointer = true;\r\n\r\n  static styles = css`\r\n    .icon {\r\n      width: 1.5rem;\r\n      height: 1.5rem;\r\n      padding: 0.25rem;\r\n      border-radius: 0.25rem;\r\n    }\r\n    .icon:hover {\r\n      background-color: var(--clr-primary-ultra-light);\r\n    }\r\n\r\n    .pointer {\r\n      cursor: pointer;\r\n    }\r\n  `;\r\n\r\n  render() {\r\n    return html`\r\n      <iron-icon\r\n        icon=\"${this.icon ?? \"more-horiz\"}\"\r\n        class=\"${this.pointer ? \"pointer\" : \"\"} icon\"\r\n      ></iron-icon>\r\n    `;\r\n  }\r\n}\r\n"]}