{"version": 3, "file": "wex-task-filepackage_test.js", "sourceRoot": "", "sources": ["../../src/test/wex-task-filepackage_test.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AACxE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAU,MAAM,kBAAkB,CAAC;AACjE,OAAO,iBAAiB,CAAC;AACzB,OAAO,KAAK,MAAM,iBAAiB,CAAC;AAEpC,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE;IACpB,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;QAC5B,MAAM,EAAE,GAAG,MAAM,OAAO,CACtB,8EAA8E,CAC/E,CAAC;QACF,MAAM,CAAC,UAAU,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QACvC,MAAM,EAAE,GAAG,MAAM,OAAO,CACtB,IAAI,CAAA;;wBAEc,QAAQ;+BACD,CAC1B,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,QAAQ,GAAS;IACnB,SAAS,EAAE,KAAK;IAChB,cAAc,EAAE,EAAE;IAClB,sBAAsB,EAAE,EAAE;IAC1B,aAAa,EAAE,CAAC;IAChB,SAAS,EAAE,CAAC;IACZ,WAAW,EAAE,EAAE;IACf,cAAc,EAAE,EAAE;IAClB,kBAAkB,EAAE,EAAE;IACtB,SAAS,EAAE,EAAE;IACb,WAAW,EAAE,EAAE;IACf,MAAM,EAAE,CAAC;IACT,eAAe,EAAE,EAAE;IACnB,UAAU,EAAE,CAAC;IACb,aAAa,EAAE,EAAE;IACjB,iBAAiB,EAAE,QAAQ;CAC5B,CAAC;AAEF,MAAM,SAAS,GAAG,IAAI,KAAK,CACzB,OAAO,EACP,SAAS,EACT,YAAY,CACb,CAAA", "sourcesContent": ["import { Task } from \"@bds/types\";\r\nimport { WexTaskFilepackage } from \"../components/wex-task-filepackage\";\r\nimport { fixture, assert, html, expect } from \"@open-wc/testing\";\r\nimport \"./store-wrapper\";\r\nimport Store from 'store/beedle.js';\r\n\r\nsuite(\"renders\", () => {\r\n  test(\"is defined\", async () => {\r\n    const el = await fixture(\r\n      \"<store-wrapper><wex-task-filepackage></wex-task-filepackage></store-wrapper>\"\r\n    );\r\n    assert.instanceOf(el, WexTaskFilepackage);\r\n  });\r\n\r\n  test(\"renders a radio group\", async () => {\r\n    const el = await fixture(\r\n      html`<wex-task-filepackage\r\n        .view=\"task\"\r\n        .selectedTask=${testTask}\r\n      ></wex-task-filepackage>`\r\n    );\r\n  });\r\n});\r\n\r\nlet testTask: Task = {\r\n  isClaimed: false,\r\n  processDefName: \"\",\r\n  processInstDescription: \"\",\r\n  processInstId: 0,\r\n  projectId: 0,\r\n  projectName: \"\",\r\n  taskActiveDate: \"\",\r\n  taskDefDescription: \"\",\r\n  taskDefId: \"\",\r\n  taskDefName: \"\",\r\n  taskId: 0,\r\n  taskInstComment: \"\",\r\n  taskInstId: 0,\r\n  taskStartDate: \"\",\r\n  userWexCapability: \"Author\",\r\n};\r\n\r\nconst mockStore = new Store(\r\n  actions,\r\n  mutations,\r\n  initialState\r\n)\r\n"]}