import { LitElement, html, css } from "lit";
// import { property } from "lit/decorators";
// @ts-ignore
import { storeInstance } from "store";
import { Router } from "@vaadin/router";
class WexPublishHeader extends LitElement {
    constructor() {
        //   @property({ type: Object }) state: any = {}; // need to define state
        super(...arguments);
        this.state = {};
        this.string = [];
        this._subscription = null;
        this._tabs = [];
    }
    //   private _subtabs: string[] = [];
    static get styles() {
        return css `
      * {
        box-sizing: border-box;
      }
      #publish-tabs {
        display: flex;
        padding: 0;
        margin: 0;
        list-style: none;
        border-bottom: 1px solid;
        border-color: var(--primary-100);
      }
      .tab {
        display: flex;
        align-items: center;
        padding: var(--spacing-md) var(--spacing-md);
        cursor: pointer;
        color: var(--color-text);
      }
      .tab[active] {
        border-bottom: 2px solid;
        font-weight: bold;
      }
      .tab:not([active]):hover {
        background-color: var(--clr-white);
        color: var(--primary);
      }
    `;
    }
    connectedCallback() {
        super.connectedCallback();
        const state = storeInstance.state;
        this.state = state;
        this.string = state[state.langCode];
        this._subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        this._init(state);
    }
    disconnectedCallback() {
        storeInstance.unsubscribe(this._subscription);
    }
    firstUpdated() {
        this._setTab();
    }
    _init(state) {
        this._tabs = state.menus.publish;
        // this._subtabs = state.menus.publish_run || [];
    }
    stateChange(state) {
        this.string = state[state.langCode];
    }
    _setTab() {
        const url = new URL(window.location.href);
        const tabs = url.pathname.split("/");
        // if (tabs.length == 6) {
        //   const subtab = tabs.pop();
        //   for (let x of this._subtabs) {
        //     x.active = x.name == subtab;
        //   }
        //   Store.dispatch("setState", {
        //     property: "publish_subtab",
        //     value: subtab,
        //   });
        // }
        const currentTab = tabs.pop();
        for (let tab of this._tabs) {
            tab.active = tab.name == currentTab;
        }
        storeInstance.dispatch("setState", {
            property: "publish_tab",
            value: currentTab,
        });
        this.requestUpdate();
    }
    render() {
        const template = html ` <ul id="publish-tabs">
      ${this._tabs.map((tab) => html `
          <li
            class="tab"
            ?active=${!!tab.active}
            @click="${this._handleClick.bind(this, tab.name)}"
          >
            ${this.string[tab.label]}
          </li>
        `)}
    </ul>`;
        return template;
    }
    _handleClick(path) {
        Router.go("/wex/" + this.state.langCode + "/publish/" + path);
    }
}
customElements.define("wex-publish-header", WexPublishHeader);
//# sourceMappingURL=publish.js.map