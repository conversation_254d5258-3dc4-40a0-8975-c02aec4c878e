{"version": 3, "file": "select-file.js", "sourceRoot": "", "sources": ["../../../src/components/dialog/select-file.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,mCAAmC,CAAC;AAC3C,OAAO,qCAAqC,CAAC;AAC7C,OAAO,kCAAkC,CAAC;AAE1C,OAAO,mBAAmB,CAAC;AAC3B,OAAO,6BAA6B,CAAC;AACrC,OAAO,qBAAqB,CAAC;AAC7B,OAAO,+BAA+B,CAAC;AACvC,OAAO,4BAA4B,CAAC;AACpC,OAAO,+BAA+B,CAAC;AACvC,OAAO,8BAA8B,CAAC;AACtC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAK5D,IAAM,mBAAmB,GAAzB,MAAM,mBAAoB,SAAQ,UAAU;IAO1C,MAAM,KAAK,UAAU;QACnB,OAAO;YACL,cAAc,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;YAChC,eAAe,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;YACjC,UAAU,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;YAC5B,YAAY,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;SAC/B,CAAC;IACJ,CAAC;IAED,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuCT,CAAC;IACJ,CAAC;IAED;QACE,KAAK,EAAE,CAAC;QA3DkB,mBAAc,GAAwB,EAAE,CAAC;QACzC,oBAAe,GAAmB,IAAI,CAAC;QACvC,eAAU,GAAW,EAAE,CAAC;QACxB,iBAAY,GAAoB,IAAI,CAAC;QACjE,SAAI,GAA+B,IAAI,CAAC;QA0RxC,eAAU,GAAG,GAAG,EAAE;YAChB,6BAA6B;YAC7B,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjE,CAAC,CAAC;QArOA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QAEtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YACrD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,gBAAgB;IAClB,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,YAAY;QACV,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QACpE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,UAAU,CAAC,IAAI,IAAI,CAAA;IAChE,CAAC;IAED,KAAK;QACH,aAAa,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,oDAAoD;QACjG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAC/C,kCAAkC,CACnC,CAAC;QACF,6EAA6E;QAC7E,6BAA6B;QAC7B,0CAA0C;QAC1C,0BAA0B;QAC1B,wCAAwC;QACxC,OAAO;QACP,IAAI;QACJ,4CAA4C;QAC5C,4CAA4C;QAC5C,6BAA6B;QAC7B,0BAA0B;QAC1B,MAAM;QACN,MAAM;QACN,kCAAkC;IACpC,CAAC;IAED,iBAAiB;QACf,iGAAiG;QACjG,IAAI,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE,CAAC;YAC9C,qBAAqB;YACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,yBAAyB;YACzB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,4EAA4E;QAC5E,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,mBAAmB,CAAC,KAAK;QACvB,0CAA0C;QAC1C,8BAA8B;QAC9B,eAAe;QACf,uCAAuC;QACvC,oCAAoC;QACpC,KAAK;QACL,IAAI,KAAK,CAAC,yBAAyB,EAAE,CAAC;YACpC,+DAA+D;YAC/D,qBAAqB;YACrB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,yBAAyB,CAAC;YACpD,kCAAkC;YAClC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,iEAAiE;YACjE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,mBAAmB;QACnB,+DAA+D;QAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC7C,8CAA8C;QAC9C,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,QAAQ,GAAG,SAAS,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,kBAAkB;QAChB,8IAA8I;QAE9I,8DAA8D;QAC9D,MAAM,OAAO,GACX,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC;QAChE,IAAI,OAAO,EAAE,CAAC;YACZ,wCAAwC;YACxC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;QACjC,CAAC;IACH,CAAC;IAED,WAAW,CAAC,KAAK;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,uBAAuB,CAAC;QAEpD,oBAAoB;QACpB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,+BAA+B;YAEzD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;QAED,+CAA+C;QAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,eAAe,CAAC,IAAgB,EAAE,MAAc;QAC9C,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO;QAEhC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAEvD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CACnD,IAAI,EACJ,MAAM,CAAC,MAAM,CAAC,EACd,EAAE,CACH,CAAC;QACF,IAAI,CAAC,MAAM;YAAE,OAAO;QACpB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;QACxB,yBAAyB;QACzB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,4BAA4B;YACtC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,+BAA+B;YACzC,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE;SAChD,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;IAC/B,CAAC;IAED,qBAAqB;IACrB,4BAA4B;IAC5B,IAAI;IAEJ,kBAAkB;IAClB,2BAA2B;IAC3B,yCAAyC;IACzC,uCAAuC;IACvC,mEAAmE;IACnE,QAAQ;IACR,IAAI;IAEJ,wDAAwD;IACxD,oCAAoC;IACpC,0CAA0C;IAC1C,2DAA2D;IAC3D,gFAAgF;IAChF,uCAAuC;IACvC,4CAA4C;IAC5C,iDAAiD;IACjD,yBAAyB;IACzB,WAAW;IACX,sFAAsF;IACtF,IAAI;IAEJ,+BAA+B;IAC/B,aAAa;QACX,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAE7D,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,gCAAgC;YAC1C,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;SACtC,CAAC,CAAC;QACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,kCAAkC;YAC5C,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB;SACxC,CAAC,CAAC;QAEH,mCAAmC;QAEnC,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,sCAAsC;YAChD,KAAK,EAAE,KAAK;SACb,CAAC,CAAC;QACH,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,gCAAgC;YAC1C,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;IACvC,CAAC;IAOD,iBAAiB,CAAC,CAAC;QACjB,wCAAwC;QACxC,IAAI,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,mBAAmB,GACrB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;QAC5D,IAAI,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC5D,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,CACxC,CAAC,EACD,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAClC,CAAC;QACJ,CAAC;QACD,IACE,SAAS;YACT,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,GAAG,mBAAmB,CAAC,MAAM,EACxE,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,CACxC,CAAC,EACD,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAClC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,yBAAyB;IACzB,qBAAqB;IACrB,kBAAkB;QAChB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,2BAA2B;YACrC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IACzB,mBAAmB;IACnB,uBAAuB;IACvB,2CAA2C;IAC3C,iCAAiC;IACjC,2CAA2C;IAC3C,mDAAmD;IACnD,yBAAyB;IACzB,UAAU;IACV,uCAAuC;IACvC,MAAM;IACN,IAAI;IAEJ,aAAa;QACX,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAClD,mDAAmD;QACnD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,+BAA+B;YAC/B,kEAAkE;YAClE,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAEvE,IAAI,CAAC,eAAe,CAClB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAC7C,CAAC;QACJ,CAAC;IACH,CAAC;IAED,yBAAyB;IACzB,kBAAkB;QAChB,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAChE,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,gCAAgC;YAC1C,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK;SAClC,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED,yBAAyB;IACzB,qBAAqB,CAAC,CAAC;QACrB,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YACtD,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;YACjD,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;gBACjC,QAAQ,EAAE,sCAAsC;gBAChD,KAAK,EAAE,CAAC,CAAC,aAAa,CAAC,OAAO;aAC/B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,sBAAsB;QACpB,OAAO,IAAI,CAAA;;;gCAGiB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;;;+BAI9B,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;2BAC9C,CAAC,gCAAgC,CAAC;;;6BAGhC,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;2BAC5C,CAAC,kCAAkC,CAAC;;;;;oBAK3C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;aACpC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;;;;;sBAKnB,IAAI,CAAC,KAAK,CAAC,oCAAoC;qBAChD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;kBACxC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;;;KAG9C,CAAC;IACJ,CAAC;IAED,qBAAqB;QACnB,OAAO,IAAI,CAAA;;;;;mBAKI,IAAI,CAAC,UAAU;;;;;;mBAMf,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;;;KAGhD,CAAC;IACJ,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAA;;UAEL,IAAI,CAAC,sBAAsB,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE;;KAElE,CAAC;IACJ,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAA;;;;KAIV,CAAC;IACJ,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAA;;;;;;qBAMM,IAAI,CAAC,kBAAkB;;;;KAIvC,CAAC;IACJ,CAAC;IAED,6CAA6C;IAC7C,MAAM;QACJ,OAAO,IAAI,CAAA;;;iBAGE,CAAC,CAAC,IAAI,CAAC,cAAc;2BACX,IAAI,CAAC,cAAc;sBACxB,IAAI,CAAC,UAAU;;;;;;;;;YASzB,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE;;;KAGxE,CAAC;IACJ,CAAC;CAwBF,CAAA;AAhf6B;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;2DAA0C;AACzC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;4DAAwC;AACvC;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;uDAAyB;AACxB;IAA3B,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;yDAAsC;AAJ7D,mBAAmB;IADxB,aAAa,CAAC,wBAAwB,CAAC;GAClC,mBAAmB,CAifxB", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { storeInstance } from \"store/index.js\";\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\nimport \"@ui5/webcomponents/dist/CheckBox.js\";\r\nimport \"@ui5/webcomponents/dist/Input.js\";\r\n\r\nimport \"../wex-folders.js\";\r\nimport \"../wex-select-file-files.js\";\r\nimport \"../common/dialog.js\";\r\nimport \"../common/project-selector.js\";\r\nimport \"./content/file-explorer.js\";\r\nimport \"../select/global_languages.js\";\r\nimport \"../select/global_branches.js\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\nimport { Collection, FileMeta, Project } from \"@bds/types\";\r\nimport { BDSTree } from \"bds-tree\";\r\n\r\n@customElement(\"wex-dialog-select-file\")\r\nclass WexDialogSelectFile extends LitElement {\r\n  @property({ type: Object }) dialogOpenData: Record<string, any> = {};\r\n  @property({ type: Object }) selectedProject: Project | null = null;\r\n  @property({ type: String }) searchTerm: string = \"\";\r\n  @property({ type: Object }) selectedFile: FileMeta | null = null;\r\n  tree: BDSTree<Collection> | null = null;\r\n\r\n  static get properties() {\r\n    return {\r\n      dialogOpenData: { type: Object },\r\n      selectedProject: { type: Object },\r\n      searchTerm: { type: String },\r\n      selectedFile: { type: Object },\r\n    };\r\n  }\r\n\r\n  static get styles() {\r\n    return css`\r\n      wex-main-pane {\r\n        height: 100%;\r\n      }\r\n      .top {\r\n        width: 100%;\r\n        flex: 1;\r\n      }\r\n      .middle {\r\n        flex: 9;\r\n      }\r\n      .bottom {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: row;\r\n        flex-shrink: 0;\r\n        height: 10%;\r\n      }\r\n\r\n      #proj {\r\n        flex: 2;\r\n      }\r\n\r\n      #searchterm {\r\n        clear: both;\r\n        --_ui5_input_width: 8rem;\r\n      }\r\n\r\n      #display {\r\n        flex: 1;\r\n        width: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n      }\r\n\r\n      .selectedfileinput {\r\n        width: 100%;\r\n      }\r\n    `;\r\n  }\r\n\r\n  constructor() {\r\n    super();\r\n    this.state = {};\r\n    this.string = {};\r\n    this._subscription = null;\r\n\r\n    this.dialogOpenData = null;\r\n\r\n    this.selectedFile = null;\r\n    this.selectedFilePathId = \"\";\r\n    this.searchTerm = \"\";\r\n\r\n    this.selectedProject = null;\r\n    this.foldersData = {};\r\n\r\n    this.fileExplorer = null;\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this.state = storeInstance.state;\r\n    this.string = this.state[this.state.langCode];\r\n    this._subscription = storeInstance.subscribe((state) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    // this._init();\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this._subscription);\r\n  }\r\n\r\n  firstUpdated() {\r\n    this._init();\r\n    this.searchTermField = this.shadowRoot.querySelector(\"#searchterm\");\r\n    this.tree = this.shadowRoot?.querySelector(\"bds-tree\") || null\r\n  }\r\n\r\n  _init() {\r\n    storeInstance.dispatch(\"getCollectionTree\"); // this sets a state var? Which one is updated here?\r\n    this.fileExplorer = this.shadowRoot.querySelector(\r\n      \"wex-dialog-content-file-explorer\"\r\n    );\r\n    // this.folderselement = this.shadowRoot.querySelector(\"#fileselectfolders\");\r\n    // if (this.folderselement) {\r\n    //   this.folderselement.addEventListener(\r\n    //     \"selectedrendered\",\r\n    //     this._scrollToSelected.bind(this)\r\n    //   );\r\n    // }\r\n    // TODO EJS add this to the dialog component\r\n    // this.addEventListener(\"keydown\", (e) => {\r\n    //   if (e.key == \"Escape\") {\r\n    //     this.closeDialog();\r\n    //   }\r\n    // });\r\n    //this.fileExplorer.refreshData();\r\n  }\r\n\r\n  _updateSearchTerm() {\r\n    // if there is a search term in state - why would a term be in state before the dialog is opened?\r\n    if (this.state.select_file_dialog_search_term) {\r\n      // set it to instance\r\n      this.searchTerm = this.state.select_file_dialog_search_term;\r\n    } else {\r\n      // or set instance to nil\r\n      this.searchTerm = \"\";\r\n    }\r\n  }\r\n\r\n  _updateFolderData() {\r\n    // this.foldersData = JSON.parse(JSON.stringify(this.state.global_folders));\r\n    if (this.state.global_folders) {\r\n      this.foldersData = JSON.parse(JSON.stringify(this.state.global_folders));\r\n    }\r\n  }\r\n\r\n  _updateSelectedFile(state) {\r\n    // state change from wex-select-file-files\r\n    // if there is a selected file\r\n    // console.log(\r\n    //   \"state.select_file_selected_file\",\r\n    //   state.select_file_selected_file\r\n    // );\r\n    if (state.select_file_selected_file) {\r\n      // console.log(\"state file?\", state.select_file_selected_file);\r\n      // set it to instance\r\n      this.selectedFile = state.select_file_selected_file;\r\n      // set the path id to instance var\r\n      this.selectedFilePathId = this.selectedFile.resPathId;\r\n    } else {\r\n      // null the vars (they should be null already in the constructor)\r\n      this.selectedFile = null;\r\n      this.selectedFilePathId = \"\";\r\n    }\r\n  }\r\n\r\n  _setCurrentFolder() {\r\n    // setCurrentFolder\r\n    // const folderObj = this.state.select_file_dialog_open.folder;\r\n    const folderObj = this.dialogOpenData.folder;\r\n    // console.log(\"setCurrentFolder\", folderObj);\r\n    if (folderObj) {\r\n      const folderId = folderObj.id;\r\n      this._findFolderById(this.foldersData, folderId, this);\r\n    }\r\n  }\r\n\r\n  _setCurrentProject() {\r\n    // this.selectedProject = this.state.task_filter_project; // confusing; is there a global project selector? Can it inherit from the task page?\r\n\r\n    // const project = this.state.select_file_dialog_open.project;\r\n    const project =\r\n      this.dialogOpenData.project || this.state.task_filter_project;\r\n    if (project) {\r\n      // this._selectProjectCallback(project);\r\n      this.selectedProject = project;\r\n    }\r\n  }\r\n\r\n  stateChange(state) {\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this.dialogOpenData = state.select_file_dialog_open;\r\n\r\n    // if dialog is open\r\n    if (this.dialogOpenData) {\r\n      this._updateFolderData(); // outside open check in source\r\n\r\n      this._updateSearchTerm();\r\n      this._updateSelectedFile(state);\r\n      this._setCurrentFolder();\r\n      this._setCurrentProject();\r\n    }\r\n\r\n    // does this need to be moved to file explorer?\r\n    this.contextMenu = JSON.parse(JSON.stringify(this.state.menus.folder_menu));\r\n    this.contextMenu.map((item) => {\r\n      item.text = this.string[item.label];\r\n    });\r\n  }\r\n\r\n  _findFolderById(root: Collection, collId: string) {\r\n    if (!this.tree || !root) return;\r\n\r\n    console.log(\"FIND FOLDER\", this.tree);\r\n\r\n    this.tree.treeController.setNode(root, String(collId));\r\n\r\n    const result = this.tree.treeController.findNodeByKey(\r\n      root,\r\n      String(collId),\r\n      []\r\n    );\r\n    if (!result) return;\r\n    const { node } = result;\r\n    // found the right folder\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"select_file_dialog_project\",\r\n      value: null,\r\n    });\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"select_file_dialog_folderitem\",\r\n      value: { ...node, path: node.collectionPathId },\r\n    });\r\n  }\r\n\r\n  _clearSelectedFile() {\r\n    this.selectedFile = null;\r\n    this.selectedFilePathId = \"\";\r\n  }\r\n\r\n  // _getSearchTerm() {\r\n  //   return this.searchTerm;\r\n  // }\r\n\r\n  // sub-dialog call\r\n  // _projectSelectDialog() {\r\n  //   storeInstance.dispatch(\"setState\", {\r\n  //     property: \"project_dialog_open\",\r\n  //     value: { callback: this._selectProjectCallback.bind(this) },\r\n  //   });\r\n  // }\r\n\r\n  // callback for opening project dialog... try to remove?\r\n  // _selectProjectCallback(project) {\r\n  //   console.log(\"selectProjectCallback\");\r\n  //   console.log(\"selectProjectCallback project\", project);\r\n  //   console.log(\"selectProjectCallback selectedProject\", this.selectedProject);\r\n  //   // this.selectedProject = project;\r\n  //   // storeInstance.dispatch(\"setState\", {\r\n  //   //   property: \"select_file_dialog_project\",\r\n  //   //   value: project,\r\n  //   // });\r\n  //   // this._findFolderById(this.foldersData, project.projectHomeCollectionId, this);\r\n  // }\r\n\r\n  // should be instance variables\r\n  _clearFilters() {\r\n    console.log(\"clear filters\", this.state.default_lang_object);\r\n\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"select_file_dialog_lang_filter\",\r\n      value: this.state.default_lang_object,\r\n    });\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"select_file_dialog_branch_filter\",\r\n      value: this.state.default_branch_object,\r\n    });\r\n\r\n    // this.searchTermField.value = \"\";\r\n\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"select_file_dialog_search_subfolders\",\r\n      value: false,\r\n    });\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"select_file_dialog_search_term\",\r\n      value: \"\",\r\n    });\r\n\r\n    this._clearSelectedFile();\r\n\r\n    this.fileExplorer.refreshFilesList();\r\n  }\r\n\r\n  _onConfirm = () => {\r\n    // console.log(\"_onConfirm\");\r\n    this.state.select_file_dialog_open.callback(this.selectedFile);\r\n  };\r\n\r\n  _scrollToSelected(e) {\r\n    // console.log(\"scrolling to selected\");\r\n    var selectedY = e.detail.getBoundingClientRect().y;\r\n    var folderContainerRect =\r\n      this.folderselement.parentElement.getBoundingClientRect();\r\n    if (selectedY < this.folderselement.parentElement.scrollTop) {\r\n      this.folderselement.parentElement.scrollBy(\r\n        0,\r\n        selectedY - folderContainerRect.y\r\n      );\r\n    }\r\n    if (\r\n      selectedY >\r\n      this.folderselement.parentElement.scrollTop + folderContainerRect.height\r\n    ) {\r\n      this.folderselement.parentElement.scrollBy(\r\n        0,\r\n        selectedY - folderContainerRect.y\r\n      );\r\n    }\r\n  }\r\n\r\n  // should be instance var\r\n  // this.selectedFile;\r\n  _clearSelectedFile() {\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"select_file_selected_file\",\r\n      value: null,\r\n    });\r\n  }\r\n\r\n  // should be instance var\r\n  // this.folderItem;\r\n  // _folderSelected(e) {\r\n  //   if (this.folderselected != e.detail) {\r\n  //     this._clearSelectedFile();\r\n  //     storeInstance.dispatch(\"setState\", {\r\n  //       property: \"select_file_dialog_folderitem\",\r\n  //       value: e.detail,\r\n  //     });\r\n  //     this.folderselected == e.detail;\r\n  //   }\r\n  // }\r\n\r\n  _jumpToFolder() {\r\n    console.log(\"jumpToFolder\", this.selectedProject);\r\n    // check only if there is a project vs all projects\r\n    if (this.selectedProject) {\r\n      // console.log(\"jumpToFolder\");\r\n      // console.log(\"jumpToFolder this.foldersData\", this.foldersData);\r\n      console.log(\"jumpToFolder this.selectedProject\", this.selectedProject);\r\n\r\n      this._findFolderById(\r\n        this.foldersData,\r\n        this.selectedProject.projectHomeCollectionId\r\n      );\r\n    }\r\n  }\r\n\r\n  // should be instance var\r\n  _handleSearchClick() {\r\n    console.log(\"dispatch search for \", this.searchTermField.value);\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"select_file_dialog_search_term\",\r\n      value: this.searchTermField.value,\r\n    });\r\n    this._clearSelectedFile();\r\n  }\r\n\r\n  // should be instance var\r\n  _handleCheckboxChange(e) {\r\n    if (this.includesubfolders != e.currentTarget.checked) {\r\n      this._clearSelectedFile();\r\n      this.includesubfolders = e.currentTarget.checked;\r\n      storeInstance.dispatch(\"setState\", {\r\n        property: \"select_file_dialog_search_subfolders\",\r\n        value: e.currentTarget.checked,\r\n      });\r\n    }\r\n  }\r\n\r\n  _renderProjectControls() {\r\n    return html`\r\n      <wex-row-item id=\"proj\" justifyContent=\"stretch\" alignItems=\"center\">\r\n        <wex-project-selector\r\n          .jumpToFolderAction=${this._jumpToFolder.bind(this)}\r\n        ></wex-project-selector>\r\n\r\n        <wex-select-global-languages\r\n          @language-selected=${() => this.fileExplorer.refreshFilesList()}\r\n          .filterTargets=${[\"select_file_dialog_lang_filter\"]}\r\n        ></wex-select-global-languages>\r\n        <wex-select-global-branches\r\n          @branch-selected=${() => this.fileExplorer.refreshFilesList()}\r\n          .filterTargets=${[\"select_file_dialog_branch_filter\"]}\r\n        ></wex-select-global-branches>\r\n\r\n        <ui5-button\r\n          design=\"Transparent\"\r\n          @click=\"${this._clearFilters.bind(this)}\"\r\n          >${this.string[\"_clearfilters\"]}</ui5-button\r\n        >\r\n\r\n        <ui5-checkbox\r\n          id=\"search-chk\"\r\n          ?checked=\"${this.state.select_file_dialog_search_subfolders}\"\r\n          @change=\"${this._handleCheckboxChange.bind(this)}\"\r\n          text=\"${this.string[\"_includesubfolders\"]}\"\r\n        ></ui5-checkbox>\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n\r\n  _renderSearchControls() {\r\n    return html`\r\n      <wex-row-item id=\"srch\" justifyContent=\"flex-end\" alignItems=\"center\">\r\n        <ui5-input\r\n          id=\"searchterm\"\r\n          type=\"Text\"\r\n          value=\"${this.searchTerm}\"\r\n          show-clear-icon\r\n        ></ui5-input>\r\n\r\n        <wex-icon\r\n          icon=\"search\"\r\n          @click=${this._handleSearchClick.bind(this)}\r\n        ></wex-icon>\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n\r\n  _renderTop() {\r\n    return html`\r\n      <wex-row-item class=\"top\" justifyContent=\"space-between\">\r\n        ${this._renderProjectControls()} ${this._renderSearchControls()}\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n\r\n  _renderMiddle() {\r\n    return html`\r\n      <wex-row-item class=\"middle\">\r\n        <wex-dialog-content-file-explorer></wex-dialog-content-file-explorer>\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n\r\n  _renderBottom() {\r\n    return html`\r\n      <wex-row-item class=\"bottom\">\r\n        <wex-col-item id=\"display\">\r\n          <ui5-input\r\n            class=\"selectedfileinput ui5-content-density-compact\"\r\n            readonly\r\n            value=\"${this.selectedFilePathId}\"\r\n          ></ui5-input>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n\r\n  // render select file dialog (main component)\r\n  render() {\r\n    return html`\r\n      <wex-dialog\r\n        id=\"select-file-dialog\"\r\n        .open=\"${!!this.dialogOpenData}\"\r\n        .dialogSettings=\"${this.dialogSettings}\"\r\n        .onConfirm=\"${this._onConfirm}\"\r\n        confirmLabel=\"Ok\"\r\n        cancelLabel=\"Close\"\r\n        headerText=\"Select a File\"\r\n        dialogOpenStateProperty=\"select_file_dialog_open\"\r\n        width=\"90%\"\r\n        height=\"90%\"\r\n      >\r\n        <wex-col-item>\r\n          ${this._renderTop()} ${this._renderMiddle()} ${this._renderBottom()}\r\n        </wex-col-item>\r\n      </wex-dialog>\r\n    `;\r\n  }\r\n  // move to instance variable\r\n  // _clearSearch() {\r\n  //   storeInstance.dispatch(\"setState\", {\r\n  //     property: \"select_file_dialog_search_term\",\r\n  //     value: null,\r\n  //   });\r\n  //   this._clearSelectedFile();\r\n  // }\r\n  // should be instance vars\r\n  // this.searchSubfolders;\r\n  // this.searchTerm;\r\n  // _clearSearch() {\r\n  //   this.searchTermField.value = \"\";\r\n  //   storeInstance.dispatch(\"setState\", {\r\n  //     property: \"select_file_dialog_search_subfolders\",\r\n  //     value: false,\r\n  //   });\r\n  //   storeInstance.dispatch(\"setState\", {\r\n  //     property: \"select_file_dialog_search_term\",\r\n  //     value: \"\",\r\n  //   });\r\n  //   this._clearSelectedFile();\r\n  // }\r\n}\r\n"]}