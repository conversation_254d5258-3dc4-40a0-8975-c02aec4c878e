import { LitElement } from "lit";
import "@ui5/webcomponents-compat/dist/Table.js";
import "@ui5/webcomponents-compat/dist/TableColumn.js";
import "@ui5/webcomponents-compat/dist/TableRow.js";
import "@ui5/webcomponents-compat/dist/TableCell.js";
export declare class WexTable extends LitElement {
    columns: any[];
    rows: any[];
    enableSelect: boolean;
    enableMenu: boolean;
    menuItems: any[];
    defaultHeight: string;
    activeFilters: any;
    cellRenderers: any;
    private string;
    private _subscription;
    static get styles(): import("lit").CSSResult;
    connectedCallback(): void;
    disconnectedCallback(): void;
    firstUpdated(): void;
    updated(): void;
    stateChange(state: any): void;
    _isAllSelected(): boolean | 0;
    _handleSort(e: Event): void;
    _handleSelect(item: any, e: CustomEvent): void;
    _handleSelectAll(e: CustomEvent): void;
    _handleClick(item: any, e: MouseEvent): void;
    _handleRightClick(item: any, e: CustomEvent): void;
    _handleClickContextMenu(e: CustomEvent): void;
    _handleAction(item: any, e: CustomEvent): void;
    /**
     * This incorporates filtering logic directly into wex table. If there are no filterValues passed
     * to the table, it will just return all rows.
     *
     * The structure of the filterValues object should be
     * {
     *  key: [...filterStrings]
     * }
     * @returns filtered rows based on filterValues property input
     */
    _filterRows(): any[];
    render(): import("lit").TemplateResult<1>;
}
//# sourceMappingURL=wex-table.d.ts.map