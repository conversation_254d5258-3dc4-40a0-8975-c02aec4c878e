import { LitElement, html, css, PropertyValues } from "lit";
import { customElement, property } from "lit/decorators.js";

@customElement("wex-row-item")
export class WexRowItem extends LitElement {
  @property({ type: String }) border?: string;
  @property({ type: String }) justifyContent?: string;
  @property({ type: String }) alignItems?: string;
  @property({ type: String }) height?: string;

  static styles = css`
    :host {
      display: flex;
      flex: 1;
      width: 100%;
      min-height: 0;
    }

    .wrapper {
      display: flex;
      flex-direction: row;
      overflow-x: auto;
      gap: 1rem;
      flex: 1;
      min-height: 40px;
      width: 100%;
    }
    .wrapper > *:not(:last-child) {
      margin-right: 0.5rem;
    }
  `;

  updated(changedProps: PropertyValues) {
    if (changedProps.has("border")) {
      this.style.border = this.border || "";
    }
  }

  render() {
    const height = this.height ?? "auto";
    const justifyContent = this.justifyContent ?? "space-evenly";
    const alignItems = this.alignItems ?? "stretch";
    return html`
      <div
        class="wrapper"
        style="max-height: ${height}; height: ${height}; justify-content: ${justifyContent}; align-items: ${alignItems};"
      >
        <slot></slot>
      </div>
    `;
  }

  // protected createRenderRoot(): HTMLElement | DocumentFragment {
  //   return this;
  // }
}
