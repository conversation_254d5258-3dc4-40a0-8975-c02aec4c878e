var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { customElement, property } from "lit/decorators.js";
let WexRowItem = class WexRowItem extends LitElement {
    updated(changedProps) {
        if (changedProps.has("border")) {
            this.style.border = this.border || "";
        }
    }
    render() {
        const height = this.height ?? "auto";
        const justifyContent = this.justifyContent ?? "space-evenly";
        const alignItems = this.alignItems ?? "stretch";
        return html `
      <div
        class="wrapper"
        style="max-height: ${height}; height: ${height}; justify-content: ${justifyContent}; align-items: ${alignItems};"
      >
        <slot></slot>
      </div>
    `;
    }
};
WexRowItem.styles = css `
    :host {
      display: flex;
      flex: 1;
      width: 100%;
      min-height: 0;
    }

    .wrapper {
      display: flex;
      flex-direction: row;
      overflow-x: auto;
      gap: 1rem;
      flex: 1;
      min-height: 40px;
      width: 100%;
    }
    .wrapper > *:not(:last-child) {
      margin-right: 0.5rem;
    }
  `;
__decorate([
    property({ type: String })
], WexRowItem.prototype, "border", void 0);
__decorate([
    property({ type: String })
], WexRowItem.prototype, "justifyContent", void 0);
__decorate([
    property({ type: String })
], WexRowItem.prototype, "alignItems", void 0);
__decorate([
    property({ type: String })
], WexRowItem.prototype, "height", void 0);
WexRowItem = __decorate([
    customElement("wex-row-item")
], WexRowItem);
export { WexRowItem };
//# sourceMappingURL=row-item.js.map