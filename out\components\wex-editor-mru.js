var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html } from "lit";
import { customElement } from "lit/decorators.js";
import { storeInstance as Store } from "store/index.js";
import { Router } from "@vaadin/router";
import * as util from "lib/util.ts";
import * as wexlib from "lib/wexlib.js";
let WexEditorMru = class WexEditorMru extends LitElement {
    connectedCallback() {
        super.connectedCallback();
        this.subscription = Store.subscribe((state) => {
            /*WexEditLayout*/
            this.stateChange(state);
        });
        this.state = Store.state;
        this.string = this.state[this.state.langCode];
        this.maps = [];
        this._marshellRows();
    }
    disconnectedCallback() {
        Store.unsubscribe(this.subscription);
    }
    firstUpdated() {
        this.contextMenu = this.shadowRoot.querySelector("#contextmenu");
    }
    stateChange(state) {
        this.state = state;
        this.string = this.state[this.state.langCode];
        this._marshellRows();
    }
    _marshellRows() {
        this.rows = this.state.editor_mru
            ? structuredClone(this.state.editor_mru).reverse()
            : [];
        if (this.state.editor_mru_selected_file) {
            this.rows.map((row) => (row.isSelected =
                row.resLblId == this.state.editor_mru_selected_file.resLblId));
        }
    }
    render() {
        return html `
      <style>
        .lockedbyother{color:red;}
        .lockedbyselfhere{color:green;}
        .lockedbyselfelsewhere{color:orange;}

        .rowselected{
            --sapList_Background: var(--row-selected-background);
        }

        #notasks{
            font-style: oblique;
            opacity: 0.5;
            text-align:center;
            margin-top:50px;
        }
        .datarow{cursor:pointer;}
        .ditamap{font-weight:bold;}

        #filepackageheader{
            height: ${this.state.global_screen_specs
            .edit_filepackageHeaderHeight}px !important;
            color:#000;
            text-align:center;
            display:flex;
            flex-direction: row;
            justify-content: space-between;
            flex-wrap: nowrap
        }
        #filepackageheader>div{flex-shrink: 1;}


        #mrutablecontainer{
            height:${this.state.global_screen_specs.layoutHeight -
            this.state.global_screen_specs
                .edit_filepackageHeaderHeight}px; !important;
            overflow: auto;
        }

        #filerole{
            display: inline-block;;
            margin-left:-12px;
            cursor: pointer;
            }

        #filerole ui5-radio-button{

            }

        .ui5button{margin-top:7px;}
        #nextprev{
            line-height:30px;
            padding-top:10px;
            color:#32363A;
            margin-right:-6px;
        }

        #nexticon{cursor:pointer;}
        #previcon{cursor:pointer;}
        #nexticon[disabled]{cursor:forbidden;color:#ccc;}
        #previcon[disabled]{cursor:forbidden;color:#ccc;}

        .reviewedicon{
            --iron-icon-width:16px;
        }
        .hide{visibility: hidden;}
        .sortbuttoncontainer{
            display:flex;
            flex-direction:column;
        }
        .sortbuttoncontainer iron-icon{
            margin-left: .25rem;
            --iron-icon-width:.9rem
        }
        iron-icon.asc{margin-bottom:-.6rem}
        iron-icon.desc{margin-top:-.6rem}
        iron-icon.muted{
            color: #ccc;
        }

        .colhead{
            line-height: 1.4rem;
            display:flex;
            flex-direction: row;
            align-items:center;

        }
        .colhead>span:first-child{
            font-size:.8rem;
        }
        .iconcontatiner{
            height:1.4rem;
            overflow:hidden;
            }
        .sidecomponent{
            width: ${(this.state.global_screen_specs.edit_leftSideWidthPx -
            this.state.global_screen_specs.edit_side_tabs_width) *
            0.9}px;
            min-width: ${(this.state.global_screen_specs.viewportWidth * 0.25 -
            this.state.global_screen_specs.edit_side_tabs_width) *
            0.9}px;
        }
        #sidecomponentparent{
            display:flex;
            flex-direction:column;
            justify-content: space-between;
            align-items:center;
            background-color:#F2F2F2;
        }
        ui5-table-cell>div{
            white-space: nowrap;
        }
        .disabled{
            color:#ccc;
            cursor:forbidden;
        }
        *[disabled]{
            color:#ccc;
            cursor:forbidden;
        }
        #nextprev ui5-button{
            height:1.5rem;
            width:2rem;
        }
        ui5-button>iron-icon{
            width:15px;
        }

        .fileicon, .previewicon, .editicon, .reviewedicon{width: 1rem;}
        .fileicon:hover, .previewicon:hover, .editicon:hover, reviewedicon:hover{color:#000;}
        #mruheader{
            background-color: #fff;
            height: 50px;
            line-height: 50px;
            color: var(--_ui5_tc_headeritem_text_selected_color);
            text-align:center;
            }
      </style>
      <div id="mruheader">${this.string["_mostrecentlyused"]}</div>
      <div id="mrutablecontainer">
        <vaadin-context-menu
          id="contextmenu"
          selector=".has-menu"
          @item-selected="${this._contextmenuclicked.bind(this)}"
        >
          <ui5-table
            id="filepackagetable"
            no-data-text="${this.string["_nofileseditormru"]}"
            ?show-no-data="${this.rows.length == 0}"
            sticky-column-header
          >
            <ui5-table-column
              class="foo"
              slot="columns"
              popin-text="${this.string["_name"]}"
            >
              <span class="colhead">
                <span class="">${this.string["_name"]}</span>
              </span>
            </ui5-table-column>

            ${this.rows.map((item) => html ` <ui5-table-row
                 data-reslblid=${item.resLblId}
                  class="datarow has-menu ${item.mapClass} ${item.isSelected
            ? "rowselected"
            : ""}"
                  .item="${item}"
                  @dblclick="${this._defaultAction.bind(this)}"
                  @click="${this._handleRowClicked.bind(this)}"
                  @contextmenu="${this._handleRowRightClicked.bind(this)}"
                >
                  ${this._rowTemplate(item)}
                </ui5-table-row>`)}
          </ui5-table>
        </vaadin-context-menu>
      </div>
    `;
    }
    _rowTemplate(itm) {
        return html `<ui5-table-cell align="left"
      ><div>
        <iron-icon
          icon="vaadin:file-text-o"
          id="properties"
          class="fileicon ${itm.iconClass}"
          title="${this.string["_properties"]}"
        ></iron-icon>
        <iron-icon
          .item="${itm}"
          icon="vaadin:eye"
          title="${this.string["_preview"]}"
          class="${itm.selected} previewicon"
          id="preview"
        >
        </iron-icon>
        <iron-icon
          .item="${itm}"
          icon="vaadin:edit"
          title="${this.string["_edit"]}"
          class="${itm.selected} editicon ${util.isFileEditable(itm)
            ? ""
            : "hide"}"
          id="edit"
        >
        </iron-icon>
        &nbsp;${itm.name}
      </div>
    </ui5-table-cell>`;
    }
    _handleRowRightClicked(e) {
        //modify conditional context menu items
        var effectiveCap = this.state.wex_user.globalCapability;
        if (e.currentTarget.item) {
            var menuItems = util.buildFileMenu(e.currentTarget.item, this.state.menus.file_menu, "mru", effectiveCap);
            menuItems.map((item) => (item.text = this.string[item.label]));
            this.contextMenu.items = menuItems;
            if (this.state.editor_mru_selected_file) {
                if (this.state.editor_mru_selected_file.resLblId ==
                    e.currentTarget.item.resLblId) {
                    return;
                }
            }
            Store.dispatch("setState", {
                property: "editor_mru_selected_file",
                value: e.currentTarget.item,
            });
        }
    }
    _contextmenuclicked(e) {
        switch (e.detail.value.name) {
            case "properties":
                Store.dispatch("filePropertiesRequest", this.state.editor_mru_selected_file);
                break;
            case "delete":
                Store.dispatch("deleteFile", this.state.editor_mru_selected_file);
                break;
            case "edit":
                this._editAction();
                break;
            case "preview":
                Router.go("/wex/" +
                    this.state.langCode +
                    "/preview?resLblId=" +
                    this.state.editor_mru_selected_file.resLblId +
                    "&aod=" +
                    this.state.editor_mru_selected_file.verCreateDate);
                break;
            case "previewnewwindow":
                wexlib.previewNewWindow({
                    resLblId: this.state.editor_mru_selected_file.resLblId,
                    aod: "now",
                    mimeType: this.state.editor_mru_selected_file.mimeType,
                    langCode: this.state.langCode,
                });
                break;
            case "open_ditamap":
                Store.dispatch("handleFileAction", {
                    action: "open_ditamap",
                    file: this.state.editor_mru_selected_file,
                });
                break;
            case "edit_ditamap":
                // Store.dispatch("setState", {
                //   property: "editor_side_active_tab",
                //   value: "ditamaps",
                // });
                // var tmp = JSON.parse(
                //   JSON.stringify(this.state.editor_mru_selected_file)
                // );
                // tmp.isDitabase = true;
                // Store.dispatch("checkOutDitabase", tmp);
                this._editAction();
        }
    }
    _defaultAction(e) {
        Store.dispatch("setState", {
            property: "editor_mru_selected_file",
            value: e.currentTarget.item,
        });
        this._editAction();
    }
    _handleRowClicked(e) {
        const path = e.composedPath();
        var iconClicked = path[0].tagName == "IRON-ICON" && !path[0].disabled;
        if (iconClicked) {
            var icon = path[0];
            switch (icon.id) {
                case "preview":
                    Store.dispatch("setState", {
                        property: "editor_mru_selected_file",
                        value: e.currentTarget.item,
                    });
                    Router.go("/wex/" +
                        this.state.langCode +
                        "/preview?resLblId=" +
                        this.state.editor_mru_selected_file.resLblId +
                        "&aod=" +
                        this.state.editor_mru_selected_file.verCreateDate +
                        "&previewStyle=html");
                    break;
                case "edit":
                    Store.dispatch("setState", {
                        property: "editor_mru_selected_file",
                        value: e.currentTarget.item,
                    });
                    this._editAction();
                    break;
                case "properties":
                    Store.dispatch("setState", {
                        property: "editor_mru_selected_file",
                        value: e.currentTarget.item,
                    });
                    if (e.currentTarget.item) {
                        if (this.state.editor_mru_selected_file) {
                            if (this.state.editor_mru_selected_file.resLblId !=
                                e.currentTarget.item.resLblId) {
                                Store.dispatch("setState", {
                                    property: "editor_mru_selected_file",
                                    value: e.currentTarget.item,
                                });
                            }
                        }
                        else {
                            Store.dispatch("setState", {
                                property: "editor_mru_selected_file",
                                value: e.currentTarget.item,
                            });
                        }
                        Store.dispatch("filePropertiesRequest", this.state.editor_mru_selected_file);
                        break;
                    }
            }
        }
        else {
            if (e.currentTarget.item) {
                if (this.state.editor_mru_selected_file) {
                    if (this.state.editor_mru_selected_file.resLblId ==
                        e.currentTarget.item.resLblId) {
                        Store.dispatch("setState", {
                            property: "editor_mru_selected_file",
                            value: null,
                        });
                        return;
                    }
                }
                Store.dispatch("setState", {
                    property: "editor_mru_selected_file",
                    value: e.currentTarget.item,
                });
            }
        }
    }
    _editAction() {
        Store.dispatch("checkEditFile", this.state.editor_mru_selected_file);
    }
};
WexEditorMru = __decorate([
    customElement("wex-editor-mru")
], WexEditorMru);
//# sourceMappingURL=wex-editor-mru.js.map