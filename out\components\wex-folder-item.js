var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import "@ui5/webcomponents/dist/Icon.js";
import "@vaadin/vaadin-icons/vaadin-icons.js";
import { customElement, property } from "lit/decorators";
let WexFolderItem = class WexFolderItem extends LitElement {
    constructor() {
        super();
        this.childrenClass = "nokids";
        this.selectedClass = " ";
        this.selectedNodeId = -1; // ?
        this.isDraggable = false;
    }
    connectedCallback() {
        super.connectedCallback();
        this.addEventListener("dragstart", this._onDragStart.bind(this));
        this.addEventListener("dragenter", this._onDragEnter.bind(this));
        this.addEventListener("dragover", this._onDragOver.bind(this));
        this.addEventListener("dragleave", this._onDragLeave.bind(this));
        this.addEventListener("drop", this._onDrop.bind(this));
    }
    updated(_changedProperties) {
        if (_changedProperties.has("item")) {
            this.updateItem();
        }
    }
    updateItem() {
        if (!this.item)
            return;
        if (this.item.hasOwnProperty("children")) {
            this.childrenClass = "haskids"; // ?
        }
        if (this.item.selected) {
            this.selectedClass = " selected ";
        }
        if (this.item.selected) {
            this.dispatchEvent(new CustomEvent("selectedrendered", {
                bubbles: true,
                composed: true,
                detail: this,
            }));
        }
    }
    folderItemDetailTemplate() {
        if (this.contextmenu) {
            return html ` <vaadin-context-menu
        id="contextmenu"
        .items="${this.contextmenu}"
        @item-selected="${this._contextmenuclicked.bind(this)}"
      >
        <div
          class="folder-item-title
            ${this.selectedClass}"
          id="div${this.item.id}"
          @click="${this.selectNode.bind(this)}"
          @contextmenu="${this.selectNode.bind(this)}"
          draggable="${this.isDraggable}"
        >
          ${this.iconTemplate()} ${this.item.name}
        </div>
      </vaadin-context-menu>`;
        }
        else {
            return html `<div
        class="folder-item-title  ${this.selectedClass}"
        id="div${this.item.id}"
        @click="${this.selectNode.bind(this)}"
        @contextmenu="${this._preventDefault.bind(this)}"
        draggable="${this.isDraggable}"
      >
        ${this.iconTemplate()} ${this.item.name}
      </div>`;
        }
    }
    iconTemplate() {
        if (this.item.hasOwnProperty("children")) {
            if (this.item.open) {
                return html `<iron-icon
          icon="vaadin:angle-down"
          @click="${this.toggleNode.bind(this)}"
        ></iron-icon>`;
            }
            else {
                return html `<iron-icon
          icon="vaadin:angle-right"
          @click="${this.toggleNode.bind(this)}"
        ></iron-icon>`;
            }
        }
        else {
            return html `<iron-icon
        class="spacer"
        icon="vaadin:angle-right"
      ></iron-icon>`;
        }
    }
    getChilditemsTemplate() {
        if (this.item.open && this.item.hasOwnProperty("children")) {
            return html ` <ul class="folder-item-list">
        ${this.item.children.map((item) => html `
            <wex-folder-item
              class="folder-item"
              .item="${item}"
              .isDraggable=${this.isDraggable}
              .contextmenu="${this.contextmenu}"
              .selectedNodeId="${this.selectedNodeId}"
            ></wex-folder-item>
          `)}
      </ul>`;
        }
        else {
            return html ``;
        }
    }
    _preventDefault(e) {
        e.preventDefault();
    }
    _onDragStart(e) {
        e.stopPropagation();
        e.dataTransfer.setData("text/plain", this.item.id);
    }
    _onDragEnter(e) {
        e.stopPropagation();
    }
    _onDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    _onDragLeave(e) {
        e.stopPropagation();
    }
    _onDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        this.dispatchEvent(new CustomEvent("folderdropfolder", {
            bubbles: true,
            composed: true,
            detail: {
                collectionId: Number(e.dataTransfer.getData("text/plain")),
                parentId: Number(this.item.id),
            },
        }));
    }
    _contextmenuclicked(e) {
        this.dispatchEvent(new CustomEvent("foldercontextmenuclick", {
            bubbles: false,
            composed: true,
            detail: e.detail.value.name,
        }));
    }
    selectNode(e) {
        if (!this.item.selected) {
            setTimeout(function () {
                this.dispatchEvent(new CustomEvent("folderselected", {
                    bubbles: true,
                    composed: true,
                    detail: this.item,
                }));
            }.bind(this), 200);
        }
    }
    toggleNode(e) {
        e.preventDefault();
        e.stopPropagation();
        this.item.open = !this.item.open;
        this.dispatchEvent(new CustomEvent("folderopenstatus", {
            bubbles: true,
            composed: true,
            detail: this.item,
        }));
    }
    render() {
        if (this.item.hasOwnProperty("children") && this.item.open) {
            return html ` ${this.folderItemDetailTemplate()}
        <li class="folder-item ${this.childrenClass}">
          ${this.getChilditemsTemplate()}
        </li>`;
        }
        else {
            return html `${this.folderItemDetailTemplate()}`;
        }
    }
    static get styles() {
        return css `
      .over {
        font-weight: bold;
      }
      .folder-item-title {
        padding: var(--spacing-sm) var(--spacing-xs);
        display: flex;
        align-items: center;
        cursor: pointer;
        white-space: nowrap;
      }
      .folder-item-list {
        margin-block-start: 0em;
        margin-block-end: 0em;
        margin-inline-start: 0px;
        margin-inline-end: 0px;
        padding-inline-start: 0px;
      }
      .over {
        background-color: lightgrey;
      }
      .selected {
        background-color: var(--row-selected-background);
      }
      ul.open {
        display: block;
      }
      .folder-item-list {
        list-style: none;
      }
      .haskids {
        margin-left: 0;
      }
      .nokids {
        padding-left: 20px;
      }
      .folder-item {
        display: block;
        margin-left: 5px;
        line-height: 0px;
      }
      iron-icon {
        margin-right: var(--spacing-xs);
        --iron-icon-width: 16px;
      }
      .spacer {
        color: rgba(255, 255, 255, 0);
      }
    `;
    }
};
__decorate([
    property({ type: Object })
], WexFolderItem.prototype, "item", void 0);
__decorate([
    property({ type: String })
], WexFolderItem.prototype, "childrenClass", void 0);
__decorate([
    property({ type: String })
], WexFolderItem.prototype, "selectedClass", void 0);
WexFolderItem = __decorate([
    customElement("wex-folder-item")
], WexFolderItem);
//# sourceMappingURL=wex-folder-item.js.map