export const PHONES_1_BOOKMAP = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE bookmap PUBLIC "-//OASIS//DTD DITA BookMap//EN" "/SysSchema/dita/dtd/bookmap/dtd/bookmap.dtd">
<bookmap xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/"
	id="xd_1d4ce9524273c6b7--1a62fcbf-156d8df6bb1--7ff0" xml:lang="en-US"
	class="- map/map bookmap/bookmap ">
	<booktitle class="- topic/title bookmap/booktitle ">
		<mainbooktitle class="- topic/ph bookmap/mainbooktitle ">
			<?xm-replace_text Main Book Title?>Phone 1 User Guide</mainbooktitle>
	</booktitle>

	<bookmeta class="- map/topicmeta bookmap/bookmeta ">
		<bookid class="- topic/data bookmap/bookid ">
			<bookpartno class="- topic/data bookmap/bookpartno ">
				<?xm-replace_text Book Part Number?>
			</bookpartno>
			<isbn class="- topic/data bookmap/isbn ">
				<?xm-replace_text ISBN Number?>
			</isbn>
		</bookid>
		<bookrights class="- topic/data bookmap/bookrights ">
			<copyrfirst class="- topic/data bookmap/copyrfirst ">
				<year class="- topic/ph bookmap/year ">
					<?xm-replace_text First Year?>
				</year>
			</copyrfirst>
			<copyrlast class="- topic/data bookmap/copyrlast ">
				<year class="- topic/ph bookmap/year ">
					<?xm-replace_text Last Year?>
				</year>
			</copyrlast>
			<bookowner class="- topic/data bookmap/bookowner ">
				<person class="- topic/data bookmap/person ">
					<?xm-replace_text Copyright owner?>
				</person>
			</bookowner>
		</bookrights>
	</bookmeta>
	<frontmatter class="- map/topicref bookmap/frontmatter ">
		<booklists class="- map/topicref bookmap/booklists ">
			<toc class="- map/topicref bookmap/toc " />
		</booklists>
		<keydef keys="productSpec" href="/Content/phone_1_spec_xi1622_1_1.xml"
			class="+ map/topicref mapgroup-d/keydef " />
		<keydef href="/Content/phone1_small_xi1596_1_1.jpg" format="png" keys="phoneImage"
			class="+ map/topicref mapgroup-d/keydef " />
	</frontmatter>
	<part href="/Content/submap_phones_xi1609_1_1.ditamap" format="ditamap"
		class="- map/topicref bookmap/part " />
	<backmatter class="- map/topicref bookmap/backmatter ">
		<booklists class="- map/topicref bookmap/booklists ">
			<indexlist class="- map/topicref bookmap/indexlist " />
		</booklists>
	</backmatter>
</bookmap>`;

export const SUBMAP_PHONES = `<map xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/"
    id="map_1C03AB61C63B4FA6A95F36368EFD4772" title="Submap Phones" xml:lang="en-US"
    class="- map/map ">
    <topicref format="dita" href="/Content/begin_xi1612_1_1.xml" class="- map/topicref ">
        <topicref format="dita" href="/Content/getting_started_xi1615_1_1.xml"
            class="- map/topicref " />
        <topicref format="dita" href="/Content/insert_SIMcard_xi1616_1_1.xml"
            class="- map/topicref " />
        <topicref format="dita" href="/Content/charge_battery_xi1613_1_1.xml"
            class="- map/topicref " />
    </topicref>
    <topicref format="dita" href="/Content/basics_xi1610_1_1.xml" class="- map/topicref ">
        <topicref format="dita" href="/Content/overview_handset_xi1621_1_1.xml"
            class="- map/topicref " />
        <topicref format="dita" href="/Content/make_and_receive_calls_xi1619_1_1.xml"
            class="- map/topicref ">
            <topicref format="dita" href="/Content/make_calls_xi1620_1_1.xml"
                class="- map/topicref " />
            <topicref format="dita" href="/Content/receive_call_xi1625_1_1.xml"
                class="- map/topicref " />
            <topicref format="dita" href="/Content/reject_call_xi1626_1_1.xml"
                class="- map/topicref " />
        </topicref>
        <topicref format="dita" href="/Content/retrieve_voicemail_xi1631_1_1.xml"
            class="- map/topicref " />
        <topicref href="/Content/taking_photos_xi1637_1_1.xml" class="- map/topicref ">
            <topicref format="dita" href="/Content/take_photos_xi1636_1_1.xml"
                class="- map/topicref " />
            <topicref href="/Content/take_front-facing_photo_xi1635_1_1.xml" class="- map/topicref " />
        </topicref>
        <topicref format="dita" href="/Content/listen%20_music_xi1618_1_1.xml"
            class="- map/topicref " />
        <topicref href="/Content/exchange_data_xi1614_1_1.xml" class="- map/topicref " />
        <topicref format="dita" href="/Content/reset_phone_xi1630_1_1.xml" class="- map/topicref " />
    </topicref>
    <topicref format="dita" href="/Content/troubleshooting_xi1638_1_1.xml" class="- map/topicref ">
        <topicref href="/Content/screen_frozen_xi1632_1_1.xml" class="- map/topicref " />
        <topicref format="dita" href="/Content/battery_drain_xi1611_1_1.xml" class="- map/topicref " />
    </topicref>
    <topicref audience="technician" format="dita" href="/Content/service_section_xi1633_1_1.xml"
        class="- map/topicref ">
        <topicref format="dita" href="/Content/replace_battery_xi1627_1_1.xml"
            class="- map/topicref " />
        <topicref href="/Content/insert_battery_xi1617_1_1.xml" class="- map/topicref " />
    </topicref>
    <topicref format="dita" href="/Content/specifications_xi1634_1_1.xml" class="- map/topicref ">
        <topicref keyref="productSpec" class="- map/topicref " />
    </topicref>
    <topicref href="/Content/QuickStart_MP4_Handset_xi1641_1_1.xml" format="dita" platform="html"
        class="- map/topicref " />
    <topichead navtitle="Glossary" toc="no" class="+ map/topicref mapgroup-d/topichead ">
        <glossref format="dita" href="/Content/dual_band_xi1582_1_1.xml" keys="quad_band"
            linking="normal" print="yes" search="yes" toc="no" type="glossentry"
            class="+ map/topicref glossref-d/glossref " />
        <glossref format="dita" href="/Content/earphones_xi1584_1_1.xml" keys="earphones"
            linking="normal" print="yes" search="no" toc="no" type="glossentry"
            class="+ map/topicref glossref-d/glossref " />
        <topicref format="dita" href="/Content/nfc_antenna_xi1588_1_1.xml" keys="nfc"
            class="- map/topicref " />
    </topichead>
    <mapref format="ditamap" href="/Content/subject_scheme_Atts_sample_xi1608_1_1.ditamap"
        class="+ map/topicref mapgroup-d/mapref " />
    <reltable toc="yes" class="- map/reltable ">
        <relheader class="- map/relheader ">
            <relcolspec linking="sourceonly" class="- map/relcolspec " />
            <relcolspec linking="targetonly" class="- map/relcolspec " />
        </relheader>
        <relrow class="- map/relrow ">
            <relcell collection-type="family" class="- map/relcell ">
                <topicref format="dita" href="/Content/getting_started_xi1615_1_1.xml"
                    class="- map/topicref " />
            </relcell>
            <relcell collection-type="family" class="- map/relcell ">
                <topicref format="dita" href="/Content/basics_xi1610_1_1.xml"
                    class="- map/topicref " />
            </relcell>
        </relrow>
        <relrow class="- map/relrow ">
            <relcell class="- map/relcell ">
                <topicref format="dita" href="/Content/troubleshooting_xi1638_1_1.xml"
                    class="- map/topicref " />
            </relcell>
            <relcell class="- map/relcell ">
                <topicref format="dita" href="/Content/specifications_xi1634_1_1.xml"
                    class="- map/topicref " />
            </relcell>
        </relrow>
        <relrow class="- map/relrow ">
            <relcell class="- map/relcell ">
                <topicref format="dita" href="/Content/service_section_xi1633_1_1.xml"
                    class="- map/topicref " />
            </relcell>
            <relcell collection-type="family" class="- map/relcell ">
                <topicref format="html" href="http://www.bluestream.com/" scope="external"
                    class="- map/topicref ">
                    <topicmeta class="- map/topicmeta ">
                        <navtitle class="- topic/navtitle ">Another Resource</navtitle>
                    </topicmeta>
                </topicref>
            </relcell>
        </relrow>
        <relrow class="- map/relrow " />
        <relrow class="- map/relrow " />
    </reltable>
</map>`;

export const SUBJECT_SCHEME = `<subjectScheme xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/" xml:lang="en-US"
    class="- map/map subjectScheme/subjectScheme ">
    <!-- Define the values for the audience attribute -->
    <subjectdef keys="users" class="- map/topicref subjectScheme/subjectdef ">
        <subjectdef keys="general" class="- map/topicref subjectScheme/subjectdef " />
        <subjectdef keys="support" class="- map/topicref subjectScheme/subjectdef " />
        <subjectdef keys="technician" class="- map/topicref subjectScheme/subjectdef " />
    </subjectdef>
    <!-- Define the values for the platform attribute -->
    <subjectdef keys="output" class="- map/topicref subjectScheme/subjectdef ">
        <subjectdef keys="pdf" class="- map/topicref subjectScheme/subjectdef " />
        <subjectdef keys="html" class="- map/topicref subjectScheme/subjectdef " />
    </subjectdef>
    <!--  Bind the attributes to the values  -->
    <enumerationdef class="- map/topicref subjectScheme/enumerationdef ">
        <attributedef name="audience" class="- topic/data subjectScheme/attributedef " />
        <subjectdef keyref="users" class="- map/topicref subjectScheme/subjectdef " />
    </enumerationdef>
    <enumerationdef class="- map/topicref subjectScheme/enumerationdef ">
        <attributedef name="platform" class="- topic/data subjectScheme/attributedef " />
        <subjectdef keyref="output" class="- map/topicref subjectScheme/subjectdef " />
    </enumerationdef>
</subjectScheme>`;
