import { LitElement, html, css } from "lit";
import { customElement, property } from "lit/decorators.js";
// @ts-ignore
import * as wexlib from "lib/wexlib";

import "../../components/header/publish";
import "../../components/publish/publish-outputs-pane";

@customElement("wex-page-publish-adhoc-jobs")
export class WexPagePublishAdhocJobs extends LitElement {
  @property({ type: Array }) outputJobs: any[] = [];

  static get styles() {
    return css`
      wex-publish-outputs-pane {
        width: 50%;
      }
    `;
  }

  connectedCallback(): void {
    super.connectedCallback();
    this._refreshOutputJobs();
  }

  _refreshOutputJobs = async () => {
    this.outputJobs = await wexlib.getPubDefOutputJobs();
  };

  _renderMainPane() {
    const template = html` <wex-publish-outputs-pane
      .outputJobs=${this.outputJobs}
      class="right"
    ></wex-publish-outputs-pane>`;
    return template;
    return html`
      <div>
        <pre>${JSON.stringify(this.outputJobs)}</pre>
      </div>
    `;
  }

  render() {
    const template = html`
      <div id="publish-adhoc-jobs-container">
        <wex-publish-header></wex-publish-header>
        <wex-main-pane> ${this._renderMainPane()} </wex-main-pane>
      </div>
    `;
    return template;
  }
}
