import { LitElement, html, css } from "lit";
import { customElement, property } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store";
// @ts-ignore
import * as wexlib from "lib/wexlib";

import "../base/row-item";
import "../base/col-item";
// import "./wex-table";
import "../wex-table";

import "@ui5/webcomponents/dist/Button.js";

@customElement("wex-publish-outputs-pane")
export class WexPublishOutputsPane extends LitElement {
  @property({ type: Array }) outputJobs: any[] = [];

  //   private state: any = {};
  private string: any = [];
  private outputJobsColumns: any = [];
  //   private outputJobs: any = [];

  static get styles() {
    return css`
      :host {
        flex: 1;
      }
    `;
  }

  connectedCallback() {
    super.connectedCallback();
    const state = storeInstance.state;
    // this.state = state;
    this.string = state[state.langCode];
    // this._subscription = Store.subscribe((state) => {
    //   this.stateChange(state);
    // });

    // this._defJobsColumns = Store.state.columns.publishJobsDefJobs;
    this.outputJobsColumns = state.columns.publishJobsOutputJobs;
    // this._init();
  }

  _openPopover(id: string) {
    const outputJob = this.outputJobs.find((job: any) => job.selected);
    if (!outputJob) return;
    storeInstance.dispatch("setState", {
      property: "output_job_info_popover_open",
      value: {
        outputJob,
        opener: this.shadowRoot?.querySelector("#" + id),
      },
    });
  }

  //   _handleSort(type: string, e: CustomEvent) {
  // const data = e.detail;
  // if (!type || !data) return;
  // const { field, value } = data;
  // if (type == "defjobs") {
  //   // shallow clone sufficient
  //   const columns = [...this._defJobsColumns];
  //   columns.forEach((x) =>
  //     x.field == field ? (x.sort = value) : (x.sort = null)
  //   );
  //   // shallow clone sufficient
  //   const rows = [...this._defJobs];
  //   rows.sort((a, b) => {
  //     const c = `${a[field]}`.toUpperCase();
  //     const d = `${b[field]}`.toUpperCase();
  //     if (value == "asc") {
  //       return c.localeCompare(d, undefined, { sensitivity: "base" });
  //     } else if (value == "desc") {
  //       return d.localeCompare(c, undefined, { sensitivity: "base" });
  //     }
  //   });
  //   this._defJobs = rows;
  //   this._defJobsColumns = columns;
  // } else if (type == "outputjobs") {
  //   //
  // }
  //   }

  _renderOutputJobActionBar() {
    const template = html`
      <wex-row-item alignItems="center" justifyContent="flex-start">
      </wex-row-item>
      <wex-row-item justifyContent="flex-end" alignItems="center">
        <ui5-button
          id="OutputJobDetails"
          @click="${this._openPopover.bind(this, "OutputJobDetails")}"
          >Details</ui5-button
        >
        <ui5-button id="OutputJobDownload" @click="${this._handleDownloadClick}"
          >Download</ui5-button
        >
      </wex-row-item>
    `;
    return template;
  }

  async _handleClickOutputJobRow(e: CustomEvent) {
    try {
      const data = e.detail;
      if (!data) return;

      if (e.detail.icon === "icons:file-download") {
        this._handleDownloadClick();
      }

      const obj = { ...data.value };

      const rows = [...this.outputJobs];
      rows.forEach(
        (row) => (row.selected = row.outputJobInfoId == obj.outputJobInfoId)
      );
      this.outputJobs = rows;
    } catch {}
  }

  async _handleDownloadClick() {
    const job = this.outputJobs.find((job: any) => job.selected);

    const res = await wexlib.getPublishOutput(job.lblSeqIdOutput);
    const downloadLink = document.createElement("a");
    downloadLink.href = URL.createObjectURL(res);
    downloadLink.download = job.outputFileName;
    document.body.appendChild(downloadLink);
    downloadLink.click();

    document.body.removeChild(downloadLink);
    URL.revokeObjectURL(downloadLink.href);
  }

  render() {
    const template = html`
      <wex-col-item class="right">
        <h3>${this.string["_output_jobs"]}</h3>
        <wex-row-item justifyContent="space-between">
          ${this._renderOutputJobActionBar()}
        </wex-row-item>

        <wex-table
          defaultHeight="70vh"
          .columns="${this.outputJobsColumns}"
          .rows="${this.outputJobs}"
          .enableSelect=${false}
          @click=${this._handleClickOutputJobRow.bind(this)}
          .enableMenu=${false}
        ></wex-table>
      </wex-col-item>
    `;
    // @sort=${this._handleSort.bind(this, "outputjobs")}
    return template;
  }
}
