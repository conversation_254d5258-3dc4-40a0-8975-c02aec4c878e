var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { LitElement, html, css } from "lit";
import { property, state, customElement } from "lit/decorators.js";
// @ts-ignore
import { storeInstance } from "store/index.js";
// import * as wexlib from "lib/wexlib.js";
import "../base/col-item";
import "../base/row-item";
import "../base/fixed-item";
import "../base/icon";
import "../base/multi-combobox";
let WexPublishDefinitionsFilterBar = class WexPublishDefinitionsFilterBar extends LitElement {
    constructor() {
        super(...arguments);
        this.pubDefs = null;
        this.projects = null;
        this.categories = null;
        this.string = null;
        this.filterOptions = null;
        // private cachedFilterOptions: any[] | null = null;
        this.activeFilters = {};
        this.subscription = null;
        this._handleCategorySelect = (e) => {
            console.log("NYI: category select needs setup and testing", e);
        };
    }
    static get styles() {
        return css ``;
    }
    connectedCallback() {
        super.connectedCallback();
        const state = storeInstance.state;
        // this._state = storeInstance.state;
        this.string = state[state.langCode];
        this.subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        this._init();
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        storeInstance.unsubscribe(this.subscription);
    }
    stateChange(state) {
        // this._state = state;
        this.string = state[state.langCode];
    }
    async _init() {
        try {
            this.projects = await storeInstance.waitFor("_state.pages.publish.projects");
            this.categories = await storeInstance.waitFor("_state.pages.publish.categories");
            this._generateFilterOptions();
            // console.log("this.categories", this.categories);
            // console.log("this.projects", this.projects);
        }
        catch (error) {
            console.error("Error during initialization:", error);
        }
    }
    _generateFilterOptions() {
        const filterOptions = {
            category: {
                placeholder: "Category",
                mcbItems: this.categories?.map((category) => category.name),
            },
            projectName: {
                placeholder: "Project Name",
                // mcbItems: ["rgewrger", "ergwergre"],
                mcbItems: this.projects?.map((project) => project.name),
            },
        };
        this.filterOptions = filterOptions;
    }
    _createPubDef() {
        storeInstance.dispatch("setState", {
            property: "publish_defs_create_dialog_open",
            value: {
                mode: "Create",
                projects: this.projects,
            },
        });
    }
    _handleFilterChange(e) {
        const { selectedItems, comboBoxGroup } = e.detail;
        let activeFilters = {};
        // this works for separate comboboxes
        if (comboBoxGroup) {
            activeFilters = { ...this.activeFilters };
            activeFilters[comboBoxGroup] = selectedItems.map((item) => item);
        }
        else {
            // console.log("NOOOOO COMBOBOX");
            // // this works for grouped comboboxes
            // // activeFilters = {};
            // activeFilters = selectedItems.reduce((acc, item) => {
            //   const group = item.dataset.group;
            //   const text = item.text;
            //   if (!acc[group]) acc[group] = [];
            //   acc[group].push(text);
            //   return acc;
            // }, {});
        }
        this.activeFilters = activeFilters;
        console.log("CHECK THIS", activeFilters);
        this.dispatchEvent(new CustomEvent("set-active-filters", {
            detail: { activeFilters },
            bubbles: true,
            composed: true,
        }));
    }
    render() {
        return html `
      <wex-row-item height="42px">
        <wex-fixed-item>
          <wex-icon
            icon="add"
            title="${this.string["_create_pubdef"]}"
            @click="${this._createPubDef.bind(this)}"
          ></wex-icon>
        </wex-fixed-item>
        <wex-col-item>
          <wex-multi-combobox
            .mcbData="${this.filterOptions}"
            @selection-change="${this._handleCategorySelect}"
            .dataGroup=${"category"}
            .initialSelectedItems=${this.activeFilters}
          >
          </wex-multi-combobox>
        </wex-col-item>
        <wex-col-item>
          <wex-multi-combobox
            .mcbData="${this.filterOptions}"
            @selection-change="${this._handleFilterChange}"
            dataGroup="projectName"
            .initialSelectedItems=${this.activeFilters}
          >
          </wex-multi-combobox>
        </wex-col-item>
      </wex-row-item>
    `;
    }
};
__decorate([
    property({ type: Array })
], WexPublishDefinitionsFilterBar.prototype, "pubDefs", void 0);
__decorate([
    property({ type: Array })
], WexPublishDefinitionsFilterBar.prototype, "projects", void 0);
__decorate([
    property({ type: Array })
], WexPublishDefinitionsFilterBar.prototype, "categories", void 0);
__decorate([
    state()
], WexPublishDefinitionsFilterBar.prototype, "string", void 0);
WexPublishDefinitionsFilterBar = __decorate([
    customElement("wex-publish-definitions-filter-bar")
], WexPublishDefinitionsFilterBar);
export { WexPublishDefinitionsFilterBar };
//# sourceMappingURL=definitions-filter-bar.js.map