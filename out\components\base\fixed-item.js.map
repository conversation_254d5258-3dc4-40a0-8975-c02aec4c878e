{"version": 3, "file": "fixed-item.js", "sourceRoot": "", "sources": ["../../../src/components/base/fixed-item.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAG3C,IAAM,YAAY,GAAlB,MAAM,YAAa,SAAQ,UAAU;IAU1C,MAAM;QACJ,MAAM,QAAQ,GAAG,IAAI,CAAA,eAAe,CAAC;QACrC,OAAO,QAAQ,CAAC;IAClB,CAAC;;AAZM,mBAAM,GAAG,GAAG,CAAA;;;;;;;GAOlB,AAPY,CAOX;AARS,YAAY;IADxB,aAAa,CAAC,gBAAgB,CAAC;GACnB,YAAY,CAcxB", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement } from \"lit/decorators.js\";\r\n\r\n@customElement(\"wex-fixed-item\")\r\nexport class WexFixedItem extends LitElement {\r\n  static styles = css`\r\n    :host {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n  `;\r\n\r\n  render() {\r\n    const template = html`<slot></slot>`;\r\n    return template;\r\n  }\r\n}\r\n"]}