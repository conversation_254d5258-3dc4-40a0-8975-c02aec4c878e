import { LitElement, PropertyValues, css } from "lit";
import { html, render } from "lit/html.js";
import { storeInstance } from "store/index.js";
import { Router } from "@vaadin/router/dist/vaadin-router.js";
import PubSub from "pubsub-js";
import * as util from "lib/util.ts";
import "bds-tree";

// import "./wex-folders";
// import "./wex-browse-folders-panel";
import "./wex-browse-files";
import "@vaadin/vaadin-split-layout/vaadin-split-layout.js";
import "@vaadin/vaadin-context-menu/vaadin-context-menu.js";
import "@polymer/iron-icon/iron-icon.js";
import "@polymer/iron-icons/iron-icons.js";
import "@vaadin/vaadin-icons/vaadin-icons.js";
import "@ui5/webcomponents/dist/DatePicker";
// import "@ui5/webcomponents/dist/assets.js";
import "@ui5/webcomponents/dist/Button.js";
import "@ui5/webcomponents/dist/Dialog.js";
import "@ui5/webcomponents/dist/Label.js";
import "@ui5/webcomponents/dist/Input.js";
import "@ui5/webcomponents/dist/Icon.js";
import "@ui5/webcomponents/dist/Select.js";
import "@ui5/webcomponents/dist/Option.js";
import { customElement, property } from "lit/decorators";
import { BDSTree } from "bds-tree";
import { Collection, Project } from "@bds/types";

@customElement("wex-layout-browse")
export class WexLayoutBrowse extends LitElement {
  @property({ type: Object }) tree: BDSTree<Collection> | null = null;
  @property({ type: Object }) state: any;
  @property({ type: Object }) string: Record<string, string> = {};
  @property({ type: Object }) subscription: any;
  @property({ type: Object }) folderData: Collection | null = null;
  @property({ type: Object }) contextMenu: any;
  @property({ type: Object }) files: Element | null = null;
  @property({ type: Object }) folders: Element | null = null;
  @property({ type: Boolean }) _showMobileFilter: boolean = false;
  // @property({ type: Object }) browseProjectContext: Project | null = null;
  @property({ type: String }) formerAsof: string = "Now";
  @property({ type: String }) asOfNameDisplay: string = "Now";
  @property({ type: String }) asOfDateDisplay: string = "Now";

  // Router location property - set automatically by Vaadin Router
  location: any = null;
  treeConfig: any;

  constructor() {
    super();
    this.state = null;
    this.subscription = null;
  }

  // Getter to handle router location - Vaadin Router sets this automatically
  get routerLocation() {
    return this.location || { params: {} };
  }

  connectedCallback() {
    super.connectedCallback();
    this.state = storeInstance.state;
    this.subscription = storeInstance.subscribe((state: any) => {
      this.stateChange(state);
    });

    this.browseProjectContext = this.state.browse_project_context;
    this.formerAsof = "Now"; //testing
    this.string = this.state[this.state.langCode];
    this.folderData = this.state.global_folders;
    this.contextMenu = structuredClone(this.state.menus.folder_menu) ?? [];
    this.treeConfig = {
      initialNodeId: this.location?.params?.collectionId,
      labelPath: "name",
      idPath: "id",
      childPath: "children",
      contextMenuRenderer: this._contextMenuRenderer.bind(this),
    };

    const canPaste =
      !!this.state.browse_file_to_copy || !!this.state.browse_file_to_cut;
    // TEMP -- disable paste if nothing is copied or cut
    // need to find a different solution
    if (!canPaste) {
      let idx = this.contextMenu.findIndex((x: any) => x.name == "paste_file");
      if (idx >= 0) this.contextMenu[idx].disabled = true;
    }
    this.contextMenu.forEach((item: any) => {
      item.text = this.string[item.label];
    });
    this.addEventListener(
      "folderdropfolder",
      this._folderDropFolder.bind(this)
    );
    if (this.location) {
      if (this.location.params.collectionId) {
        util.updatePageLocation(
          "browse",
          "/wex/" +
            this.state.langCode +
            "/browse/" +
            this.location.params.collectionId,
          true
        );
        storeInstance.dispatch("setState", {
          property: "selected_collectionId",
          value: this.location.params.collectionId,
        });
      }
    }
    storeInstance.dispatch("getCollectionTree");
  }

  disconnectedCallback() {
    storeInstance.unsubscribe(this.subscription);
  }

  stateChange(state: any) {
    this.state = state;
    this.string = this.state[this.state.langCode];

    this.folderData = this.state.global_folders;
    this.browseProjectContext = this.state.browse_project_context;

    if (this.state.selected_collectionId) {
      var collId = this.state.selected_collectionId;
      storeInstance.dispatch("setState", {
        property: "selected_collectionId",
        value: null,
      });
      this._folderById(this.folderData, collId);
    }

    if (!this.state.browse_selected_folder) {
      storeInstance.dispatch("setState", {
        property: "browse_selected_folder",
        value: this.folderData,
      });
    }

    if (this.state.browse_asof_filter) {
      if (this.state.browse_asof_filter.hasOwnProperty("gmtLabelDate")) {
        this.asOfNameDisplay = this.state.browse_asof_filter.name;
        this.asOfDateDisplay = this.state.browse_asof_filter.gmtLabelDate;
      } else {
        if (Date.parse(this.state.browse_asof_filter)) {
          this.asOfNameDisplay = this.state.browse_asof_filter;
          this.asOfDateDisplay = this.state.browse_asof_filter;
        } else {
          this.asOfNameDisplay = "Now";
          this.asOfDateDisplay = "Now";
        }
      }
    }

    this.files = this.shadowRoot?.querySelector("#files") || null;
    this.folders = this.shadowRoot?.querySelector("#browsefolders") || null;
    if (this.formerAsof != this.asOfNameDisplay) {
      // this.files.refreshData();
      storeInstance.dispatch("setState", {
        property: "browse_refresh_files",
        value: true,
      });
      this.formerAsof = this.asOfNameDisplay;
    }
  }

  firstUpdated(): void {
    this.tree = this.shadowRoot?.querySelector("bds-tree") || null;
    if (this.location?.params?.collectionId) {
      this._folderById(this.folderData, this.location.params.collectionId);
    }
  }

  _folderContextMenuClick(e: any, data: any) {
    console.log(e, data);
    switch (e.detail?.value?.name) {
      case "import":
        storeInstance.dispatch("setState", {
          property: "folder_import_dialog_open",
          value: data.node,
        });
        break;
      case "newfromtemplate":
        var tmpLockFolders = false;
        if (this.state.wex_user.effectiveCap == "contribute") {
          tmpLockFolders = true;
        }
        storeInstance.dispatch("setState", {
          property: "extended_folder_create_file_dialog_open",
          value: {
            target: "folder",
            folder: data.node,
            branch: this.state.browse_branch_filter,
            lang: this.state.browse_lang_filter,
            lockedFolders: tmpLockFolders,
          },
        });
        //storeInstance.dispatch("setState", {property: "folder_create_file_dialog_open", value:{target: "folder", payload: this.state.browse_selected_folder } } )
        break;
      case "extnewfromtemplate":
        var tmpLockFolders = false;
        if (this.state.wex_user.effectiveCap == "contribute") {
          tmpLockFolders = true;
        }
        storeInstance.dispatch("setState", {
          property: "extended_folder_create_file_dialog_open",
          value: {
            target: "folder",
            folder: data.node,
            branch: this.state.browse_branch_filter,
            lang: this.state.browse_lang_filter,
            lockedFolders: tmpLockFolders,
          },
        });
        break;
      case "search_folder":
        const filters = structuredClone(this.state.search_filters);
        let folderFilter = filters.find((x: any) => x.name === "folder");
        folderFilter.active = true;
        storeInstance.dispatch("setState", {
          property: "search_filters",
          value: filters,
        });
        storeInstance.dispatch("setState", {
          property: "search_folder_filter",
          value: data.node,
        });
        Router.go("/wex/" + this.state.langCode + "/search");
        break;
      case "create_folder":
        PubSub.publish("dialog-browse-create-folder-open", {
          folder: data.node,
        });
        break;
      case "rename_folder":
        PubSub.publish("dialog-browse-rename-folder-open", {
          folder: data.node,
        });
        break;
      case "import_zip":
        storeInstance.dispatch("setState", {
          property: "import_files_dialog_open",
          value: true,
        });
        break;
      case "export_folder":
        console.log("Export folder clicked");
        // storeInstance.dispatch("setState", {
        //   property: "export_files_dialog_open",
        //   value: data.node,
        // });
        break;
      case "delete_folder":
        PubSub.publish("dialog-browse-delete-folder-open", {
          folderId: data.node.id,
          folderName: data.node.name,
          branchId: this.state.browse_branch_filter.branchId,
        });
        break;
    }
  }

  _folderDropFolder(e: any) {
    const { collectionId, parentId } = e.detail;
    if (collectionId === parentId) return;
    storeInstance.dispatch("browseMoveFolder", {
      collectionId: collectionId,
      parentId: parentId,
    });
  }

  _folderById(root: any, collId: any) {
    if (!this.tree || !root) return;

    // First set the node to expand the tree path
    this.tree.treeController.setNode(root, String(collId));

    // Then find the node
    const result = this.tree.treeController.findNodeByKey(
      root,
      String(collId),
      []
    );
    if (!result) return;
    const { node } = result;
    util.updatePageLocation(
      "browse",
      "/wex/" + this.state.langCode + "/browse/" + node.id,
      true
    );
    storeInstance.dispatch("setState", {
      property: "browse_selected_folder",
      value: { ...node, path: node.collectionPathId },
    });
    storeInstance.dispatch("setState", {
      property: "browse_refresh_files",
      value: true,
    });
  }

  _selectedProjectName() {
    if (this.browseProjectContext) {
      return this.browseProjectContext.name;
    } else {
      return "";
    }
  }

  _toggleFilters() {
    this._showMobileFilter = !this._showMobileFilter;
    storeInstance.dispatch("touch");
  }

  _contextMenuRenderer(content: any, data: any) {
    return html`<vaadin-context-menu
      id="contextmenu"
      .items="${this.contextMenu}"
      @item-selected="${(e: any) => this._folderContextMenuClick(e, data)}"
    >
      ${content}
    </vaadin-context-menu>`;
  }

  render() {
    // Defensive check to prevent rendering issues
    if (!this.state || !this.string) {
      return html`<div>Loading...</div>`;
    }

    return html`
      <style>
        #browseheader {
          border-top: 1px solid;
          border-bottom: 1px solid;
          border-color: var(--primary-100);
          padding: var(--spacing-md) var(--spacing-sm);
          display: flex;
          justify-content: space-between;
          gap: var(--spacing-xxs);
          align-items: center;
        }

        #folderpane {
          height: calc(100vh - 7rem);
          width: ${this.state.global_screen_specs.browse_leftSideWidthPerc}%;
          overflow: auto;
        }
        #filespane {
          height: calc(100vh - 7rem);
          width: ${this.state.global_screen_specs.browse_rightSideWidthPerc}%;
          overflow: auto;
        }
        #layout {
          height: 100%;
        }

        .btn {
          display: flex;
          align-items: center;
          background-color: #fbfbfb;
          border: 1px solid white;
          border-radius: 2px;
          justify-content: space-between;
          overflow: hidden;
        }
        .btn:hover {
          border: 1px solid var(--clr-primary);
        }
        .btn > * {
          color: inherit;
        }
        .btn > span {
          white-space: nowrap;
          overflow: hidden;
        }
        .project-filter-btn {
          border: 1px solid; */
        }
        .action-el {
          cursor: pointer;
        }
        #headerleft {
          grid-column: 1 / 2;
          justify-self: start;
          display: flex;
          align-items: center;
        }
        #headerfiltericon {
          display: flex;
          justify-content: center;
        }
        #headerright {
          grid-column: 3 / 4;
          justify-self: end;
          display: flex;
          align-items: center;
          gap: var(--spacing-xs)
        }
        @media screen and (min-width: 0px) {
          /* #browseheader {
            display: grid;
            grid-template-columns: minmax(0, 1fr) min-content minmax(0, 1fr);
            grid-template-rows: ${this.state.global_screen_specs
          .subheaderHeight}px 1fr;
            align-items: center;
          } */
          .mobile {
            display: block;
          }
          #headerfilters {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
            justify-self: center;
            flex-direction: column;
            align-items: center;
            padding-bottom: 8px;
          }
          #headerfilters[data-mobile="true"] {
            display: flex;
          }
          #headerfilters[data-mobile="false"] {
            display: none;
          }
          .filter-btn {
            width: fit-content;
          }
          .filterinputs {
            --_ui5_input_width: 8rem;
          }
        }
        @media screen and (min-width: 780px) {
          #headerfilters {
            grid-column: 1 / 4;
            grid-row: 2 / 3;
            justify-self: center;
            flex-direction: row;
            align-items: center;
            padding-bottom: 8px;
          }
          er-btn {
            width: calc(7rem - 2 * var(--padding));
          }
        }
        @media screen and (min-width: 1280px) {
          .desktop {
            display: block;
          }
          .mobile {
            display: none;
          }
          #browseheader {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
          }
          #headermiddle {
            grid-column: 2 / 3;
            justify-self: center;
          }
          #headerfilters {
            grid-column: 2 / 3;
            justify-self: center;
            flex-direction: row;
            align-items: center;
            padding-bottom: 0px;
          }
          #headerfilters[data-mobile="true"],
          #headerfilters[data-mobile="false"] {
            display: flex;
          }

          iron-icon {
            width: 1rem;
          }
        }
      </style>

      <div id="browseheader">
        <div id="headerleft">
          <ui5-button
            class="btn dialog-btn project-filter-btn action-el"
            @click="${this._projectSelectDialog.bind(this)}"
            title="${this.browseProjectContext
              ? this.browseProjectContext.name
              : this.string["_select_project_label"]}"
          >
            ${this.browseProjectContext
              ? this.browseProjectContext.name
              : this.string["_select_project_label"]}
          </ui5-button>
          <ui5-button
            ?disabled="${!this.browseProjectContext}"
            id="jump-proj-fldr-btn"
            @click="${this._jumpToFolder.bind(this)}"
            class="ui5button ui5-content-density-compact"
            title="${this.string["_jump_to_project_folder"]}"
          >
            <iron-icon
              icon="vaadin:arrow-circle-right-o"
              title="${this.string["_jump_to_project_folder"]}"
            >
            </iron-icon>
          </ui5-button>
        </div>

        <div id="headerfiltericon" class="mobile">
          <iron-icon
            id="headermiddle"
            class="mobile action-el"
            icon="vaadin:sliders"
            @click="${this._toggleFilters.bind(this)}"
          >
          </iron-icon>
        </div>
        <div
          id="headerfilters"
          class="desktop"
          data-mobile="${this._showMobileFilter}"
        >
          <ui5-select
            class="filterinputs"
            id="langselector"
            class="ui5-content-density-compact"
            @change="${this._setLangFilter.bind(this)}"
          >
            ${this.state.global_langs.map(
              (item: any) =>
                html`<ui5-option
                  ?selected="${this.state.browse_lang_filter == item}"
                  .item="${item}"
                  >${item.description}</ui5-option
                >`
            )}
          </ui5-select>

          <ui5-select
            class="filterinputs"
            id="branchselector"
            class="ui5-content-density-compact"
            @change="${this._setBranchFilter.bind(this)}"
          >
            ${this.state.global_branches.map(
              (item: any) =>
                html`<ui5-option
                  ?selected="${this.state.browse_branch_filter == item}"
                  .item="${item}"
                  >${item.name}</ui5-option
                >`
            )}
          </ui5-select>

          <ui5-select
            class="filterinputs"
            id="mimetypeselector"
            class="ui5-content-density-compact"
            @change="${this._setMimetypeFilter.bind(this)}"
          >
            ${this.state.menus.mimetypes.map(
              (item: any) =>
                html`<ui5-option
                  ?selected="${this.state.browse_mimetype_filter == item.value}"
                  value="${item.value}"
                  >${item.name}</ui5-option
                >`
            )}
          </ui5-select>

          <ui5-button
            value="${this.asOfDateDisplay}"
            class="btn dialog-btn filter-btn action-el"
            @click="${this._asOfDialog.bind(this)}"
            title="${this.asOfDateDisplay}"
          >
            <span>${this.asOfNameDisplay}</span>
            <iron-icon icon="icons:date-range" title="Select date range">
            </iron-icon>
          </ui5-button>
        </div>

        <div id="headerright">
          <ui5-button
            class="ui5button ui5-content-density-compact"
            @click="${this._clearFilters.bind(this)}"
            >${this.string["_clearfilters"]}
          </ui5-button>
          <ui5-button
            id="refreshbtn"
            @click="${this._refresh.bind(this)}"
            class="ui5-content-density-compact"
            design="Emphasized"
            >${this.string["_refresh"]}</ui5-button
          >
        </div>
      </div>
      <vaadin-split-layout id="layout">
        <div id="folderpane">
          <bds-tree
            .config=${this.treeConfig}
            .root=${this.folderData}
            @treeClick="${this._folderSelected.bind(this)}"
          ></bds-tree>
          <!-- <wex-folders
            id="browsefolders"
            .folders="${this.folderData}"
            .isDraggable="${true}"
            .selectedfolder="${this.state.browse_selected_folder}"
            .contextmenu="${this.contextMenu}"
            @folderselected="${this._folderSelected.bind(this)}"
          ></wex-folders> -->
        </div>
        <div id="filespane">
          <wex-browse-files id="files"></wex-browse-files>
        </div>
      </vaadin-split-layout>
    `;
  }

  _asOfDialog(_e: any) {
    storeInstance.dispatch("setState", {
      property: "asof_dialog_open",
      value: {
        branchId: this.state.browse_branch_filter.branchId,
        statefield: "browse_asof_filter",
      },
    });
  }
  _clearFilters(_e: any) {
    storeInstance.dispatch("setState", {
      property: "browse_asof_filter",
      value: this.state.default_asof,
    });
    storeInstance.dispatch("setState", {
      property: "browse_lang_filter",
      value: this.state.default_lang_object,
    });
    storeInstance.dispatch("setState", {
      property: "browse_branch_filter",
      value: this.state.default_branch_object,
    });
    storeInstance.dispatch("setState", {
      property: "browse_mimetype_filter",
      value: null,
    });
    storeInstance.dispatch("setState", {
      property: "browse_project_context",
      value: null,
    });
    // this.files.refreshData();
    storeInstance.dispatch("setState", {
      property: "browse_refresh_files",
      value: true,
    });
  }
  _refresh(_e: any) {
    // this.files.refreshData();
    storeInstance.dispatch("setState", {
      property: "browse_refresh_files",
      value: true,
    });
  }

  _folderSelected(e: any) {
    console.log(e);
    if (this.state.browse_selected_folder.id != e.detail.node.id) {
      util.updatePageLocation(
        "browse",
        "/wex/" + this.state.langCode + "/browse/" + e.detail.node.id,
        true
      );
      storeInstance.dispatch("setState", {
        property: "browse_selected_file",
        value: null,
      });
      storeInstance.dispatch("setState", {
        property: "browse_selected_folder",
        value: { ...e.detail.node, path: e.detail.node.collectionPathId },
      });
      // this.files.refreshData();
      storeInstance.dispatch("setState", {
        property: "browse_refresh_files",
        value: true,
      });
    }
  }

  _setLangFilter(e: any) {
    if (this.browseProjectContext) {
      if (
        e.detail.selectedOption.item.langId !=
        this.browseProjectContext.languageId
      ) {
        storeInstance.dispatch("setState", {
          property: "browse_project_context",
          value: null,
        });
      }
    }
    storeInstance.dispatch("setState", {
      property: "browse_lang_filter",
      value: e.detail.selectedOption.item,
    });
    // this.files.refreshData();
    storeInstance.dispatch("setState", {
      property: "browse_refresh_files",
      value: true,
    });
  }
  _setBranchFilter(e: any) {
    if (this.browseProjectContext) {
      if (
        e.detail.selectedOption.item.branchId !=
        this.browseProjectContext.branchId
      ) {
        storeInstance.dispatch("setState", {
          property: "browse_project_context",
          value: null,
        });
      }
    }
    storeInstance.dispatch("setState", {
      property: "browse_branch_filter",
      value: e.detail.selectedOption.item,
    });
    // this.files.refreshData();
    storeInstance.dispatch("setState", {
      property: "browse_refresh_files",
      value: true,
    });
  }
  _setMimetypeFilter(e: any) {
    storeInstance.dispatch("setState", {
      property: "browse_mimetype_filter",
      value: e.detail.selectedOption.value,
    });
    // this.files.refreshData();
    storeInstance.dispatch("setState", {
      property: "browse_refresh_files",
      value: true,
    });
  }

  _projectSelectDialog() {
    storeInstance.dispatch("setState", {
      property: "project_dialog_open",
      value: { callback: this._selectProjectCallback.bind(this) },
    });
  }

  _selectProjectCallback(prj: Project) {
    if (prj) {
      var langItem;
      var branchItem;
      langItem = this.state.global_langs.find(function (item: any) {
        return item.langId === prj?.languageId;
      });
      branchItem = this.state.global_branches.find(function (item: any) {
        return item.branchId === prj.branchId;
      });
      storeInstance.dispatch("setState", {
        property: "browse_lang_filter",
        value: langItem,
      });
      storeInstance.dispatch("setState", {
        property: "browse_branch_filter",
        value: branchItem,
      });
      storeInstance.dispatch("setState", {
        property: "browse_project_context",
        value: prj,
      });

      // Set the tree node and navigate to the project folder
      if (this.tree && this.folderData) {
        this.tree.treeController.setNode(
          this.folderData,
          String(prj.projectHomeCollectionId)
        );
        const result = this.tree.treeController.findNodeByKey(
          this.folderData,
          String(prj.projectHomeCollectionId),
          []
        );
        if (result) {
          const { node } = result;
          util.updatePageLocation(
            "browse",
            "/wex/" + this.state.langCode + "/browse/" + node.id,
            true
          );
          storeInstance.dispatch("setState", {
            property: "browse_selected_folder",
            value: { ...node, path: node.collectionPathId },
          });
          // this.files.refreshData();
          storeInstance.dispatch("setState", {
            property: "browse_refresh_files",
            value: true,
          });
        }
      }
    } else {
      storeInstance.dispatch("setState", {
        property: "browse_project_context",
        value: null,
      });
    }
  }

  _jumpToFolder(_e: any) {
    if (this.browseProjectContext) {
      this._folderById(
        this.folderData,
        this.browseProjectContext.projectHomeCollectionId
      );
    }
  }
}
