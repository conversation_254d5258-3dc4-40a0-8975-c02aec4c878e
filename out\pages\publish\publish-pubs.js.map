{"version": 3, "file": "publish-pubs.js", "sourceRoot": "", "sources": ["../../../src/pages/publish/publish-pubs.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAE5C,OAAO,iCAAiC,CAAC;AAEzC,MAAM,kBAAmB,SAAQ,UAAU;IACzC,MAAM,KAAK,UAAU;QACnB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA,EAAE,CAAC;IACf,CAAC;IAED,eAAe;QACb,MAAM,QAAQ,GAAG,IAAI,CAAA,+BAA+B,CAAC;QACrD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,MAAM;QACJ,MAAM,QAAQ,GAAG,IAAI,CAAA;;;0BAGC,IAAI,CAAC,eAAe,EAAE;;KAE3C,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED,cAAc,CAAC,MAAM,CAAC,uBAAuB,EAAE,kBAAkB,CAAC,CAAC", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\n\r\nimport \"../../components/header/publish\";\r\n\r\nclass WexPagePublishPubs extends LitElement {\r\n  static get properties() {\r\n    return {};\r\n  }\r\n\r\n  static get styles() {\r\n    return css``;\r\n  }\r\n\r\n  _renderMainPane() {\r\n    const template = html`<div>PUBLICATION EDITOR</div>`;\r\n    return template;\r\n  }\r\n\r\n  render() {\r\n    const template = html`\r\n      <div id=\"publish-pubs-container\">\r\n        <wex-publish-header></wex-publish-header>\r\n        <wex-main-pane> ${this._renderMainPane()} </wex-main-pane>\r\n      </div>\r\n    `;\r\n    return template;\r\n  }\r\n}\r\n\r\ncustomElements.define(\"wex-page-publish-pubs\", WexPagePublishPubs);\r\n"]}