import { LitElement, html, css } from "lit-element";
import { storeInstance } from "store/index.js";
import { Router } from "@vaadin/router";

class WexReportsHeader extends LitElement {
  static get properties() {
    return {};
  }

  static get styles() {
    return css`
      :host {
        display: block;
        overflow: hidden;
      }
      * {
        box-sizing: border-box;
      }
      #reports-tabs {
        display: flex;
        padding: 0;
        margin: 0;
        list-style: none;
      }
      .header-container {
        border-bottom: 1px solid var(--primary-100);
      }
      .tab {
        display: flex;
        align-items: center;
        padding: var(--spacing-md) var(--spacing-md);
        cursor: pointer;
        color: var(--color-text);
        line-height: 1;
      }
      .tab[active] {
        border-bottom: 2px solid;
        font-weight: bold;
      }
      .tab:not([active]):hover {
        background-color: var(--clr-white);
        color: var(--primary);
      }
    `;
  }

  constructor() {
    super();
    this._state = {};
    this._string = {};
    this._subscription = null;

    this._tabs = [];
    // this._subtabs = [];
  }

  connectedCallback() {
    super.connectedCallback();
    this._state = storeInstance.state;
    this._string = this._state[this._state.langCode];
    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  firstUpdated() {
    this._setTab();
  }

  stateChange(state) {
    this._state = state;
    this._string = state[state.langCode];
  }

  _init() {
    this._tabs = this._state.menus.reports;
    this._setTab();
  }

  _setTab() {
    console.log("setTab");
    const url = new URL(window.location.href);
    const tabs = url.pathname.split("/");
    // if (tabs.)
    const currentTab = tabs.pop();
    this._tabs.forEach((tab) => {
      tab.active = tab.name == currentTab;
    });
    // for (let tab of this._tabs) {
    //   console.log("tab", tab);
    // }

    storeInstance.dispatch("setState", {
      property: "reports_tab",
      value: tab,
    });
    this.requestUpdate();
  }

  render() {
    const template = html`
      <wex-row-item
        justifyContent="space-between"
        alignItems="center"
        class="header-container"
      >
        <ul id="reports-tabs">
          ${this._tabs.map(
            (tab) => html`
              <li
                class="tab"
                ?active=${!!tab.active}
                @click=${() => this._handleClick(tab.name)}
              >
                ${this._string[tab.label]}
              </li>
            `
          )}
        </ul>
        <wex-row-item
          justifyContent="flex-end"
          alignItems="center"
          style="margin-right: 1rem;"
        >
          <slot></slot>
        </wex-row-item>
      </wex-row-item>
    `;
    return template;
  }

  _handleClick = (path) => {
    Router.go(`/wex/${this._state.langCode}/reports/${path}`);
  };
}

customElements.define("wex-reports-header", WexReportsHeader);
