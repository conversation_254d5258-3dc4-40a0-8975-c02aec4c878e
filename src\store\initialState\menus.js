export default {
  menus: {
    main_menu: [
      { name: "workflow", label: "_workflow" },
      { name: "browse", label: "_browse" },
      { name: "search", label: "_search" },
      { name: "reports", label: "_reports" },
      { name: "publish", label: "_publish" },
      { name: "preview", label: "_preview" },
      { name: "edit", label: "_editor" },
      { name: "logout", label: "_logout" },
    ],
    taskContextMenu: [
      { name: "details", label: "_viewdetails" },
      { name: "claim", label: "_claimtask" },
      { name: "unclaim", label: "_unclaimtask" },
      { name: "finish", label: "_finishtask" },
    ],
    taskHistoryItemContextNenu: [{ name: "details", label: "_historydetails" }],
    file_menu: {
      properties: { name: "properties", label: "_properties" },
      preview: { name: "preview", label: "_preview" },
      previewNewWindow: {
        name: "previewnewwindow",
        label: "_previewnewwindow",
      },
      edit_topic: { name: "edit", label: "_edit_topic" },
      edit_ditamap: { name: "edit_ditamap", label: "_edit_ditamap" },
      open_ditamap: { name: "open_ditamap", label: "_open_ditamap" },
      export_files: { name: "export_files", label: "_export_files" },
      publish: { name: "publish", label: "_publish" },
      editHeadMenuItem: { name: "edit", label: "_edit_head" },
      removeMenuItem: { name: "remove", label: "_remove" },
      highlightFileFolder: {
        name: "highlightfolder",
        label: "_highlightfolder",
      },
      openfilelocationMenuItem: {
        name: "openfilelocation",
        label: "_openfilelocation",
      },
      toggleDoneMenuItem: { name: "toggledone", label: "_toggledone" },
      cut: { name: "cut", label: "_cut" },
      copy: { name: "copy", label: "_copy" },
      paste: { name: "paste", label: "_paste" },
      // rename: { name: "rename", label: "_rename" },
      delete: { name: "delete", label: "_delete" },
    },
    folder_menu: [
      { name: "import", label: "_importfile" },
      { name: "newfromtemplate", label: "_createfile" },
      // { name: "paste_file", label: "_paste" },
      { component: "hr" },
      { name: "search_folder", label: "_browse_search_folder" },
      { name: "create_folder", label: "_browse_create_folder" },
      { name: "rename_folder", label: "_browse_rename_folder" },
      { name: "import_zip", label: "_import_zip" },
      { name: "export_folder", label: "_export_folder" },
      { name: "delete_folder", label: "_browse_delete_folder" },
    ],
    langs: [
      { name: "en-Us", id: "1" },
      { name: "fr-FR", id: "2" },
    ],
    mimetypes: [
      { name: "All", value: "" },
      { name: "text/plain", value: "text/plain" },
      { name: "text/xml", value: "text/xml" },
      { name: "text/html", value: "text/html" },
      { name: "image/jpeg", value: "image/jpeg" },
      { name: "image/gif", value: "image/gif" },
      { name: "image/png", value: "image/png" },
      { name: "application/pdf", value: "application/pdf" },
      {
        name: "application/x-zip-compressed",
        value: "application/x-zip-compressed",
      },
    ],
    editor_modes: [
      { name: "readonly", value: "_readonly" },
      { name: "review", value: "_review" },
      { name: "author", value: "_author" },
    ],
    editor_moremenu: [
      {
        name: "open",
        value: "_openfile",
        icon: "vaadin:folder-open",
        available_in_readonly_mode: true,
      },
      // {
      //   name: "close",
      //   value: "_close",
      //   icon: "vaadin:close",
      //   available_in_readonly_mode: true,
      // },
      {
        name: "print",
        value: "_print",
        icon: "vaadin:print",
        available_in_readonly_mode: true,
      },
      {
        name: "tags",
        value: "_tags",
        icon: "vaadin:code",
        available_in_readonly_mode: true,
      },
      {
        name: "validate",
        value: "_validate",
        icon: "vaadin:check-circle-o",
        available_in_readonly_mode: false,
      },
      // {
      //   name: "comments",
      //   value: "_comments",
      //   icon: "vaadin:comments",
      //   available_in_readonly_mode: false,
      // },
      {
        name: "find",
        value: "_find",
        icon: "vaadin:search",
        available_in_readonly_mode: false,
      },
      {
        name: "undo",
        value: "_undo",
        icon: "vaadin:reply",
        available_in_readonly_mode: false,
      },
      {
        name: "redo",
        value: "_redo",
        icon: "vaadin:share",
        available_in_readonly_mode: false,
      },
    ],
    editor_tabs: [
      { name: "ditamaps", label: "_ditamaps", active: false },
      { name: "tasks", label: "_tasks", active: false },
      { name: "fileinfo", label: "_fileinfo", active: false },
      { name: "mru", label: "_mru", active: false },
    ],

    // modes deprecated, tabs
    reports_modes: [
      { name: "locks", value: "_locks" },
      { name: "rooms", value: "_rooms" },
    ],

    reports: [
      { name: "locks", label: "_locks" },
      { name: "rooms", label: "_rooms" },
    ],
    publish: [
      { name: "publications", label: "_publications" },
      { name: "definitions", label: "_definitions" },
      // { name: "run", label: "_run" },
      { name: "jobs", label: "_jobs" },
      { name: "adhoc-jobs", label: "_adhoc_jobs" },
    ],
    publish_run: [
      { name: "definitions", label: "_definitions" },
      { name: "adhoc", label: "_adhoc" },
    ],
    workflow: [
      { name: "tasks", label: "_tasks" },
      { name: "processes", label: "_processes" },
    ],
    publishContextMenu: [{ name: "delete", label: "_delete" }],

    dialog_select_actions: [
      {
        name: "select-all",
        label: "_select_all",
        icon: "vaadin:angle-double-right",
      },
      { name: "select", label: "_select", icon: "vaadin:angle-right" },
      { name: "deselect", label: "_deselect", icon: "vaadin:angle-left" },
      {
        name: "deselect-all",
        label: "_deselect_all",
        icon: "vaadin:angle-double-left",
      },
    ],
  },
  search_filters: [{ name: "folder", label: "_folder", active: false }],
};
