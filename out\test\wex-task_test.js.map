{"version": 3, "file": "wex-task_test.js", "sourceRoot": "", "sources": ["../../src/test/wex-task_test.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,kBAAkB,CAAC;AACzD,OAAO,iBAAiB,CAAC;AACzB,OAAO,KAAK,MAAM,iBAAiB,CAAC;AAIpC,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;IAClB,EAAE,CAAC,IAAI,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;QACzE;;WAEG;QACH,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAC1D,MAAM,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,CAAA,oBAAoB,KAAK,6CAA6C,CAAC,CAAC;IACzG,CAAC,CAAC,CAAA;IACF,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAA;IACrC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;IACzB,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;IAC/B,EAAE,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAA;IACrD,EAAE,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAA;IACpF,EAAE,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAA;AACtF,CAAC,CAAC,CAAA;AAEF,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;IACpB,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IACrC,EAAE,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IAC5C,EAAE,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;IACxD,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC9B,CAAC,CAAC,CAAA;AAEF,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;IAClB,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;QAClB,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IACrC,EAAE,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;IAChD,EAAE,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;IAChD,EAAE,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IACvC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;IAC9B,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAA;IAChC,UAAU;IACV,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACrB,EAAE,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QAC1D,EAAE,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACvD,EAAE,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;QACjF,EAAE,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QACnE,EAAE,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAA;AAEF,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;IACvB,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnB,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnB,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACvB,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IACzB,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC9B,EAAE,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;IACtD,EAAE,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;IACxD,EAAE,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IAC7C,EAAE,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IACvC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC5B,CAAC,CAAC,CAAC", "sourcesContent": ["\r\nimport { assert, fixture, html } from \"@open-wc/testing\";\r\nimport \"./store-wrapper\";\r\nimport Store from 'store/beedle.js';\r\n\r\n\r\n\r\ndescribe(\"Page\", () => {\r\n    it.skip(\"shows no tasks found if no tasks and fetch is complete\", async () => {\r\n        /**\r\n         * 1. Fetch the call via an action and mock the response (will need waitFor?)\r\n         */\r\n        const store = new Store(actions, mutations, initialState);\r\n        const el = await fixture(html`<mock-app .store=${store}><wex-task-page></wex-task-page></mock-app>`);\r\n    })\r\n    it.skip(\"shows error if fetch fails\")\r\n    it.skip(\"shows one task\")\r\n    it.skip(\"shows multiple tasks\")\r\n    it.skip(\"shows claimed tasks before unclaimed tasks\")\r\n    it.skip(\"shows loading spinner if fetch is pending and there are no tasks in cache\")\r\n    it.skip(\"shows updating message if fetch is pending and there are tasks in cache\")\r\n})\r\n\r\ndescribe(\"Filter\", () => {\r\n    it.skip(\"requests tasks by project\");\r\n    it.skip(\"requests tasks by claimed status\");\r\n    it.skip(\"requests tasks by project and claimed status\");\r\n    it.skip(\"clears filters\");\r\n})\r\n\r\ndescribe(\"Tile\", () => {\r\n    it(\"is defined\", () => {\r\n        assert.isDefined(document.createElement(\"wex-task-tile\"));\r\n    });\r\n\r\n    it.skip(\"displays task information\");\r\n    it.skip(\"requests task unclaimed when claimed\");\r\n    it.skip(\"requests task claimed when unclaimed\");\r\n    it.skip(\"finish task launches dialog\");\r\n    it.skip(\"shows primary files\")\r\n    it.skip(\"shows reference files\")\r\n    // Loading\r\n    describe(\"Loading\", () => {\r\n        it.skip(\"shows skeleton when no files and fetch pending\");\r\n        it.skip(\"shows skeleton when error and fetch pending\"); \r\n        it.skip(\"shows files with updating spinner when files cached and fetch pending\");\r\n        it.skip(\"shows no files message when no files and fetch complete\");\r\n        it.skip(\"shows error message when error and fetch complete\");\r\n    });\r\n})\r\n\r\ndescribe(\"Tile Menu\", () => {\r\n    it.skip(\"details\");\r\n    it.skip(\"diagram\");\r\n    it.skip(\"history\");\r\n});\r\n\r\ndescribe(\"Filepackage\", () => {\r\n    it.skip(\"displays file name\");\r\n    it.skip(\"displays file status (reviewed/unreviewed)\");\r\n    it.skip(\"double click launches editor if task claimed\");\r\n    it.skip(\"shows context menu on right click\");\r\n    it.skip(\"toggle file reviewed status\");\r\n    it.skip(\"preview file\");\r\n});"]}