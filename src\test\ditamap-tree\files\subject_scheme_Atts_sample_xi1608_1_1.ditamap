<subjectScheme xmlns:ditaarch="http://dita.oasis-open.org/architecture/2005/" xml:lang="en-US"
    class="- map/map subjectScheme/subjectScheme ">
    <!-- Define the values for the audience attribute -->
    <subjectdef keys="users" class="- map/topicref subjectScheme/subjectdef ">
        <subjectdef keys="general" class="- map/topicref subjectScheme/subjectdef " />
        <subjectdef keys="support" class="- map/topicref subjectScheme/subjectdef " />
        <subjectdef keys="technician" class="- map/topicref subjectScheme/subjectdef " />
    </subjectdef>
    <!-- Define the values for the platform attribute -->
    <subjectdef keys="output" class="- map/topicref subjectScheme/subjectdef ">
        <subjectdef keys="pdf" class="- map/topicref subjectScheme/subjectdef " />
        <subjectdef keys="html" class="- map/topicref subjectScheme/subjectdef " />
    </subjectdef>
    <!--  Bind the attributes to the values  -->
    <enumerationdef class="- map/topicref subjectScheme/enumerationdef ">
        <attributedef name="audience" class="- topic/data subjectScheme/attributedef " />
        <subjectdef keyref="users" class="- map/topicref subjectScheme/subjectdef " />
    </enumerationdef>
    <enumerationdef class="- map/topicref subjectScheme/enumerationdef ">
        <attributedef name="platform" class="- topic/data subjectScheme/attributedef " />
        <subjectdef keyref="output" class="- map/topicref subjectScheme/subjectdef " />
    </enumerationdef>
</subjectScheme>