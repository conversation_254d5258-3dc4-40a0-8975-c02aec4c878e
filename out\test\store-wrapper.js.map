{"version": 3, "file": "store-wrapper.js", "sourceRoot": "", "sources": ["../../src/test/store-wrapper.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AACtD,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,KAAK,CAAC;AACvC,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAE/C,oFAAoF;AACpF,MAAM,SAAS,GAAG,aAAa,CAAM,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;AAGtD,IAAM,YAAY,GAAlB,MAAM,YAAa,SAAQ,UAAU;IAArC;;QACE,UAAK,GAAQ,EAAE,CAAC;QAGhB,gBAAW,GAAG,IAAI,CAAC,KAAK,CAAC;IAK3B,CAAC;IAHC,MAAM;QACJ,OAAO,IAAI,CAAA,0BAA0B,CAAC;IACxC,CAAC;CACF,CAAA;AALC;IADC,OAAO,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;iDACP;AAJrB,YAAY;IADjB,aAAa,CAAC,UAAU,CAAC;GACpB,YAAY,CASjB", "sourcesContent": ["import { provide, createContext } from \"@lit/context\";\r\nimport { html, LitElement } from \"lit\";\r\nimport { customElement } from \"lit/decorators\";\r\n\r\n// Only the key \"store\" matters here as that is what is used in the context consumer\r\nconst mockStore = createContext<any>(Symbol(\"store\"));\r\n\r\n@customElement(\"mock-app\")\r\nclass StoreWrapper extends LitElement {\r\n  store: any = {};\r\n\r\n  @provide({ context: mockStore })\r\n  globalStore = this.store;\r\n\r\n  render() {\r\n    return html`<div><slot></slot></div>`;\r\n  }\r\n}\r\n"]}