import { LitElement, css } from "lit";
import { html, render } from "lit/html.js";
import { storeInstance } from "store/index.js";
import { Router } from "@vaadin/router";
import pubsub from "pubsub-js";
import * as util from "lib/util.ts";
import "@ui5/webcomponents/dist/Icon.js";
// import "@ui5/webcomponents/dist/Tree.js";
// import "@ui5/webcomponents/dist/TreeItem.js";
class WexBrowseFiles extends LitElement {
    static get styles() {
        return css `
      .file-icon[data-status="_locked_by_other"] {
        color: red;
      }
      .file-icon[data-status="_room_locked_by_user"],
      .file-icon[data-status="_sesh_locked_by_user"] {
        color: green;
      }
      .file-icon[data-status="_xdocs_locked_by_user"] {
        color: orange;
      }
      .file-icon[data-status="_locked_concurrent"] {
        color: blue;
      }

      .colhead {
        cursor: pointer;
      }
      .rowselected {
        --sapList_Background: var(--row-selected-background);
      }
      #notasks {
        font-style: oblique;
        opacity: 0.5;
        text-align: center;
        margin-top: 50px;
      }
      .datarow {
        cursor: pointer;
      }
      .ditamap {
        font-weight: bold;
      }
      .hide {
        visibility: hidden;
      }
      .sortbuttoncontainer {
        display: flex;
        flex-direction: column;
      }
      .sortbuttoncontainer iron-icon {
        margin-left: 0.25rem;
        --iron-icon-width: 0.9rem;
      }
      iron-icon.asc {
        margin-bottom: -0.6rem;
      }
      iron-icon.desc {
        margin-top: -0.6rem;
      }
      iron-icon.muted {
        color: #ccc;
      }

      .colhead {
        line-height: 1.4rem;
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      .iconcontatiner {
        height: 1.4rem;
        overflow: hidden;
      }

      .file-icon {
        width: 1rem;
        opacity: 0.7;
      }
      .file-icon:hover {
        opacity: 1;
      }

      .rowicons iron-icon {
        width: 1rem;
        padding-right: 30px;
        display: none;
      }
      .rowicons iron-icon:hover {
        color: #000;
      }

      .rowicons iron-icon.rowselected {
        display: inline-block;
      }

      #wex-browse-files {
        height: 100%;
      }

      #contextmenu {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .flex-spacer {
        flex-grow: 1;
      }
    `;
    }
    constructor() {
        super();
        //
        this.state = null;
        this.string = [];
        //
        this.folder = null;
        this.searchterm = null;
        this.subfolders = null;
        //
        this.contextMenu = null;
    }
    connectedCallback() {
        super.connectedCallback();
        this.state = storeInstance.state;
        this.string = this.state[this.state.langCode];
        this.subscription = storeInstance.subscribe((state) => {
            this.stateChange(state);
        });
        //this.refreshData();
    }
    disconnectedCallback() {
        storeInstance.unsubscribe(this.subscription);
    }
    refreshData() {
        var searchQuery = [];
        this.searchlang = this.state.browse_lang_filter
            ? this.state.browse_lang_filter.langCode
            : this.state.default_lang_object.langCode;
        this.searchbranch = this.state.browse_branch_filter
            ? this.state.browse_branch_filter.name
            : this.state.default_branch_object.name;
        this.asOfDate = this.state.default_asof;
        if (this.state.browse_asof_filter) {
            if (this.state.browse_asof_filter.hasOwnProperty("gmtLabelDate")) {
                this.asOfDate = this.state.browse_asof_filter.gmtLabelDate;
            }
            else {
                if (Date.parse(this.state.browse_asof_filter)) {
                    var tmpDate = new Date(Date.parse(this.state.browse_asof_filter));
                    this.asOfDate = tmpDate.toISOString();
                }
                else {
                    this.asOfDate = this.state.default_asof;
                }
            }
        }
        //XPathValueDB('//title')
        this.fieldNames =
            " Title, Name, LockOwner,  CreateDate, ActiveProcesses, FileStatus, NumComments, Size, FileId, FolderPath, MimeType";
        var folderClause = "";
        var folder = "/";
        if (this.state.browse_selected_folder) {
            folder = this.state.browse_selected_folder.path
                ? this.state.browse_selected_folder.path
                : "/";
        }
        var addMimeTypeClause = this.state.browse_mimetype_filter
            ? " AND MimeType = '" + this.state.browse_mimetype_filter + "'"
            : "";
        folderClause =
            " WHERE InFolder('" +
                folder +
                "') AND NOT (InFolderDesc('/Content/KbContent')) " +
                addMimeTypeClause;
        searchQuery.push("VIEW := PropertyView  CONTEXT (LanguageCode='" +
            this.searchlang +
            "',BranchName='" +
            this.searchbranch +
            "',AsOfDate='" +
            this.asOfDate +
            "', HeadVersionOnly='true') FOR EACH " +
            folderClause +
            " RETURN " +
            this.fieldNames);
        //storeInstance.dispatch("getBrowseFiles",  searchQuery.join("") )
        storeInstance.dispatch("getFiles", {
            query: searchQuery.join(""),
            statefield: "browse_files",
            maxrows: 0,
        });
    }
    myRender() {
        render(this.filesTemplate(), this.shadowRoot);
        this.contextMenu = this.shadowRoot.querySelector("#contextmenu");
    }
    stateChange(state) {
        this.state = state;
        this.string = this.state[this.state.langCode];
        // var refreshRquired = false;
        // if (this.folder != this.state.browse_selected_folder) {
        //   refreshRquired = true;
        //   this.folder = this.state.browse_selected_folder;
        // }
        // if (this.subfolders != this.state.select_file_dialog_search_subfolders) {
        //   refreshRquired = true;
        //   this.subfolders = this.state.select_file_dialog_search_subfolders;
        // }
        // if (refreshRquired) {
        //   storeInstance.dispatch("setState", {
        //     property: "browse_files",
        //     value: null,
        //   });
        //   this.refreshData();
        // }
        if (this.state.browse_refresh_files) {
            storeInstance.dispatch("setState", {
                property: "browse_refresh_files",
                value: false,
            });
            this.refreshData();
        }
        if (this.state.browse_files) {
            this._marshellColumns();
            this._marshellRows();
            this.myRender();
        }
    }
    _marshellColumns() {
        //filter for active col headings
        this.columns = this.state.browse_columns.filter(function (el) {
            return el.order > 0;
        }.bind(this));
        //sort by column order
        this.columns.sort(function (a, b) {
            if (a.order < b.order) {
                return -1;
            }
            if (a.order > b.order) {
                return 1;
            }
            return 0;
        });
        //localize
        this.columns.map((col) => (col.heading = this.string[col.title]));
    }
    _marshellRows() {
        this.rows = [];
        if (this.state.browse_files) {
            this.rows = structuredClone(this.state.browse_files);
            for (var i = 0; i < this.rows.length; i++) {
                this.rows[i].selected = "";
                if (this.state.browse_selected_file) {
                    if (this.rows[i].resLblId == this.state.browse_selected_file.resLblId) {
                        this.rows[i].selected = " rowselected ";
                    }
                }
                if (util.isDitamap(this.rows[i])) {
                    this.rows[i].mapClass = " ditamap ";
                }
            }
            //apply claimed filter
        }
        this.rows.sort(util.columnSort(util.getSortColPropertyName(this.columns, "name")));
    }
    /* Templates */
    filesTemplate() {
        return html `
      <div id="wex-browse-files">
        <vaadin-context-menu
          id="contextmenu"
          .items="[]"
          selector=".has-menu"
          @item-selected="${this._handleContextMenuClick.bind(this)}"
        >
          <ui5-table
            id="filestable"
            no-data-text="${this.string["_nofiles"]}"
            ?show-no-data="${this.rows.length}"
            sticky-column-header
          >
            ${this.columns.map((col) => html `<ui5-table-column
                slot="columns"
                popin-text="${col.heading}"
              >
                <span class="colhead">
                  <span>${col.heading}</span>

                  <span class="sortbuttoncontainer">
                    <span
                      class="iconcontatiner"
                      .colproperty="${col.fieldname}"
                      title="asc"
                      @click="${this._sortColumn.bind(this)}"
                      ><iron-icon
                        class="asc ${col.sort != "asc" ? "muted" : ""}"
                        icon="vaadin:caret-up"
                      ></iron-icon
                    ></span>
                    <span
                      class="iconcontatiner"
                      .colproperty="${col.fieldname}"
                      title="desc"
                      @click="${this._sortColumn.bind(this)}"
                      ><iron-icon
                        class="desc ${col.sort != "desc" ? "muted" : ""}"
                        icon="vaadin:caret-down"
                      ></iron-icon
                    ></span>
                  </span>
                </span>
              </ui5-table-column>`)}
            <ui5-table-column
              slot="columns"
              min-width="800"
              popin-text="${this.string["_actions"]}"
            >
              <span style="line-height: 1.4rem">
                <span class="colhead">
                  <span>${this.string["_actions"]}</span>
                </span>
              </span>
            </ui5-table-column>

            ${this.rows.map((item) => html `<ui5-table-row
                class="datarow has-menu ${item.mapClass} ${item.selected}"
                .item="${item}"
                @dblclick="${this._handleDblClick.bind(this)}"
                @click="${this._handleClick.bind(this)}"
                @contextmenu="${this._handleRightClick.bind(this)}"
              >
                ${this.columns.map((col) => this._rowTemplate(col, item))}
                <ui5-table-cell align="left"
                  ><div class="rowicons">
                    <iron-icon
                      .item="${item}"
                      icon="vaadin:eye"
                      title="${this.string["_preview"]}"
                      class="${item.selected}"
                      id="preview"
                    >
                    </iron-icon>

                    <iron-icon
                      .item="${item}"
                      icon="vaadin:edit"
                      title="${this.string["_edit"]}"
                      class="${item.selected} ${util.isFileEditable(item)
            ? ""
            : "hide"}"
                      id="edit"
                    >
                    </iron-icon>

                    <ui5-icon name="activate"></ui5-icon></div
                ></ui5-table-cell>
              </ui5-table-row>`)}
          </ui5-table>
          <div
            class="flex-spacer has-menu"
            @contextmenu="${this._handleRightClick.bind(this)}"
          ></div>
        </vaadin-context-menu>
      </div>
    `;
    }
    _rowTemplate(col, item) {
        //<ui5-checkbox ?checked="${item.reviewd}" .item="${itm}" @clicked="${this._toggleReviewed.bind(this)}"></ui5-checkbox>
        if (col.fieldname == "reviewed") {
            return html `<ui5-table-cell align="center"
        ><div class="reviewedicon">
          <iron-icon
            @click="${this._toggleReviewed.bind(this)}"
            .item="${item}"
            icon="${item.reviewed ? "vaadin:circle" : "vaadin:circle-thin"}"
          ></iron-icon></div
      ></ui5-table-cell>`;
        }
        else {
            if (col.fieldname == "name") {
                return html `<ui5-table-cell align="left"
          ><div>
            <iron-icon
              icon="vaadin:file-text-o"
              id="properties"
              class="file-icon ${item.iconClass}"
              title="${this.string[item.iconTitle]}"
              data-status="${item.status}"
            ></iron-icon>
            &nbsp;${item[col.fieldname]}
          </div>
        </ui5-table-cell>`;
            }
            else {
                return html `
          <ui5-table-cell align="left">
            <div>${item[col.fieldname]}</div>
          </ui5-table-cell>
        `;
            }
        }
    }
    _sortColumn(e) {
        var direction = e.currentTarget.title;
        if (e.currentTarget.colproperty) {
            var cols = util.changeColumnSort(e.currentTarget.colproperty, direction, structuredClone(this.state.browse_columns));
            storeInstance.dispatch("setState", {
                property: "browse_columns",
                value: cols,
            });
        }
    }
    _handleDblClick(e) {
        const file = e.currentTarget.item;
        storeInstance.dispatch("checkEditFile", file);
    }
    // handler for click on actions column icon on row item
    // note, emitted upon any click
    _handleClick(e) {
        const file = this.state.browse_selected_file;
        // make sure icon is clicked target
        const path = e.composedPath();
        var iconClicked = path[0].tagName == "IRON-ICON" && !path[0].disabled;
        if (iconClicked) {
            var icon = path[0];
            switch (icon.id) {
                case "preview":
                    this._gotoPreview();
                    break;
                case "edit":
                    this._handleEdit(file);
                    break;
                case "properties":
                    if (e.currentTarget.item) {
                        if (this.state.browse_selected_file) {
                            if (this.state.browse_selected_file.resLblId !=
                                e.currentTarget.item.resLblId) {
                                storeInstance.dispatch("setState", {
                                    property: "browse_selected_file",
                                    value: e.currentTarget.item,
                                });
                            }
                        }
                        else {
                            storeInstance.dispatch("setState", {
                                property: "browse_selected_file",
                                value: e.currentTarget.item,
                            });
                        }
                        storeInstance.dispatch("filePropertiesRequest", this.state.browse_selected_file);
                        break;
                    }
            }
        }
        else {
            if (e.currentTarget.item) {
                if (this.state.browse_selected_file) {
                    if (this.state.browse_selected_file.resLblId ==
                        e.currentTarget.item.resLblId) {
                        storeInstance.dispatch("setState", {
                            property: "browse_selected_file",
                            value: null,
                        });
                        return;
                    }
                }
                storeInstance.dispatch("setState", {
                    property: "browse_selected_file",
                    value: e.currentTarget.item,
                });
            }
        }
    }
    // handler for right click on row item
    _handleRightClick(e) {
        const file = e.currentTarget.item || null;
        // modify conditional context menu items
        const effectiveCap = this.state.wex_user.globalCapability;
        let menuItems = util.buildFileMenu(file, this.state.menus.file_menu, "browse", effectiveCap);
        menuItems.map((item) => (item.text = this.string[item.label]));
        // TEMP -- disable paste if nothing is copied or cut
        // need to find a different solution
        const canPaste = !!this.state.browse_file_to_copy || !!this.state.browse_file_to_cut;
        if (!canPaste) {
            let idx = menuItems.findIndex((x) => x.name == "paste");
            if (idx >= 0)
                menuItems[idx].disabled = true;
        }
        this.contextMenu.items = menuItems;
        if (file) {
            if (this.state.browse_selected_file) {
                if (this.state.browse_selected_file.resLblId == file.resLblId) {
                    return;
                }
            }
            storeInstance.dispatch("setState", {
                property: "browse_selected_file",
                value: file,
            });
        }
        else {
            storeInstance.dispatch("setState", {
                property: "browse_selected_file",
                value: null,
            });
        }
    }
    // handler for click on right click context menu item
    _handleContextMenuClick(e) {
        const file = this.state.browse_selected_file;
        switch (e.detail.value.name) {
            case "properties":
            case "edit":
            case "edit_ditamap":
            case "open_ditamap":
            case "preview":
            case "previewnewwindow":
                storeInstance.dispatch("handleFileAction", {
                    action: e.detail.value.name,
                    file: file,
                });
                break;
            case "publish":
                console.log("publish");
                storeInstance.dispatch("setState", {
                    property: "browse_publish_dialog_open",
                    value: this.state.browse_selected_file,
                });
                break;
            case "export_files":
                storeInstance.dispatch("setState", {
                    property: "export_files_dialog_open",
                    value: this.state.browse_selected_file,
                });
                break;
            case "delete":
                if (this.state.browse_selected_file)
                    pubsub.publish("dialog-browse-delete-file-open", {
                        fileId: this.state.browse_selected_file.resLblId,
                        fileName: this.state.browse_selected_file.name,
                        branchId: this.state.browse_branch_filter.branchId,
                    });
                break;
            case "cut":
                if (this.state.browse_file_to_copy) {
                    storeInstance.dispatch("setState", {
                        property: "browse_file_to_copy",
                        value: null,
                    });
                }
                storeInstance.dispatch("setState", {
                    property: "browse_file_to_cut",
                    value: this.state.browse_selected_file,
                });
                break;
            case "copy":
                if (this.state.browse_file_to_cut) {
                    storeInstance.dispatch("setState", {
                        property: "browse_file_to_cut",
                        value: null,
                    });
                }
                storeInstance.dispatch("setState", {
                    property: "browse_file_to_copy",
                    value: this.state.browse_selected_file,
                });
                break;
            case "paste":
                let action, fileId;
                if (this.state.browse_file_to_cut) {
                    action = "cut";
                    fileId = this.state.browse_file_to_cut.resLblId;
                }
                else if (this.state.browse_file_to_copy) {
                    action = "copy";
                    fileId = this.state.browse_file_to_copy.resLblId;
                }
                storeInstance.dispatch("browsePasteFile", {
                    action: action,
                    fileId: fileId,
                    folderId: this.state.browse_selected_folder.id,
                });
                break;
            case "rename":
                const fileNames = this.rows.map((x) => x.name);
                if (this.state.browse_selected_file)
                    pubsub.publish("dialog-browse-rename-file-open", {
                        file: this.state.browse_selected_file,
                        siblings: fileNames,
                    });
                break;
        }
    }
    _gotoPreview() {
        Router.go("/wex/" +
            this.state.langCode +
            "/preview?resLblId=" +
            this.state.browse_selected_file.resLblId +
            "&aod=" +
            this.state.browse_selected_file.verCreateDate +
            "&previewStyle=html");
    }
    _handleEdit(file) {
        storeInstance.dispatch("checkEditFile", file);
    }
}
customElements.define("wex-browse-files", WexBrowseFiles);
//# sourceMappingURL=wex-browse-files.js.map