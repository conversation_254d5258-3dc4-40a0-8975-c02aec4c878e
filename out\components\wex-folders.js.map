{"version": 3, "file": "wex-folders.js", "sourceRoot": "", "sources": ["../../src/components/wex-folders.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAkB,MAAM,KAAK,CAAC;AAC5D,OAAO,EAAE,aAAa,IAAI,KAAK,EAAE,MAAM,gBAAgB,CAAC;AAExD,OAAO,sBAAsB,CAAC;AAC9B,OAAO,iCAAiC,CAAC;AACzC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI5D,IAAM,UAAU,GAAhB,MAAM,UAAW,SAAQ,UAAU;IAWjC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;KAMT,CAAC;IACJ,CAAC;IAED;QACE,KAAK,EAAE,CAAC;QAjBV,mBAAc,GAAsB,IAAI,CAAC;QAEzC,gBAAW,GAAY,KAAK,CAAC;QAgB3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,IAAI,WAAW,CAAC,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5C,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED,WAAW,CAAC,KAA0B;QACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IAES,OAAO,CAAC,iBAAiC;QACjD,IAAI,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;IACH,CAAC;IAED,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM;QAC9B,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACjB,CAAC;QACD,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,kBAAkB;QAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;gBACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,CAAC;QAClB,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED,gBAAgB;QACd,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAA;;;;wBAIS,IAAI,CAAC,WAAW;iBACvB,IAAI,CAAC,OAAO;mBACV,IAAI,CAAC,EAAE;wBACF,IAAI,CAAC,WAAW;0BACd,IAAI,CAAC,gBAAgB;4BACnB,IAAI,CAAC,gBAAgB;;;UAGvC,CAAC;IACT,CAAC;CACF,CAAA;AA1GC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;kDACc;AAEzC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;+CACC;AAE7B;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;2CACK;AAT5B,UAAU;IADf,aAAa,CAAC,aAAa,CAAC;GACvB,UAAU,CA+Gf", "sourcesContent": ["import { LitElement, html, css, PropertyValues } from \"lit\";\r\nimport { storeInstance as Store } from \"store/index.js\";\r\n\r\nimport \"./wex-folder-item.ts\";\r\nimport \"@ui5/webcomponents/dist/Icon.js\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\nimport { Collection } from \"@bds/types\";\r\n\r\n@customElement(\"wex-folders\")\r\nclass WexFolders extends LitElement {\r\n  state: any;\r\n  subscription: any;\r\n  string: Record<string, string>;\r\n  @property({ type: Object })\r\n  selectedfolder: Collection | null = null;\r\n  @property({ type: Boolean })\r\n  isDraggable: boolean = false;\r\n  @property({ type: Object })\r\n  folders: Collection | undefined;\r\n\r\n  static get styles() {\r\n    return css`\r\n      #foldertree {\r\n        list-style: none;\r\n        padding: 0;\r\n        margin: 0;\r\n      }\r\n    `;\r\n  }\r\n\r\n  constructor() {\r\n    super();\r\n    this.state = null;\r\n    this.string = {};\r\n    this.subscription = null;\r\n    this.isDraggable = false;\r\n  }\r\n\r\n  set contextmenu(val) {\r\n    this.contextMenu = val;\r\n    this._prepPaths(this.folders, null, this);\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this.state = Store.state;\r\n    this.subscription = Store.subscribe((state) => {\r\n      this.stateChange(state);\r\n    });\r\n    this.string = this.state[this.state.langCode];\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    Store.unsubscribe(this.subscription);\r\n  }\r\n\r\n  stateChange(state: Record<string, any>) {\r\n    this.state = state;\r\n    this.string = this.state[this.state.langCode];\r\n  }\r\n\r\n  protected updated(changedProperties: PropertyValues) {\r\n    if (changedProperties.has(\"selectedfolder\")) {\r\n      if (this.folders) {\r\n        this._prepPaths(this.folders, null, this);\r\n      }\r\n    }\r\n  }\r\n\r\n  _prepPaths(item, path, c, parent) {\r\n    if (path) {\r\n      item.path = path + item.name;\r\n    } else {\r\n      item.path = \"\";\r\n    }\r\n    if (parent) {\r\n      item.parent = parent;\r\n    }\r\n    item.selected = false;\r\n    //item.open=false;\r\n    if (this.selectedfolder) {\r\n      if (item.id == this.selectedfolder.id) {\r\n        item.selected = true;\r\n        this._openParents(item, c);\r\n      }\r\n    }\r\n\r\n    if (item.children) {\r\n      item.children.map((itm) => c._prepPaths(itm, item.path + \"/\", c, item));\r\n    }\r\n  }\r\n\r\n  _openParents(item, c) {\r\n    if (item) {\r\n      item.open = true;\r\n    }\r\n    if (item.parent) {\r\n      c._openParents(item.parent, c);\r\n    }\r\n  }\r\n\r\n  handleTreeChange() {\r\n    this._prepPaths(this.folders, null, this);\r\n  }\r\n\r\n  render() {\r\n    return html`<ul id=\"foldertree\">\r\n      <wex-folder-item\r\n        id=\"treerootnode\"\r\n        .rootnode=\"true\"\r\n        .contextmenu=\"${this.contextMenu}\"\r\n        .item=\"${this.folders}\"\r\n        .origen=\"${this.id}\"\r\n        .isDraggable=\"${this.isDraggable}\"\r\n        @folderselected=${this.handleTreeChange}\r\n        @folderopenstatus=${this.handleTreeChange}\r\n      >\r\n      </wex-folder-item>\r\n    </ul>`;\r\n  }\r\n}\r\n"]}