import { LitElement, html } from "lit";
import { globalStoreContext } from "store/context";
import { Router } from "@vaadin/router/dist/vaadin-router.js";
import * as util from "lib/util.ts";

import "@ui5/webcomponents-compat/dist/Table.js";
import "@ui5/webcomponents-compat/dist/TableRow.js";
import "@ui5/webcomponents-compat/dist/TableCell.js";
import "@ui5/webcomponents-compat/dist/TableColumn.js";
import "@vaadin/vaadin-context-menu/vaadin-context-menu.js";
import "@vaadin/vaadin-list-box/vaadin-list-box.js";
import "@vaadin/vaadin-item/vaadin-item.js";
import "@ui5/webcomponents/dist/RadioButton";
import { customElement, property } from "lit/decorators.js";
import { Task } from "@bds/types";
import { FileMeta } from "@bds/types";
import { consume } from "@lit/context";

@customElement("wex-task-filepackage")
export class WexTaskFilepackage extends LitElement {
  @consume({context: globalStoreContext})
  store: any;

  subscription: any;
  state: any;
  string: Record<string, string> = {};
  primaryRadioBtn: Element | null = null;
  @property({ type: Array })
  contextMenuItems: MenuItem[] = [];
  @property({ type: Boolean })
  isPrimarySelected: boolean = true;
  contextMenu: Element | null = null;

  /**
   * Currently selected workflow tab
   */
  @property({ type: String })
  view: "task" | "process" = "task";
  /**
   * Task selected in the active tasks table
   */
  @property({ type: Object })
  selectedTask: Task | null = null;
  /**
   * author: 4,  contributor: 3,  review: 2,  default: 1
   */
  @property({ type: String })
  effectiveCap: string = "default";

  @property({ type: Object })
  claimedTasks: Task[] = [];
  @property({ type: Array })
  columns: Column[] = [];
  @property({ type: Array })
  rows: FileMeta[] = [];
  @property({ type: Array })
  fileList: FileMeta[] = [];
  @property({ type: Object })
  selectedFile: FileMeta | null = null;

  connectedCallback() {
    super.connectedCallback();
    this.subscription = this.store.subscribe((state: any) => {
      /*WexTaskFilepackage*/
      this.stateChange(state);
    });
    this.state = this.store.state;
    this.isPrimarySelected = this.state.filepackage_display_primary;
  }

  disconnectedCallback() {
    this.store.unsubscribe(this.subscription);
  }

  updated(changedProperties: Map<string, any>) {
    if (changedProperties.has("selectedTask")) {
      if (this.selectedTask) {
        this.store.dispatch("getFilepackage", this.selectedTask);
        this.effectiveCap = util.getEffectiveCap(
          this.selectedTask.userWexCapability
        );
      }
    }
  }

  firstUpdated() {
    this._marshellColumns();
    this._marshellRows();
    if (this.shadowRoot) {
      this.contextMenu = this.shadowRoot.querySelector("#contextmenu");
      this.primaryRadioBtn = this.shadowRoot.querySelector("#primary");
    }
  }

  stateChange(state: any) {
    this.state = state;
    this.string = this.state[this.state.langCode];
    this.lang = this.state.langCode;
    this.claimedTasks = this.state.task_task_list.filter(
      (task: Task) => task.isClaimed
    );
    this.fileList = this.state.filepackage_file_list;
    this.selectedFile = this.state.task_selected_file;
    this.isPrimarySelected = this.state.filepackage_display_primary;
    this._marshellColumns();
    this._marshellRows();
  }

  _marshellColumns() {
    //filter for active col headings
    this.columns = this.state.task_filepackage_columns.filter(
      function (el) {
        return el.order > 0;
      }.bind(this)
    );
    //sort by column order
    this.columns.sort(function (a, b) {
      if (a.order < b.order) {
        return -1;
      }
      if (a.order > b.order) {
        return 1;
      }
      return 0;
    });
    //localize
    this.columns.map((col) => (col.heading = this.string[col.title]));

    /*  find sort column
                first set the defaults
        */
    this.sortProperty = "taskInstId";
    /*      Then check is sort is set on any columns */
    for (var i = 0; i < this.columns.length; i++) {
      if (this.columns[i].sort != "none") {
        if (this.columns[i].sort == "asc") {
          this.sortProperty = this.columns[i].fieldname;
        } else {
          this.sortProperty = "-" + this.columns[i].fieldname;
        }
      }
    }
  }

  _marshellRows() {
    this.rows = [];
    if (this.fileList) {
      this.rows = structuredClone(this.fileList);
      for (var i = 0; i < this.rows.length; i++) {
        this.rows[i].selected = "";
        if (this.selectedFile) {
          if (this.rows[i].resLblId == this.selectedFile.resLblId) {
            this.rows[i].selected = " rowselected ";
          }
        }
        if (util.isDitamap(this.rows[i])) {
          this.rows[i].mapClass = " ditamap ";
        }
      }
      //apply claimed filter
    }
    this.rows.sort(
      util.columnSort(util.getSortColPropertyName(this.columns, "name"))
    );
  }

  _isClaimed() {
    return this.selectedTask.isClaimed;
  }

  _addFileButton() {
    if (util.can(this.effectiveCap, "filePackageAdd")) {
      return html`<ui5-button
        id="addfile"
        @click="${this._filePackageAction.bind(this)}"
        class=" ui5button ui5-content-density-compact"
        >${this.string["_addfile"]}</ui5-button
      >`;
    }
  }

  _importFileButton() {
    if (util.can(this.effectiveCap, "filePackageImport")) {
      return html`<ui5-button
        id="importfile"
        @click="${this._filePackageAction.bind(this)}"
        class="ui5button ui5-content-density-compact"
        >${this.string["_importfile"]}</ui5-button
      >`;
    }
  }
  _createFileButton() {
    if (util.can(this.effectiveCap, "filePackageCreate")) {
      return html`<ui5-button
        id="createfile"
        @click="${this._filePackageAction.bind(this)}"
        class="ui5button ui5-content-density-compact"
        >${this.string["_createfile"]}</ui5-button
      >`;
    }
  }

  _colHeading(col) {
    var retval = col.heading;
    return retval;
  }

  /* Templates */
  render() {
    return html`
      <style>
        .lockedbyother {
          color: red;
        }
        .lockedbyselfhere {
          color: green;
        }
        .lockedbyselfelsewhere {
          color: orange;
        }

        .rowselected {
          --sapList_Background: var(--row-selected-background);
        }
        #notasks {
          font-style: oblique;
          opacity: 0.5;
          text-align: center;
          margin-top: 50px;
        }
        .datarow {
          cursor: pointer;
        }
        .ditamap {
          font-weight: bold;
        }

        #filepackageheader {
          padding: var(--spacing-xs);
          background-color: var(--secondary);
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        #filepackagetablecontainer {
          height: ${this.state.global_screen_specs
            .task_tabsSectionContentHeight}px !important;
          overflow-y: auto;
        }
        #filerole {
          display: inline-block;
          margin-right: 3rem;
        }
        #filerole ui5-radio-button {
          --sapContent_LabelColor: #fff;
        }
        .reviewedicon {
          --iron-icon-width: 16px;
        }
        .hide {
          visibility: hidden;
        }
        .sortbuttoncontainer {
          display: flex;
          flex-direction: column;
        }
        .sortbuttoncontainer iron-icon {
          margin-left: 0.25rem;
          --iron-icon-width: 0.9rem;
        }
        iron-icon.asc {
          margin-bottom: -0.6rem;
        }
        iron-icon.desc {
          margin-top: -0.6rem;
        }
        iron-icon.muted {
          color: #ccc;
        }

        .colhead {
          line-height: 1.4rem;
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .fileicon {
          width: 1rem;
        }
        .fileicon:hover {
          color: #000;
        }

        .rowicons iron-icon {
          width: 1rem;
          padding-right: 30px;
          display: none;
        }
        .rowicons iron-icon:hover {
          color: #000;
        }

        .iconcontatiner {
          height: 1.4rem;
          overflow: hidden;
        }

        .rowicons iron-icon.rowselected {
          display: inline-block;
        }

        .rowicons iron-icon.disabled {
          cursor: default;
          color: #cfd4db;
        }
        .rowicons iron-icon.disabled:hover {
          color: #cfd4db;
        }
      </style>

      <div id="filepackageheader">
        <div id="filerole">
          <ui5-radio-button
            id="primary"
            name="filerole"
            text="${this.string["_primary"]}"
            @change="${this._fileRoleChange.bind(this)}"
            ?checked="${this.isPrimarySelected}"
          ></ui5-radio-button>
          <ui5-radio-button
            id="refernce"
            name="filerole"
            text="${this.string["_reference"]}"
            @change="${this._fileRoleChange.bind(this)}"
            ?checked="${!this.isPrimarySelected}"
          ></ui5-radio-button>
        </div>
        ${this._addFileButton()} ${this._importFileButton()}
        ${this._createFileButton()}
      </div>

      <div id="filepackagetablecontainer">
        <vaadin-context-menu
          id="contextmenu"
          selector=".has-menu"
          .items="${this.contextMenuItems}"
          @item-selected="${this._contextmenuclicked.bind(this)}"
        >
          <ui5-table
            id="filepackagetable"
            no-data-text="${this.string["_nofiles"]}"
            ?show-no-data="${this.rows.length == 0}"
            sticky-column-header
          >
            ${this.columns.map(
              (col) => html`
                <ui5-table-column
                  class="foo"
                  slot="columns"
                  min-width="800"
                  popin-text="${col.heading}"
                >
                  <span style="line-height: 1.4rem">
                    <span class="colhead">
                      <span>${this._colHeading(col)}</span>

                      <span class="sortbuttoncontainer">
                        <span
                          class="iconcontatiner"
                          .colproperty="${col.fieldname}"
                          title="asc"
                          @click="${this._sortColumn.bind(this)}"
                          ><iron-icon
                            class="asc ${col.sort != "asc" ? "muted" : ""}"
                            icon="vaadin:caret-up"
                          ></iron-icon
                        ></span>
                        <span
                          class="iconcontatiner"
                          .colproperty="${col.fieldname}"
                          title="desc"
                          @click="${this._sortColumn.bind(this)}"
                          ><iron-icon
                            class="desc ${col.sort != "desc" ? "muted" : ""}"
                            icon="vaadin:caret-down"
                          ></iron-icon
                        ></span>
                      </span>
                    </span>
                  </span>
                </ui5-table-column>
              `
            )}
            <ui5-table-column
              class="foo"
              slot="columns"
              min-width="800"
              popin-text="${this.string["_actions"]}"
            >
              <span style="line-height: 1.4rem">
                <span class="colhead">
                  <span>${this.string["_actions"]}</span>
                </span>
              </span>
            </ui5-table-column>

            ${this.rows.map(
              (item) =>
                html` <ui5-table-row
                  data-reslblid=${item.resLblId}
                  class="datarow has-menu ${item.mapClass}  ${item.selected}"
                  .item="${item}"
                  @dblclick="${this._defaultAction.bind(this)}"
                  @click="${this._handleRowClicked.bind(this)}"
                  @contextmenu="${this._handleRowRightClicked.bind(this)}"
                >
                  ${this.columns.map((col) => this._rowTemplate(col, item))}

                  <ui5-table-cell align="left"
                    ><div class="rowicons">
                      <iron-icon
                        .item="${item}"
                        icon="vaadin:eye"
                        title="${this.string["_preview"]}"
                        class="${item.selected}"
                        id="preview"
                      >
                      </iron-icon>
                      <iron-icon
                        .item="${item}"
                        icon="vaadin:edit"
                        title="${this.string["_edit"]}  "
                        class="${item.selected} ${util.isFileEditable(item)
                          ? ""
                          : "hide"}"
                        id="edit"
                      >
                      </iron-icon>
                      <ui5-icon name="activate"></ui5-icon></div
                  ></ui5-table-cell>
                </ui5-table-row>`
            )}
          </ui5-table>
        </vaadin-context-menu>
      </div>
    `;
  }

  _sortColumn(e) {
    var direction = e.currentTarget.title;
    if (e.currentTarget.colproperty) {
      var cols = util.changeColumnSort(
        e.currentTarget.colproperty,
        direction,
        structuredClone(this.state.task_filepackage_columns)
      );
      this.store.dispatch("setState", {
        property: "task_filepackage_columns",
        value: cols,
      });
    }
  }

  _rowTemplate(col, itm) {
    //<ui5-checkbox ?checked="${itm.reviewd}" .item="${itm}" @clicked="${this._toggleReviewed.bind(this)}"></ui5-checkbox>
    if (col.fieldname == "reviewed") {
      return html`<ui5-table-cell align="center"
        ><div class="reviewedicon">
          <iron-icon
            @click="${this._toggleReviewed.bind(this)}"
            .item="${itm}"
            icon="${itm.reviewed ? "vaadin:circle" : "vaadin:circle-thin"}"
          ></iron-icon></div
      ></ui5-table-cell>`;
    } else {
      if (col.fieldname == "name") {
        return html`<ui5-table-cell align="left"
          ><div>
            <iron-icon
              icon="vaadin:file-text-o"
              id="properties"
              class="fileicon ${itm.iconClass}"
              title="${this.string["_properties"]}"
            >
            </iron-icon>
            &nbsp;${itm[col.fieldname]}
          </div>
        </ui5-table-cell>`;
      } else {
        return html`<ui5-table-cell align="left"
          ><div>&nbsp;${itm[col.fieldname]}</div></ui5-table-cell
        >`;
      }
    }
  }

  _toggleReviewed(e) {
    e.preventDefault();
    e.stopPropagation();

    if (!this.selectedTask) {
      util.notifyUser("warn", "Task not selected");
      return;
    }

    if (this.selectedTask.isClaimed) {
      this._toggleReviewedAction(this.selectedTask, e.currentTarget.item);
    } else {
      util.notifyUser("warn", this.string["_notclaimed"]);
    }
  }

  _toggleReviewedAction(selectedTask: Task, selectedFile: FileMeta) {
    var payload = {
      origin: "task",
      processInstId: selectedTask.processInstId,
      TaskDefId: selectedTask.taskId,
      resLblId: selectedFile.resLblId,
      reviewed: !selectedFile.reviewed,
    };
    this.store.dispatch("setTaskFileReviewedStatus", payload);
  }

  _fileRoleChange() {
    this.store.dispatch("setState", {
      property: "filepackage_display_primary",
      value: !this.isPrimarySelected,
    });
    this.store.dispatch("getFilepackage", this.selectedTask);
  }

  addFileCallBack(addFile) {
    var fileRole = this.isPrimarySelected ? 1 : 2;
    this.store.dispatch("addFileToPackage", {
      fileRole: fileRole,
      file: addFile,
      task: this.selectedTask,
    });
    this.store.dispatch("setState", {
      property: "select_file_dialog_open",
      value: null,
    });
  }

  createFileCallBack() {
    var taskProject = this.state.global_projects.filter(
      function (prj) {
        return prj.projectId == this.selectedTask.projectId;
      }.bind(this)
    );
    if (taskProject[0]) {
      if (taskProject[0].hasOwnProperty("projectContributeCollectionId")) {
        var collectionId = taskProject[0].projectContributeCollectionId;
      }
    }
    var payload = {
      filename: this.state.task_createfile_name,
      templateId: this.state.task_createfile_template.resId,
      collectionId: collectionId,
    };
    this.store.dispatch("taskCreateFile", payload);
    this.store.dispatch("setState", {
      property: "create_file_dialog_open",
      value: null,
    });
  }

  _filePackageAction(e) {
    if (this.selectedTask.isClaimed) {
      switch (e.currentTarget.id) {
        case "addfile":
          this.store.dispatch("setState", {
            property: "select_file_dialog_open",
            value: { callback: this.addFileCallBack.bind(this) },
          });
          break;
        case "importfile":
          this.store.dispatch("setState", {
            property: "import_dialog_open",
            value: this.selectedTask,
          });
          break;
        case "createfile":
          var tmpLockFolders = false;

          if (this.selectedTask.userWexCapability == "Contribute") {
            tmpLockFolders = true;
          }
          var prj = this._getProjectFromCurrentTask();
          var fldr = null;
          if (prj) {
            fldr = util._getFolderObjectFromId(
              this.state.global_folders,
              prj.projectContributeCollectionId
            );
            //fldr = this._findFolderTest(this.state.global_folders, prj.projectContributeCollectionId);
          }

          if (fldr) {
            this.store.dispatch("setState", {
              property: "extended_folder_create_file_dialog_open",
              value: {
                target: "package",
                folder: fldr,
                branch: util.getBranchObjectFromId(
                  this.state.global_branches,
                  prj.branchId
                ),
                lang: util.getLangObjectFromId(
                  this.state.global_langs,
                  prj.languageId
                ),
                lockedFolders: tmpLockFolders,
                task: this.selectedTask,
                primaryrole: this.isPrimarySelected,
              },
            });
          }
          /*
                    var tmpFolder = this._getrFolderFromCurrentTask();
                    if(this.tmpFolder){
                        this.store.dispatch("setState", {property: "extended_folder_create_file_dialog_open", value:{target: "package", payload: this.tmpFolder } } )
                    }
                    //this.store.dispatch("setState", {property: "create_file_dialog_open" , value:  {callback: this.createFileCallBack.bind(this) }} )
                  */
          break;
      }
    } else {
      util.notifyUser("warn", this.string["_notclaimed"]);
    }
  }

  _defaultAction(e) {
    var fileItem = e.currentTarget.item;
    this.store.dispatch("setState", {
      property: "task_selected_file",
      value: fileItem,
    });
    setTimeout(
      function () {
        if (this.selectedTask.isClaimed) {
          this._editAction();
        } else {
          util.notifyUser("warn", this.string["_notclaimed"]);
        }
      }.bind(this),
      100
    );
  }

  _contextmenuclicked(e) {
    const action = e.detail.value.name;
    const file = this.selectedFile;
    switch (action) {
      case "properties":
      case "preview":
      case "previewnewwindow":
      case "open_ditamap":
      case "openfilelocation":
        this.store.dispatch("handleFileAction", {
          action: action,
          file: file,
        });
        break;
      case "edit_ditamap":
      case "edit":
        if (this.selectedTask.isClaimed) {
          this._editAction();
        } else {
          util.notifyUser("warn", this.string["_notclaimed"]);
        }
        break;
      case "remove":
        this.store.dispatch("removeFileFromPackage", { ownOnly: true });
        break;
      case "toggledone":
        if (this.selectedTask.isClaimed) {
          this._toggleReviewedAction(this.selectedTask, this.itemRightClicked); //The reviewed status can be changed without updateing the item, so must use the captured item from the right click
        } else {
          util.notifyUser("warn", this.string["_notclaimed"]);
        }
        break;
    }
  }

  _editAction() {
    this.store.dispatch("setState", {
      property: "editor_selected_task",
      value: structuredClone(this.selectedTask),
    });
    this.store.dispatch("setState", {
      property: "editor_selected_file",
      value: structuredClone(this.selectedFile),
    });
    this.store.dispatch("checkEditFile", this.selectedFile);
    this.store.dispatch("setState", {
      property: "editor_side_active_tab",
      value: "tasks",
    });
    // if (util.isDitamap(this.selectedFile)) {
    //   this.store.dispatch(
    //     "editDitamapRequest",
    //     this.selectedFile
    //   );
    //   this.store.dispatch("setState", {
    //     property: "editor_side_active_tab",
    //     value: "ditamaps",
    //   });
    // } else {
    //   this.store.dispatch(
    //     "checkEditFile",
    //     this.selectedFile
    //   );
    //   this.store.dispatch("setState", {
    //     property: "editor_side_active_tab",
    //     value: "tasks",
    //   });
    // }
    // Router.go("/wex/" + this.state.langCode + "/edit");
    // this.store.dispatch("touch");
  }

  _handleRowRightClicked(e) {
    //modify conditional context menu items
    console.log(e.currentTarget.item);
    if (e.currentTarget.item) {
      this.itemRightClicked = e.currentTarget.item;
      var menuItems = util.buildFileMenu(
        e.currentTarget.item,
        this.state.menus.file_menu,
        "filepackage",
        this.state.wex_user.globalCapability,
        this.selectedTask.userWexCapability,
        this.selectedTask.isClaimed
      );
      this.contextMenuItems = menuItems.map((item) => ({
        ...item,
        text: this.string[item.label],
      }));

      if (this.selectedFile) {
        if (this.selectedFile.resLblId == e.currentTarget.item.resLblId) {
          return;
        }
      }
      this.store.dispatch("setState", {
        property: "task_selected_file",
        value: e.currentTarget.item,
      });
    }
  }

  _handleRowClicked(e) {
    const path = e.composedPath();
    var iconClicked = path[0].tagName == "IRON-ICON" && !path[0].disabled;
    console.log(iconClicked);
    if (iconClicked) {
      const action = path[0].id;
      const file = this.selectedFile;
      switch (action) {
        case "preview":
          this.store.dispatch("handleFileAction", {
            action: action,
            file: file,
            options: null,
          });
          break;
        case "edit":
          if (this.selectedTask.isClaimed) {
            this._editAction();
          } else {
            util.notifyUser("warn", this.string["_notclaimed"]);
          }
          break;
        case "properties":
          if (e.currentTarget.item) {
            if (this.selectedFile) {
              if (this.selectedFile.resLblId != e.currentTarget.item.resLblId) {
                this.store.dispatch("setState", {
                  property: "task_selected_file",
                  value: e.currentTarget.item,
                });
              }
            } else {
              this.store.dispatch("setState", {
                property: "task_selected_file",
                value: e.currentTarget.item,
              });
            }
            this.store.dispatch("handleFileAction", {
              action: action,
              file: file,
              options: null,
            });
            break;
          }
      }
    } else {
      if (e.currentTarget.item) {
        if (this.selectedFile) {
          if (this.selectedFile.resLblId == e.currentTarget.item.resLblId) {
            this.store.dispatch("setState", {
              property: "task_selected_file",
              value: null,
            });
            return;
          }
        }
        this.store.dispatch("setState", {
          property: "task_selected_file",
          value: e.currentTarget.item,
        });
      }
    }
  }

  _getProjectFromCurrentTask() {
    var prj = null;
    for (var i = 0; i < this.state.global_projects.length; i++) {
      if (
        this.state.global_projects[i].projectId == this.selectedTask.projectId
      ) {
        prj = this.state.global_projects[i];
        break;
      }
    }
    if (prj) {
      return prj;
    }
  }

  _getrFolderFromCurrentTask() {
    var collectionId = null;
    for (var i = 0; i < this.state.global_projects.length; i++) {
      if (
        this.state.global_projects[i].projectId == this.selectedTask.projectId
      ) {
        collectionId =
          this.state.global_projects[i].projectContributeCollectionId;
        break;
      }
    }
    if (collectionId) {
      return util._getFolderObjectFromId(
        this.state.global_folders,
        collectionId
      );
      //return this._findFolderTest(this.state.global_folders, collectionId )
    }
  }

  _folderById(node, collId, c) {
    if (!c.fldrFound) {
      var retval = null;
      if (node.id == collId) {
        retval = node;
        c.fldrFound = node;
      } else {
        if (node.children) {
          for (var i = 0; i < node.children.length; i++) {
            return (retval = c._folderById(node.children[i], collId, c));
          }
        }
      }
      if (c.fldrFound) {
        return retval;
      }
    }
  }

  _findFolderTest(root, collId) {
    var stack = [],
      node,
      ii;
    stack.push(root);

    while (stack.length > 0) {
      node = stack.pop();
      if (node.id == collId) {
        // Found it!
        return node;
      } else if (node.children && node.children.length) {
        for (ii = 0; ii < node.children.length; ii += 1) {
          stack.push(node.children[ii]);
        }
      }
    }
    // Didn't find it. Return null.
    return null;
  }
}

interface Column {
  title: string;
  fieldname: string;
  order: number;
  sort: "asc" | "desc" | "" | "muted";
  heading: string;
}

interface MenuItem {
  name: string;
  label: string;
}
