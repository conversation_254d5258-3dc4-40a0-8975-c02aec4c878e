{"version": 3, "file": "provide.js", "sourceRoot": "", "sources": ["../../../src/lib/decorators/provide.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAC,eAAe,EAAC,MAAM,oCAAoC,CAAC;AAEnE;;;;;GAKG;AAEH;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,OAAO,CAAY,EACjC,OAAO,EAAE,OAAO,GAGjB;IACC,OAAO,CAAC,CACN,aAAuE,EACvE,aAE6D,EAC7D,EAAE;QACF,kCAAkC;QAClC,MAAM,aAAa,GAAG,IAAI,OAAO,EAG9B,CAAC;QACJ,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YACtC,6BAA6B;YAC7B,OAAO;gBACL,GAAG;oBACD,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtC,CAAC;gBACD,GAAG,CAAwB,KAAgB;oBACzC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACzC,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC7C,CAAC;gBACD,IAAI,CAAwB,KAAgB;oBAC1C,aAAa,CAAC,GAAG,CACf,IAAI,EACJ,IAAI,eAAe,CAAC,IAAI,EAAE,EAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAC,CAAC,CAC1D,CAAC;oBACF,OAAO,KAAK,CAAC;gBACf,CAAC;aACF,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,iCAAiC;YAChC,aAAa,CAAC,WAAsC,CAAC,cAAc,CAClE,CAAC,OAAwB,EAAQ,EAAE;gBACjC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,eAAe,CAAC,OAAO,EAAE,EAAC,OAAO,EAAC,CAAC,CAAC,CAAC;YACtE,CAAC,CACF,CAAC;YACF,4DAA4D;YAC5D,4CAA4C;YAC5C,MAAM,UAAU,GAAG,MAAM,CAAC,wBAAwB,CAChD,aAAa,EACb,aAAa,CACd,CAAC;YACF,IAAI,aAAiC,CAAC;YACtC,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;gBAC7B,MAAM,QAAQ,GAAG,IAAI,OAAO,EAA8B,CAAC;gBAC3D,aAAa,GAAG;oBACd,GAAG;wBACD,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC5B,CAAC;oBACD,GAAG,CAAwB,KAAgB;wBACzC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;wBACzC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBAC5B,CAAC;oBACD,YAAY,EAAE,IAAI;oBAClB,UAAU,EAAE,IAAI;iBACjB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC;gBACjC,aAAa,GAAG;oBACd,GAAG,UAAU;oBACb,GAAG,CAAwB,KAAgB;wBACzC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;wBACzC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBAC/B,CAAC;iBACF,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;IACH,CAAC,CAAgC,CAAC;AACpC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ReactiveElement} from '@lit/reactive-element';\nimport {Context} from '../create-context.js';\nimport {ContextProvider} from '../controllers/context-provider.js';\n\n/*\n * IMPORTANT: For compatibility with t<PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\n/**\n * A property decorator that adds a ContextProvider controller to the component\n * making it respond to any `context-request` events from its children consumer.\n *\n * @param context A Context identifier value created via `createContext`\n *\n * @example\n *\n * ```ts\n * import {provide} from '@lit/context';\n * import {Logger} from 'my-logging-library';\n * import {loggerContext} from './logger-context.js';\n *\n * class MyElement {\n *   @provide({context: loggerContext})\n *   logger = new Logger();\n * }\n * ```\n * @category Decorator\n */\nexport function provide<ValueType>({\n  context: context,\n}: {\n  context: Context<unknown, ValueType>;\n}): ProvideDecorator<ValueType> {\n  return ((\n    protoOrTarget: ClassAccessorDecoratorTarget<ReactiveElement, ValueType>,\n    nameOrContext:\n      | PropertyKey\n      | ClassAccessorDecoratorContext<ReactiveElement, ValueType>\n  ) => {\n    // Map of instances to controllers\n    const controllerMap = new WeakMap<\n      ReactiveElement,\n      ContextProvider<Context<unknown, ValueType>>\n    >();\n    if (typeof nameOrContext === 'object') {\n      // Standard decorators branch\n      return {\n        get(this: ReactiveElement) {\n          return protoOrTarget.get.call(this);\n        },\n        set(this: ReactiveElement, value: ValueType) {\n          controllerMap.get(this)!.setValue(value);\n          return protoOrTarget.set.call(this, value);\n        },\n        init(this: ReactiveElement, value: ValueType) {\n          controllerMap.set(\n            this,\n            new ContextProvider(this, {context, initialValue: value})\n          );\n          return value;\n        },\n      };\n    } else {\n      // Experimental decorators branch\n      (protoOrTarget.constructor as typeof ReactiveElement).addInitializer(\n        (element: ReactiveElement): void => {\n          controllerMap.set(element, new ContextProvider(element, {context}));\n        }\n      );\n      // proxy any existing setter for this property and use it to\n      // notify the controller of an updated value\n      const descriptor = Object.getOwnPropertyDescriptor(\n        protoOrTarget,\n        nameOrContext\n      );\n      let newDescriptor: PropertyDescriptor;\n      if (descriptor === undefined) {\n        const valueMap = new WeakMap<ReactiveElement, ValueType>();\n        newDescriptor = {\n          get(this: ReactiveElement) {\n            return valueMap.get(this);\n          },\n          set(this: ReactiveElement, value: ValueType) {\n            controllerMap.get(this)!.setValue(value);\n            valueMap.set(this, value);\n          },\n          configurable: true,\n          enumerable: true,\n        };\n      } else {\n        const oldSetter = descriptor.set;\n        newDescriptor = {\n          ...descriptor,\n          set(this: ReactiveElement, value: ValueType) {\n            controllerMap.get(this)!.setValue(value);\n            oldSetter?.call(this, value);\n          },\n        };\n      }\n      Object.defineProperty(protoOrTarget, nameOrContext, newDescriptor);\n      return;\n    }\n  }) as ProvideDecorator<ValueType>;\n}\n\n/**\n * Generates a public interface type that removes private and protected fields.\n * This allows accepting otherwise compatible versions of the type (e.g. from\n * multiple copies of the same package in `node_modules`).\n */\ntype Interface<T> = {\n  [K in keyof T]: T[K];\n};\n\ntype ProvideDecorator<ContextType> = {\n  // legacy\n  <\n    K extends PropertyKey,\n    Proto extends Interface<Omit<ReactiveElement, 'renderRoot'>>,\n  >(\n    protoOrDescriptor: Proto,\n    name?: K\n  ): FieldMustMatchContextType<Proto, K, ContextType>;\n\n  // standard\n  <\n    C extends Interface<Omit<ReactiveElement, 'renderRoot'>>,\n    V extends ContextType,\n  >(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): void;\n};\n\n// Note TypeScript requires the return type of a decorator to be `void | any`\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype DecoratorReturn = void | any;\n\ntype FieldMustMatchContextType<Obj, Key extends PropertyKey, ContextType> =\n  // First we check whether the object has the property as a required field\n  Obj extends Record<Key, infer ProvidingType>\n    ? // Ok, it does, just check whether it's ok to assign the\n      // provided type to the consuming field\n      [ProvidingType] extends [ContextType]\n      ? DecoratorReturn\n      : {\n          message: 'providing field not assignable to context';\n          context: ContextType;\n          provided: ProvidingType;\n        }\n    : // Next we check whether the object has the property as an optional field\n      Obj extends Partial<Record<Key, infer Providing>>\n      ? // Check assignability again. Note that we have to include undefined\n        // here on the providing type because it's optional.\n        [Providing | undefined] extends [ContextType]\n        ? DecoratorReturn\n        : {\n            message: 'providing field not assignable to context';\n            context: ContextType;\n            consuming: Providing | undefined;\n          }\n      : // Ok, the field isn't present, so either someone's using provide\n        // manually, i.e. not as a decorator (maybe don't do that! but if you do,\n        // you're on your own for your type checking, sorry), or the field is\n        // private, in which case we can't check it.\n        DecoratorReturn;\n"]}