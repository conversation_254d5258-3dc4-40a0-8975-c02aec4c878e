{"version": 3, "file": "ditamap-tree.d.ts", "sourceRoot": "", "sources": ["../../../src/lib/jsDitamap/ditamap-tree.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAE7C,qBAAa,WAAW;IACtB;;OAEG;IACH,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACpC;;OAEG;IACH,IAAI,EAAE,WAAW,GAAG,IAAI,CAAQ;IAChC;;OAEG;IACH,WAAW,EAAE,MAAM,GAAG,IAAI,CAAQ;gBAEtB,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC;IAMjE,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,WAAW,GAAG,IAAI,GAAG,WAAW,GAAG,IAAI;IAO3E,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;IAEhD,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,GAAG,IAAI;IAEjE;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,WAAW,GAAG,WAAW;IAiC5C;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,WAAW,GAAG,OAAO;IAQ3C;;;OAGG;IACH,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,GAAG,OAAO;IAI/C;;;;OAIG;IACH,gBAAgB,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,GAAG,IAAI,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI;IAWzF,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,EAAE,MAAM,GAAI,MAAM,GAAG,SAAS;IAgBlF,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW;IAavC,eAAe,CAAC,GAAG,EAAE,MAAM;CAI5B"}