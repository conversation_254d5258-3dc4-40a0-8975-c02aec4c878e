// deprecated; use components/header/publish.ts
import { LitElement, html, css } from "lit";
import { storeInstance as Store } from "store/index.js";
import { Router } from "@vaadin/router";
import * as util from "lib/util.ts";

class WexPublishHeader extends LitElement {
  static get properties() {
    return {};
  }

  static get styles() {
    return css`
      * {
        box-sizing: border-box;
      }
      #publish-tabs {
        display: flex;
        padding: 0;
        margin: 0;
        list-style: none;
        border-bottom: 1px solid;
        border-color: var(--primary-100);
      }
      .tab {
        display: flex;
        align-items: center;
        padding: var(--spacing-md) var(--spacing-md);
        cursor: pointer;
        color: var(--color-text);
      }
      .tab[active] {
        border-bottom: 2px solid;
        font-weight: bold;
      }
      .tab:not([active]):hover {
        background-color: var(--clr-white);
        color: var(--primary);
      }
    `;
  }

  constructor() {
    super();
    this._state = {};
    this._string = [];
    this._subscription = null;

    // internal
    this._tabs = [];
    this._subtabs = [];
  }

  connectedCallback() {
    super.connectedCallback();
    this._state = Store.state;
    this._string = Store.state[Store.state.langCode];

    this._tabs = Store.state.menus.publish;
    this._subtabs = Store.state.menus.publish_run || [];

    this._subscription = Store.subscribe((state) => {
      this.stateChange(state);
    });
  }

  disconnectedCallback() {
    Store.unsubscribe(this._subscription);
  }

  firstUpdated() {
    this._setTab();
  }

  stateChange(state) {
    this._string = state[state.langCode];
  }

  _setTab() {
    const url = new URL(window.location.href);
    const tabs = url.pathname.split("/");
    if (tabs.length == 6) {
      const subtab = tabs.pop();
      for (let x of this._subtabs) {
        x.active = x.name == subtab;
      }

      Store.dispatch("setState", {
        property: "publish_subtab",
        value: subtab,
      });
    }

    const tab = tabs.pop();
    for (let x of this._tabs) {
      x.active = x.name == tab;
    }

    Store.dispatch("setState", {
      property: "publish_tab",
      value: tab,
    });
    this.requestUpdate();
  }

  _handleClick(path, e) {
    if (path == "run") {
      const subtab = this._state.publish_subtab || "definitions";
      Router.go(
        "/wex/" + this._state.langCode + "/publish/" + path + "/" + subtab
      );
    } else {
      Router.go("/wex/" + this._state.langCode + "/publish/" + path);
    }
  }

  render() {
    const template = html` <ul id="publish-tabs">
      ${this._tabs.map(
        (x) => html`
          <li
            class="tab"
            ?active="${!!x.active}"
            @click="${this._handleClick.bind(this, x.name)}"
          >
            ${this._string[x.label]}
          </li>
        `
      )}
    </ul>`;
    return template;
  }
}

// customElements.define("wex-publish-header", WexPublishHeader);
