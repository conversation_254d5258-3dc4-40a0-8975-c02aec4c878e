{"version": 3, "file": "wex-task-diagram.js", "sourceRoot": "", "sources": ["../../src/components/wex-task-diagram.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AACvC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAG5D;;GAEG;AAEI,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,UAAU;IAAvC;;QAEL,iBAAY,GAAgB,IAAI,CAAC;IAanC,CAAC;IAXC,eAAe;IACf,MAAM;QACJ,OAAO,IAAI,CAAA;;;uDAGwC,IAAI,CAAC,YAAY;YAC9D,EAAE,aAAa,oBAAoB,IAAI,CAAC,GAAG,EAAE;;;KAGlD,CAAC;IACJ,CAAC;CACF,CAAA;AAbC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;oDACZ;AAFtB,cAAc;IAD1B,aAAa,CAAC,kBAAkB,CAAC;GACrB,cAAc,CAe1B", "sourcesContent": ["import { LitElement, html } from \"lit\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\nimport { Task } from \"@bds/types\";\r\n\r\n/**\r\n * Displays workflow diagram for selected task\r\n */\r\n@customElement(\"wex-task-diagram\")\r\nexport class WexTaskDiagram extends LitElement {\r\n  @property({ type: Object, attribute: false })\r\n  selectedTask: Task | null = null;\r\n\r\n  /* Templates */\r\n  render() {\r\n    return html`\r\n      <img\r\n        id=\"diagram\"\r\n        src=\"/lwa/jrest/GetTaskDiagram?processInstId=${this.selectedTask\r\n          ?.processInstId}&amp;cachebuster=${Date.now()}\"\r\n        style=\"display: block;margin-left: auto; margin-right: auto;\"\r\n      />\r\n    `;\r\n  }\r\n}\r\n"]}