import { LitElement, html, css } from "lit";
import { storeInstance } from "store/index.js";
// import pubsub from "pubsub-js";

// import * as util from "lib/util.ts";
import * as wexlib from "lib/wexlib.js";

import "../../components/common/dialog.js";

// import "@ui5/webcomponents/dist/Button.js";
// import "@ui5/webcomponents/dist/Dialog.js";
// import "@ui5/webcomponents/dist/Label.js";

// import "@polymer/iron-icons/iron-icons.js";

/**
 * updated EJS 20250528
 * @property dialogOpenData: array of objects with boolean selected
 */
class WexDialogSelect extends LitElement {
  static get properties() {
    return {
      dialogOpenData: { type: Object },
      // headerText: { type: String },

      _selected: { type: Array },
      _unselected: { type: Array },
    };
  }

  static get styles() {
    // * {
    //   box-sizing: border-box;
    // }
    return css`
      .italic {
        font-style: italic;
      }

      #dialog {
        width: min(90vw, 600px);
      }
      .dialog-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 1rem;
        height: min(50vh, 300px);
        color: var(--font-color);
      }
      .dialog-content * {
        color: var(--font-color);
      }
      .dialog-content > *:not(:last-child) {
        margin-right: 0.5rem;
      }

      ul {
        flex-grow: 1;
        margin: 0;
        padding: 0;
        list-style: none;
        border: 1px solid var(--clr-gray-light);
        overflow-y: auto;
        max-height: calc(2.25rem * 8);
        height: 30vh;
      }
      li {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 1rem;
        height: 2.25rem;
        border-bottom: 1px solid var(--clr-gray-light);
      }
      li > span {
        flex-grow: 1;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li > .icon-container {
        position: relative;
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
      }
      li > .icon-container:hover {
        background: var(--clr-white);
      }
      li > *:not(:last-child) {
        margin-right: 1rem;
      }
      li[active] {
        background: var(--row-selected-background);
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li:not([active]):hover {
        background: var(--clr-gray-ultra-light);
      }

      .select-column {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
      }
      .select-column > span {
        margin-bottom: 0.5rem;
      }

      .select-actions {
        display: flex;
        flex-direction: column;
      }
      .select-actions > *:not(:last-child) {
        margin-bottom: 0.5rem;
      }
      .select-actions > * {
        cursor: pointer;
      }
    `;
  }

  constructor() {
    super();
    this.dialogOpenData = null;
    this._state = {};
    this._string = {};
    this._subscription = null;

    this._actions = [];
    this._cbMsg = "";
    this._header = "";
    this._selected = [];
    this._unselected = [];

    // this.headerText = "";
  }

  connectedCallback() {
    super.connectedCallback();
    this._state = storeInstance.state;
    this._string = this._state[this._state.langCode];
    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  _init() {
    this._actions = this._state.menus.dialog_select_actions;

    // pubsub.subscribe("dialog-select", this._handler.bind(this));
    // pubsub.subscribe("dialog-close-all", this._close.bind(this));

    this.dialogOpenData = this._state.wex_select_dialog_open;
    // console.log("init", this.dialogOpenData);
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  // firstUpdated() {
  //   this.dialog = this.shadowRoot.querySelector("#dialog");
  // }

  // updated(changedProperties) {
  //   const dialog = this.shadowRoot.querySelector("ui5-dialog");
  //   if (
  //     dialog &&
  //     changedProperties.has("dialogOpenData") &&
  //     this.dialogOpenData
  //   ) {
  //     console.log("asdf");
  //     this._selected = this.dialogOpenData.filter((i) => i.selected);
  //     this._unselected = this.dialogOpenData.filter((i) => !i.selected);
  //   }
  // }

  stateChange(state) {
    this._state = state;
    this._string = state[state.langCode];
    this.dialogOpenData = state.wex_select_dialog_open;
    // this.headerText = this.dialogOpenData.headerText;

    if (!!this.dialogOpenData) {
      this._selected = this.dialogOpenData.data.filter((item) => item.selected);
      this._unselected = this.dialogOpenData.data.filter(
        (item) => !item.selected
      );
    }
  }

  _onConfirm = async () => {
    console.log("res?", this.dialogOpenData);
    const pubDefId = this.dialogOpenData.pubDefId;
    // const items = this._selected.map((item) => item.langId).join(",");
    const pubDef = this.dialogOpenData.pubDef;
    const updateTargetKey = this.dialogOpenData.updateTargetKey;
    const items = this._selected;

    const { lstLanguages, lstOutputFormats, lstPubContentDtos } = pubDef;

    const body = {
      ...pubDef,
      pubLangs: lstLanguages,
      pubContent: lstPubContentDtos,
      pubOutputs: lstOutputFormats,
    };

    body[updateTargetKey] = items;
    // if (this.dialogOpenData.updateTargetKey) {
    console.log("updated:", body);
    // }
    await wexlib.editPubDef(body);
    // await this.dialogOpenData.closeCallback(body);
    // await this.dialogOpenData.refreshCallback(pubDefId);

    return true;
  };

  render() {
    // dialogOpenStateProperty=${this.dialogOpenData.dialogOpenStateProperty}
    const template = html`
      <wex-dialog
        id="dialog"
        .open=${!!this.dialogOpenData}
        .onConfirm=${this._onConfirm}
        confirmLabel="Ok"
        cancelLabel="Cancel"
        dialogOpenStateProperty="wex_select_dialog_open"
        headerText=${this.dialogOpenData?.headerText || "No Header"}
        width="60%"
      >
        <wex-list-picker
          .selected=${this._selected}
          .unselected=${this._unselected}
          @pick-list-updated=${(e) => {
            this._selected = e.detail.selected;
            this._unselected = e.detail.unselected;
          }}
        ></wex-list-picker>
      </wex-dialog>
    `;
    return template;
  }
}

customElements.define("wex-dialog-select", WexDialogSelect);
