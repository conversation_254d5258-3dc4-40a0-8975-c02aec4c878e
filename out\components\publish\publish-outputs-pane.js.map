{"version": 3, "file": "publish-outputs-pane.js", "sourceRoot": "", "sources": ["../../../src/components/publish/publish-outputs-pane.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC5D,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AACtC,aAAa;AACb,OAAO,KAAK,MAAM,MAAM,YAAY,CAAC;AAErC,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAC1B,wBAAwB;AACxB,OAAO,cAAc,CAAC;AAEtB,OAAO,mCAAmC,CAAC;AAGpC,IAAM,qBAAqB,GAA3B,MAAM,qBAAsB,SAAQ,UAAU;IAA9C;;QACsB,eAAU,GAAU,EAAE,CAAC;QAElD,6BAA6B;QACrB,WAAM,GAAQ,EAAE,CAAC;QACjB,sBAAiB,GAAQ,EAAE,CAAC;IAyItC,CAAC;IAxIC,kCAAkC;IAElC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;KAIT,CAAC;IACJ,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,sBAAsB;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,oDAAoD;QACpD,6BAA6B;QAC7B,MAAM;QAEN,iEAAiE;QACjE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,OAAO,CAAC,qBAAqB,CAAC;QAC7D,gBAAgB;IAClB,CAAC;IAED,YAAY,CAAC,EAAU;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,SAAS;YAAE,OAAO;QACvB,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,8BAA8B;YACxC,KAAK,EAAE;gBACL,SAAS;gBACT,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,GAAG,EAAE,CAAC;aACjD;SACF,CAAC,CAAC;IACL,CAAC;IAED,gDAAgD;IAChD,yBAAyB;IACzB,8BAA8B;IAC9B,iCAAiC;IACjC,2BAA2B;IAC3B,gCAAgC;IAChC,+CAA+C;IAC/C,2BAA2B;IAC3B,4DAA4D;IAC5D,OAAO;IACP,gCAAgC;IAChC,qCAAqC;IACrC,0BAA0B;IAC1B,6CAA6C;IAC7C,6CAA6C;IAC7C,4BAA4B;IAC5B,uEAAuE;IACvE,oCAAoC;IACpC,uEAAuE;IACvE,QAAQ;IACR,QAAQ;IACR,0BAA0B;IAC1B,oCAAoC;IACpC,qCAAqC;IACrC,OAAO;IACP,IAAI;IACJ,MAAM;IAEN,yBAAyB;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAA;;;;;;oBAML,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,kBAAkB,CAAC;;;qDAGf,IAAI,CAAC,oBAAoB;;;;KAIzE,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,CAAc;QAC3C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC;YACtB,IAAI,CAAC,IAAI;gBAAE,OAAO;YAElB,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;gBAC5C,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,CAAC;YAED,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAE9B,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,IAAI,CAAC,OAAO,CACV,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,eAAe,IAAI,GAAG,CAAC,eAAe,CAAC,CACrE,CAAC;YACF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,oBAAoB;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE7D,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC9D,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACjD,YAAY,CAAC,IAAI,GAAG,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC7C,YAAY,CAAC,QAAQ,GAAG,GAAG,CAAC,cAAc,CAAC;QAC3C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACxC,YAAY,CAAC,KAAK,EAAE,CAAC;QAErB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACxC,GAAG,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,MAAM;QACJ,MAAM,QAAQ,GAAG,IAAI,CAAA;;cAEX,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;;YAE7B,IAAI,CAAC,yBAAyB,EAAE;;;;;sBAKtB,IAAI,CAAC,iBAAiB;mBACzB,IAAI,CAAC,UAAU;0BACR,KAAK;mBACZ,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC;wBACnC,KAAK;;;KAGxB,CAAC;QACF,qDAAqD;QACrD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AA7I4B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;yDAAwB;AADvC,qBAAqB;IADjC,aAAa,CAAC,0BAA0B,CAAC;GAC7B,qBAAqB,CA8IjC", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { customElement, property } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store\";\r\n// @ts-ignore\r\nimport * as wexlib from \"lib/wexlib\";\r\n\r\nimport \"../base/row-item\";\r\nimport \"../base/col-item\";\r\n// import \"./wex-table\";\r\nimport \"../wex-table\";\r\n\r\nimport \"@ui5/webcomponents/dist/Button.js\";\r\n\r\n@customElement(\"wex-publish-outputs-pane\")\r\nexport class WexPublishOutputsPane extends LitElement {\r\n  @property({ type: Array }) outputJobs: any[] = [];\r\n\r\n  //   private state: any = {};\r\n  private string: any = [];\r\n  private outputJobsColumns: any = [];\r\n  //   private outputJobs: any = [];\r\n\r\n  static get styles() {\r\n    return css`\r\n      :host {\r\n        flex: 1;\r\n      }\r\n    `;\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    // this.state = state;\r\n    this.string = state[state.langCode];\r\n    // this._subscription = Store.subscribe((state) => {\r\n    //   this.stateChange(state);\r\n    // });\r\n\r\n    // this._defJobsColumns = Store.state.columns.publishJobsDefJobs;\r\n    this.outputJobsColumns = state.columns.publishJobsOutputJobs;\r\n    // this._init();\r\n  }\r\n\r\n  _openPopover(id: string) {\r\n    const outputJob = this.outputJobs.find((job: any) => job.selected);\r\n    if (!outputJob) return;\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"output_job_info_popover_open\",\r\n      value: {\r\n        outputJob,\r\n        opener: this.shadowRoot?.querySelector(\"#\" + id),\r\n      },\r\n    });\r\n  }\r\n\r\n  //   _handleSort(type: string, e: CustomEvent) {\r\n  // const data = e.detail;\r\n  // if (!type || !data) return;\r\n  // const { field, value } = data;\r\n  // if (type == \"defjobs\") {\r\n  //   // shallow clone sufficient\r\n  //   const columns = [...this._defJobsColumns];\r\n  //   columns.forEach((x) =>\r\n  //     x.field == field ? (x.sort = value) : (x.sort = null)\r\n  //   );\r\n  //   // shallow clone sufficient\r\n  //   const rows = [...this._defJobs];\r\n  //   rows.sort((a, b) => {\r\n  //     const c = `${a[field]}`.toUpperCase();\r\n  //     const d = `${b[field]}`.toUpperCase();\r\n  //     if (value == \"asc\") {\r\n  //       return c.localeCompare(d, undefined, { sensitivity: \"base\" });\r\n  //     } else if (value == \"desc\") {\r\n  //       return d.localeCompare(c, undefined, { sensitivity: \"base\" });\r\n  //     }\r\n  //   });\r\n  //   this._defJobs = rows;\r\n  //   this._defJobsColumns = columns;\r\n  // } else if (type == \"outputjobs\") {\r\n  //   //\r\n  // }\r\n  //   }\r\n\r\n  _renderOutputJobActionBar() {\r\n    const template = html`\r\n      <wex-row-item alignItems=\"center\" justifyContent=\"flex-start\">\r\n      </wex-row-item>\r\n      <wex-row-item justifyContent=\"flex-end\" alignItems=\"center\">\r\n        <ui5-button\r\n          id=\"OutputJobDetails\"\r\n          @click=\"${this._openPopover.bind(this, \"OutputJobDetails\")}\"\r\n          >Details</ui5-button\r\n        >\r\n        <ui5-button id=\"OutputJobDownload\" @click=\"${this._handleDownloadClick}\"\r\n          >Download</ui5-button\r\n        >\r\n      </wex-row-item>\r\n    `;\r\n    return template;\r\n  }\r\n\r\n  async _handleClickOutputJobRow(e: CustomEvent) {\r\n    try {\r\n      const data = e.detail;\r\n      if (!data) return;\r\n\r\n      if (e.detail.icon === \"icons:file-download\") {\r\n        this._handleDownloadClick();\r\n      }\r\n\r\n      const obj = { ...data.value };\r\n\r\n      const rows = [...this.outputJobs];\r\n      rows.forEach(\r\n        (row) => (row.selected = row.outputJobInfoId == obj.outputJobInfoId)\r\n      );\r\n      this.outputJobs = rows;\r\n    } catch {}\r\n  }\r\n\r\n  async _handleDownloadClick() {\r\n    const job = this.outputJobs.find((job: any) => job.selected);\r\n\r\n    const res = await wexlib.getPublishOutput(job.lblSeqIdOutput);\r\n    const downloadLink = document.createElement(\"a\");\r\n    downloadLink.href = URL.createObjectURL(res);\r\n    downloadLink.download = job.outputFileName;\r\n    document.body.appendChild(downloadLink);\r\n    downloadLink.click();\r\n\r\n    document.body.removeChild(downloadLink);\r\n    URL.revokeObjectURL(downloadLink.href);\r\n  }\r\n\r\n  render() {\r\n    const template = html`\r\n      <wex-col-item class=\"right\">\r\n        <h3>${this.string[\"_output_jobs\"]}</h3>\r\n        <wex-row-item justifyContent=\"space-between\">\r\n          ${this._renderOutputJobActionBar()}\r\n        </wex-row-item>\r\n\r\n        <wex-table\r\n          defaultHeight=\"70vh\"\r\n          .columns=\"${this.outputJobsColumns}\"\r\n          .rows=\"${this.outputJobs}\"\r\n          .enableSelect=${false}\r\n          @click=${this._handleClickOutputJobRow.bind(this)}\r\n          .enableMenu=${false}\r\n        ></wex-table>\r\n      </wex-col-item>\r\n    `;\r\n    // @sort=${this._handleSort.bind(this, \"outputjobs\")}\r\n    return template;\r\n  }\r\n}\r\n"]}