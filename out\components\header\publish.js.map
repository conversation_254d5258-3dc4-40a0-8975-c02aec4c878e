{"version": 3, "file": "publish.js", "sourceRoot": "", "sources": ["../../../src/components/header/publish.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,6CAA6C;AAC7C,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,MAAM,gBAAiB,SAAQ,UAAU;IAAzC;QACE,yEAAyE;;QAEjE,UAAK,GAAQ,EAAE,CAAC;QAChB,WAAM,GAAQ,EAAE,CAAC;QACjB,kBAAa,GAAG,IAAI,CAAC;QACrB,UAAK,GAAU,EAAE,CAAC;IA8G5B,CAAC;IA7GC,qCAAqC;IAErC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BT,CAAC;IACJ,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YAC1D,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC;IAED,oBAAoB;QAClB,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,YAAY;QACV,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,KAAU;QACd,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;QACjC,iDAAiD;IACnD,CAAC;IAED,WAAW,CAAC,KAAU;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,OAAO;QACL,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrC,0BAA0B;QAC1B,+BAA+B;QAC/B,mCAAmC;QACnC,mCAAmC;QACnC,MAAM;QAEN,iCAAiC;QACjC,kCAAkC;QAClC,qBAAqB;QACrB,QAAQ;QACR,IAAI;QAEJ,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC9B,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC3B,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,IAAI,UAAU,CAAC;QACtC,CAAC;QAED,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,aAAa;YACvB,KAAK,EAAE,UAAU;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,MAAM;QACJ,MAAM,QAAQ,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC,KAAK,CAAC,GAAG,CACd,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAA;;;sBAGC,CAAC,CAAC,GAAG,CAAC,MAAM;sBACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC;;cAE9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;;SAE3B,CACF;UACG,CAAC;QACP,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,YAAY,CAAC,IAAY;QACvB,MAAM,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,WAAW,GAAG,IAAI,CAAC,CAAC;IAChE,CAAC;CACF;AAED,cAAc,CAAC,MAAM,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,CAAC", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\n// import { property } from \"lit/decorators\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store\";\r\nimport { Router } from \"@vaadin/router\";\r\n\r\nclass WexPublishHeader extends LitElement {\r\n  //   @property({ type: Object }) state: any = {}; // need to define state\r\n\r\n  private state: any = {};\r\n  private string: any = [];\r\n  private _subscription = null;\r\n  private _tabs: any[] = [];\r\n  //   private _subtabs: string[] = [];\r\n\r\n  static get styles() {\r\n    return css`\r\n      * {\r\n        box-sizing: border-box;\r\n      }\r\n      #publish-tabs {\r\n        display: flex;\r\n        padding: 0;\r\n        margin: 0;\r\n        list-style: none;\r\n        border-bottom: 1px solid;\r\n        border-color: var(--primary-100);\r\n      }\r\n      .tab {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: var(--spacing-md) var(--spacing-md);\r\n        cursor: pointer;\r\n        color: var(--color-text);\r\n      }\r\n      .tab[active] {\r\n        border-bottom: 2px solid;\r\n        font-weight: bold;\r\n      }\r\n      .tab:not([active]):hover {\r\n        background-color: var(--clr-white);\r\n        color: var(--primary);\r\n      }\r\n    `;\r\n  }\r\n\r\n  connectedCallback(): void {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    this.state = state;\r\n    this.string = state[state.langCode];\r\n    this._subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    this._init(state);\r\n  }\r\n\r\n  disconnectedCallback(): void {\r\n    storeInstance.unsubscribe(this._subscription);\r\n  }\r\n\r\n  firstUpdated() {\r\n    this._setTab();\r\n  }\r\n\r\n  _init(state: any) {\r\n    this._tabs = state.menus.publish;\r\n    // this._subtabs = state.menus.publish_run || [];\r\n  }\r\n\r\n  stateChange(state: any): void {\r\n    this.string = state[state.langCode];\r\n  }\r\n\r\n  _setTab() {\r\n    const url = new URL(window.location.href);\r\n    const tabs = url.pathname.split(\"/\");\r\n    // if (tabs.length == 6) {\r\n    //   const subtab = tabs.pop();\r\n    //   for (let x of this._subtabs) {\r\n    //     x.active = x.name == subtab;\r\n    //   }\r\n\r\n    //   Store.dispatch(\"setState\", {\r\n    //     property: \"publish_subtab\",\r\n    //     value: subtab,\r\n    //   });\r\n    // }\r\n\r\n    const currentTab = tabs.pop();\r\n    for (let tab of this._tabs) {\r\n      tab.active = tab.name == currentTab;\r\n    }\r\n\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"publish_tab\",\r\n      value: currentTab,\r\n    });\r\n    this.requestUpdate();\r\n  }\r\n\r\n  render() {\r\n    const template = html` <ul id=\"publish-tabs\">\r\n      ${this._tabs.map(\r\n        (tab) => html`\r\n          <li\r\n            class=\"tab\"\r\n            ?active=${!!tab.active}\r\n            @click=\"${this._handleClick.bind(this, tab.name)}\"\r\n          >\r\n            ${this.string[tab.label]}\r\n          </li>\r\n        `\r\n      )}\r\n    </ul>`;\r\n    return template;\r\n  }\r\n\r\n  _handleClick(path: string) {\r\n    Router.go(\"/wex/\" + this.state.langCode + \"/publish/\" + path);\r\n  }\r\n}\r\n\r\ncustomElements.define(\"wex-publish-header\", WexPublishHeader);\r\n"]}