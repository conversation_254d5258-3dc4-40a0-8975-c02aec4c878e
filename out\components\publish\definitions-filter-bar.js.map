{"version": 3, "file": "definitions-filter-bar.js", "sourceRoot": "", "sources": ["../../../src/components/publish/definitions-filter-bar.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AACnE,aAAa;AACb,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC/C,2CAA2C;AAE3C,OAAO,kBAAkB,CAAC;AAC1B,OAAO,kBAAkB,CAAC;AAC1B,OAAO,oBAAoB,CAAC;AAC5B,OAAO,cAAc,CAAC;AACtB,OAAO,wBAAwB,CAAC;AAGzB,IAAM,8BAA8B,GAApC,MAAM,8BAA+B,SAAQ,UAAU;IAAvD;;QACsB,YAAO,GAAiB,IAAI,CAAC;QAC7B,aAAQ,GAAiB,IAAI,CAAC;QAC9B,eAAU,GAAiB,IAAI,CAAC;QAElD,WAAM,GAAe,IAAI,CAAC;QAE3B,kBAAa,GAAiB,IAAI,CAAC;QAC3C,oDAAoD;QAC5C,kBAAa,GAAQ,EAAE,CAAC;QACxB,iBAAY,GAAe,IAAI,CAAC;QAsGxC,0BAAqB,GAAG,CAAC,CAAC,EAAE,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,8CAA8C,EAAE,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC;IAiCJ,CAAC;IAvIC,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA,EAAE,CAAC;IACf,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QAClC,qCAAqC;QACrC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,KAAU,EAAE,EAAE;YACzD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;IAED,oBAAoB;QAClB,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAC7B,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,WAAW,CAAC,KAAU;QACpB,uBAAuB;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,IAAI,CAAC,QAAQ,GAAG,MAAM,aAAa,CAAC,OAAO,CACzC,+BAA+B,CAChC,CAAC;YACF,IAAI,CAAC,UAAU,GAAG,MAAM,aAAa,CAAC,OAAO,CAC3C,iCAAiC,CAClC,CAAC;YACF,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,mDAAmD;YACnD,+CAA+C;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,sBAAsB;QACpB,MAAM,aAAa,GAAG;YACpB,QAAQ,EAAE;gBACR,WAAW,EAAE,UAAU;gBACvB,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;aAC5D;YACD,WAAW,EAAE;gBACX,WAAW,EAAE,cAAc;gBAC3B,uCAAuC;gBACvC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;aACxD;SACF,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED,aAAa;QACX,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE;YACjC,QAAQ,EAAE,iCAAiC;YAC3C,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB;SACF,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB,CAAC,CAAc;QAChC,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;QAElD,IAAI,aAAa,GAAG,EAAE,CAAC;QAEvB,qCAAqC;QACrC,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC1C,aAAa,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,kCAAkC;YAClC,uCAAuC;YACvC,yBAAyB;YACzB,wDAAwD;YACxD,sCAAsC;YACtC,4BAA4B;YAC5B,sCAAsC;YACtC,2BAA2B;YAC3B,gBAAgB;YAChB,UAAU;QACZ,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QACzC,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,oBAAoB,EAAE;YACpC,MAAM,EAAE,EAAE,aAAa,EAAE;YACzB,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;SACf,CAAC,CACH,CAAC;IACJ,CAAC;IAMD,MAAM;QACJ,OAAO,IAAI,CAAA;;;;;qBAKM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;sBAC5B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;wBAK3B,IAAI,CAAC,aAAa;iCACT,IAAI,CAAC,qBAAqB;yBAClC,UAAU;oCACC,IAAI,CAAC,aAAa;;;;;;wBAM9B,IAAI,CAAC,aAAa;iCACT,IAAI,CAAC,mBAAmB;;oCAErB,IAAI,CAAC,aAAa;;;;;KAKjD,CAAC;IACJ,CAAC;CACF,CAAA;AAlJ4B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;+DAA8B;AAC7B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;gEAA+B;AAC9B;IAA1B,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;kEAAiC;AAElD;IAAR,KAAK,EAAE;8DAA2B;AALxB,8BAA8B;IAD1C,aAAa,CAAC,oCAAoC,CAAC;GACvC,8BAA8B,CAmJ1C", "sourcesContent": ["import { LitElement, html, css } from \"lit\";\r\nimport { property, state, customElement } from \"lit/decorators.js\";\r\n// @ts-ignore\r\nimport { storeInstance } from \"store/index.js\";\r\n// import * as wexlib from \"lib/wexlib.js\";\r\n\r\nimport \"../base/col-item\";\r\nimport \"../base/row-item\";\r\nimport \"../base/fixed-item\";\r\nimport \"../base/icon\";\r\nimport \"../base/multi-combobox\";\r\n\r\n@customElement(\"wex-publish-definitions-filter-bar\")\r\nexport class WexPublishDefinitionsFilterBar extends LitElement {\r\n  @property({ type: Array }) pubDefs: any[] | null = null;\r\n  @property({ type: Array }) projects: any[] | null = null;\r\n  @property({ type: Array }) categories: any[] | null = null;\r\n\r\n  @state() string: any | null = null;\r\n\r\n  private filterOptions: any[] | null = null;\r\n  // private cachedFilterOptions: any[] | null = null;\r\n  private activeFilters: any = {};\r\n  private subscription: any | null = null;\r\n\r\n  static get styles() {\r\n    return css``;\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    const state = storeInstance.state;\r\n    // this._state = storeInstance.state;\r\n    this.string = state[state.langCode];\r\n    this.subscription = storeInstance.subscribe((state: any) => {\r\n      this.stateChange(state);\r\n    });\r\n\r\n    this._init();\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    super.disconnectedCallback();\r\n    storeInstance.unsubscribe(this.subscription);\r\n  }\r\n\r\n  stateChange(state: any) {\r\n    // this._state = state;\r\n    this.string = state[state.langCode];\r\n  }\r\n\r\n  async _init() {\r\n    try {\r\n      this.projects = await storeInstance.waitFor(\r\n        \"_state.pages.publish.projects\"\r\n      );\r\n      this.categories = await storeInstance.waitFor(\r\n        \"_state.pages.publish.categories\"\r\n      );\r\n      this._generateFilterOptions();\r\n      // console.log(\"this.categories\", this.categories);\r\n      // console.log(\"this.projects\", this.projects);\r\n    } catch (error) {\r\n      console.error(\"Error during initialization:\", error);\r\n    }\r\n  }\r\n\r\n  _generateFilterOptions() {\r\n    const filterOptions = {\r\n      category: {\r\n        placeholder: \"Category\",\r\n        mcbItems: this.categories?.map((category) => category.name),\r\n      },\r\n      projectName: {\r\n        placeholder: \"Project Name\",\r\n        // mcbItems: [\"rgewrger\", \"ergwergre\"],\r\n        mcbItems: this.projects?.map((project) => project.name),\r\n      },\r\n    };\r\n    this.filterOptions = filterOptions;\r\n  }\r\n\r\n  _createPubDef() {\r\n    storeInstance.dispatch(\"setState\", {\r\n      property: \"publish_defs_create_dialog_open\",\r\n      value: {\r\n        mode: \"Create\",\r\n        projects: this.projects,\r\n      },\r\n    });\r\n  }\r\n\r\n  _handleFilterChange(e: CustomEvent) {\r\n    const { selectedItems, comboBoxGroup } = e.detail;\r\n\r\n    let activeFilters = {};\r\n\r\n    // this works for separate comboboxes\r\n    if (comboBoxGroup) {\r\n      activeFilters = { ...this.activeFilters };\r\n      activeFilters[comboBoxGroup] = selectedItems.map((item) => item);\r\n    } else {\r\n      // console.log(\"NOOOOO COMBOBOX\");\r\n      // // this works for grouped comboboxes\r\n      // // activeFilters = {};\r\n      // activeFilters = selectedItems.reduce((acc, item) => {\r\n      //   const group = item.dataset.group;\r\n      //   const text = item.text;\r\n      //   if (!acc[group]) acc[group] = [];\r\n      //   acc[group].push(text);\r\n      //   return acc;\r\n      // }, {});\r\n    }\r\n\r\n    this.activeFilters = activeFilters;\r\n    console.log(\"CHECK THIS\", activeFilters);\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"set-active-filters\", {\r\n        detail: { activeFilters },\r\n        bubbles: true,\r\n        composed: true,\r\n      })\r\n    );\r\n  }\r\n\r\n  _handleCategorySelect = (e) => {\r\n    console.log(\"NYI: category select needs setup and testing\", e);\r\n  };\r\n\r\n  render() {\r\n    return html`\r\n      <wex-row-item height=\"42px\">\r\n        <wex-fixed-item>\r\n          <wex-icon\r\n            icon=\"add\"\r\n            title=\"${this.string[\"_create_pubdef\"]}\"\r\n            @click=\"${this._createPubDef.bind(this)}\"\r\n          ></wex-icon>\r\n        </wex-fixed-item>\r\n        <wex-col-item>\r\n          <wex-multi-combobox\r\n            .mcbData=\"${this.filterOptions}\"\r\n            @selection-change=\"${this._handleCategorySelect}\"\r\n            .dataGroup=${\"category\"}\r\n            .initialSelectedItems=${this.activeFilters}\r\n          >\r\n          </wex-multi-combobox>\r\n        </wex-col-item>\r\n        <wex-col-item>\r\n          <wex-multi-combobox\r\n            .mcbData=\"${this.filterOptions}\"\r\n            @selection-change=\"${this._handleFilterChange}\"\r\n            dataGroup=\"projectName\"\r\n            .initialSelectedItems=${this.activeFilters}\r\n          >\r\n          </wex-multi-combobox>\r\n        </wex-col-item>\r\n      </wex-row-item>\r\n    `;\r\n  }\r\n}\r\n"]}