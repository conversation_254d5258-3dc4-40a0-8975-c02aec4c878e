import { LitElement, html, css } from "lit-element";
import { storeInstance } from "store/index.js";

import "../base/col-item";
import "../base/row-item";
import "../base/fixed-item";

// objects in the arrays must have a name key to function correctly
// the primary use case is currently to update values on a pubdef
// emits both selected and unselected on every change
class WexListPicker extends LitElement {
  static get properties() {
    return {
      selected: { type: Array },
      unselected: { type: Array },
    };
  }

  static get styles() {
    // * {
    //   box-sizing: border-box;
    // }
    return css`
      :host {
        flex: 1;
      }
      .italic {
        font-style: italic;
      }

      #dialog {
        width: min(90vw, 600px);
      }
      .dialog-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 1rem;
        height: min(50vh, 300px);
        color: var(--font-color);
      }
      .dialog-content * {
        color: var(--font-color);
      }
      .dialog-content > *:not(:last-child) {
        margin-right: 0.5rem;
      }

      ul {
        flex-grow: 1;
        margin: 0;
        padding: 0;
        list-style: none;
        border: 1px solid var(--clr-gray-light);
        overflow-y: auto;
        max-height: calc(2.25rem * 8);
        height: 30vh;
      }
      li {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 1rem;
        height: 2.25rem;
        border-bottom: 1px solid var(--clr-gray-light);
      }
      li > span {
        flex-grow: 1;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li > .icon-container {
        position: relative;
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
      }
      li > .icon-container:hover {
        background: var(--clr-white);
      }
      li > *:not(:last-child) {
        margin-right: 1rem;
      }
      li[active] {
        background: var(--row-selected-background);
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li:not([active]):hover {
        background: var(--clr-gray-ultra-light);
      }

      .select-column {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
      }
      .select-column > span {
        margin-bottom: 0.5rem;
      }

      .select-actions {
        display: flex;
        flex-direction: column;
      }
      .select-actions > *:not(:last-child) {
        margin-bottom: 0.5rem;
      }
      .select-actions > * {
        cursor: pointer;
      }
    `;
  }

  constructor() {
    super();
    this._state = {};
    this._string = {};

    this._actions = [];

    this.selected = [];
    this.unselected = [];
  }

  connectedCallback() {
    super.connectedCallback();
    this._state = storeInstance.state;
    this._string = this._state[this._state.langCode];
    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  _init() {
    this._actions = this._state.menus.dialog_select_actions;
  }

  stateChange(state) {
    this._state = state;
    this._string = state[state.langCode];
  }

  // idx - index from render function i
  _handleSelect(type, idx) {
    if (type === "selected") {
      // deep clone
      const list = JSON.parse(JSON.stringify(this.selected));
      list[idx].active = !list[idx].active;
      this.selected = list;
    } else if (type === "deselect") {
      // deep clone
      const list = JSON.parse(JSON.stringify(this.unselected));
      list[idx].active = !list[idx].active;
      this.unselected = list;
    }

    // // needs to event for every change at the moment
    // this.dispatchEvent(
    //   new CustomEvent("pick-list-updated", {
    //     detail: {
    //       selected: this.selected,
    //       unselected: this.unselected,
    //     },
    //     bubbles: true,
    //     composed: true,
    //   })
    // );
  }

  _handleAction(type) {
    if (type === "select") {
      let a = this.unselected.filter((x) => x.active);
      a = this.selected.concat(a);
      this.selected = JSON.parse(JSON.stringify(a));
      this.selected.forEach((x) => (x.active = false));
      this.selected.sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;
        return 0;
      });

      let b = this.unselected.filter((x) => !x.active);
      this.unselected = JSON.parse(JSON.stringify(b));
      this.unselected.forEach((x) => (x.active = false));
      this.unselected.sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;
        return 0;
      });

      this.requestUpdate();
    } else if (type === "deselect") {
      let a = this.selected.filter((x) => x.active);
      a = this.unselected.concat(a);
      this.unselected = JSON.parse(JSON.stringify(a));
      this.unselected.forEach((x) => (x.active = false));
      this.unselected.sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;
        return 0;
      });

      let b = this.selected.filter((x) => !x.active);
      this.selected = JSON.parse(JSON.stringify(b));
      this.selected.forEach((x) => (x.active = false));
      this.selected.sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;
        return 0;
      });

      this.requestUpdate();
    } else if (type === "select-all") {
      this.selected = this.selected.concat(this.unselected);
      this.selected.forEach((x) => (x.active = false));
      this.selected.sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;
        return 0;
      });
      this.unselected = [];
    } else if (type === "deselect-all") {
      this.unselected = this.unselected.concat(this.selected);
      this.unselected.forEach((x) => (x.active = false));
      this.unselected.sort((a, b) => {
        if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
        if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;
        return 0;
      });
      this.selected = [];
    }

    // needs to event for every change at the moment
    this.dispatchEvent(
      new CustomEvent("pick-list-updated", {
        detail: {
          selected: this.selected,
          unselected: this.unselected,
        },
        bubbles: true,
        composed: true,
      })
    );
  }

  render() {
    // console.log("render", this.selected, this.unselected);
    return html`
      <wex-row-item justifyContent="stretch">
        <wex-col-item>
          <span>${this._string["_available"]}</span>
          <ul>
            ${!this.unselected.length
              ? html`
                  <li>
                    <span class="italic"
                      >${this._string["_no_item_available"]}</span
                    >
                  </li>
                `
              : null}
            ${this.unselected.map(
              (item, idx) =>
                html`<li
                  ?active="${item.active}"
                  @click="${this._handleSelect.bind(this, "deselect", idx)}"
                >
                  <span>${item.name}</span>
                </li>`
            )}
          </ul>
        </wex-col-item>

        <wex-fixed-item justifyContent="center" alignItems="center">
          ${this._actions.map(
            (x) => html`
              <iron-icon
                icon="${x.icon}"
                title="${this._string[x.label]}"
                @click="${this._handleAction.bind(this, x.name)}"
              ></iron-icon>
            `
          )}
        </wex-fixed-item>

        <wex-col-item>
          <span>${this._string["_selected"]}</span>
          <ul>
            ${!this.selected.length
              ? html`
                  <li>
                    <span class="italic"
                      >${this._string["_no_item_selected"]}</span
                    >
                  </li>
                `
              : null}
            ${this.selected.map(
              (item, idx) =>
                html`<li
                  ?active="${item.active}"
                  @click="${this._handleSelect.bind(this, "selected", idx)}"
                >
                  <span>${item.name}</span>
                </li>`
            )}
          </ul>
        </wex-col-item>
      </wex-row-item>
    `;
  }
}

customElements.define("wex-list-picker", WexListPicker);
