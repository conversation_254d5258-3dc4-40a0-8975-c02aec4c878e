export default {
  "en-US": {
    locale_short_name: "en-US",
    locale_name: "English US",
    // login
    _login: "Login",
    _logout: "Logout",
    _username: "Userna<PERSON>",
    _password: "Password",
    // nav
    _home: "Home",
    _tasks: "Tasks",
    _browse: "Browse",
    _search: "Search",
    _reports: "Reports",
    _publish: "Publish",
    _processes: "Processes",
    _preview: "Preview",
    _editor: "Editor",
    // common
    _language: "Language",
    _all: "All",
    _any: "Any",
    _ok: "Ok",
    _confirm: "Confirm",
    _project: "Project",
    _projects: "Projects",
    _categories: "Categories",
    _name: "Name",
    _index: "Index",
    _date: "Date",
    _description: "Description",
    _close: "Close",
    _save: "Save",
    _cancel: "Cancel",
    _cut: "Cut",
    _copy: "Copy",
    _paste: "Paste",
    _create: "Create",
    _rename: "<PERSON><PERSON>",
    _delete: "Delete",
    _deleted: "Deleted",
    _properties: "Properties",
    _prev: "Previous",
    _next: "Next",
    _select: "Select",
    _reload: "Reload",
    _import: "Import",
    _creator: "Creator",
    _folder: "Folder",
    _branch: "Branch",
    _rootmap: "Rootmap",
    _rootmaps: "Rootmaps",
    _comment: "Comment",
    _utilities: "Utilities",
    _diagram: "Diagram",
    _primary: "Primary",
    _reference: "Reference",
    _left: "Left",
    _right: "Right",
    _claimed: "Claimed",
    _size: "Size",
    _unclaimed: "Unclaimed",
    _refresh: "Refresh",
    _remove: "Remove",
    _success: "Success",
    _more: "More",
    _actions: "Actions",
    _options: "Options",
    _user: "User",
    _status: "Status",
    _active: "Active",
    _reset: "Reset",
    _reviewed: "Reviewed",
    _imported: "Imported",
    _uploaded: "Uploaded",
    _title: "Title",
    _history: "History",
    _lock: "Lock",
    _unlock: "Unlock",
    _available: "Available",
    _selected: "Selected",

    _type: "Type",
    _warning: "Warning",

    // common phrases
    _edit_x: (x) => `Edit ${x}`,
    _select_x: (x) => `Select ${x}`,
    _close: "Close",

    // context menu
    _edit: "Edit",
    _edit_topic: "Edit Topic",
    _edit_ditamap: "Edit Inline Topics",
    _open_ditamap: "Open Ditamap",
    _export_folder: "Export folder",
    _export_files: "Export file(s)",
    _publish: "Publish",
    //
    _all_project_cats: "All Project Categories",
    _all_tasks: "Claimed status",
    _notclaimed:
      "TASK NOT CLAIMED: Task must be claimed to perform this operation",
    _gotohomepage: "Go to home page",
    _edit: "Open in editor",
    _edit_head: "Open head version in editor",
    _changelanguage: "Change language",
    _task_filters_label: "",
    _browse_filters_label: "FILTERS",
    _search_filters_label: "FILTERS",
    _search_results_view: "Search Results View",
    _table_view: "Table View",
    _filters_label: "FILTERS",
    _clearfilters: "Reset Filters",
    _selectproject: "Select Project",
    _isClaimed: "Claimed",
    _processDefName: "Process Name",
    _processInstDescription: "Process Description",
    _processInstId: "Process Instance Id",
    _projectId: "Project Id",
    _projectName: "Project Name",
    _taskActiveDate: "Last Active Date",
    _taskDefDescription: "Task Definition Description",
    _taskDefId: "Task Definition Id",
    _taskDefName: "Task Name",
    _taskId: "Task Definition Id",
    _taskInstComment: "Task Instance Comment",
    _taskInstId: "Task Inst Id",
    _taskStartDate: "Start Date",
    _processId: "Process Id",
    _userWexCapability: "Wex Capability",
    _filepackage: "File Package",
    _notaskselected: "No task selected",
    _startTime: "Start time",
    _completionTime: "Completion time",
    _taskName: "Task name",
    _claimtask: "Claim task",
    _unclaimtask: "Unclaim task",
    _claimAction: "claim/unclaim",
    _viewtaskdetails: "View task details",
    _taskhistorydetails: "History Details",
    _resLblId: "Resource Id",
    _editmap: "Edit map",
    _taskclaimed: "Task claimed",
    _taskunclaimed: "Task unclaimed",
    // columns, properties
    _fileproperties: "File properties",
    _fileid: "File id",
    _versionnumber: "Version number",
    _pathid: "Path id",
    _rootelement: "Root element",
    _lockOwner: "Lock owner",
    _lock_owner: "Lock Owner",
    _lockLocation: "Lock location",
    _lock_location: "Lock Location",
    _rogue_lock: "Rogue Lock",
    _lockDate: "Lock date",
    _lock_date: "Lock Date",
    _modifieddate: "Modified date",
    _fileremoved: "File removed",
    _containing_folder: "Containing Folder",
    _linkor_files: "Linkor Files",
    _uri: "Original Uri",
    _attribute_name: "Attribute Name",
    _new_target_uri: "New Target Uri",
    //
    _openfile: "Open file",
    _addfile: "Add file",
    _importfile: "Import file",
    _import_zip: "Import Zip",
    _createfile: "Create file",
    _selectfile: "Select file",
    _addtopic: "Add topic",
    _createtopic: "Create topic",
    _savejumptoprojectfolder: "Jump to project folder",
    _includesubfolders: "Include subfolders",
    _ditaClass: "DitaClass",
    _mimeType: "Mimetype",
    _roomdeleted: "The room has been deleted successfully",
    _roomdeleteerror: "There was an error deleting room: ",
    _norooms: "There are no rooms currently in existence.",
    _nopeeruserinfo: "Select one (1) room to see peer information.",
    _nofiles: "No files match criteria",
    _fileadded: "File added to package",
    _highlightfolder: "Highlight folder",
    _filetype: "File type",
    _selectuploadfiles: "Select files to upload",
    _nofilesselected: "No files selected yet",
    _dupefilenameerror: "Duplicate file name can not create target",
    _serverError: "Server Error",
    _unknownerror: "Unknown error",
    _errordialog: "Error Dialog",
    _errorgeneral: "An error occurred.",
    _selecttemplate: "Select template",
    _filename: "File name",
    _enterfilename: "Enter a file name",
    _checkfilename: "Validate filename",
    _filenamevalid: "File name valid",
    _mustbexml: "File name must end in .xml or .ditamap",
    _invalidfilename: "Invalid file name",
    _filenamealreadyexistsinpackage: "File name already exists in package",
    _taskproperties: "Task properties",
    _projectcatlable: "Project Category",
    _asofnowlabel: "As of 'Now'",
    _asoflabellabel: "Label",
    _asofdatelabel: "As of Date (GMT)",
    _setasofdate: "Set As Of Date",
    _newfromtemplate: "Create file",
    _accessdenied: "Access Denied",
    _addremovefilter: "Add/Remove filters",
    _filtername: "Filter Name",
    _selectfilters: "Search Filters",
    _selectfolder: "Select Folder",
    _utc: "GMT",
    _openfilelocation: "Open file location",
    _profile: "Filter profile",
    _previewmru: "Recently viewed",
    _nomaps: "No maps found",
    _selectrootmap: "Select Rootmap",
    _selectprofile: "Select Filter Profile",
    _noprofiles: "No filter profiles found",
    _profiles: "Filter profiles",
    _style: "Style",
    _previewnewwindow: "Preview in new window",
    _opendocs: "open documents",
    _noclaimedtasks: "No claimed tasks available",
    _verCreateDate: "Create Date",
    _lineageId: "Lineage Id",
    _rootElementName: "Root Element Name",
    _openfiles: "Open files",
    _claimedtasks: "Claimed tasks",
    _notasks: "No tasks",
    _finishtask: "Finish task",
    _badcredentials: "Incorrect credentials",
    _toggledone: "Toggle reviewed status",
    _pastesib: "Paste as next sibling",
    _pastechild: "Paste as first child",
    _alreadylockedelseware: "Locked by you at: ",
    _lockedby: "Locked by another user: ",
    _lockedbybase: "Locked by",
    _dirtyeditorslable: "Unsaved files",
    _forceclose: "Lose All Changes",
    _saveall: "Save All",
    _savecomment: "Save Comment",
    _folderPath: "Folder path",
    _filePath: "File path",
    _filter_by_project: "Filter by project",
    _jump_to_project_folder: "Jump to project home folder",
    _jump_to_a_project_folder: "Jump to a project home folder",
    _notloggedinSummary: "You are not logged in",
    _completionTime: "Completion Time",
    _isXml: "isXml",
    _noditamapsopen: "No ditamaps open",
    _verNum: "Version Number",
    _verCreator: "Version Creator",
    _openprevnext: "Open previous or next file",
    _selecttask: "Select task",
    _displayonlyprimaryfiles: "Display only primary files",
    _displayonlyreferencefiles: "Display only reference files",
    _noprimaryfiles: "No primary files match criteria",
    _noreferencefiles: "No reference files match criteria",
    // lock state
    _not_locked: "Not locked",
    _locked_concurrent: "File is locked to shared mode and ready for editing",
    _locked_by_other: "File is already check out by another user",
    _room_locked_by_user: "You have this file locked and ready for editing",
    _sesh_locked_by_user: "You have this file locked and ready for editing",
    _xdocs_locked_by_user: "You have this file locked at another location",
    // error dialog
    _alreadycheckedout:
      "File is already check out by another user, or in a different location",
    // capability
    _insufficientcap: "You lack capability (permission) for this mode",
    _global: "Global",
    _workflow: "Workflow",
    _globalCapability: "Global capability",
    _effectivecapability: "Effective capability",
    _effectivecapabilitysource: "Eff. Capability Source",
    _filedetails: "File details",
    _lockdetails: "Lock information",
    _editmodesavailable: "Edit Modes Available",
    _nolock: "No active lock.",
    _capdetails: "Capability information",
    _permissions: "File Permissions",
    _allowed: "Allowed",
    _verdetails: "Version information",
    _noediting_readonly: "No editing in read only mode",
    _saveorlosedialog:
      "Click <strong>OK</strong> to lose changes and close anyway<br/><br/>Click <strong>Cancel</strong> to return to document so that you may review and save changes",
    _documentdirty: "Document has unsaved changes",
    _viewdetails: "Task details",
    _historydetails: "History details",
    _not_available_in_read_only_mode: "Not available in read only mode",
    _unsavedchanges: "Unsaved changes",
    _saveallmaps: "Save all maps",
    _claimtoedit: "Claim task to review and edit topics",
    _opensidebar: "Open side bar",
    _closesidebar: "Close side bar",
    _resPathId: "Resource path",
    _parentmaps: "Parent Maps",
    _noopenfiles: "No open file",
    _select_project_label: "All projects",
    _all_tasks_label: "All tasks",
    _rootmap_selector_label: "Select rootmap",
    _profile_selector_label: "Select profile",
    _openInEditor: "File is open in editor",
    // editor > wex editor controls
    _readonly: "Read Only",
    _review: "Review",
    _author: "Author",
    _exclusive: "Exclusive",
    _concurrent: "Shared",
    _readonly_mode: "Read Only mode",
    _review_mode: "Review mode",
    _author_mode: "Author mode",
    _change_mode: "Change mode",
    _print: "Print",
    _tags: "Tags",
    _validate: "Validate",
    _comments: "Comments",
    _find: "Find",
    _undo: "Undo",
    _redo: "Redo",

    // editor > dialog > create image file
    _editor_create_image: "Create Image",
    _editor_create_image_temp: "New image name",

    _msg_lock_mode_fail: "Failed to edit topic as it is used in another room.",

    // editor > wex navigation tabs
    _ditamaps: "Ditamaps",
    _fileinfo: "File info",
    _mru: "Recent",
    //
    _mostrecentlyused: "Most Recently Used",
    _nofileseditormru: "No files opened yet this session",
    _canteditbookmaps: "Can't edit bookmaps",
    _unabletolockditabase: "Unable to lock ditabase",
    _ditabaselockrule:
      "You must be able to lock all topics in order to open a ditabase.",
    _clearsearch: "Clear search term",
    _insufficientpermsopenfile:
      "Insufficient permissions to open files this way",
    _incominglinkserror: "Delete not allowed: incoming links",
    _invalidfiletype: "Invalid file type",
    _ditaclass: "Dita class",
    _permissiondenied: "Permission denied",
    _savebeforefinish:
      "Cannot finish, or unclaim, task while unsaved files are open in editor. Please save or close open files and try again.",
    _messagedialog: "Message dialog",
    _dirtyeditorsmessage:
      "You have the following unsaved files open in the editor. Please choose how to proceed.",
    _unabletolockditamap: "Unable to check-out ditamap",
    _unabletolockditamapdesc: "One or more submaps are already checked-out:",
    _extendedcreate: "Extended new from template",
    _folderrequired: "Please select a folder",
    _templaterequired: "Please select a template",
    _filerequired: "Please enter a filename",
    _filenamealreadyexists:
      "A file with the same name already exists in this folder",
    _noTemplatesFoundFor: "No templates found for ",
    _cantunlockditabase: "Click close (x) to unlock a ditaBase",
    _closereachableheader: "Confirm close reachable files",
    _closereachable:
      "Click ok to confirm closing open files related to this task",
    _openfileslabel: "Open files will be closed",

    // reports
    _locks: "Locks",
    _rooms: "Rooms",
    _room_report_col1: "Seed Filename",
    _room_report_col2: "Lock Mode",
    _room_report_col3: "Creating User",
    _select_users: "Select Users",
    _reports_selected_users: "Selected User(s)",
    _reports_show_rooms: "Show locks attached to rooms only",
    _reports_dialog_unlock_warning:
      "Unlocking locks could interrupt a user's editing session and work could be lost.  Do you want to proceed?",

    _msg_no_file_selected: "Select a file to unlock first.",
    _msg_unlock_files_success: "Unlocked files successfully.",
    _msg_cannot_unlock_rooms: "Rooms cannot be unlocked manually.",

    // dialog > select-editor-mode
    _select_editor_mode: "Edit Settings",
    _edit_mode_options: "Edit Mode",
    _edit_mode_option_readonly: "Read Only",
    _edit_mode_option_review: "Review",
    _edit_mode_option_author: "Author",
    _lock_mode_options: "Edit Access",
    _lock_mode_option_exclusive: "Exclusive",
    _lock_mode_option_concurrent: "Shared",
    _edit_mode_required: "Edit Mode is required.",
    _lock_mode_required: "Edit Access is required.",

    // browse > folderpane, dialog
    _browse_search_folder: "Search folder...",

    // dialog > wex-select
    _select_all: "Select All",
    _deselect: "Deselect",
    _deselect_all: "Deselect All",
    _no_item_available: "No item available",
    _no_item_selected: "No item selected",

    // browse > dialog

    _browse_create_folder: "New folder",
    _browse_create_folder_msg: "Enter a new folder name",
    _browse_create_folder_sub: "New folder name",
    _browse_create_folder_btn: "Create folder",
    _browse_rename_folder: "Rename folder",
    _browse_rename_folder_msg: "Enter a new folder name",
    _browse_rename_folder_sub: "New folder name",
    _browse_rename_folder_btn: "Rename folder",
    _browse_delete_folder: "Delete folder",
    _browse_delete_folder_msg: (x) =>
      `Are you sure you want to permanently delete "${x}"?`,
    _browse_rename_file: "Rename file",
    _browse_rename_file_msg: "Enter a new file name",
    _browse_rename_file_sub: "New file name",
    _browse_rename_file_btn: "Rename file",
    _browse_delete_file: "Delete file",
    _browse_delete_msg: (x) =>
      `Are you sure you want to permanently delete "${x}"?`,

    _msg_browse_create_folder_success: "Created folder successfully.",
    _msg_browse_rename_folder_success: "Renamed folder successfully.",
    _msg_browse_move_folder_success: "Moved folder successfully.",
    _msg_browse_delete_folder_success: "Deleted folder successfully.",
    _msg_browse_cut_file_success: "Moved file successfully.",
    _msg_browse_copy_file_success: "Copied file successfully.",
    _msg_browse_delete_file_success: "Deleted file successfully.",
    _msg_browse_rename_file_success: "Renamed file successfully.",
    // warn
    _warn_file_edit_type_invalid: "Cannot edit this file type.",
    _warn_project_required: "Please select a project.",

    // publish
    _definition: "Definition Name",
    _definitions: "Definitions",
    _run: "Run",
    _adhoc: "Adhoc",
    _jobs: "Jobs",
    _jobs_total: "Outputs",

    _all_proj_categories: "All categories",
    _no_projects: "No projects found",
    _no_pubdefs: "No definitions found",
    _project_name: "Project Name",
    _pubdef_name: "Definition Name",
    _publish_select_project_inst: "Select a project.",
    _publish_select_pubdef_inst: "Select a publish definition.",

    // publish > definitions
    _publications: "Publications",
    _pub_content: "Publication Content",
    _languages: "Languages",
    _output_format: "Output Format",
    _no_item: "No item",
    _msg_publish_defs_create_pubdef_success: "Created definition successfully.",
    _msg_publish_defs_delete_pubdef_success: "Deleted definition successfully.",

    // publish > run
    _publish_run_create_pubdef: "Go Create Definition",
    _publish_run_no_pubdefs_msg:
      "You don't have any definitions created for this project yet.",
    _publish_run_no_pubdefs_inst:
      "Create a new definition or navigate to a different project.",
    _notifications: "Notifications",
    _notifications_msg: "Receive e-mail notifications when there is an update.",
    _schedule: "Schedule",
    _msg_pubish_run_success:
      "Requested to publish selected definitions successfully.",

    // publish > run > dialog
    _confirm_publish_run_header: "You're about to run a job",
    _confirm_publish_run_notif_inst:
      "Add user groups to receive notifications regarding this job",
    _confirm_publish_run_lang_inst:
      "Uncheck any language to restrict publishing",
    _confirm_publish_run_msg:
      "Are you sure you want to run the selected definitions?",

    // publish > jobs
    _def_jobs: "Jobs",
    _adhoc_jobs: "Adhoc Jobs",
    _output_jobs: "Outputs",
    _id: "Id",
    _publisher: "Publisher",
    _start_date: "Start Date",
    _end_date: "End Date",
    _log: "Log",
    _output: "Output",
    _publication: "Publication",
    _format: "Format",
    _ditaval: "Ditaval",
    _status: "Status",
    _progress: "Progress",

    _outputJobInfoId: "Output Job Id",
    _dPubContentName: "?",
    _outputFileName: "Output Filename",
    _startDate: "Start Date",
    _endDate: "End Date",
    _langCode: "Language Code",
    _outputTypeName: "Format",
    _pctComplete: "Status",

    // publish > dialog
    _create_pubdef: "Create Definition",

    // process
    _candidate_tasks: "Tasks",
    _candidates: "Candidates",
    _candidate_role: "Role",
    _process_package_title: "Title",
    _process_package_name: "Name",
    // error
    _error: "Error",
    _error_403: "Forbidden",
    _error_404: "Not Found",
    _error_409: "Conflict",
    _error_412: "Precondition Failed",
    _error_500: "Internal Server Error",
    _error_unk: "Unknown Error",
    _error_does_not_exist: (x) => `${x} does not exist.`,
    _error_folder_name_exists: "A folder with the same name already exists.",
    _error_folder_name_invalid: "Invalid folder name.",
    _error_file_name_exists: "A file with the same name already exists.",
    _error_file_name_invalid: "Invalid file name.",
    _error_invalid_value: "Please enter a valid value.",
    _error_empty_value: "This field cannot be empty.",
    // error
    _error_readonly_mode: "You are in read only mode.",
    _error_lock_status_changed:
      "File's lock status has changed since its last activity.",

    _export_files_dialog: "Export File(s)",
    _publish_files_dialog: "Publish",
    _export_include_linked_files: "Include Linked Files",

    _wip: "This page or feature is under development.",

    _desc: "Description",
    _pubDefId: "Id",
  },
};
