{"mappings": "ACAA,soHA+JA,0CAMA,mDAKA,qHAOA,oHAOA", "sources": ["wex3.8bc001fb.css", "src/styles/main.css"], "sourcesContent": [":root {\n  --clr-primary: #1d3d62;\n  --clr-primary-light: #336cac;\n  --clr-primary-ultra-light: #6c9cd3;\n  --clr-primary-100: #b5cde9;\n  --clr-secondary: #00a9c8;\n  --clr-tertiary: #5c6bc0;\n  --clr-white: #fafafa;\n  --clr-gray-ultra-light: #f2f2f2;\n  --clr-gray-light: #cdcdcd;\n  --clr-gray: #838383;\n  --clr-gray-dark: #383838;\n  --clr-black: #070707;\n  --clr-red: #ff0800;\n  --clr-rgb-gray-ultra-light: 242, 242, 242;\n  --clr-bg: #e0e6f305;\n  --clr-positive: #107e3e;\n  --clr-warn: #e9730c;\n  --clr-negative: #b00;\n  --clr-info: #0a6ed1;\n  --clr-neutral: #6a6d70;\n  --clr-disabled: var(--clr-neutral);\n  --header-color: var(--secondary);\n  --subheader-color: var(--clr-gray-ultra-light);\n  --row-selected-background: var(--primary-100);\n  --on-primary-color: rgba(var(--on-primary-color-rgb), 1);\n  --on-surface-link-color: #265b91;\n  --font-family-sans: \"Lato\", \"Roboto\", \"Helvetica\", \"sans-serif\";\n  --font-color: var(--clr-gray-dark);\n  --header-with-filters-height: 10rem;\n  --spacing-xxs: .125px;\n  --spacing-xs: .25rem;\n  --spacing-sm: .5rem;\n  --spacing-md: 1rem;\n  --spacing-lg: 2rem;\n  --spacing-xl: 4rem;\n  --primary-100: #e0e6f3;\n  --primary-300: #9caed1;\n  --primary-500: #0063a3;\n  --primary-700: #003d66;\n  --primary-900: #00253d;\n  --primary: var(--primary-500);\n  --color-text: #43515a;\n  --secondary: var(--clr-secondary);\n  --headerlogo-url: url(\"logo-bluestream.5ee50f0f.svg\");\n  --on-primary-color-rgb: 255, 255, 255;\n  --on-primary-light-variant-color-rgb: 0, 0, 0;\n  --on-primary-dark-variant-color-rgb: 255, 255, 255;\n  --target-search-word-highlight-color: khaki;\n  --on-primary-light-variant-color: rgba(var(--on-primary-light-variant-color-rgb), 1);\n  --on-primary-dark-variant-color: rgba(var(--on-primary-dark-variant-color-rgb), 1);\n  --on-primary-muted: rgba(var(--on-primary-color-rgb), .7);\n  --on-primary-light-variant-muted: rgba(var(--on-primary-light-variant-color-rgb), .7);\n  --on-primary-dark-variant-muted: rgba(var(--on-primary-dark-variant-color-rgb), .7);\n  --on-primary-link-color-muted: rgba(var(--on-primary-color-rgb), .7);\n  --on-primary-link-color-active: rgba(var(--on-primary-color-rgb), 1);\n  --on-surface-color-rgba: 54, 54, 54;\n  --on-surface-color: rgba(var(--on-surface-color-rgba), 1);\n  --on-surface-color-muted: rgba(var(--on-surface-color-rgba), .7);\n  --h1-font-size: 3rem;\n  --h2-font-size: 2rem;\n  --h3-font-size: 1.5rem;\n  --subtitle-font-size: 1rem;\n  --button-font-size: .875rem;\n  --button-font-size-small: .75rem;\n  --body-font-size: .875rem;\n  --preview-font-size: 1.1rem;\n  --overline-font-size: .875rem;\n  --caption-font-size: .75rem;\n  --app-drawer-width: 300px;\n  --article-title-font-size: 2.6rem;\n  --article-title-line-height: 3rem;\n  --article-title-font-weight: 300;\n  --article-title-text-transform: none;\n  --block-padding-vertical-large: 50px;\n  --block-padding-vertical: 20px;\n  --global-border: #ddd;\n  --global-border-radius: 4px;\n  --app-header-height: 128px;\n  --block-padding: 20px;\n  --block-padding-large: 50px;\n  --import-table-height: 20rem;\n  --import-table-border: 1px solid #f2f2f2;\n  --surface-border: 1px solid #dcdcdc;\n  --border-radius: 4px;\n  --max-topic-width: 980px;\n  --Button_Selected_Background: #0854a0;\n  --Button_Selected_BorderColor: #0854a0;\n  --Button_Selected_TextColor: #fff;\n  --Button_Hover_BorderColor: #0854a0;\n  --Button_Hover_Background: #ebf5fe;\n  --Button_Hover_TextColor: #0854a0;\n  --Button_Active_Background: #0854a0;\n  --Button_Active_BorderColor: #0854a0;\n  --Button_Active_TextColor: #fff;\n  --sapButton_Emphasized_Background: var(--primary) !important;\n  --sapButton_BorderCornerRadius: 0 !important;\n  --sapFontFamily: var(--font-family-sans) !important;\n  --sapField_BorderColor: var(--primary-300) !important;\n  --sapButton_BorderColor: var(--primary-300) !important;\n  --sapButton_TextColor: var(--primary) !important;\n  --sapSelectedColor: var(--color-text) !important;\n  --sapContent_HeaderShadow: none !important;\n  --sapObjectHeader_Background: none !important;\n  --_ui5-v1-24-20_input_margin_top_bottom: 0 !important;\n  --_ui5-v2-11-0_input_margin_top_bottom: 0 !important;\n  --_ui5-v1-24-20_input_border_radius: 0 !important;\n}\n\n* {\n  box-sizing: border-box;\n  border: 0;\n  margin: 0;\n}\n\nhtml {\n  background-color: var(--clr-bg);\n  font-size: 16px;\n}\n\nbody {\n  background-color: var(--clr-bg);\n  height: 100vh;\n  font-size: var(--body-font-size);\n  font-family: var(--font-family-sans);\n}\n\n.rowselected {\n  box-sizing: border-box;\n  background-color: var(--row-selected-background);\n  margin: -8px -4px;\n  padding: 8px 4px;\n}\n\n* {\n  box-sizing: border-box;\n}\n/*# sourceMappingURL=wex3.8bc001fb.css.map */\n", ":root {\r\n  /* colors */\r\n\r\n  --clr-primary: hsla(212, 54%, 25%, 1); /* #265b91 */\r\n  --clr-primary-light: hsla(212, 54%, 43.75%, 1);\r\n  --clr-primary-ultra-light: hsla(212, 54%, 62.5%, 1);\r\n  --clr-primary-100: hsla(212, 54%, 81.25%, 1);\r\n  --clr-secondary: #00a9c8;\r\n\r\n  --clr-tertiary: #5c6bc0;\r\n  --clr-white: #fafafa;\r\n  --clr-gray-ultra-light: #f2f2f2;\r\n  --clr-gray-light: #cdcdcd;\r\n  --clr-gray: #838383;\r\n  --clr-gray-dark: #383838;\r\n  --clr-black: #070707;\r\n  --clr-red: #ff0800;\r\n\r\n  --clr-rgb-gray-ultra-light: 242, 242, 242;\r\n\r\n  --clr-bg: #e0e6f305;\r\n\r\n  --clr-positive: #107e3e;\r\n  --clr-warn: #e9730c;\r\n  --clr-negative: #b00;\r\n  --clr-info: #0a6ed1;\r\n  --clr-neutral: #6a6d70;\r\n  --clr-disabled: var(--clr-neutral);\r\n\r\n  --header-color: var(--secondary);\r\n  --subheader-color: var(--clr-gray-ultra-light);\r\n  --row-selected-background: var(--primary-100);\r\n\r\n  --on-primary-color: rgba(var(--on-primary-color-rgb), 1);\r\n\r\n  --on-surface-link-color: #265b91;\r\n\r\n  --font-family-sans: \"Lato\", \"Roboto\", \"Helvetica\", \"sans-serif\";\r\n  --font-color: var(--clr-gray-dark);\r\n\r\n  --header-with-filters-height: 10rem; /** used to calculate body size in some cases */\r\n\r\n  /* Spacing */\r\n  --spacing-xxs: 0.125px;\r\n  --spacing-xs: 0.25rem;\r\n  --spacing-sm: 0.5rem;\r\n  --spacing-md: 1rem;\r\n  --spacing-lg: 2rem;\r\n  --spacing-xl: 4rem;\r\n\r\n  /* bds-colors */\r\n  --primary-100: #e0e6f3;\r\n  --primary-300: #9caed1;\r\n  --primary-500: #0063a3;\r\n  --primary-700: #003d66;\r\n  --primary-900: #00253d;\r\n  --primary: var(--primary-500);\r\n  --color-text: #43515a;\r\n  --secondary: var(--clr-secondary);\r\n  /* --secondary: #22817b; */\r\n\r\n  /* Sap Overrides */\r\n  --sapButton_Emphasized_Background: var(--primary) !important;\r\n  --sapButton_BorderCornerRadius: 0 !important;\r\n  --sapFontFamily: var(--font-family-sans) !important;\r\n  --sapField_BorderColor: var(--primary-300) !important;\r\n  --sapButton_BorderColor: var(--primary-300) !important;\r\n  --sapButton_TextColor: var(--primary) !important;\r\n  --sapSelectedColor: var(--color-text) !important;\r\n  --sapContent_HeaderShadow: none !important;\r\n  --sapObjectHeader_Background: none !important;\r\n  --_ui5-v1-24-20_input_margin_top_bottom: 0 !important;\r\n  --_ui5-v2-11-0_input_margin_top_bottom: 0 !important;\r\n  --_ui5-v1-24-20_input_border_radius: 0 !important;\r\n}\r\n\r\n/* not used, keep temp just in case */\r\n:root {\r\n  /* logo */\r\n  --headerlogo-url: url(\"/src/styles/logo-bluestream.svg\");\r\n\r\n  /* type colors */\r\n  --on-primary-color-rgb: 255, 255, 255;\r\n  --on-primary-light-variant-color-rgb: 0, 0, 0;\r\n  --on-primary-dark-variant-color-rgb: 255, 255, 255;\r\n\r\n  --target-search-word-highlight-color: khaki;\r\n\r\n  --on-primary-light-variant-color: rgba(\r\n    var(--on-primary-light-variant-color-rgb),\r\n    1\r\n  );\r\n  --on-primary-dark-variant-color: rgba(\r\n    var(--on-primary-dark-variant-color-rgb),\r\n    1\r\n  );\r\n\r\n  --on-primary-muted: rgba(var(--on-primary-color-rgb), 0.7);\r\n  --on-primary-light-variant-muted: rgba(\r\n    var(--on-primary-light-variant-color-rgb),\r\n    0.7\r\n  );\r\n  --on-primary-dark-variant-muted: rgba(\r\n    var(--on-primary-dark-variant-color-rgb),\r\n    0.7\r\n  );\r\n\r\n  --on-primary-link-color-muted: rgba(var(--on-primary-color-rgb), 0.7);\r\n  --on-primary-link-color-active: rgba(var(--on-primary-color-rgb), 1);\r\n\r\n  --on-surface-color-rgba: 54, 54, 54;\r\n  --on-surface-color: rgba(var(--on-surface-color-rgba), 1);\r\n  --on-surface-color-muted: rgba(var(--on-surface-color-rgba), 0.7);\r\n\r\n  --h1-font-size: 3rem;\r\n  --h2-font-size: 2rem;\r\n  --h3-font-size: 1.5rem;\r\n  --subtitle-font-size: 1rem;\r\n  --button-font-size: 0.875rem;\r\n  --button-font-size-small: 0.75rem;\r\n  --body-font-size: 0.875rem;\r\n  --preview-font-size: 1.1rem;\r\n  --overline-font-size: 0.875rem;\r\n  --caption-font-size: 0.75rem;\r\n\r\n  --app-drawer-width: 300px;\r\n\r\n  --article-title-font-size: 2.6rem;\r\n  --article-title-line-height: 3rem;\r\n  --article-title-font-weight: 300;\r\n  --article-title-text-transform: none;\r\n  --block-padding-vertical-large: 50px;\r\n  --block-padding-vertical: 20px;\r\n  --global-border: #ddd;\r\n  --global-border-radius: 4px;\r\n\r\n  --app-drawer-width: 300px;\r\n  --app-header-height: 128px;\r\n\r\n  --block-padding: 20px;\r\n  --block-padding-large: 50px;\r\n  --import-table-height: 20rem;\r\n  --import-table-border: 1px solid #f2f2f2;\r\n\r\n  --surface-border: 1px solid #dcdcdc;\r\n  --border-radius: 4px;\r\n  --max-topic-width: 980px;\r\n\r\n  --Button_Selected_Background: #0854a0;\r\n  --Button_Selected_BorderColor: #0854a0;\r\n  --Button_Selected_TextColor: #fff;\r\n  --Button_Hover_BorderColor: #0854a0;\r\n  --Button_Hover_Background: #ebf5fe;\r\n  --Button_Hover_TextColor: #0854a0;\r\n  --Button_Active_Background: #0854a0;\r\n  --Button_Active_BorderColor: #0854a0;\r\n  --Button_Active_TextColor: #fff;\r\n}\r\n\r\n* {\r\n  margin: 0;\r\n  border: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nhtml {\r\n  background-color: var(--clr-bg);\r\n  font-size: 16px;\r\n}\r\n\r\nbody {\r\n  height: 100vh;\r\n  background-color: var(--clr-bg);\r\n  font-size: var(--body-font-size);\r\n  font-family: var(--font-family-sans);\r\n}\r\n\r\n.rowselected {\r\n  margin: -8px -4px -8px -4px;\r\n  padding: 8px 4px 8px 4px;\r\n  box-sizing: border-box;\r\n  background-color: var(--row-selected-background);\r\n}\r\n\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n"], "names": [], "version": 3, "file": "wex3.8bc001fb.css.map"}