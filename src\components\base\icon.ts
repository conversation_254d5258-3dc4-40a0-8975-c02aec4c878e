import { LitElement, html, css } from "lit";
import { customElement, property } from "lit/decorators.js";

@customElement("wex-icon")
export class WexIcon extends LitElement {
  @property({ type: String }) icon = "";
  @property({ type: Boolean }) pointer = true;

  static styles = css`
    .icon {
      width: 1.5rem;
      height: 1.5rem;
      padding: 0.25rem;
      border-radius: 0.25rem;
    }
    .icon:hover {
      background-color: var(--clr-primary-ultra-light);
    }

    .pointer {
      cursor: pointer;
    }
  `;

  render() {
    return html`
      <iron-icon
        icon="${this.icon ?? "more-horiz"}"
        class="${this.pointer ? "pointer" : ""} icon"
      ></iron-icon>
    `;
  }
}
