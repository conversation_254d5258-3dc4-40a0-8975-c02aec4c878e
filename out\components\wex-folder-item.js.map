{"version": 3, "file": "wex-folder-item.js", "sourceRoot": "", "sources": ["../../src/components/wex-folder-item.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAkB,MAAM,KAAK,CAAC;AAC5D,OAAO,iCAAiC,CAAC;AACzC,OAAO,sCAAsC,CAAC;AAC9C,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAIzD,IAAM,aAAa,GAAnB,MAAM,aAAc,SAAQ,UAAU;IAQpC;QACE,KAAK,EAAE,CAAC;QALV,kBAAa,GAAW,QAAQ,CAAC;QAEjC,kBAAa,GAAW,GAAG,CAAC;QAI1B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,iBAAiB;QACf,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzD,CAAC;IAES,OAAO,CAAC,kBAAkC;QAClD,IAAI,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAED,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO;QAEvB,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC,IAAI;QACtC,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,kBAAkB,EAAE;gBAClC,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,IAAI;aACb,CAAC,CACH,CAAC;QACJ,CAAC;IACH,CAAC;IAED,wBAAwB;QACtB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,IAAI,CAAA;;kBAEC,IAAI,CAAC,WAAW;0BACR,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;cAI/C,IAAI,CAAC,aAAa;mBACb,IAAI,CAAC,IAAI,CAAC,EAAE;oBACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;0BACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;uBAC7B,IAAI,CAAC,WAAW;;YAE3B,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;;6BAEpB,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAA;oCACmB,IAAI,CAAC,aAAa;iBACrC,IAAI,CAAC,IAAI,CAAC,EAAE;kBACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;wBACpB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;qBAClC,IAAI,CAAC,WAAW;;UAE3B,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;aAClC,CAAC;QACV,CAAC;IACH,CAAC;IAED,YAAY;QACV,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAA;;oBAEC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;sBACxB,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAA;;oBAEC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;sBACxB,CAAC;YACjB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAA;;;oBAGG,CAAC;QACjB,CAAC;IACH,CAAC;IAED,qBAAqB;QACnB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAA;UACP,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CACtB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAA;;;uBAGD,IAAI;6BACE,IAAI,CAAC,WAAW;8BACf,IAAI,CAAC,WAAW;iCACb,IAAI,CAAC,cAAc;;WAEzC,CACF;YACG,CAAC;QACT,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAA,EAAE,CAAC;QAChB,CAAC;IACH,CAAC;IAED,eAAe,CAAC,CAAC;QACf,CAAC,CAAC,cAAc,EAAE,CAAC;IACrB,CAAC;IAED,YAAY,CAAC,CAAC;QACZ,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,YAAY,CAAC,CAAC;QACZ,CAAC,CAAC,eAAe,EAAE,CAAC;IACtB,CAAC;IAED,WAAW,CAAC,CAAC;QACX,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;IACtB,CAAC;IAED,YAAY,CAAC,CAAC;QACZ,CAAC,CAAC,eAAe,EAAE,CAAC;IACtB,CAAC;IAED,OAAO,CAAC,CAAC;QACP,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,kBAAkB,EAAE;YAClC,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE;gBACN,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAC1D,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;aAC/B;SACF,CAAC,CACH,CAAC;IACJ,CAAC;IAED,mBAAmB,CAAC,CAAC;QACnB,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,wBAAwB,EAAE;YACxC,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;SAC5B,CAAC,CACH,CAAC;IACJ,CAAC;IAED,UAAU,CAAC,CAAC;QACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACxB,UAAU,CACR;gBACE,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,gBAAgB,EAAE;oBAChC,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,IAAI;oBACd,MAAM,EAAE,IAAI,CAAC,IAAI;iBAClB,CAAC,CACH,CAAC;YACJ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,GAAG,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IAED,UAAU,CAAC,CAAC;QACV,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACjC,IAAI,CAAC,aAAa,CAChB,IAAI,WAAW,CAAC,kBAAkB,EAAE;YAClC,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,IAAI,CAAC,IAAI;SAClB,CAAC,CACH,CAAC;IACJ,CAAC;IAED,MAAM;QACJ,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAA,IAAI,IAAI,CAAC,wBAAwB,EAAE;iCACnB,IAAI,CAAC,aAAa;YACvC,IAAI,CAAC,qBAAqB,EAAE;cAC1B,CAAC;QACX,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAA,GAAG,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;QAClD,CAAC;IACH,CAAC;IAED,MAAM,KAAK,MAAM;QACf,OAAO,GAAG,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgDT,CAAC;IACJ,CAAC;CACF,CAAA;AAnQC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;2CACE;AAE7B;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;oDACM;AAEjC;IADC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;oDACC;AANxB,aAAa;IADlB,aAAa,CAAC,iBAAiB,CAAC;GAC3B,aAAa,CAqQlB", "sourcesContent": ["import { LitElement, html, css, PropertyValues } from \"lit\";\r\nimport \"@ui5/webcomponents/dist/Icon.js\";\r\nimport \"@vaadin/vaadin-icons/vaadin-icons.js\";\r\nimport { customElement, property } from \"lit/decorators\";\r\nimport { Collection } from \"@bds/types\";\r\n\r\n@customElement(\"wex-folder-item\")\r\nclass WexFolderItem extends LitElement {\r\n  @property({ type: Object })\r\n  item: Collection | undefined;\r\n  @property({ type: String })\r\n  childrenClass: string = \"nokids\";\r\n  @property({ type: String })\r\n  selectedClass: string = \" \";\r\n\r\n  constructor() {\r\n    super();\r\n    this.selectedNodeId = -1; // ?\r\n    this.isDraggable = false;\r\n  }\r\n\r\n  connectedCallback() {\r\n    super.connectedCallback();\r\n    this.addEventListener(\"dragstart\", this._onDragStart.bind(this));\r\n    this.addEventListener(\"dragenter\", this._onDragEnter.bind(this));\r\n    this.addEventListener(\"dragover\", this._onDragOver.bind(this));\r\n    this.addEventListener(\"dragleave\", this._onDragLeave.bind(this));\r\n    this.addEventListener(\"drop\", this._onDrop.bind(this));\r\n  }\r\n\r\n  protected updated(_changedProperties: PropertyValues): void {\r\n    if (_changedProperties.has(\"item\")) {\r\n      this.updateItem();\r\n    }\r\n  }\r\n\r\n  updateItem() {\r\n    if (!this.item) return;\r\n\r\n    if (this.item.hasOwnProperty(\"children\")) {\r\n      this.childrenClass = \"haskids\"; // ?\r\n    }\r\n    if (this.item.selected) {\r\n      this.selectedClass = \" selected \";\r\n    }\r\n\r\n    if (this.item.selected) {\r\n      this.dispatchEvent(\r\n        new CustomEvent(\"selectedrendered\", {\r\n          bubbles: true,\r\n          composed: true,\r\n          detail: this,\r\n        })\r\n      );\r\n    }\r\n  }\r\n\r\n  folderItemDetailTemplate() {\r\n    if (this.contextmenu) {\r\n      return html` <vaadin-context-menu\r\n        id=\"contextmenu\"\r\n        .items=\"${this.contextmenu}\"\r\n        @item-selected=\"${this._contextmenuclicked.bind(this)}\"\r\n      >\r\n        <div\r\n          class=\"folder-item-title\r\n            ${this.selectedClass}\"\r\n          id=\"div${this.item.id}\"\r\n          @click=\"${this.selectNode.bind(this)}\"\r\n          @contextmenu=\"${this.selectNode.bind(this)}\"\r\n          draggable=\"${this.isDraggable}\"\r\n        >\r\n          ${this.iconTemplate()} ${this.item.name}\r\n        </div>\r\n      </vaadin-context-menu>`;\r\n    } else {\r\n      return html`<div\r\n        class=\"folder-item-title  ${this.selectedClass}\"\r\n        id=\"div${this.item.id}\"\r\n        @click=\"${this.selectNode.bind(this)}\"\r\n        @contextmenu=\"${this._preventDefault.bind(this)}\"\r\n        draggable=\"${this.isDraggable}\"\r\n      >\r\n        ${this.iconTemplate()} ${this.item.name}\r\n      </div>`;\r\n    }\r\n  }\r\n\r\n  iconTemplate() {\r\n    if (this.item.hasOwnProperty(\"children\")) {\r\n      if (this.item.open) {\r\n        return html`<iron-icon\r\n          icon=\"vaadin:angle-down\"\r\n          @click=\"${this.toggleNode.bind(this)}\"\r\n        ></iron-icon>`;\r\n      } else {\r\n        return html`<iron-icon\r\n          icon=\"vaadin:angle-right\"\r\n          @click=\"${this.toggleNode.bind(this)}\"\r\n        ></iron-icon>`;\r\n      }\r\n    } else {\r\n      return html`<iron-icon\r\n        class=\"spacer\"\r\n        icon=\"vaadin:angle-right\"\r\n      ></iron-icon>`;\r\n    }\r\n  }\r\n\r\n  getChilditemsTemplate() {\r\n    if (this.item.open && this.item.hasOwnProperty(\"children\")) {\r\n      return html` <ul class=\"folder-item-list\">\r\n        ${this.item.children.map(\r\n          (item) => html`\r\n            <wex-folder-item\r\n              class=\"folder-item\"\r\n              .item=\"${item}\"\r\n              .isDraggable=${this.isDraggable}\r\n              .contextmenu=\"${this.contextmenu}\"\r\n              .selectedNodeId=\"${this.selectedNodeId}\"\r\n            ></wex-folder-item>\r\n          `\r\n        )}\r\n      </ul>`;\r\n    } else {\r\n      return html``;\r\n    }\r\n  }\r\n\r\n  _preventDefault(e) {\r\n    e.preventDefault();\r\n  }\r\n\r\n  _onDragStart(e) {\r\n    e.stopPropagation();\r\n    e.dataTransfer.setData(\"text/plain\", this.item.id);\r\n  }\r\n\r\n  _onDragEnter(e) {\r\n    e.stopPropagation();\r\n  }\r\n\r\n  _onDragOver(e) {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n  }\r\n\r\n  _onDragLeave(e) {\r\n    e.stopPropagation();\r\n  }\r\n\r\n  _onDrop(e) {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"folderdropfolder\", {\r\n        bubbles: true,\r\n        composed: true,\r\n        detail: {\r\n          collectionId: Number(e.dataTransfer.getData(\"text/plain\")),\r\n          parentId: Number(this.item.id),\r\n        },\r\n      })\r\n    );\r\n  }\r\n\r\n  _contextmenuclicked(e) {\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"foldercontextmenuclick\", {\r\n        bubbles: false,\r\n        composed: true,\r\n        detail: e.detail.value.name,\r\n      })\r\n    );\r\n  }\r\n\r\n  selectNode(e) {\r\n    if (!this.item.selected) {\r\n      setTimeout(\r\n        function () {\r\n          this.dispatchEvent(\r\n            new CustomEvent(\"folderselected\", {\r\n              bubbles: true,\r\n              composed: true,\r\n              detail: this.item,\r\n            })\r\n          );\r\n        }.bind(this),\r\n        200\r\n      );\r\n    }\r\n  }\r\n\r\n  toggleNode(e) {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    this.item.open = !this.item.open;\r\n    this.dispatchEvent(\r\n      new CustomEvent(\"folderopenstatus\", {\r\n        bubbles: true,\r\n        composed: true,\r\n        detail: this.item,\r\n      })\r\n    );\r\n  }\r\n\r\n  render() {\r\n    if (this.item.hasOwnProperty(\"children\") && this.item.open) {\r\n      return html` ${this.folderItemDetailTemplate()}\r\n        <li class=\"folder-item ${this.childrenClass}\">\r\n          ${this.getChilditemsTemplate()}\r\n        </li>`;\r\n    } else {\r\n      return html`${this.folderItemDetailTemplate()}`;\r\n    }\r\n  }\r\n\r\n  static get styles() {\r\n    return css`\r\n      .over {\r\n        font-weight: bold;\r\n      }\r\n      .folder-item-title {\r\n        padding: var(--spacing-sm) var(--spacing-xs);\r\n        display: flex;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        white-space: nowrap;\r\n      }\r\n      .folder-item-list {\r\n        margin-block-start: 0em;\r\n        margin-block-end: 0em;\r\n        margin-inline-start: 0px;\r\n        margin-inline-end: 0px;\r\n        padding-inline-start: 0px;\r\n      }\r\n      .over {\r\n        background-color: lightgrey;\r\n      }\r\n      .selected {\r\n        background-color: var(--row-selected-background);\r\n      }\r\n      ul.open {\r\n        display: block;\r\n      }\r\n      .folder-item-list {\r\n        list-style: none;\r\n      }\r\n      .haskids {\r\n        margin-left: 0;\r\n      }\r\n      .nokids {\r\n        padding-left: 20px;\r\n      }\r\n      .folder-item {\r\n        display: block;\r\n        margin-left: 5px;\r\n        line-height: 0px;\r\n      }\r\n      iron-icon {\r\n        margin-right: var(--spacing-xs);\r\n        --iron-icon-width: 16px;\r\n      }\r\n      .spacer {\r\n        color: rgba(255, 255, 255, 0);\r\n      }\r\n    `;\r\n  }\r\n}\r\n"]}