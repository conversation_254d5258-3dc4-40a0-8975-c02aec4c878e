# Tasks

Get the tasks from the store and display. Fetch in the background to refresh.
If there are no tasks show loading screen while fetching.

## Task Page

- Fetches the tasks
- Passes tasks to the task tiles
- Manages the task filters

Loading:

- If there are no tasks and the fetch is pending show loading screen.
- If there is an error and the fetch is pending show loading screen.
- If there are tasks in the cache and fetch is pending show tasks and the updating spinner
- If there are no tasks and the fetch is compmlete show no tasks message.
- If there is an error and the fetch is complete show error message.

## Task Tile

- Displays task information
- Fetches the files associated with the task
- Passes files to the task file component
- Handles task actions

Loading:

- If there are no files and the fetch is pending show skeleton.
- If there is an error and the fetch is pending show skeleton.
- If there are files in the cache and fetch is pending show files and the updating spinner
- If there are no files and the fetch is compmlete show no files message.
- If there is an error and the fetch is complete show error message.

## Task File
- Displays file information
- Handles file actions
**How do we handle storing the files in cache?** - Use a map by task id?

```ts
/**
 * Displays task information and files associated with the task.
 */
class TaskPage extends LitElement {
  @state({ type: String }) _status: "success" | "error" | "pending" = "pending";
  @state({ type: Array }) _tasks: Task[] = [];

  connectedCallback() {
    super.connectedCallback();
    this.fetchTasks();
  }

  // Use beedle wait for?
  async fetchTasks() {
    // fetch tasks
    // set tasks in beedle store
  }

  render() {
    return html`<ul>
      ${this._tasks.map((task) => html`<task-tile .task=${task}></task-tile>`)}
    </ul>`;
  }
}
```

```ts
/**
 * Displays task information and files associated with the task.
 */
class TaskTile extends LitElement {
  @property({ type: Object }) task: Task | null = null;
  @state({ type: Object }) _files: FileMeta[] = [];
  @state({ type: String }) _status: "success" | "error" | "pending" = "pending";

  render() {}
}
```
