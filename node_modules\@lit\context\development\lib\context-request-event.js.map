{"version": 3, "file": "context-request-event.js", "sourceRoot": "", "sources": ["../../src/lib/context-request-event.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAiCH;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,mBACX,SAAQ,KAAK;IAQb;;;;;;OAMG;IACH,YACE,OAAU,EACV,aAAsB,EACtB,QAAyC,EACzC,SAAmB;QAEnB,KAAK,CAAC,iBAAiB,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,KAAK,CAAC;IACtC,CAAC;CACF", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ContextType, Context} from './create-context.js';\n\ndeclare global {\n  interface HTMLElementEventMap {\n    /**\n     * A 'context-request' event can be emitted by any element which desires\n     * a context value to be injected by an external provider.\n     */\n    'context-request': ContextRequestEvent<Context<unknown, unknown>>;\n  }\n}\n\n/**\n * A callback which is provided by a context requester and is called with the value satisfying the request.\n * This callback can be called multiple times by context providers as the requested value is changed.\n */\nexport type ContextCallback<ValueType> = (\n  value: ValueType,\n  unsubscribe?: () => void\n) => void;\n\n/**\n * Interface definition for a ContextRequest\n */\nexport interface ContextRequest<C extends Context<unknown, unknown>> {\n  readonly context: C;\n  readonly contextTarget: Element;\n  readonly callback: ContextCallback<ContextType<C>>;\n  readonly subscribe?: boolean;\n}\n\n/**\n * An event fired by a context requester to signal it desires a specified context with the given key.\n *\n * A provider should inspect the `context` property of the event to determine if it has a value that can\n * satisfy the request, calling the `callback` with the requested value if so.\n *\n * If the requested context event contains a truthy `subscribe` value, then a provider can call the callback\n * multiple times if the value is changed, if this is the case the provider should pass an `unsubscribe`\n * method to the callback which consumers can invoke to indicate they no longer wish to receive these updates.\n *\n * If no `subscribe` value is present in the event, then the provider can assume that this is a 'one time'\n * request for the context and can therefore not track the consumer.\n */\nexport class ContextRequestEvent<C extends Context<unknown, unknown>>\n  extends Event\n  implements ContextRequest<C>\n{\n  readonly context: C;\n  readonly contextTarget: Element;\n  readonly callback: ContextCallback<ContextType<C>>;\n  readonly subscribe?: boolean;\n\n  /**\n   *\n   * @param context the context key to request\n   * @param contextTarget the original context target of the requester\n   * @param callback the callback that should be invoked when the context with the specified key is available\n   * @param subscribe when, true indicates we want to subscribe to future updates\n   */\n  constructor(\n    context: C,\n    contextTarget: Element,\n    callback: ContextCallback<ContextType<C>>,\n    subscribe?: boolean\n  ) {\n    super('context-request', {bubbles: true, composed: true});\n    this.context = context;\n    this.contextTarget = contextTarget;\n    this.callback = callback;\n    this.subscribe = subscribe ?? false;\n  }\n}\n"]}