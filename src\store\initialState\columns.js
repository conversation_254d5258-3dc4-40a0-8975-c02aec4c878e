/**
 * Contains definitions for column headers in various table layouts
 */
export default {
  task_list_columns: [
    { title: "_isClaimed", fieldname: "isClaimed", order: 18, sort: "none" },
    { title: "_claimAction", fieldname: "isClaimed", order: 19, sort: "none" },
    { title: "_projectName", fieldname: "projectName", order: 2, sort: "none" },
    { title: "_taskInstId", fieldname: "taskInstId", order: 0, sort: "none" },
    {
      title: "_processDefName",
      fieldname: "processDefName",
      order: 4,
      sort: "none",
    },
    { title: "_taskDefName", fieldname: "taskDefName", order: 6, sort: "none" },
    {
      title: "_taskStartDate",
      fieldname: "taskStartDate",
      order: 8,
      sort: "asc",
    },
    { title: "_finishtask", fieldname: "finished", order: 20, sort: "none" },
    {
      title: "_processInstDescription",
      fieldname: "processInstDescription",
      order: 5,
      sort: "none",
    },
    {
      title: "_processInstId",
      fieldname: "processInstId",
      order: 0,
      sort: "none",
    },
    { title: "_projectId", fieldname: "projectId", order: 0, sort: "none" },
    {
      title: "_taskActiveDate",
      fieldname: "taskActiveDate",
      order: 0,
      sort: "none",
    },
    {
      title: "_taskDefDescription",
      fieldname: "taskDefDescription",
      order: 0,
      sort: "none",
    },
    { title: "_taskDefId", fieldname: "taskDefId", order: 0, sort: "none" },
    { title: "_taskId", fieldname: "taskId", order: 0, sort: "none" },
    {
      title: "_taskInstComment",
      fieldname: "taskInstComment",
      order: 0,
      sort: "none",
    },
    {
      title: "_userWexCapability",
      fieldname: "userWexCapability",
      order: 0,
      sort: "none",
    },
  ],
  task_history_columns: [
    { title: "_active", fieldname: "active", order: 0, sort: "none" },
    { title: "_user", fieldname: "claimingUserName", order: 1, sort: "none" },
    { title: "_startTime", fieldname: "startTime", order: 2, sort: "desc" },
    { title: "_taskName", fieldname: "taskDefName", order: 3, sort: "none" },
  ],
  task_filepackage_columns: [
    { title: "_reviewed", fieldname: "reviewed", order: 1, sort: "none" },
    { title: "_name", fieldname: "name", order: 2, sort: "asc" },
    { title: "_title", fieldname: "title", order: 3, sort: "none" },
    { title: "_resLblId", fieldname: "resLblId", order: 4, sort: "none" },
    { title: "_ditaClass", fieldname: "ditaClass", order: 0, sort: "none" },
    { title: "_isXml", fieldname: "isXml", order: 0, sort: "none" },
    { title: "_lineageId", fieldname: "lineageId", order: 0, sort: "none" },
    { title: "_mimeType", fieldname: "mimeType", order: 0, sort: "none" },
    { title: "_resPathId", fieldname: "resPathId", order: 0, sort: "none" },
    {
      title: "_rootElementName",
      fieldname: "rootElementName",
      order: 0,
      sort: "none",
    },
    { title: "_verNum", fieldname: "verNum", order: 0, sort: "none" },
    {
      title: "_verCreateDate",
      fieldname: "verCreateDate",
      order: 0,
      sort: "none",
    },
    { title: "_verCreator", fieldname: "verCreator", order: 0, sort: "none" },
  ],
  /** PROCESS COLUMNS */
  process_list_columns: [
    { label: "_processId", field: "processInstId", order: 0, sort: "none" },
    {
      label: "_processInstDescription",
      field: "description",
      order: 0,
      sort: "none",
    },
    {
      label: "_processDefName",
      field: "processDefinitionName",
      order: 0,
      sort: "none",
    },

    { label: "_projectName", field: "projectName", order: 0, sort: "none" },
    {
      label: "_taskDefName",
      field: "activeParallelTasks[0].taskDefName",
      order: 6,
      sort: "none",
    },
    {
      label: "_taskStartDate",
      field: "startDate",
      order: 8,
      sort: "asc",
    },
    {
      label: "_isClaimed",
      field: "activeParallelTasks[0].isClaimed",
      order: 0,
      sort: "none",
    },
  ],
  candidate_columns: [
    { label: "_candidate_tasks", field: "m_taskDefName" },
    { label: "_candidate_role", field: "m_candidateRoleName" },
    // { label: "_candidates", field: "candidateUserNames" },
  ],
  process_package_columns: [
    { label: "_process_package_title", field: "title" },
    { label: "_process_package_name", field: "name" },
  ],
  /** ---------- */
  editor_filepackage_columns: [
    { title: "_reviewed", fieldname: "reviewed", order: 1, sort: "none" },
    { title: "_name", fieldname: "name", order: 2, sort: "asc" },
    { title: "_title", fieldname: "title", order: 0, sort: "none" },
    { title: "_resLblId", fieldname: "resLblId", order: 0, sort: "none" },
    { title: "_ditaClass", fieldname: "ditaClass", order: 0, sort: "none" },
    { title: "_isXml", fieldname: "isXml", order: 0, sort: "none" },
    { title: "_lineageId", fieldname: "lineageId", order: 0, sort: "none" },
    { title: "_mimeType", fieldname: "mimeType", order: 0, sort: "none" },
    { title: "_resPathId", fieldname: "resPathId", order: 0, sort: "none" },
    {
      title: "_rootElementName",
      fieldname: "rootElementName",
      order: 0,
      sort: "none",
    },
    { title: "_verNum", fieldname: "verNum", order: 0, sort: "none" },
    {
      title: "_verCreateDate",
      fieldname: "verCreateDate",
      order: 0,
      sort: "none",
    },
    { title: "_verCreator", fieldname: "verCreator", order: 0, sort: "none" },
  ],
  selectfile_dialog_columns: [
    { title: "_name", fieldname: "name", order: 1, sort: "asc" },
    { title: "_title", fieldname: "title", order: 2, sort: "none" },
    { title: "_size", fieldname: "size", order: 2, sort: "none" },
    { title: "_mimeType", fieldname: "mimeType", order: 3, sort: "none" },
  ],
  file_picker_columns: [
    { label: "_name", field: "name", order: 1, sort: "asc" },
    { label: "_title", field: "title", order: 2, sort: "none" },
    { label: "_size", field: "size", order: 2, sort: "none" },
    { label: "_mimeType", field: "mimeType", order: 3, sort: "none" },
  ],
  search_columns: [
    { title: "_name", fieldname: "name", order: 0, sort: "none" },
    { title: "_title", fieldname: "title", order: 1, sort: "none" },
    { title: "_resLblId", fieldname: "resLblId", order: 0, sort: "none" },
    { title: "_ditaClass", fieldname: "ditaClass", order: 0, sort: "none" },
    { title: "_isXml", fieldname: "isXml", order: 0, sort: "none" },
    { title: "_lineageId", fieldname: "lineageId", order: 0, sort: "none" },
    { title: "_mimeType", fieldname: "mimeType", order: 0, sort: "none" },
    { title: "_resPathId", fieldname: "resPathId", order: 0, sort: "none" },
    {
      title: "_rootElementName",
      fieldname: "rootElementName",
      order: 0,
      sort: "none",
    },
    { title: "_verNum", fieldname: "verNum", order: 0, sort: "none" },
    {
      title: "_verCreateDate",
      fieldname: "verCreateDate",
      order: 0,
      sort: "none",
    },
    { title: "_verCreator", fieldname: "verCreator", order: 0, sort: "none" },
    { title: "_folderPath", fieldname: "folderPath", order: 0, sort: "none" },
    { title: "_filePath", fieldname: "resPathId", order: 2, sort: "none" },
  ],
  browse_columns: [
    { title: "_name", fieldname: "name", order: 2, sort: "asc" },
    { title: "_title", fieldname: "title", order: 3, sort: "none" },
    { title: "_resLblId", fieldname: "resLblId", order: 0, sort: "none" },
    { title: "_ditaClass", fieldname: "ditaClass", order: 0, sort: "none" },
    { title: "_isXml", fieldname: "isXml", order: 0, sort: "none" },
    { title: "_lineageId", fieldname: "lineageId", order: 0, sort: "none" },
    { title: "_mimeType", fieldname: "mimeType", order: 0, sort: "none" },
    { title: "_resPathId", fieldname: "resPathId", order: 0, sort: "none" },
    {
      title: "_rootElementName",
      fieldname: "rootElementName",
      order: 0,
      sort: "none",
    },
    { title: "_verNum", fieldname: "verNum", order: 0, sort: "none" },
    {
      title: "_verCreateDate",
      fieldname: "verCreateDate",
      order: 0,
      sort: "none",
    },
    { title: "_verCreator", fieldname: "verCreator", order: 0, sort: "none" },
  ],
  reports_columns: [
    {
      field: "status",
      label: "",
      isSortable: false,
      sort: null,
    },
    {
      field: "title",
      label: "_title",
      isSortable: true,
      sort: "asc",
    },
    {
      field: "name",
      label: "_name",
      isSortable: true,
      sort: null,
    },
    {
      field: "lockOwner",
      label: "_lock_owner",
      isSortable: true,
      sort: null,
    },
    {
      field: "lockLocation",
      label: "_lock_location",
      isSortable: false,
      sort: null,
    },
    {
      field: "lockDate",
      label: "_lock_date",
      isSortable: true,
      sort: null,
    },
    {
      field: "resPathId",
      label: "_containing_folder",
      isSortable: false,
      sort: null,
    },
  ],
  columns: {
    publishDefs: [
      { field: "pubDefId", label: "_pubDefId", isSortable: false, sort: null },
      { field: "name", label: "_name", isSortable: false, sort: null },
      { field: "desc", label: "_desc", isSortable: false, sort: null },
      {
        field: "projectName",
        label: "_projectName",
        isSortable: false,
        sort: null,
      },
      {
        field: "projectId",
        label: "_projectId",
        isSortable: false,
        sort: null,
      },
    ],
    publishRunDefs: [
      { field: "name", label: "_name", isSortable: true, sort: null },
    ],
    publishJobsDefJobs: [
      // { field: "pubdefJobId", label: "_id", isSortable: false, sort: null },
      {
        field: "pubdefName",
        label: "_definition",
        isSortable: true,
        sort: null,
      },
      {
        field: "creatorUserName",
        label: "_publisher",
        isSortable: true,
        sort: null,
      },
      {
        field: "jobStartDate",
        label: "_start_date",
        isSortable: true,
        sort: null,
      },
      {
        field: "outputJobTotal",
        label: "_jobs_total",
        isSortable: false,
        sort: null,
      },
    ],
    publishJobsOutputJobs: [
      // {
      //   field: "outputJobInfoId",
      //   label: "_id",
      //   isSortable: true,
      //   sort: null,
      // },
      // {
      //   field: "outputFileName",
      //   label: "_output",
      //   isSortable: true,
      //   sort: null,
      // },
      // {
      //   field: "",
      //   label: "_definition",
      //   isSortable: true,
      //   sort: null,
      // },
      {
        field: "dPubContentName",
        label: "_publication",
        isSortable: true,
        sort: null,
      },
      // { field: "ditaval", label: "_ditaval", isSortable: true, sort: null },
      { field: "langCode", label: "_language", isSortable: true, sort: null },
      {
        field: "outputTypeName",
        label: "_format",
        isSortable: true,
        sort: null,
      },
      {
        field: "pctComplete",
        label: "_progress",
        isSortable: false,
        sort: null,
        // renderer: "pctCompleteRenderer",
      },
      {
        field: "status",
        label: "_status",
        isSortable: false,
        sort: null,
        // renderer: "pctCompleteRenderer",
      },
      // {
      //   field: "info",
      //   label: "_info",
      //   isSortable: false,
      //   sort: null,
      //   renderer: "infoRenderer",
      // },
      // { field: "pctComplete", label: "_status", isSortable: true, sort: null },
      // {
      //   field: "startDate",
      //   label: "_start_date",
      //   isSortable: true,
      //   sort: null,
      // },
      // {
      //   field: "endDate",
      //   label: "_end_date",
      //   isSortable: true,
      //   sort: null,
      // },
    ],
  },
};
