import { LitElement, html, css } from "lit-element";
import { storeInstance } from "store";
import pubsub from "pubsub-js";
import * as util from "lib/util.js";
import * as wexlib from "lib/wexlib.js";

// import "../wex-table";
import "../../components/wex-table";
import "../../components/base/row-item";
import "../../components/base/icon";
import "../../components/header/reports";

const DEBOUNCE_DURATION = 1000;

class WexPageReportsLocks extends LitElement {
  static get properties() {
    return {
      _users: { type: Array },
      _columns: { type: Array },
      _rows: { type: Array },
    };
  }

  static get styles() {
    return css`
      #reports-locks-container {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        max-height: 100%;
      }
      .mainpane {
        display: flex;
        flex: 1;
        flex-direction: row;
        align-items: stretch;
        justify-content: space-evenly;
        justify-content: center;
        /* padding: 0rem 1.25rem 0 1.25rem; */
        color: var(--font-color);
        height: 100%;
      }
      .mainpane > *:not(:last-child) {
        margin-bottom: 1rem;
        border: 1px solid #f00;
      }
      .split-layout {
        width: 100%;
        height: 100%;
      }
      section.left {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;

        align-items: center;
        padding: 2rem 1.25rem;
        width: 20%;
      }

      section.left > *:not(:last-child) {
        margin-bottom: 0.5rem;
      }

      .sidepane-item {
        width: min(250px, 100%);
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
      .sidepane-list {
        width: min(250px, 100%);
      }
      .check-container {
        display: flex;
        align-items: center;
      }
      .check-container > *:not(:last-child) {
        margin-right: 0.5rem;
      }

      button {
        margin: 0;
        padding: 0;
        border: 0;
        background: transparent;
        overflow: hidden;
        cursor: pointer;
      }

      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        border: 1px solid var(--clr-gray-light);
        overflow-y: auto;
      }
      li {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 0 1rem;
        height: 2.25rem;
        border-bottom: 1px solid var(--clr-gray-light);
      }
      li > span {
        flex-grow: 1;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li > .icon-container {
        position: relative;
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
      }
      li > .icon-container:hover {
        background: var(--clr-white);
      }
      li > *:not(:last-child) {
        margin-right: 1rem;
      }
      li[active] {
        background: var(--row-selected-background);
      }
      li[active] {
        background: var(--row-selected-background);
      }
      li:not([active]):hover {
        background: var(--clr-gray-ultra-light);
      }
    `;
  }

  constructor() {
    super();
    this._state = null;
    this._string = {};
    this._subscription = null;

    // properties
    // this._modeMenu = [];
    this._users = [];
    this._columns = [];
    this._rows = [];

    this._menuItems = [];
    this._unlockEnabled = true;
    this._selectUserEnabled = false;
    this._roomsEnabled = false;
    this._debounceTimer = {
      users: 0,
      rows: 0,
      unlock: 0,
    };

    // mode
    // this._mode = "";
    // this._modeMenuBtn = null;
    // this._modeMenuPopup = null;
  }

  connectedCallback() {
    super.connectedCallback();
    this._state = storeInstance.state;
    this._string = this._state[this._state.langCode];
    this._subscription = storeInstance.subscribe((state) => {
      this.stateChange(state);
    });

    this._init();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    storeInstance.unsubscribe(this._subscription);
  }

  async _init() {
    this._columns = this._state.reports_columns;
    this._users = await this._initUsers();
    this._rows = await this._initRows();
    this._initSelectUser();
  }

  _initUsers() {
    return new Promise((resolve) => {
      if (this._users.length > 0) {
        resolve(this._users);
      }

      const res = [
        { name: this._state.wex_user.userName },
        { name: "concurrent" },
      ];
      resolve(res);
    });
  }

  async _initSelectUser() {
    if (!this._state.wex_user) return false;

    const cap = this._state.wex_user.globalCapability;
    const a = util.getCapValue(cap);
    const b = util.getCapValue("review");
    this._selectUserEnabled = a > b;

    try {
      const name = this._state.wex_user.userName;
      const group = "XdocsAdmin";
      this._unlockEnabled = await wexlib.isUserInGroup(name, group);
    } catch {}
  }

  _initRows() {
    debugger;
    if (!this._users.length) return [];
    return new Promise(async (resolve, reject) => {
      try {
        const rows = await wexlib.requestLockedFiles(this._users);
        const user = this._state.wex_user;
        rows.forEach((x) => (x.status = util.getFileLockStatus(x, user)));
        rows.forEach((x) =>
          (x.roomRow =
            (x.lockorType == "RoomConcurrent" ||
              x.lockorType == "RoomExclusive") &&
            x.status != "_rogue_lock")
            ? true
            : false
        );
        const filters = rows.filter(
          (x) => Boolean(x.roomRow) == Boolean(this._roomsEnabled)
        );
        /*
        const filters = rows.filter((x) => {
          if (this._roomsEnabled) return true;
          return (
            x.lockorType !== "RoomConcurrent" &&
            x.lockorType !== "RoomExclusive" 
          );
        });
        */
        resolve(filters);
        console.log("etf");
      } catch (err) {
        console.error(err);
        reject();
      }
    });
  }

  async _refresh() {
    const d = Date.now() - this._debounceTimer.rows;
    if (d <= DEBOUNCE_DURATION) return;

    this._rows = await this._initRows();
    this._debounceTimer.rows = Date.now();
  }

  stateChange(state) {
    this._state = state;
    this._string = state[state.langCode];
  }

  _toggleRooms() {
    const d = Date.now() - this._debounceTimer.rows;
    if (d <= DEBOUNCE_DURATION) return;

    this._roomsEnabled = !this._roomsEnabled;
    this._refresh();
  }

  _hasSelected() {
    const res = this._rows.find((x) => x.selected);
    return !!res;
  }

  _handleRightClick(e) {
    const data = e.detail;
    if (!data) return;

    const { value: file } = data;
    // deep clone
    const rows = JSON.parse(JSON.stringify(this._rows));
    // select row if not selected
    if (file) {
      // if right-clicked item is already selected, keep selected (do nothing)
      // else deselect all and select right-clicked row
      const idx = rows
        .filter((x) => x.selected)
        .findIndex((x) => x.resLblId == file.resLblId);
      if (idx < 0) {
        rows.forEach((x) => (x.selected = x.resLblId == file.resLblId));
      }
    }
    // right-click not on row
    else {
      //
    }
    const selected = rows.filter((x) => x.selected);
    const cap = this._state.wex_user.globalCapability;
    const menuItems = util.buildFileMenu(
      selected.length == 1 ? file : null,
      this._state.menus.file_menu,
      "reports",
      cap
    );
    menuItems.map((x) => (x.text = this._string[x.label]));
    this._menuItems = menuItems;
    this._rows = rows;
  }

  // move functionality to dialog itself
  async _unlock() {
    const files = this._rows.filter((x) => x.selected);
    if (!files.length) {
      return util.notifyUser("warn", this._string["_msg_no_file_selected"]);
    }

    /*
      const invalidFiles = this._rows.filter(
        (x) =>
          x.selected &&
          (x.lockorType == "RoomConcurrent" || x.lockorType == "RoomExclusive")
      );
      if (invalidFiles.length > 0) {
        return util.notifyUser("warn", this._string["_msg_cannot_unlock_rooms"]);
      }
      */

    const d = Date.now() - this._debounceTimer.unlock;
    if (d <= DEBOUNCE_DURATION) return;

    const rogueLockFiles = files.filter((file) => file.status == "_rogue_lock");
    const normalLockFiles = files.filter(
      (file) => file.status != "_rogue_lock"
    );
    if (rogueLockFiles.length > 0) {
      await wexlib.requestUnlockFiles(rogueLockFiles, true);
    }
    if (normalLockFiles.length > 0) {
      await wexlib.requestUnlockFiles(normalLockFiles, false);
    }
    this._debounceTimer.unlock = Date.now();
    this._refresh();
    files.forEach((x) =>
      storeInstance.dispatch("removeEditorById", x.resLblId)
    );
    util.notifyUser("positive", this._string["_msg_unlock_files_success"]);
  }

  // _handleAction() {
  //   console.log("_handleAction");
  // }
  async _handleAction(action = null, e) {
    e.stopPropagation();
    // const action = e.currentTarget.getAttribute("data-action");
    if (!action) return;

    switch (action) {
      case "refresh":
        this._refresh();
        break;
      case "unlock":
        pubsub.publish("dialog-message", {
          type: "open",
          data: {
            header: this._string["_warning"],
            message: this._string["_reports_dialog_unlock_warning"],
            callback: this._unlock.bind(this),
          },
        });
        break;
      default:
        break;
    }
  }

  // _handleFileAction() {
  //   console.log("_handleFileAction");
  // }
  _handleFileAction(e) {
    const data = e.detail;
    if (!data) return;
    const files = this._rows.filter((x) => x.selected);
    if (files.length !== 1) return;

    const file = files.shift();
    const { action } = data;
    storeInstance.dispatch("handleFileAction", { action, file });
  }

  _handleSelectAllRows(e) {
    const data = e.detail;
    if (!data) return;

    const { value } = data;
    // deep clone
    const rows = JSON.parse(JSON.stringify(this._rows));
    rows.forEach((row) => (row.selected = value));
    this._rows = rows;
  }

  _handleSort() {
    console.log("_handleSort");
  }

  _handleSelectRow(e) {
    const data = e.detail;
    if (!data) return;

    const { value: file } = data;
    // deep clone
    const rows = JSON.parse(JSON.stringify(this._rows));
    rows.forEach((row) =>
      row.resLblId == file.resLblId ? (row.selected = !row.selected) : null
    );
    this._rows = rows;
  }

  _handleRowAction() {
    console.log("_handleRowAction");
  }

  async _handleRequestSelectUsers() {
    try {
      const user = this._state.wex_user.userName;
      const list = this._users;
      const res = await wexlib.requestUsers({
        currUser: user,
        currList: list,
      });
      // TODO: EJS this needs to use the dialogOpenData model now
      storeInstance.dispatch("setState", {
        property: "wex_select_dialog_open",
        value: {
          // dialogOpenStateProperty: "wex_select_dialog_open",
          headerText: "Select a User",
          data: res,
        },
      });
      // pubsub.publish("dialog-select", {
      //   type: "open",
      //   data: {
      //     cbMsg: "res-reports-select-users",
      //     header: this._string["_select_users"],
      //     list: res,
      //   },
      // });
    } catch (err) {
      console.log(err);
    }
  }

  _renderHeader() {
    const unlockEnabled = this._unlockEnabled && this._hasSelected();
    const template = html`
      <wex-reports-header>
        <ui5-button
          design="Emphasized"
          ?disabled="${!unlockEnabled}"
          aria-action="unlock"
          @click=${this._handleAction.bind(this, "unlock")}
        >
          ${this._string["_unlock"]}
        </ui5-button>
        <ui5-button
          design="Emphasized"
          aria-action="refresh"
          @click=${this._handleAction.bind(this, "refresh")}
        >
          ${this._string["_refresh"]}
        </ui5-button>
      </wex-reports-header>
    `;
    return template;
  }

  _renderLeftPane() {
    const template = html`
      <section class="left">
        <div class="sidepane-item">
          <h3>${this._string["_reports_selected_users"]}</h3>
          ${this._selectUserEnabled
            ? html`
                <wex-icon
                  icon="create"
                  title="${this._string["_select_users"]}"
                  @click="${this._handleRequestSelectUsers.bind(this)}"
                ></wex-icon>
              `
            : null}
        </div>
        <ul class="sidepane-list">
          ${this._users.map(
            (user) =>
              html`<li><span title="${user.name}">${user.name}</span></li>`
          )}
        </ul>
        <div class="check-container">
          <button @click="${this._toggleRooms.bind(this)}">
            <wex-icon
              icon="${this._roomsEnabled
                ? "icons:check-box"
                : "icons:check-box-outline-blank"}"
              class="action-icon check-icon"
            ></wex-icon>
          </button>
          <span>${this._string["_reports_show_rooms"]}</span>
        </div>
      </section>
    `;
    return template;
  }

  _renderRightPane() {
    const template = html`
      <section class="right">
        <wex-table
          .columns="${this._columns}"
          .rows="${this._rows}"
          @sort=${this._handleSort.bind(this)}
          @actionclick=${this._handleRowAction.bind(this)}
          .enableSelect="${true}"
          @select=${this._handleSelectRow.bind(this)}
          @selectall=${this._handleSelectAllRows.bind(this)}
          .enableMenu="${true}"
          .menuItems="${this._menuItems}"
          @rightclick=${this._handleRightClick.bind(this)}
          @menuclick=${this._handleFileAction.bind(this)}
        ></wex-table>
      </section>
    `;
    return template;
  }

  _renderMainPane() {
    const template = html`
      <section class="mainpane">
        <vaadin-split-layout class="split-layout">
          ${this._renderLeftPane()} ${this._renderRightPane()}
        </vaadin-split-layout>
      </section>
    `;
    return template;
  }

  render() {
    const template = html`
      <div id="reports-locks-container">
        ${this._renderHeader()} ${this._renderMainPane()}
      </div>
    `;
    return template;
  }
}

customElements.define("wex-page-reports-locks", WexPageReportsLocks);
